﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <HigherTimeframeCandles>
    <HigherTimeframeCandles xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2099-12-01T00:00:00</From>
      <Panel>-1</Panel>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
      <To>1800-01-01T00:00:00</To>
      <Calculate>OnPriceChange</Calculate>
      <Displacement>0</Displacement>
      <IsAutoScale>true</IsAutoScale>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>Higher timeframe candles</Name>
      <Plots />
      <SelectedValueSeries>0</SelectedValueSeries>
      <InputPlot>0</InputPlot>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
      <DrawVerticalGridLines>true</DrawVerticalGridLines>
      <DrawOnPricePanel>true</DrawOnPricePanel>
      <PaintPriceMarkers>true</PaintPriceMarkers>
      <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
      <IndicatorId>1084</IndicatorId>
      <MaxSerialized>0</MaxSerialized>
      <MinSerialized>0</MinSerialized>
      <ZOrder>-2147483648</ZOrder>
      <PeriodType>Minute</PeriodType>
      <Period>60</Period>
      <BullishColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</BullishColorSerializable>
      <BearishColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</BearishColorSerializable>
      <WickColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</WickColorSerializable>
      <Opacity>20</Opacity>
      <OutlineOpacity>100</OutlineOpacity>
      <BorderWidth>1</BorderWidth>
    </HigherTimeframeCandles>
  </HigherTimeframeCandles>
</NinjaTrader>