﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-530159dfe034489b905a4c0599b99e6b>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>821;43</Location>
      <Size>1100;988</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>148</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-c0633b80f5a7416a83337e0f16e79842>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>7</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-14T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2099-12-01T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>d2fde978b1d34060bbf7732caaea433f</BarsSeriesId>
                <Id>d2fde978b1d34060bbf7732caaea433f</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>2.4939348695153094</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1">
              <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>7</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-05-17T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-22T00:00:00</To>
                <Calculate>OnPriceChange</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Price line</Name>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Ask line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Bid line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Last line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>7</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10005</ZOrder>
                <ShowAskLine>false</ShowAskLine>
                <ShowBidLine>false</ShowBidLine>
                <ShowLastLine>true</ShowLastLine>
                <AskLineLength>100</AskLineLength>
                <BidLineLength>100</BidLineLength>
                <LastLineLength>100</LastLineLength>
                <AskStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </AskStroke>
                <BidStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </BidStroke>
                <LastStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </LastStroke>
              </PriceLine>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1">
              <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>7</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>false</DisplayInDataBox>
                <From>2025-05-17T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-22T00:00:00</To>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Bar timer</Name>
                <Plots />
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>10</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10006</ZOrder>
              </BarTimer>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
          </Indicators>
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>8.313118</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>876</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21239.75</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>20687.75</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-c0633b80f5a7416a83337e0f16e79842>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>Playback101</Account>
        <ATM>NQ</ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>3</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-530159dfe034489b905a4c0599b99e6b>
  </NTWindows>
</NinjaTrader>