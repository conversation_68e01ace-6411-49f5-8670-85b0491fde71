#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.NinjaScript;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Strategies;
#endregion

namespace NinjaTrader.NinjaScript.Strategies.KCStrategies
{
    public class Swinger : KCAlgoBase
    {
        private SwingHiLow dhlIndicator; // Using the class name you finalized (e.g., SwingHiLow_MAXMIN)

		private double lastSwingLowForStop;
		private double lastSwingHighForStop;
		
		// Variables to store the bar number of the last touch of DHL levels
		private int barOfLastP1LowTouch = -1;
		private int barOfLastP2LowTouch = -1;
		private int barOfLastP1HighTouch = -1;
		private int barOfLastP2HighTouch = -1;
		
		// Variables to store the *price* of the DHL level at the time of the touch
		private double priceAtLastP1LowTouch = 0;
		private double priceAtLastP2LowTouch = 0;
		private double priceAtLastP1HighTouch = 0;
		private double priceAtLastP2HighTouch = 0;
		
        #region Strategy Parameters
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "DHL Period 1 (Short)", Order = 1, GroupName = "08a. Strategy - DHL Params")]
        public int DhlPeriod1 { get; set; } = 30;

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "DHL Period 2 (Long)", Order = 2, GroupName = "08a. Strategy - DHL Params")]
        public int DhlPeriod2 { get; set; } = 60;

        [NinjaScriptProperty]
        [Display(Name = "Enable Trend Entries", Order = 10, GroupName = "08a. Strategy - Entry Types")]
        public bool EnableTrendEntries { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "Enable Reversal Entries", Order = 11, GroupName = "08a. Strategy - Entry Types")]
        public bool EnableReversalEntries { get; set; } = true;
        
        [NinjaScriptProperty]
        [Display(Name = "Enable Retest Reversal Entries", Order = 12, GroupName = "08a. Strategy - Entry Types", Description = "Enable reversal entries based on price retesting a DHL level and then reversing.")]
        public bool EnableRetestReversalEntries { get; set; } = true; // Default to true for testing

        [NinjaScriptProperty]
        [Display(Name = "Use KCBase Trend Filter", Order = 13, GroupName = "08a. Strategy - Entry Types", Description ="Applies KCAlgoBase uptrend/downtrend flags as an additional filter to respective entries.")]
        public bool UseKcBaseTrendFilter { get; set; } = true;

        // Visual parameters for DHL lines if you want to override them from strategy
        // (Alternatively, configure them directly on the indicator instance on the chart)
        // For simplicity, we'll assume DHL indicator's own visual settings are used.
        // If you want to control DHL's LineWidth, Colors, Styles from strategy, add properties here
        // and pass them in InitializeIndicators.

        #endregion

		public override string DisplayName { get { return Name; } }
		
        protected override void OnStateChange()
        {
            base.OnStateChange();

            if (State == State.SetDefaults)
            {
                Description = "Trades trend and reversal signals based on Dual High/Low indicator.";
                Name = "Swinger v1.0.0";
                StrategyName = "Swinger";
                Version = "1.0.0 Jun. 2025";
                Credits = "Strategy by Khanh Nguyen";
                ChartType =  "Tbars 50";
				
				EnableRetestReversalEntries = true; // Ensure it has a default
				
                InitialStop		= 80;
				ProfitTarget	= 80;
            }
			else if (State == State.Configure)
            {
               
            }
            else if (State == State.DataLoaded)
            {
                InitializeIndicators();
            }
        }
        
        protected override void OnBarUpdate()
        {
            // It's critical that BarsRequiredToTrade in KCAlgoBase is sufficient
            // AND CurrentBar must be high enough for dhlIndicator's *longest* period.
            int requiredBarsForDHL = Math.Max(DhlPeriod1, DhlPeriod2);
            if (CurrentBars[0] < BarsRequiredToTrade || CurrentBars[0] < requiredBarsForDHL)
            {
                // PrintOnce("Swinger_OBU_TooFewBars", $"Bar: {CurrentBar}, Need Base: {BarsRequiredToTrade}, Need DHL: {requiredBarsForDHL}");
                return;
            }
            
            if (dhlIndicator == null) // dhlIndicator could be null if InitializeIndicators failed
            {
                 PrintOnce("Swinger_OBU_NoIndi", "DHL Indicator is null in OBU. Cannot proceed.");
                 return;
            }

            // --- Update DHL Touch Information ---
            // (The rest of this section remains the same, as it uses dhlIndicator.PeriodXLowValue[0]
            //  and the CurrentBar check above should ensure these are safe to access)
            double currentP1Low = dhlIndicator.Period1LowValue[0];
            double currentP2Low = dhlIndicator.Period2LowValue[0];
            double currentP1High = dhlIndicator.Period1HighValue[0];
            double currentP2High = dhlIndicator.Period2HighValue[0];

            if (currentP1Low > 0 && Math.Abs(Low[0] - currentP1Low) < (TickSize * 0.5))
            {
                barOfLastP1LowTouch = CurrentBar;
                priceAtLastP1LowTouch = currentP1Low;
            }
            if (currentP2Low > 0 && Math.Abs(Low[0] - currentP2Low) < (TickSize * 0.5))
            {
                barOfLastP2LowTouch = CurrentBar;
                priceAtLastP2LowTouch = currentP2Low;
            }

            if (currentP1High > 0 && Math.Abs(High[0] - currentP1High) < (TickSize * 0.5))
            {
                barOfLastP1HighTouch = CurrentBar;
                priceAtLastP1HighTouch = currentP1High;
            }
            if (currentP2High > 0 && Math.Abs(High[0] - currentP2High) < (TickSize * 0.5))
            {
                barOfLastP2HighTouch = CurrentBar;
                priceAtLastP2HighTouch = currentP2High;
            }
            // --- End Update DHL Touch Information ---

            base.OnBarUpdate(); 
        }
		
        protected override bool ValidateEntryLong()
        {
            // dhlIndicator null check is now primarily in OnBarUpdate before this is called by base.OnBarUpdate()
            // but an additional check here is safe.
            if (dhlIndicator == null || CurrentBar < 1) 
                return false;

            // Ensure DHL indicator has enough bars for its calculation before accessing its plot values.
            // MAX/MIN(Series, Period) needs 'Period' bars of data to be reliable.
            // The plots are 0-indexed, so dhlIndicator.Period1HighValue[0] is valid if CurrentBar >= Period -1.
            // Since we also use [1] for some logic, CurrentBar >= Period is safer for those.
            // The most restrictive is using Period2 (the longer period).
            if (CurrentBar < Math.Max(DhlPeriod1, DhlPeriod2) -1) { // -1 because MAX/MIN(High, P)[0] is valid at CB = P-1
                 //PrintOnce($"ValLE_DHL_NotEnoughData_{CurrentBar}", $"{Time[0]}: DHL not enough data for ValidateEntryLong. CB: {CurrentBar}");
                 return false;
            }

            // --- Trend Entry ---
            bool trendEntrySignal = false;
            if (EnableTrendEntries)
            {
                // For CrossAbove, it checks [0] vs [0] and [1] vs [1].
                // Ensure both series have valid data at [0] and [1].
                // The CurrentBar check above should cover dhlIndicator.PeriodXHighValue[0].
                // For [1], we need CurrentBar >= Math.Max(DhlPeriod1, DhlPeriod2).
                if (CurrentBar >= Math.Max(DhlPeriod1, DhlPeriod2)) // Stricter check for [1] access
                {
                    if (dhlIndicator.Period1HighValue.IsValidDataPoint(1) && dhlIndicator.Period2HighValue.IsValidDataPoint(1))
                    {
                        bool shortHighAboveLongHigh = CrossAbove(dhlIndicator.Period1HighValue, dhlIndicator.Period2HighValue, 1);
                        if (shortHighAboveLongHigh)
                        {
                            trendEntrySignal = true;
                            PrintOnce($"ValLE_DHLTrend_{CurrentBar}", $"{Time[0]}: DHL Trend Long: P1High ({dhlIndicator.Period1HighValue[0]:F2}) crossed P2High ({dhlIndicator.Period2HighValue[0]:F2}) from previous bar values.");
                        }
                    }
                }
            }

            // --- Existing Reversal Entry ---
            bool existingReversalEntrySignal = false;
            if (EnableReversalEntries)
            {
                if (dhlIndicator.Period1LowValue.IsValidDataPoint(0) && dhlIndicator.Period2LowValue.IsValidDataPoint(0))
                {
                    bool shortLowAboveLongLow = dhlIndicator.Period1LowValue[0] > dhlIndicator.Period2LowValue[0];
                    bool priceRisingOriginal = Close[0] > Open[0] && Close[0] > Close[1];
                    if (shortLowAboveLongLow && priceRisingOriginal && dhlIndicator.Period1LowValue[0] > 0 && dhlIndicator.Period2LowValue[0] > 0)
                    {
                        existingReversalEntrySignal = true;
                        PrintOnce($"ValLE_DHLReversalOrig_{CurrentBar}", $"{Time[0]}: DHL Original Reversal Long: P1Low ({dhlIndicator.Period1LowValue[0]:F2}) > P2Low ({dhlIndicator.Period2LowValue[0]:F2}) AND Price Rising");
                    }
                }
            }

            // --- CORRECTED RETEST REVERSAL LOGIC ---
            bool retestReversalEntrySignal = false;
            if (EnableRetestReversalEntries && CurrentBar > 0) 
            {
                // barOfLast...Touch variables are updated in OnBarUpdate prior to this call
                bool prevBarTouchedP1Low = (barOfLastP1LowTouch == CurrentBar - 1);
                bool prevBarTouchedP2Low = (barOfLastP2LowTouch == CurrentBar - 1);
                bool prevBarTouchedAnyDhlLow = prevBarTouchedP1Low || prevBarTouchedP2Low;

                bool currentLowHigherThanPrev = Low[0] > Low[1];
                bool priceIsRisingNow = Close[0] > Open[0]; 

                if (prevBarTouchedAnyDhlLow && currentLowHigherThanPrev && priceIsRisingNow)
                {
                    retestReversalEntrySignal = true;
                    double retestLevelPrice = prevBarTouchedP1Low ? priceAtLastP1LowTouch : priceAtLastP2LowTouch;
                    string retestLevelName  = prevBarTouchedP1Low ? "P1Low" : "P2Low";
                    PrintOnce($"ValLE_DHLRetestRev_{CurrentBar}", $"{Time[0]}: DHL Retest Reversal Long: Low[1] ({Low[1]:F2}) touched {retestLevelName} ({retestLevelPrice:F2}) on Bar {CurrentBar-1}. Low[0] > Low[1] AND Bullish Candle.");
                    lastSwingLowForStop = retestLevelPrice;
                }
            }
            // --- END CORRECTED RETEST REVERSAL LOGIC ---

            if (!trendEntrySignal && !existingReversalEntrySignal && !retestReversalEntrySignal) return false;

            if (UseKcBaseTrendFilter && !uptrend) 
            {
                PrintOnce($"ValLE_DHL_KCTrendFail_{CurrentBar}",$"{Time[0]}: DHL Long Signal IGNORED - KCBase trend filter (uptrend=false)");
                return false;
            }
            
            LogMessage($"DHL Long Signal: TrendT={trendEntrySignal}, OrigReversalT={existingReversalEntrySignal}, RetestReversalT={retestReversalEntrySignal}", "ENTRY_VALIDATE");
            return true; 
        }
		
        protected override bool ValidateEntryShort()
        {
            if (dhlIndicator == null || CurrentBar < 1)
                return false;
            
            if (CurrentBar < Math.Max(DhlPeriod1, DhlPeriod2) -1 ) {
                //PrintOnce($"ValSE_DHL_NotEnoughData_{CurrentBar}", $"{Time[0]}: DHL not enough data for ValidateEntryShort. CB: {CurrentBar}");
                return false;
            }

            // --- Trend Entry ---
            bool trendEntrySignal = false;
            if (EnableTrendEntries)
            {
                 if (CurrentBar >= Math.Max(DhlPeriod1, DhlPeriod2)) // Stricter check for [1] access for CrossBelow
                {
                    if (dhlIndicator.Period1LowValue.IsValidDataPoint(1) && dhlIndicator.Period2LowValue.IsValidDataPoint(1))
                    {
                        bool shortLowBelowLongLow = CrossBelow(dhlIndicator.Period1LowValue, dhlIndicator.Period2LowValue, 1);
                        if (shortLowBelowLongLow)
                        {
                            trendEntrySignal = true;
                            PrintOnce($"ValSE_DHLTrend_{CurrentBar}", $"{Time[0]}: DHL Trend Short: P1Low ({dhlIndicator.Period1LowValue[0]:F2}) crossed P2Low ({dhlIndicator.Period2LowValue[0]:F2}) from previous bar values.");
                        }
                    }
                }
            }

            // --- Existing Reversal Entry ---
            bool existingReversalEntrySignal = false;
            if (EnableReversalEntries)
            {
                 if (dhlIndicator.Period1HighValue.IsValidDataPoint(0) && dhlIndicator.Period2HighValue.IsValidDataPoint(0))
                 {
                    bool shortHighBelowLongHigh = dhlIndicator.Period1HighValue[0] < dhlIndicator.Period2HighValue[0];
                    bool priceFallingOriginal = Close[0] < Open[0] && Close[0] < Close[1];
                    if (shortHighBelowLongHigh && priceFallingOriginal && dhlIndicator.Period1HighValue[0] > 0 && dhlIndicator.Period2HighValue[0] > 0)
                    {
                        existingReversalEntrySignal = true;
                        PrintOnce($"ValSE_DHLReversalOrig_{CurrentBar}", $"{Time[0]}: DHL Original Reversal Short: P1High ({dhlIndicator.Period1HighValue[0]:F2}) < P2High ({dhlIndicator.Period2HighValue[0]:F2}) AND Price Falling");
                    }
                 }
            }

            // --- CORRECTED RETEST REVERSAL LOGIC ---
            bool retestReversalEntrySignal = false;
            if (EnableRetestReversalEntries && CurrentBar > 0)
            {
                bool prevBarTouchedP1High = (barOfLastP1HighTouch == CurrentBar - 1);
                bool prevBarTouchedP2High = (barOfLastP2HighTouch == CurrentBar - 1);
                bool prevBarTouchedAnyDhlHigh = prevBarTouchedP1High || prevBarTouchedP2High;

                bool currentHighLowerThanPrev = High[0] < High[1];
                bool priceIsFallingNow = Close[0] < Open[0]; 

                if (prevBarTouchedAnyDhlHigh && currentHighLowerThanPrev && priceIsFallingNow)
                {
                    retestReversalEntrySignal = true;
                    double retestLevelPrice = prevBarTouchedP1High ? priceAtLastP1HighTouch : priceAtLastP2HighTouch;
                    string retestLevelName = prevBarTouchedP1High ? "P1High" : "P2High";
                    PrintOnce($"ValSE_DHLRetestRev_{CurrentBar}", $"{Time[0]}: DHL Retest Reversal Short: High[1] ({High[1]:F2}) touched {retestLevelName} ({retestLevelPrice:F2}) on Bar {CurrentBar-1}. High[0] < High[1] AND Bearish Candle.");
                    lastSwingHighForStop = retestLevelPrice;
                }
            }
            // --- END CORRECTED RETEST REVERSAL LOGIC ---
            
            if (!trendEntrySignal && !existingReversalEntrySignal && !retestReversalEntrySignal) return false;

            if (UseKcBaseTrendFilter && !downtrend) 
            {
                 PrintOnce($"ValSE_DHL_KCTrendFail_{CurrentBar}",$"{Time[0]}: DHL Short Signal IGNORED - KCBase trend filter (downtrend=false)");
                return false;
            }

            LogMessage($"DHL Short Signal: TrendT={trendEntrySignal}, OrigReversalT={existingReversalEntrySignal}, RetestReversalT={retestReversalEntrySignal}", "ENTRY_VALIDATE");
            return true;
        }      
//       	protected override bool ValidateExitLong()
//        {
//            // Logic for validating long exits
//            return enableExit? true: false;
//        }

//        protected override bool ValidateExitShort()
//        {
//			// Logic for validating short exits
//			return enableExit? true: false;
//        }

        #region Indicators
        protected override void InitializeIndicators()
        {
            // Initialize your Dual High/Low indicator (ensure class name matches your file)
            // The generated helper for SwingHiLow_MAXMIN would take these parameters:
            // (int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
            // We will use default visual parameters from the indicator itself for now.
            // If you removed visual parameters from SwingHiLow_MAXMIN, the call is simpler.
            try
            {
                // Assuming SwingHiLow_MAXMIN is the final name and has visual parameters
                // If SwingHiLow_MAXMIN was simplified to only take Period1 & Period2, adjust this call:
                // dhlIndicator = SwingHiLow_MAXMIN(DhlPeriod1, DhlPeriod2);
                dhlIndicator = SwingHiLow(DhlPeriod1, DhlPeriod2, 2, DashStyleHelper.Solid, DashStyleHelper.Dash); 
                                              // Using default values for visual params here ^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                              // Or, add strategy params for these and pass them.

                if (dhlIndicator != null)
                {
                    AddChartIndicator(dhlIndicator);
                    LogMessage($"{Name}: Dual High/Low indicator initialized.", "INIT");
                }
                else
                {
                    LogError("INIT_FAILED", "FAILED to initialize Dual High/Low indicator.");
                }
            }
            catch (Exception ex)
            {
                 LogError("EXCEPTION", $"initializing Dual High/Low indicator: {ex.Message}");
            }
        }
        #endregion

    }
}
