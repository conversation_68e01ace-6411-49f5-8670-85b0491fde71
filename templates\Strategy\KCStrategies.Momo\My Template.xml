﻿<?xml version="1.0" encoding="utf-8"?>
<StrategyTemplate>
  <StrategyType>NinjaTrader.NinjaScript.Strategies.KCStrategies.Momo</StrategyType>
  <OptimizerType>NinjaTrader.NinjaScript.Optimizers.DefaultOptimizer</OptimizerType>
  <OptimizerParameters>
    <ArrayOfParameterWrapper xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <ParameterWrapper>
        <DisplayName>IsStrategyGenerator</DisplayName>
        <Name>IsStrategyGenerator</Name>
        <Value xsi:type="xsd:boolean">false</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Keep best # results</DisplayName>
        <Name>KeepBestResults</Name>
        <Value xsi:type="xsd:int">10</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>LogTypeName</DisplayName>
        <Name>LogTypeName</Name>
        <Value xsi:type="xsd:string">Optimizer</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Visible</DisplayName>
        <Name>IsVisible</Name>
        <Value xsi:type="xsd:boolean">true</Value>
      </ParameterWrapper>
    </ArrayOfParameterWrapper>
  </OptimizerParameters>
  <OptimizationFitness>NinjaTrader.NinjaScript.OptimizationFitnesses.MaxProfitFactor</OptimizationFitness>
  <OptimizationParameters>
    <ArrayOfParameter xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">5</Max>
        <Min xsi:type="xsd:int">5</Min>
        <Name>MomoUp</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">-5</Max>
        <Min xsi:type="xsd:int">-5</Min>
        <Name>MomoDown</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>-5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>EnableFixedProfitTarget</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>EnableRegChanProfitTarget</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">60</Max>
        <Min xsi:type="xsd:int">60</Min>
        <Name>MinRegChanTargetDistanceTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>60</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">120</Max>
        <Min xsi:type="xsd:int">120</Min>
        <Name>MinRegChanStopDistanceTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>120</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>EnableDynamicProfitTarget</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Limit</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>OrderType</Name>
        <ParameterTypeSerializable>NinjaTrader.Cbi.OrderType, NinjaTrader.Core, Version=8.1.4.2, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Limit</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">4</Max>
        <Min xsi:type="xsd:double">4</Min>
        <Name>LimitOffset</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>Contracts</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">4</Max>
        <Min xsi:type="xsd:int">4</Min>
        <Name>TickMove</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">97</Max>
        <Min xsi:type="xsd:int">97</Min>
        <Name>InitialStop</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>97</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">40</Max>
        <Min xsi:type="xsd:double">40</Min>
        <Name>ProfitTarget</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>40</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>EnableProfitTarget2</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>Contracts2</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">44</Max>
        <Min xsi:type="xsd:double">44</Min>
        <Name>ProfitTarget2</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>44</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>EnableProfitTarget3</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>Contracts3</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">48</Max>
        <Min xsi:type="xsd:double">48</Min>
        <Name>ProfitTarget3</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>48</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>EnableProfitTarget4</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>Contracts4</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">52</Max>
        <Min xsi:type="xsd:double">52</Min>
        <Name>ProfitTarget4</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>52</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Tick_Trail</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>TrailStopType</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.Strategies.KCStrategies.KCAlgoBase+TrailStopTypeKC, 12fdda309dfa4711b54903ffe9db7d93, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Tick_Trail</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">14</Max>
        <Min xsi:type="xsd:int">14</Min>
        <Name>AtrPeriod</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>14</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">1.5</Max>
        <Min xsi:type="xsd:double">1.5</Min>
        <Name>atrMultiplier</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1.5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">0.75</Max>
        <Min xsi:type="xsd:double">0.75</Min>
        <Name>RiskRewardRatio</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0.75</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>enableAtrProfitTarget</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>BESetAuto</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">32</Max>
        <Min xsi:type="xsd:int">32</Min>
        <Name>BE_Trigger</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>32</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">4</Max>
        <Min xsi:type="xsd:int">4</Min>
        <Name>BE_Offset</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableBackgroundSignal</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>enableExit</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>step1ProfitTrigger</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">97</Max>
        <Min xsi:type="xsd:int">97</Min>
        <Name>step1StopLoss</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>97</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">44</Max>
        <Min xsi:type="xsd:int">44</Min>
        <Name>step2ProfitTrigger</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>44</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">40</Max>
        <Min xsi:type="xsd:int">40</Min>
        <Name>step2StopLoss</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>40</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">52</Max>
        <Min xsi:type="xsd:int">52</Min>
        <Name>step3ProfitTrigger</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>52</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">16</Max>
        <Min xsi:type="xsd:int">16</Min>
        <Name>step3StopLoss</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>16</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>dailyLossProfit</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">100000</Max>
        <Min xsi:type="xsd:double">100000</Min>
        <Name>DailyProfitLimit</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>100000</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">1000</Max>
        <Min xsi:type="xsd:double">1000</Min>
        <Name>DailyLossLimit</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1000</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableTrailingDrawdown</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">1000</Max>
        <Min xsi:type="xsd:double">1000</Min>
        <Name>TrailingDrawdown</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1000</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">3000</Max>
        <Min xsi:type="xsd:double">3000</Min>
        <Name>StartTrailingDD</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>3000</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>TradesPerDirection</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">5</Max>
        <Min xsi:type="xsd:int">5</Min>
        <Name>longPerDirection</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">5</Max>
        <Min xsi:type="xsd:int">5</Min>
        <Name>shortPerDirection</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>SecsSinceEntry</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>iBarsSinceExit</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableBuySellPressure</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showBuySellPressure</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableVMA</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showVMA</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableHmaHooks</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showHmaHooks</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">16</Max>
        <Min xsi:type="xsd:int">16</Min>
        <Name>HmaPeriod</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>16</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableRegChan1</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableRegChan2</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showRegChan1</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showRegChan2</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showRegChanHiLo</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">40</Max>
        <Min xsi:type="xsd:int">40</Min>
        <Name>RegChanPeriod</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>40</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">4</Max>
        <Min xsi:type="xsd:double">4</Min>
        <Name>RegChanWidth</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">3</Max>
        <Min xsi:type="xsd:double">3</Min>
        <Name>RegChanWidth2</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>3</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableMomo</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showMomo</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableADX</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showAdx</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">7</Max>
        <Min xsi:type="xsd:int">7</Min>
        <Name>adxPeriod</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>7</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">25</Max>
        <Min xsi:type="xsd:int">25</Min>
        <Name>AdxThreshold</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>25</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">50</Max>
        <Min xsi:type="xsd:int">50</Min>
        <Name>adxThreshold2</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>50</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">45</Max>
        <Min xsi:type="xsd:int">45</Min>
        <Name>adxExitThreshold</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>45</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>enableVolatility</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">1.5</Max>
        <Min xsi:type="xsd:double">1.5</Min>
        <Name>atrThreshold</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1.5</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>enableEMAFilter</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showEMA</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">110</Max>
        <Min xsi:type="xsd:int">110</Min>
        <Name>emaLength</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>110</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showPivots</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>EnableChoppinessDetection</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">4</Max>
        <Min xsi:type="xsd:int">4</Min>
        <Name>SlopeLookBack</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">0.125</Max>
        <Min xsi:type="xsd:double">0.125</Min>
        <Name>FlatSlopeFactor</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0.125</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">25</Max>
        <Min xsi:type="xsd:int">25</Min>
        <Name>ChopAdxThreshold</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>25</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Time2</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Time3</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Time4</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Time5</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Time6</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>showDailyPnl</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>BottomLeft</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>PositionDailyPNL</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.DrawingTools.TextPosition, 12fdda309dfa4711b54903ffe9db7d93, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>BottomLeft</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>showPnl</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>TopLeft</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>PositionPnl</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.DrawingTools.TextPosition, 12fdda309dfa4711b54903ffe9db7d93, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>TopLeft</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>ShowHistorical</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>useWebHook</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
    </ArrayOfParameter>
  </OptimizationParameters>
  <Strategy>
    <Momo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>2015</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>50</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>25</Value>
        <Value2>100</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2099-12-01T00:00:00</From>
      <Panel>-1</Panel>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
      <To>1800-01-01T00:00:00</To>
      <Calculate>OnEachTick</Calculate>
      <Displacement>0</Displacement>
      <IsAutoScale>true</IsAutoScale>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>Momo v5.2</Name>
      <Plots />
      <SelectedValueSeries>0</SelectedValueSeries>
      <BarsPeriodParameter>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name />
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </BarsPeriodParameter>
      <BarsRequiredToTrade>120</BarsRequiredToTrade>
      <Category>NinjaScript</Category>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <DaysToLoad>5</DaysToLoad>
      <DefaultQuantity>1</DefaultQuantity>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <EntriesPerDirection>10</EntriesPerDirection>
      <EntryHandling>AllEntries</EntryHandling>
      <ExitOnSessionCloseSeconds>30</ExitOnSessionCloseSeconds>
      <IncludeCommission>false</IncludeCommission>
      <IsAggregated>false</IsAggregated>
      <IsExitOnSessionCloseStrategy>true</IsExitOnSessionCloseStrategy>
      <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
      <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <IsWaitUntilFlat>false</IsWaitUntilFlat>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <OptimizationPeriod>10</OptimizationPeriod>
      <OrderFillResolution>Standard</OrderFillResolution>
      <OrderFillResolutionType>Tick</OrderFillResolutionType>
      <OrderFillResolutionValue>1</OrderFillResolutionValue>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
      <SetOrderQuantity>Strategy</SetOrderQuantity>
      <Slippage>0</Slippage>
      <StartBehavior>WaitUntilFlat</StartBehavior>
      <StopTargetHandling>PerEntryExecution</StopTargetHandling>
      <SupportsOptimizationGraph>true</SupportsOptimizationGraph>
      <TestPeriod>28</TestPeriod>
      <TradingHoursSerializable />
      <Gtd>1800-01-01T00:00:00</Gtd>
      <Template />
      <TimeInForce>Gtc</TimeInForce>
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ZOrder>-2147483648</ZOrder>
      <isLong>false</isLong>
      <isShort>false</isShort>
      <isFlat>false</isFlat>
      <exitLong>false</exitLong>
      <exitShort>false</exitShort>
      <longSignal>false</longSignal>
      <shortSignal>false</shortSignal>
      <tickTrail>true</tickTrail>
      <BaseAlgoVersion>KCAlgoBase v5.4</BaseAlgoVersion>
      <Author>indiVGA, Khanh Nguyen, Oshi, based on ArchReactor</Author>
      <StrategyName>Momo</StrategyName>
      <Version>5.2 Apr. 2025</Version>
      <Credits>Strategy by Khanh Nguyen</Credits>
      <ChartType>Orenko 34-40-40</ChartType>
      <paypal>https://www.paypal.com/signin</paypal>
      <EnableFixedProfitTarget>true</EnableFixedProfitTarget>
      <EnableRegChanProfitTarget>false</EnableRegChanProfitTarget>
      <MinRegChanTargetDistanceTicks>60</MinRegChanTargetDistanceTicks>
      <MinRegChanStopDistanceTicks>120</MinRegChanStopDistanceTicks>
      <EnableDynamicProfitTarget>false</EnableDynamicProfitTarget>
      <OrderType>Limit</OrderType>
      <LimitOffset>4</LimitOffset>
      <Contracts>1</Contracts>
      <TickMove>4</TickMove>
      <InitialStop>97</InitialStop>
      <ProfitTarget>40</ProfitTarget>
      <EnableProfitTarget2>true</EnableProfitTarget2>
      <Contracts2>1</Contracts2>
      <ProfitTarget2>44</ProfitTarget2>
      <EnableProfitTarget3>true</EnableProfitTarget3>
      <Contracts3>1</Contracts3>
      <ProfitTarget3>48</ProfitTarget3>
      <EnableProfitTarget4>true</EnableProfitTarget4>
      <Contracts4>1</Contracts4>
      <ProfitTarget4>52</ProfitTarget4>
      <TrailStopType>Tick_Trail</TrailStopType>
      <AtrPeriod>14</AtrPeriod>
      <atrMultiplier>1.5</atrMultiplier>
      <RiskRewardRatio>0.75</RiskRewardRatio>
      <enableAtrProfitTarget>false</enableAtrProfitTarget>
      <BESetAuto>true</BESetAuto>
      <BE_Trigger>32</BE_Trigger>
      <BE_Offset>4</BE_Offset>
      <enableBackgroundSignal>true</enableBackgroundSignal>
      <enableExit>false</enableExit>
      <step1ProfitTrigger>1</step1ProfitTrigger>
      <step1StopLoss>97</step1StopLoss>
      <step2ProfitTrigger>44</step2ProfitTrigger>
      <step2StopLoss>40</step2StopLoss>
      <step3ProfitTrigger>52</step3ProfitTrigger>
      <step3StopLoss>16</step3StopLoss>
      <dailyLossProfit>true</dailyLossProfit>
      <DailyProfitLimit>100000</DailyProfitLimit>
      <DailyLossLimit>1000</DailyLossLimit>
      <enableTrailingDrawdown>true</enableTrailingDrawdown>
      <TrailingDrawdown>1000</TrailingDrawdown>
      <StartTrailingDD>3000</StartTrailingDD>
      <TradesPerDirection>false</TradesPerDirection>
      <longPerDirection>5</longPerDirection>
      <shortPerDirection>5</shortPerDirection>
      <SecsSinceEntry>0</SecsSinceEntry>
      <iBarsSinceExit>0</iBarsSinceExit>
      <enableBuySellPressure>true</enableBuySellPressure>
      <showBuySellPressure>false</showBuySellPressure>
      <enableVMA>true</enableVMA>
      <showVMA>true</showVMA>
      <enableHmaHooks>true</enableHmaHooks>
      <showHmaHooks>true</showHmaHooks>
      <HmaPeriod>16</HmaPeriod>
      <enableRegChan1>true</enableRegChan1>
      <enableRegChan2>true</enableRegChan2>
      <showRegChan1>true</showRegChan1>
      <showRegChan2>true</showRegChan2>
      <showRegChanHiLo>true</showRegChanHiLo>
      <RegChanPeriod>40</RegChanPeriod>
      <RegChanWidth>4</RegChanWidth>
      <RegChanWidth2>3</RegChanWidth2>
      <enableMomo>true</enableMomo>
      <showMomo>false</showMomo>
      <MomoUp>5</MomoUp>
      <MomoDown>-5</MomoDown>
      <enableADX>true</enableADX>
      <showAdx>false</showAdx>
      <adxPeriod>7</adxPeriod>
      <AdxThreshold>25</AdxThreshold>
      <adxThreshold2>50</adxThreshold2>
      <adxExitThreshold>45</adxExitThreshold>
      <enableVolatility>true</enableVolatility>
      <atrThreshold>1.5</atrThreshold>
      <enableEMAFilter>false</enableEMAFilter>
      <showEMA>false</showEMA>
      <emaLength>110</emaLength>
      <showPivots>false</showPivots>
      <EnableChoppinessDetection>true</EnableChoppinessDetection>
      <SlopeLookBack>4</SlopeLookBack>
      <FlatSlopeFactor>0.125</FlatSlopeFactor>
      <ChopAdxThreshold>25</ChopAdxThreshold>
      <Start>2025-05-11T06:30:00</Start>
      <End>2025-05-11T09:30:00</End>
      <Time2>false</Time2>
      <Start2>2025-05-11T11:30:00</Start2>
      <End2>2025-05-11T13:00:00</End2>
      <Time3>false</Time3>
      <Start3>2025-05-11T15:00:00</Start3>
      <End3>2025-05-11T18:00:00</End3>
      <Time4>false</Time4>
      <Start4>2025-05-11T00:00:00</Start4>
      <End4>2025-05-11T03:30:00</End4>
      <Time5>false</Time5>
      <Start5>2025-05-11T06:30:00</Start5>
      <End5>2025-05-11T13:00:00</End5>
      <Time6>false</Time6>
      <Start6>2025-05-11T00:00:00</Start6>
      <End6>2025-05-11T23:59:00</End6>
      <showDailyPnl>true</showDailyPnl>
      <PositionDailyPNL>BottomLeft</PositionDailyPNL>
      <colorDailyProfitLossSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF00FFFF&lt;/SolidColorBrush&gt;</colorDailyProfitLossSerialize>
      <showPnl>false</showPnl>
      <PositionPnl>TopLeft</PositionPnl>
      <colorPnlSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</colorPnlSerialize>
      <ShowHistorical>true</ShowHistorical>
      <useWebHook>false</useWebHook>
      <DiscordWebhooks>https://discord.com/channels/963493404988289124/1343311936736989194</DiscordWebhooks>
    </Momo>
  </Strategy>
</StrategyTemplate>