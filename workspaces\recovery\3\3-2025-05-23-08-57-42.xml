﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-936cac2f88344fe4a54b23536c5ddf00>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>970;16</Location>
      <Size>826;993</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>149</TraderWidth>
      <TabControl>
        <SelectedIndex>1</SelectedIndex>
        <Tab-1d3aa23566d344b983ce801c096b3a74>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>30</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>3</DaysBack>
                <From>2025-05-17T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2025-05-14T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ 06-25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>80363439bd3e45e7913b2dd93a80276a</BarsSeriesId>
                <Id>80363439bd3e45e7913b2dd93a80276a</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>1.8888888888888888</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators />
          <CrosshairType>Off</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>6.296296</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Off</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>881</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21159.5</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>21138.25</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-1d3aa23566d344b983ce801c096b3a74>
        <Tab-f8ffbcffb2544548929afa4bbf1fd87f>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>15</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-17T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2025-05-14T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>MES 06-25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>80363439bd3e45e7913b2dd93a80276a</BarsSeriesId>
                <Id>7b06015af8bb4f27b3d333d3bee5ff33</Id>
                <Instrument>MES JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>0.96034418256640475</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies>
            <Strategy0 BarsIndex="0">357445161</Strategy0>
          </Strategies>
          <Indicators />
          <CrosshairType>Off</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>3.201147</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Off</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>881</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>6153.25</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>5403.25</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-f8ffbcffb2544548929afa4bbf1fd87f>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>Playback101</Account>
        <ATM>TEST1 - 1</ATM>
        <Instrument>MES JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-936cac2f88344fe4a54b23536c5ddf00>
  </NTWindows>
</NinjaTrader>