#region Using declarations
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.Gui.Tools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public enum TimeFrameModeTEFASignalsPro
    {
        ChartTimeFrame,
        CustomTimeFrame
    }

    public class TEFASignalsPro : Indicator
    {
        // Constants for default values and settings
        private const string IndicatorName = "TEFA Signals Pro";
        private const string IndicatorDescription = "Advanced trading indicator that integrates multiple technical analysis tools including.";

        private const string BuySignalText = "▲";
        private const string SellSignalText = "▼";
        private const string ArialFont = "Arial";
        private const int TextOpacity = 50;

        // Private indicator instances
        private MACD macd;
        private WilliamsREMA williamsREMA;
        private CCI cci;
        private EMA cciEma;
        private Stochastics stochastics;
        private Momentum momentum;
        private EMA momentumEma;
        private Range priceRange;
        private EMA rangeEma;
        private UltimatMA ultimatMA;

        // Signal tracking
        private bool lastBuySignal;
        private bool lastSellSignal;

        // Secondary DataSeries configuration
        private BarsPeriodType secondaryPeriodType = BarsPeriodType.Minute;
        private int secondaryPeriodValue = 1;

        #region Properties
        // 1. MACD Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Fast", GroupName = "1. MACD", Order = 0)]
        public int Fast { get; set; } = 5;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Slow", GroupName = "1. MACD", Order = 1)]
        public int Slow { get; set; } = 8;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Smooth", GroupName = "1. MACD", Order = 2)]
        public int Smooth { get; set; } = 5;

        // 2. Williams %R Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Williams %R Period", GroupName = "2. Williams %R", Order = 0)]
        public int WilliamsRPeriod { get; set; } = 21;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Williams %R EMA Period", GroupName = "2. Williams %R", Order = 1)]
        public int WilliamsREMAPeriod { get; set; } = 13;

        // 3. CCI Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "CCI Period", GroupName = "3. CCI", Order = 0)]
        public int CCIPeriod { get; set; } = 21;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "CCI EMA Period", GroupName = "3. CCI", Order = 1)]
        public int CCIEMAPeriod { get; set; } = 13;

        // 4. Momentum Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Momentum Period", GroupName = "4. Momentum", Order = 0)]
        public int MomentumPeriod { get; set; } = 21;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Momentum EMA Period", GroupName = "4. Momentum", Order = 1)]
        public int MomentumEMAPeriod { get; set; } = 13;

        // 5. Stochastic Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Stochastic %K Period", GroupName = "5. Stochastic", Order = 0)]
        public int StochasticKPeriod { get; set; } = 21;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Stochastic %D Period", GroupName = "5. Stochastic", Order = 1)]
        public int StochasticDPeriod { get; set; } = 13;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Stochastic Smooth", GroupName = "5. Stochastic", Order = 2)]
        public int StochasticSmooth { get; set; } = 3;

        // 6. Range Filter Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Sampling Period", GroupName = "6. Range Filter", Order = 0)]
        public int SamplingPeriod { get; set; } = 10;

        [Range(0.1, double.MaxValue), NinjaScriptProperty]
        [Display(Name = "Range Multiplier", GroupName = "6. Range Filter", Order = 1)]
        public double RangeMultiplier { get; set; } = 1.0;

        // 7. Ultimate MA Parameters
        [NinjaScriptProperty]
        [Display(Name = "MA Type", Order = 0, GroupName = "7. Ultimate MA")]
        public UMAType SelectedMAType { get; set; } = UMAType.SMA;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "MA Period", Order = 1, GroupName = "7. Ultimate MA")]
        public int PeriodMA { get; set; } = 5;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "TCount", GroupName = "7. Ultimate MA", Order = 2)]
        public int TCount { get; set; } = 3;

        [Range(0, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "VFactor", GroupName = "7. Ultimate MA", Order = 3)]
        public double VFactor { get; set; } = 0.7;

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Smooth Length", Order = 4, GroupName = "7. Ultimate MA")]
        public int SmoothLength { get; set; } = 2;

        [NinjaScriptProperty]
        [Display(Name = "Time Frame Mode", GroupName = "7. Ultimate MA", Order = 5)]
        public TimeFrameModeTEFASignalsPro SelectedTimeFrameModeTEFASignalsPro { get; set; } = TimeFrameModeTEFASignalsPro.ChartTimeFrame;

        [NinjaScriptProperty]
        [Display(Name = "Period Type", GroupName = "7. Ultimate MA", Order = 6)]
        public BarsPeriodType SecondaryPeriodType
        {
            get => secondaryPeriodType;
            set => secondaryPeriodType = value;
        }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Period Value", GroupName = "7. Ultimate MA", Order = 7)]
        public int SecondaryPeriodValue
        {
            get => secondaryPeriodValue;
            set => secondaryPeriodValue = value;
        }

        // 8. High/Low Lookback Parameters
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Lookback Bars for High/Low", GroupName = "8. High/Low Lookback", Order = 0)]
        public int LookBackBars { get; set; } = 2;       
        
        // A. Indicator Activation
        [NinjaScriptProperty]
        [Display(Name = "Use MACD", GroupName = "A. Indicator Activation", Order = 0)]
        public bool UseMACD { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Williams %R", GroupName = "A. Indicator Activation", Order = 1)]
        public bool UseWilliamsR { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use CCI", GroupName = "A. Indicator Activation", Order = 2)]
        public bool UseCCI { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Momentum", GroupName = "A. Indicator Activation", Order = 3)]
        public bool UseMomentum { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Stochastics", GroupName = "A. Indicator Activation", Order = 4)]
        public bool UseStochastics { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Range Filter", GroupName = "A. Indicator Activation", Order = 5)]
        public bool UseRangeFilter { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Ultimate MA", GroupName = "A. Indicator Activation", Order = 6)]
        public bool UseUltimateMA { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use Open/Close Condition", GroupName = "A. Indicator Activation", Order = 7)]
        public bool UseOpenCloseCondition { get; set; } = true;
		
        [NinjaScriptProperty]
        [Display(Name = "Use High/Low Condition", GroupName = "A. Indicator Activation", Order = 8)]
        public bool UseHighLowCondition { get; set; } = false;
		
        // B. Signal Display
        [NinjaScriptProperty]
        [XmlIgnore]
        [Display(Name = "Buy Signal Color", Order = 0, GroupName = "B. Signal Display")]
        public Brush BuySignalColor { get; set; }
		
        [Browsable(false)]
        public string BuySignalColorSerializable
        {
            get { return Serialize.BrushToString(BuySignalColor); }
            set { BuySignalColor = Serialize.StringToBrush(value); }
        }
		
        [NinjaScriptProperty]
        [XmlIgnore]
        [Display(Name = "Sell Signal Color", Order = 1, GroupName = "B. Signal Display")]
        public Brush SellSignalColor { get; set; }
		
        [Browsable(false)]
        public string SellSignalColorSerializable
        {
            get { return Serialize.BrushToString(SellSignalColor); }
            set { SellSignalColor = Serialize.StringToBrush(value); }
        }
		
        [NinjaScriptProperty]
        [Display(Name = "Color Background", Order = 2, GroupName = "B. Signal Display")]
        public bool ColorBackground { get; set; } = true;
		
        [Range(0.0, 1.0), NinjaScriptProperty]
        [Display(Name = "Buy Background Opacity", Order = 3, GroupName = "B. Signal Display")]
        public double BuyBackgroundOpacity { get; set; } = 0.25;
		
        [Range(0.0, 1.0), NinjaScriptProperty]
        [Display(Name = "Sell Background Opacity", Order = 4, GroupName = "B. Signal Display")]
        public double SellBackgroundOpacity { get; set; } = 0.25;
		
        [Range(1, 100), NinjaScriptProperty]
        [Display(Name = "Signal Font Size", Order = 5, GroupName = "B. Signal Display")]
        public int SignalFontSize { get; set; } = 12;
		
        [Range(1, 50), NinjaScriptProperty]
        [Display(Name = "Text Offset Multiplier", Order = 6, GroupName = "B. Signal Display")]
        public int TextOffsetMultiplier { get; set; } = 5;
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = IndicatorDescription;
                Name = IndicatorName;
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = false;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                BuySignalColor = Brushes.Cyan;
                SellSignalColor = Brushes.Magenta;

                AddPlot(new Stroke(Brushes.Orange, 2), PlotStyle.Line, "UltimateMA");
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "BuySellSignals");
            }
            else if (State == State.Configure && SelectedTimeFrameModeTEFASignalsPro == TimeFrameModeTEFASignalsPro.CustomTimeFrame)
            {
                AddDataSeries(SecondaryPeriodType, SecondaryPeriodValue);
            }
            else if (State == State.DataLoaded)
            {
                InitializeIndicators();
            }
        }

        private void InitializeIndicators()
        {
            macd = MACD(Fast, Slow, Smooth);
            williamsREMA = WilliamsREMA(WilliamsRPeriod, WilliamsREMAPeriod);
            // Use standard CCI and EMA instead of custom CCIEMA
            cci = CCI(CCIPeriod);
            cciEma = EMA(cci, CCIEMAPeriod);

            // Use standard Momentum and EMA instead of custom MomentumEMA
            momentum = Momentum(MomentumPeriod);
            momentumEma = EMA(momentum, MomentumEMAPeriod);
            stochastics = Stochastics(Close, StochasticKPeriod, StochasticDPeriod, StochasticSmooth);
            // Create a simple range filter using EMA of price range
            priceRange = Range();
            rangeEma = EMA(priceRange, SamplingPeriod);

            var barsArrayIndex = SelectedTimeFrameModeTEFASignalsPro == TimeFrameModeTEFASignalsPro.CustomTimeFrame ? 1 : 0;
            ultimatMA = UltimatMA(BarsArray[barsArrayIndex], SelectedMAType, PeriodMA, TCount, VFactor, SmoothLength);
        }

        protected override void OnBarUpdate()
        {
            if (BarsInProgress != 0 || !IsEnoughDataAvailable())
                return;

            UpdateIndicatorValues();
            UpdatePlotsAndSignals();
        }

        private bool IsEnoughDataAvailable()
        {
            var currentBar = CurrentBars[0];
            if (currentBar < Math.Max(Fast, Math.Max(Slow, Smooth)) ||
                currentBar < Math.Max(WilliamsRPeriod, WilliamsREMAPeriod) ||
                currentBar < Math.Max(CCIPeriod, CCIEMAPeriod) ||
                currentBar < Math.Max(MomentumPeriod, MomentumEMAPeriod) ||
                currentBar < Math.Max(StochasticKPeriod, StochasticDPeriod) ||
                currentBar < SamplingPeriod ||
                currentBar < Math.Max(PeriodMA, SmoothLength))
                return false;

            return SelectedTimeFrameModeTEFASignalsPro == TimeFrameModeTEFASignalsPro.CustomTimeFrame
                ? CurrentBars[1] >= Math.Max(PeriodMA, SmoothLength)
                : currentBar >= Math.Max(PeriodMA, SmoothLength);
        }

        private void UpdateIndicatorValues()
        {
            var maValue = ultimatMA.Values[0][0];
            Values[0][0] = maValue;
            Values[1][0] = 0;

            PlotBrushes[0][0] = ultimatMA.Signal[0] == 1 ? BuySignalColor : SellSignalColor;
        }

        private void UpdatePlotsAndSignals()
        {
            // Check if all filters are disabled
            bool allFiltersDisabled = !UseMACD && !UseWilliamsR && !UseCCI && !UseMomentum && 
                                      !UseStochastics && !UseRangeFilter && !UseUltimateMA && 
                                      !UseHighLowCondition && !UseOpenCloseCondition;

            var macdDiff = macd.Diff[0];
            var macdDiffPrev = macd.Diff[1];
            var williamsValue = williamsREMA[0];
            var emaOfWilliamsR = williamsREMA.Values[1][0];
            var cciValue = cci[0];
            var emaOfCci = cciEma[0];
            var momentumValue = momentum[0];
            var emaOfMomentum = momentumEma[0];
            var stoK = stochastics.K[0];
            var stoD = stochastics.D[0];
            var rangeFilterCondition = rangeEma[0] > rangeEma[1] ? 1 : (rangeEma[0] < rangeEma[1] ? -1 : 0);
            var maSignal = ultimatMA.Signal[0];
            var open = Open[0];
            var close = Close[0];

            var maxHigh = CurrentBars[0] >= LookBackBars ? MAX(High, LookBackBars)[1] : 0;
            var minLow = CurrentBars[0] >= LookBackBars ? MIN(Low, LookBackBars)[1] : 0;

            // Reset values and do not generate signals if all filters are disabled
            if (allFiltersDisabled)
            {
                Values[1][0] = 0;
                BarBrush = null;
                CandleOutlineBrush = null;
                BackBrushes[0] = null;
                lastBuySignal = false;
                lastSellSignal = false;
                return; // Exit the method without generating signals
            }

            // Buy Signal Conditions
            if (IsBuySignal(williamsValue, emaOfWilliamsR, cciValue, emaOfCci, momentumValue, emaOfMomentum, stoK, stoD, macdDiff, macdDiffPrev, rangeFilterCondition, maSignal, close, open, maxHigh))
            {
                if (!lastBuySignal)
                {
                    Draw.Text(this, $"BUY{CurrentBars[0]}", false, BuySignalText, 0, Low[0] - TextOffsetMultiplier * TickSize, 0, BuySignalColor, new SimpleFont(ArialFont, SignalFontSize), TextAlignment.Center, BuySignalColor, Brushes.Black, TextOpacity);
                    lastBuySignal = true;
                    lastSellSignal = false;
                    Values[1][0] = 1;
                }
                BarBrush = BuySignalColor;
                CandleOutlineBrush = BuySignalColor;
            }
                       // Sell Signal Conditions
            else if (IsSellSignal(williamsValue, emaOfWilliamsR, cciValue, emaOfCci, momentumValue, emaOfMomentum, stoK, stoD, macdDiff, macdDiffPrev, rangeFilterCondition, maSignal, close, open, minLow))
            {
                if (!lastSellSignal)
                {
                    Draw.Text(this, $"SELL{CurrentBars[0]}", false, SellSignalText, 0, High[0] + TextOffsetMultiplier * TickSize, 0, SellSignalColor, new SimpleFont(ArialFont, SignalFontSize), TextAlignment.Center, SellSignalColor, Brushes.Black, TextOpacity);
                    lastBuySignal = false;
                    lastSellSignal = true;
                    Values[1][0] = -1;
                }
                BarBrush = SellSignalColor;
                CandleOutlineBrush = SellSignalColor;
            }
            else
            {
                Values[1][0] = 0;
            }

            if (ColorBackground)
            {
                if (lastBuySignal)
                {
                    // Create a new color with configurable opacity for the buy signal
                    SolidColorBrush buyBrush = new SolidColorBrush(((SolidColorBrush)BuySignalColor).Color);
                    buyBrush.Opacity = BuyBackgroundOpacity;
                    BackBrushes[0] = buyBrush;
                }
                else if (lastSellSignal)
                {
                    // Create a new color with configurable opacity for the sell signal
                    SolidColorBrush sellBrush = new SolidColorBrush(((SolidColorBrush)SellSignalColor).Color);
                    sellBrush.Opacity = SellBackgroundOpacity;
                    BackBrushes[0] = sellBrush;
                }
            }
        }

        private bool IsBuySignal(double williams, double emaWilliams, double cci, double emaCci, double momentum, double emaMomentum, double stoK, double stoD, double macdDiff, double macdDiffPrev, double rangeFilter, double maSignal, double close, double open, double maxHigh)
        {
            bool result = true;
            if (UseWilliamsR) result = result && williams > emaWilliams;
            if (UseCCI) result = result && cci > emaCci;
            if (UseMomentum) result = result && momentum > emaMomentum;
            if (UseStochastics) result = result && stoK > stoD;
            if (UseMACD) result = result && macdDiff > 0 && macdDiff > macdDiffPrev;
            if (UseRangeFilter) result = result && rangeFilter == 1;
            if (UseUltimateMA) result = result && maSignal == 1;
            if (UseOpenCloseCondition) result = result && close > open;
            if (UseHighLowCondition) result = result && close > maxHigh;
            return result;
        }

        private bool IsSellSignal(double williams, double emaWilliams, double cci, double emaCci, double momentum, double emaMomentum, double stoK, double stoD, double macdDiff, double macdDiffPrev, double rangeFilter, double maSignal, double close, double open, double minLow)
        {
            bool result = true;
            if (UseWilliamsR) result = result && williams < emaWilliams;
            if (UseCCI) result = result && cci < emaCci;
            if (UseMomentum) result = result && momentum < emaMomentum;
            if (UseStochastics) result = result && stoK < stoD;
            if (UseMACD) result = result && macdDiff < 0 && macdDiff < macdDiffPrev;
            if (UseRangeFilter) result = result && rangeFilter == -1;
            if (UseUltimateMA) result = result && maSignal == -1;
            if (UseOpenCloseCondition) result = result && close < open;
            if (UseHighLowCondition) result = result && close < minLow;
            return result;
        }
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private TEFASignalsPro[] cacheTEFASignalsPro;
		public TEFASignalsPro TEFASignalsPro(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			return TEFASignalsPro(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, cCIPeriod, cCIEMAPeriod, momentumPeriod, momentumEMAPeriod, stochasticKPeriod, stochasticDPeriod, stochasticSmooth, samplingPeriod, rangeMultiplier, selectedMAType, periodMA, tCount, vFactor, smoothLength, selectedTimeFrameModeTEFASignalsPro, secondaryPeriodType, secondaryPeriodValue, lookBackBars, useMACD, useWilliamsR, useCCI, useMomentum, useStochastics, useRangeFilter, useUltimateMA, useOpenCloseCondition, useHighLowCondition, buySignalColor, sellSignalColor, colorBackground, buyBackgroundOpacity, sellBackgroundOpacity, signalFontSize, textOffsetMultiplier);
		}

		public TEFASignalsPro TEFASignalsPro(ISeries<double> input, int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			if (cacheTEFASignalsPro != null)
				for (int idx = 0; idx < cacheTEFASignalsPro.Length; idx++)
					if (cacheTEFASignalsPro[idx] != null && cacheTEFASignalsPro[idx].Fast == fast && cacheTEFASignalsPro[idx].Slow == slow && cacheTEFASignalsPro[idx].Smooth == smooth && cacheTEFASignalsPro[idx].WilliamsRPeriod == williamsRPeriod && cacheTEFASignalsPro[idx].WilliamsREMAPeriod == williamsREMAPeriod && cacheTEFASignalsPro[idx].CCIPeriod == cCIPeriod && cacheTEFASignalsPro[idx].CCIEMAPeriod == cCIEMAPeriod && cacheTEFASignalsPro[idx].MomentumPeriod == momentumPeriod && cacheTEFASignalsPro[idx].MomentumEMAPeriod == momentumEMAPeriod && cacheTEFASignalsPro[idx].StochasticKPeriod == stochasticKPeriod && cacheTEFASignalsPro[idx].StochasticDPeriod == stochasticDPeriod && cacheTEFASignalsPro[idx].StochasticSmooth == stochasticSmooth && cacheTEFASignalsPro[idx].SamplingPeriod == samplingPeriod && cacheTEFASignalsPro[idx].RangeMultiplier == rangeMultiplier && cacheTEFASignalsPro[idx].SelectedMAType == selectedMAType && cacheTEFASignalsPro[idx].PeriodMA == periodMA && cacheTEFASignalsPro[idx].TCount == tCount && cacheTEFASignalsPro[idx].VFactor == vFactor && cacheTEFASignalsPro[idx].SmoothLength == smoothLength && cacheTEFASignalsPro[idx].SelectedTimeFrameModeTEFASignalsPro == selectedTimeFrameModeTEFASignalsPro && cacheTEFASignalsPro[idx].SecondaryPeriodType == secondaryPeriodType && cacheTEFASignalsPro[idx].SecondaryPeriodValue == secondaryPeriodValue && cacheTEFASignalsPro[idx].LookBackBars == lookBackBars && cacheTEFASignalsPro[idx].UseMACD == useMACD && cacheTEFASignalsPro[idx].UseWilliamsR == useWilliamsR && cacheTEFASignalsPro[idx].UseCCI == useCCI && cacheTEFASignalsPro[idx].UseMomentum == useMomentum && cacheTEFASignalsPro[idx].UseStochastics == useStochastics && cacheTEFASignalsPro[idx].UseRangeFilter == useRangeFilter && cacheTEFASignalsPro[idx].UseUltimateMA == useUltimateMA && cacheTEFASignalsPro[idx].UseOpenCloseCondition == useOpenCloseCondition && cacheTEFASignalsPro[idx].UseHighLowCondition == useHighLowCondition && cacheTEFASignalsPro[idx].BuySignalColor == buySignalColor && cacheTEFASignalsPro[idx].SellSignalColor == sellSignalColor && cacheTEFASignalsPro[idx].ColorBackground == colorBackground && cacheTEFASignalsPro[idx].BuyBackgroundOpacity == buyBackgroundOpacity && cacheTEFASignalsPro[idx].SellBackgroundOpacity == sellBackgroundOpacity && cacheTEFASignalsPro[idx].SignalFontSize == signalFontSize && cacheTEFASignalsPro[idx].TextOffsetMultiplier == textOffsetMultiplier && cacheTEFASignalsPro[idx].EqualsInput(input))
						return cacheTEFASignalsPro[idx];
			return CacheIndicator<TEFASignalsPro>(new TEFASignalsPro(){ Fast = fast, Slow = slow, Smooth = smooth, WilliamsRPeriod = williamsRPeriod, WilliamsREMAPeriod = williamsREMAPeriod, CCIPeriod = cCIPeriod, CCIEMAPeriod = cCIEMAPeriod, MomentumPeriod = momentumPeriod, MomentumEMAPeriod = momentumEMAPeriod, StochasticKPeriod = stochasticKPeriod, StochasticDPeriod = stochasticDPeriod, StochasticSmooth = stochasticSmooth, SamplingPeriod = samplingPeriod, RangeMultiplier = rangeMultiplier, SelectedMAType = selectedMAType, PeriodMA = periodMA, TCount = tCount, VFactor = vFactor, SmoothLength = smoothLength, SelectedTimeFrameModeTEFASignalsPro = selectedTimeFrameModeTEFASignalsPro, SecondaryPeriodType = secondaryPeriodType, SecondaryPeriodValue = secondaryPeriodValue, LookBackBars = lookBackBars, UseMACD = useMACD, UseWilliamsR = useWilliamsR, UseCCI = useCCI, UseMomentum = useMomentum, UseStochastics = useStochastics, UseRangeFilter = useRangeFilter, UseUltimateMA = useUltimateMA, UseOpenCloseCondition = useOpenCloseCondition, UseHighLowCondition = useHighLowCondition, BuySignalColor = buySignalColor, SellSignalColor = sellSignalColor, ColorBackground = colorBackground, BuyBackgroundOpacity = buyBackgroundOpacity, SellBackgroundOpacity = sellBackgroundOpacity, SignalFontSize = signalFontSize, TextOffsetMultiplier = textOffsetMultiplier }, input, ref cacheTEFASignalsPro);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.TEFASignalsPro TEFASignalsPro(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			return indicator.TEFASignalsPro(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, cCIPeriod, cCIEMAPeriod, momentumPeriod, momentumEMAPeriod, stochasticKPeriod, stochasticDPeriod, stochasticSmooth, samplingPeriod, rangeMultiplier, selectedMAType, periodMA, tCount, vFactor, smoothLength, selectedTimeFrameModeTEFASignalsPro, secondaryPeriodType, secondaryPeriodValue, lookBackBars, useMACD, useWilliamsR, useCCI, useMomentum, useStochastics, useRangeFilter, useUltimateMA, useOpenCloseCondition, useHighLowCondition, buySignalColor, sellSignalColor, colorBackground, buyBackgroundOpacity, sellBackgroundOpacity, signalFontSize, textOffsetMultiplier);
		}

		public Indicators.TEFASignalsPro TEFASignalsPro(ISeries<double> input , int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			return indicator.TEFASignalsPro(input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, cCIPeriod, cCIEMAPeriod, momentumPeriod, momentumEMAPeriod, stochasticKPeriod, stochasticDPeriod, stochasticSmooth, samplingPeriod, rangeMultiplier, selectedMAType, periodMA, tCount, vFactor, smoothLength, selectedTimeFrameModeTEFASignalsPro, secondaryPeriodType, secondaryPeriodValue, lookBackBars, useMACD, useWilliamsR, useCCI, useMomentum, useStochastics, useRangeFilter, useUltimateMA, useOpenCloseCondition, useHighLowCondition, buySignalColor, sellSignalColor, colorBackground, buyBackgroundOpacity, sellBackgroundOpacity, signalFontSize, textOffsetMultiplier);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.TEFASignalsPro TEFASignalsPro(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			return indicator.TEFASignalsPro(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, cCIPeriod, cCIEMAPeriod, momentumPeriod, momentumEMAPeriod, stochasticKPeriod, stochasticDPeriod, stochasticSmooth, samplingPeriod, rangeMultiplier, selectedMAType, periodMA, tCount, vFactor, smoothLength, selectedTimeFrameModeTEFASignalsPro, secondaryPeriodType, secondaryPeriodValue, lookBackBars, useMACD, useWilliamsR, useCCI, useMomentum, useStochastics, useRangeFilter, useUltimateMA, useOpenCloseCondition, useHighLowCondition, buySignalColor, sellSignalColor, colorBackground, buyBackgroundOpacity, sellBackgroundOpacity, signalFontSize, textOffsetMultiplier);
		}

		public Indicators.TEFASignalsPro TEFASignalsPro(ISeries<double> input , int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, int cCIPeriod, int cCIEMAPeriod, int momentumPeriod, int momentumEMAPeriod, int stochasticKPeriod, int stochasticDPeriod, int stochasticSmooth, int samplingPeriod, double rangeMultiplier, UMAType selectedMAType, int periodMA, int tCount, double vFactor, int smoothLength, TimeFrameModeTEFASignalsPro selectedTimeFrameModeTEFASignalsPro, BarsPeriodType secondaryPeriodType, int secondaryPeriodValue, int lookBackBars, bool useMACD, bool useWilliamsR, bool useCCI, bool useMomentum, bool useStochastics, bool useRangeFilter, bool useUltimateMA, bool useOpenCloseCondition, bool useHighLowCondition, Brush buySignalColor, Brush sellSignalColor, bool colorBackground, double buyBackgroundOpacity, double sellBackgroundOpacity, int signalFontSize, int textOffsetMultiplier)
		{
			return indicator.TEFASignalsPro(input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, cCIPeriod, cCIEMAPeriod, momentumPeriod, momentumEMAPeriod, stochasticKPeriod, stochasticDPeriod, stochasticSmooth, samplingPeriod, rangeMultiplier, selectedMAType, periodMA, tCount, vFactor, smoothLength, selectedTimeFrameModeTEFASignalsPro, secondaryPeriodType, secondaryPeriodValue, lookBackBars, useMACD, useWilliamsR, useCCI, useMomentum, useStochastics, useRangeFilter, useUltimateMA, useOpenCloseCondition, useHighLowCondition, buySignalColor, sellSignalColor, colorBackground, buyBackgroundOpacity, sellBackgroundOpacity, signalFontSize, textOffsetMultiplier);
		}
	}
}

#endregion
