#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media; // For System.Windows.Media.Color and Brushes for defaults
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.Core; // Required for Core.Globals
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.Cbi; // Required for Trade class

// Required for advanced drawing
using SharpDX;
using SharpDX.Direct2D1;
using SharpDX.DirectWrite;
// Explicit using alias to resolve ambiguity for common types if needed, but full qualification is safer
// using MediaBrush = System.Windows.Media.Brush; // Avoid using this due to threading issues
using MediaColor = System.Windows.Media.Color; // Alias for clarity
using DxBrush = SharpDX.Direct2D1.Brush;
using DxColor = SharpDX.Color; // Alias for clarity
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public partial class MAKER_V1 : Strategy
    {
        #region Variables
        // Logging system
        private enum LogLevel { Critical, Error, Warning, Info, Debug, Verbose }
        // Log category constants for consistent categorization
        private static class LogCategory
        {
            public const string SIGNAL = "SIGNAL";
            public const string ORDER = "ORDER";
            public const string ATM = "ATM";
            public const string RISK = "RISK";
            public const string TIME = "TIME";
            public const string SESSION = "SESSION";
            public const string TREND = "TREND";
            public const string VOLUME = "VOLUME";
            public const string FILTER = "FILTER";
            public const string SYSTEM = "SYSTEM";
            public const string TRADE = "TRADE";
        }
        private LogLevel CurrentLogLevel = LogLevel.Info; // Default log level
        private string LogFilePath = string.Empty; // Will be set in OnStateChange
        private System.IO.StreamWriter LogFileWriter = null;

        private RSI rsi; // RSI indicator instance
        private SMA fastMA; // Fast moving average for trend detection
        private SMA slowMA; // Slow moving average for trend detection
        // ADX removed as requested
        private Series<double> signalQuality; // Signal quality score
        // Volume ratio removed as requested

        private string atmStrategyId = string.Empty;
        private string orderId = string.Empty;
        private bool isAtmStrategyCreated = false; // Flag to track ATM creation callback

        // Trend Tracking
        private enum TrendDirection { Up, Down, Sideways }
        private TrendDirection currentTrend = TrendDirection.Sideways;

        // Session tracking
        private SessionIterator sessionIterator;

        // Risk Management Tracking
        private int consecutiveLosses = 0;
        private double dailyStartingBalance = 0;
        private DateTime lastTradeTime = DateTime.MinValue;
        private DateTime lastBalanceInitDay = DateTime.MinValue; // Track the last day we initialized the balance
        private bool inRecoveryMode = false;
        private DateTime recoveryModeEndTime = DateTime.MinValue;

        // Performance Stats class for tracking performance metrics
        private class PerformanceStats
        {
            public int TotalTrades { get; set; } = 0;
            public int WinningTrades { get; set; } = 0;
            public int LosingTrades { get; set; } = 0;
            public double TotalProfit { get; set; } = 0;
            public double TotalLoss { get; set; } = 0;
            public double TotalPnL { get; set; } = 0;
            public double WinRate { get { return TotalTrades > 0 ? (double)WinningTrades / TotalTrades * 100 : 0; } }
            public double ProfitFactor { get { return TotalLoss != 0 ? Math.Abs(TotalProfit / TotalLoss) : 0; } }
            public double NetProfit { get { return TotalProfit + TotalLoss; } }
            public double ExpectedValue { get { return TotalTrades > 0 ? TotalPnL / TotalTrades : 0; } }

            public void AddTrade(double pnl)
            {
                TotalTrades++;
                TotalPnL += pnl;

                if (pnl > 0)
                {
                    WinningTrades++;
                    TotalProfit += pnl;
                }
                else if (pnl < 0)
                {
                    LosingTrades++;
                    TotalLoss += pnl;
                }
            }
        }

        // Performance Metrics class for caching display values
        private class PerformanceMetrics
        {
            public double DailyPnL { get; set; } = 0;
            public double WeeklyPnL { get; set; } = 0;
            public double MonthlyPnL { get; set; } = 0;
            public double TotalPnL { get; set; } = 0;
            public double TotalNetPnl { get; set; } = 0;
            public double UnrealizedPnl { get; set; } = 0;
            public int TotalTrades { get; set; } = 0;
            public int WinningTrades { get; set; } = 0;
            public int LosingTrades { get; set; } = 0;
            public double WinRate { get; set; } = 0;
            public double ProfitFactor { get; set; } = 0;
            public double MaxDrawdown { get; set; } = 0;
            public double AvgTradePnl { get; set; } = 0;
            public double AvgWinPnl { get; set; } = 0;
            public double AvgLossPnl { get; set; } = 0;
            public double CurrentRSI { get; set; } = 0;
            public TrendDirection CurrentTrend { get; set; } = TrendDirection.Sideways;
            public double CurrentADX { get; set; } = 0;
            public double SignalQuality { get; set; } = 0;
        }

        // Time-based performance tracking
        private Dictionary<int, PerformanceStats> hourlyPerformance = new Dictionary<int, PerformanceStats>();
        private Dictionary<DayOfWeek, PerformanceStats> dailyPerformance = new Dictionary<DayOfWeek, PerformanceStats>();

        // Panel Drawing Resources - Initialize in OnStateChange(State.DataLoaded)
        // Use SharpDX.DirectWrite.Factory, SharpDX.DirectWrite.TextFormat directly
        private SharpDX.DirectWrite.Factory      dwFactory;
        private SharpDX.DirectWrite.TextFormat   terminalTextFormat;
        // Use SharpDX Brushes directly, create them from Color parameters
        private SharpDX.Direct2D1.SolidColorBrush panelBackgroundBrush;
        private SharpDX.Direct2D1.SolidColorBrush panelBorderBrush;
        private SharpDX.Direct2D1.SolidColorBrush textBrushDefault;   // Main text color brush
        private SharpDX.Direct2D1.SolidColorBrush textBrushWhite;
        private SharpDX.Direct2D1.SolidColorBrush textBrushProfit;    // Profit color brush
        private SharpDX.Direct2D1.SolidColorBrush textBrushLoss;      // Loss color brush
        private SharpDX.Direct2D1.SolidColorBrush textBrushYellow;

        // Performance Tracking (cache some values for panel)
        private PerformanceMetrics cachedMetrics = new PerformanceMetrics();

        // Thread-safe storage for UI colors
        private MediaColor panelTextColorValue;
        private MediaColor panelBackgroundColorValue;
        private MediaColor profitColorValue;
        private MediaColor lossColorValue;

        #endregion

        #region Parameters

        // Parameter Presets
        [NinjaScriptProperty]
        [Display(Name = "Parameter Preset", Description = "Select a parameter preset configuration", Order = 1, GroupName = "Presets")]
        public string ParameterPreset { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Apply Preset", Description = "Set to true to apply the selected preset", Order = 2, GroupName = "Presets")]
        public bool ApplyPreset { get; set; }

        // RSI Parameters
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "RSI Period", Order = 1, GroupName = "RSI Parameters")]
        public int RSIPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "RSI Smooth Period", Order = 2, GroupName = "RSI Parameters")]
        public int RSISmooth { get; set; }

        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Overbought Threshold", Order = 3, GroupName = "RSI Parameters")]
        public double OverboughtThreshold { get; set; }

        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Oversold Threshold", Order = 4, GroupName = "RSI Parameters")]
        public double OversoldThreshold { get; set; }

        // Trend Filter Parameters
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Fast MA Period", Order = 1, GroupName = "Trend Filter")]
        public int FastMAPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Slow MA Period", Order = 2, GroupName = "Trend Filter")]
        public int SlowMAPeriod { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Enable Trend Filter", Order = 3, GroupName = "Trend Filter")]
        public bool EnableTrendFilter { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Only Trade With Trend", Order = 4, GroupName = "Trend Filter")]
        public bool OnlyTradeWithTrend { get; set; }

        // ATM Strategy Parameters
        [NinjaScriptProperty]
        [Display(Name = "ATM Strategy Template", Order = 1, GroupName = "ATM Strategy")]
        public string AtmStrategyTemplate { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Exit Existing Positions on Signal", Order = 2, GroupName = "ATM Strategy")]
        public bool ExitExistingPositions { get; set; } // Note: ATM handles exits, this might be redundant or for manual overrides

        // Logging Parameters
        [NinjaScriptProperty]
        [Display(Name = "Log Level", Description = "Controls the verbosity of logging", Order = 1, GroupName = "Logging")]
        public string LogLevelSetting { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Enable File Logging", Description = "Enable logging to file", Order = 2, GroupName = "Logging")]
        public bool EnableFileLoggingSetting { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Log Indicator Values", Description = "Log detailed indicator values on each bar", Order = 3, GroupName = "Logging")]
        public bool LogIndicatorValuesSetting { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Log Market Context", Description = "Log market context information periodically", Order = 4, GroupName = "Logging")]
        public bool LogMarketContextSetting { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Log Trade Details", Description = "Log detailed trade information", Order = 5, GroupName = "Logging")]
        public bool LogTradeDetailsSetting { get; set; }

        // Panel Parameters
        [NinjaScriptProperty]
        [Display(Name = "Show Metrics Panel", Order = 1, GroupName = "Metrics Panel")]
        public bool ShowPanel { get; set; }

        [NinjaScriptProperty]
        [Range(6, 20)]
        [Display(Name = "Panel Font Size", Order = 2, GroupName = "Metrics Panel")]
        public int PanelFontSize { get; set; }

        // Use System.Windows.Media.Color for UI parameter selection
        // Store the Color, not the Brush, to avoid threading issues
        [XmlIgnore] // Indicate that this property is not directly serialized to XML
        [Display(Name = "Panel Text Color", Order = 3, GroupName = "Metrics Panel")]
        public MediaColor PanelTextColor
        {
            get { return panelTextColorValue; }
            set { panelTextColorValue = value; } // Store the color value
        }

        [Browsable(false)] // Hide the serializable string version from the UI
        public string PanelTextColorSerializable // Serializable string representation of the Color
        {
            get { return Serialize.BrushToString(new System.Windows.Media.SolidColorBrush(PanelTextColor)); }
            set { PanelTextColor = ((System.Windows.Media.SolidColorBrush)Serialize.StringToBrush(value)).Color; } // Deserialize to Color
        }

        [XmlIgnore]
        [Display(Name = "Panel Background", Order = 4, GroupName = "Metrics Panel")]
        public MediaColor PanelBackgroundColor
        {
            get { return panelBackgroundColorValue; }
            set { panelBackgroundColorValue = value; } // Store the color value
        }

        [Browsable(false)]
        public string PanelBackgroundColorSerializable
        {
            get { return Serialize.BrushToString(new System.Windows.Media.SolidColorBrush(PanelBackgroundColor)); }
            set { PanelBackgroundColor = ((System.Windows.Media.SolidColorBrush)Serialize.StringToBrush(value)).Color; } // Deserialize to Color
        }

        [XmlIgnore]
        [Display(Name = "Profit Color", Order = 5, GroupName = "Metrics Panel")]
        public MediaColor ProfitColor
        {
            get { return profitColorValue; }
            set { profitColorValue = value; } // Store the color value
        }

        [Browsable(false)]
        public string ProfitColorSerializable
        {
            get { return Serialize.BrushToString(new System.Windows.Media.SolidColorBrush(ProfitColor)); }
            set { ProfitColor = ((System.Windows.Media.SolidColorBrush)Serialize.StringToBrush(value)).Color; } // Deserialize to Color
        }

         [XmlIgnore]
        [Display(Name = "Loss Color", Order = 6, GroupName = "Metrics Panel")]
        public MediaColor LossColor
        {
            get { return lossColorValue; }
            set { lossColorValue = value; } // Store the color value
        }

        [Browsable(false)]
        public string LossColorSerializable
        {
            get { return Serialize.BrushToString(new System.Windows.Media.SolidColorBrush(LossColor)); }
            set { LossColor = ((System.Windows.Media.SolidColorBrush)Serialize.StringToBrush(value)).Color; } // Deserialize to Color
        }

        #endregion

        #region OnStateChange
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description                 = "RSI Strategy with bracket orders, trend filter, and performance panel.";
                Name                        = "MAKER_V1"; // Renamed
                Calculate                   = Calculate.OnBarClose;
                EntriesPerDirection         = 1;
                EntryHandling               = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds   = 30;
                IsFillLimitOnTouch          = false;
                MaximumBarsLookBack         = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution         = OrderFillResolution.Standard;
                Slippage                    = 0;
                StartBehavior               = StartBehavior.WaitUntilFlat;
                TimeInForce                 = TimeInForce.Day;
                TraceOrders                 = true;
                RealtimeErrorHandling       = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling          = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade         = 51; // Ensure enough bars for longest MA and RSI lookback

                // Set default preset options
                ParameterPreset = "Default";
                ApplyPreset = false;

                // Set default logging parameters
                LogLevelSetting = "Info"; // Default log level
                EnableFileLoggingSetting = true; // Enable file logging by default
                LogIndicatorValuesSetting = true; // Log indicator values by default
                LogMarketContextSetting = true; // Log market context by default
                LogTradeDetailsSetting = true; // Log trade details by default
                CurrentLogLevel = LogLevel.Info; // Default log level

                // Optimized RSI parameters based on backtest data analysis
                RSIPeriod = 14;
                RSISmooth = 3; // Increased smoothing for more stable signals based on backtest
                OverboughtThreshold = 75; // Increased to 75 to reduce false short signals
                OversoldThreshold = 25; // Decreased to 25 to reduce false long signals

                // Modified trend filter parameters to allow more trades
                FastMAPeriod = 20; // Adjusted for better trend detection
                SlowMAPeriod = 50; // Increased for more reliable trend identification
                EnableTrendFilter = true;
                OnlyTradeWithTrend = false; // Allow counter-trend trades based on RSI signals

                // Default ATM Strategy parameters
                AtmStrategyTemplate = "ATM_VOLATILE_OPTIMIZED"; // Data-driven optimized template based on backtest analysis
                ExitExistingPositions = true;

                // Ensure DefaultQuantity matches the ATM template's EntryQuantity
                DefaultQuantity = 6; // Updated to match ATM_VOLATILE_OPTIMIZED template's EntryQuantity

                // Default Panel Parameters - Use System.Windows.Media.Colors
                ShowPanel = true;
                PanelFontSize = 10;
                PanelTextColor = System.Windows.Media.Colors.LimeGreen; // Classic terminal green
                PanelBackgroundColor = MediaColor.FromArgb(200, 0, 0, 0); // Semi-transparent black
                ProfitColor = System.Windows.Media.Colors.LimeGreen;
                LossColor = System.Windows.Media.Colors.Red;

                IsInstantiatedOnEachOptimizationIteration = false; // Keep this for optimization
            }
            else if (State == State.Configure)
            {
                // Add Data Series if needed (example: secondary timeframe)
                // AddDataSeries(Data.BarsPeriodType.Minute, 5);

                // Check if enough bars for calculations
                int requiredBars = Math.Max(BarsRequiredToTrade,
                                   Math.Max(RSIPeriod + RSISmooth + 1, // RSI needs lookback + smooth + 1 for prev value
                                   Math.Max(FastMAPeriod, SlowMAPeriod)));
                BarsRequiredToTrade = Math.Max(BarsRequiredToTrade, requiredBars); // Update BarsRequiredToTrade dynamically
            }
            else if (State == State.DataLoaded)
            {
                // Initialize logging system
                InitializeLogging();
                Log(LogLevel.Info, $"Strategy initializing for {Instrument.FullName}");

                // Initialize Indicators - Moved here to ensure BarsRequiredToTrade is correctly set
                // and Primary Instrument data is available.
                rsi = this.RSI(Close, RSIPeriod, RSISmooth);
                fastMA = this.SMA(Close, FastMAPeriod);
                slowMA = this.SMA(Close, SlowMAPeriod);
                // ADX removed as requested

                Log(LogLevel.Info, $"Indicators initialized: this.RSI({RSIPeriod},{RSISmooth}), FastMA({FastMAPeriod}), SlowMA({SlowMAPeriod})");

                // Initialize time series for signal quality
                signalQuality = new Series<double>(this);
                // Volume ratio removed as requested

                // Initialize Market Structure Zones if enabled
                if (EnableMarketStructureZones)
                {
                    InitializeMarketStructureZones();
                    Log(LogLevel.Info, "Market Structure Zones functionality enabled");
                }

                // Verify that the ATM template exists and is accessible
                if (!VerifyAtmTemplateExists())
                {
                    // If template verification fails, fall back to the original template
                    Print($"{Time[0]} WARNING: Falling back to ATM_VOLATILE template due to verification failure.");
                    AtmStrategyTemplate = "ATM_VOLATILE";
                    // Adjust DefaultQuantity to match the original template
                    DefaultQuantity = 5;

                    // Try to verify the fallback template
                    if (!VerifyAtmTemplateExists())
                    {
                        Print($"{Time[0]} CRITICAL ERROR: Both optimized and fallback ATM templates failed verification. Strategy may not trade properly.");
                    }
                }

                // Initialize SessionIterator for session time tracking
                sessionIterator = new SessionIterator(Bars);

                // Add indicators to chart only if they aren't already there (optional, good practice)
                // Check ChartControl availability before adding indicators.
                if (ChartControl != null)
                {
                    // Check if indicators are already on the chart before adding them
                    if (ChartIndicators.FirstOrDefault(i => i.Name == rsi.Name) == null) AddChartIndicator(rsi);
                    if (ChartIndicators.FirstOrDefault(i => i.Name == fastMA.Name) == null) AddChartIndicator(fastMA);
                    if (ChartIndicators.FirstOrDefault(i => i.Name == slowMA.Name) == null) AddChartIndicator(slowMA);
                    // ADX removed as requested
                }

                // All performance optimization parameters removed as requested

                // Initialize drawing resources - These are safe here as RenderTarget isn't accessed yet
                 if (dwFactory == null)
                    dwFactory = new SharpDX.DirectWrite.Factory();

                if (terminalTextFormat == null || terminalTextFormat.FontSize != PanelFontSize) // Recreate if font size changed
                {
                    if (terminalTextFormat != null) terminalTextFormat.Dispose();
                    // Qualify SharpDX types explicitly
                    terminalTextFormat = new SharpDX.DirectWrite.TextFormat(dwFactory, "Consolas",
                        SharpDX.DirectWrite.FontWeight.Normal,
                        SharpDX.DirectWrite.FontStyle.Normal,
                        SharpDX.DirectWrite.FontStretch.Normal,
                        PanelFontSize)
                    {
                        TextAlignment = SharpDX.DirectWrite.TextAlignment.Leading,
                        ParagraphAlignment = SharpDX.DirectWrite.ParagraphAlignment.Near
                    };
                }

                 // Brushes will be created/recreated in OnRender based on RenderTarget availability
                 // No need to create SharpDX brushes here, as RenderTarget might not be ready or could change.
            }
            else if (State == State.Terminated)
            {
                // Close log file
                Log(LogLevel.Info, "Strategy terminating, closing log file");
                CloseLogFile();

                // Dispose of SharpDX resources ONLY
                DisposeDrawingResources();
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Estimates the margin requirement per contract for the current instrument.
        /// This is a workaround since MasterInstrument.MarginPerContract is not available.
        /// </summary>
        /// <returns>Estimated margin requirement per contract in USD</returns>
        private double GetEstimatedMarginRequirement()
        {
            try
            {
                // Get instrument information
                string symbol = Instrument.MasterInstrument.Name;
                double pointValue = Instrument.MasterInstrument.PointValue;

                // Estimate margin based on instrument type and point value
                // These are rough estimates and should be adjusted based on actual broker requirements
                if (symbol.Contains("ES") || symbol.Contains("NQ") || symbol.Contains("YM"))
                {
                    // E-mini contracts (ES, NQ, YM)
                    return 500.0; // Typical intraday margin for E-mini contracts
                }
                else if (symbol.Contains("MES") || symbol.Contains("MNQ") || symbol.Contains("MYM"))
                {
                    // Micro E-mini contracts
                    return 50.0; // Typical intraday margin for Micro E-mini contracts
                }
                else if (symbol.Contains("CL") || symbol.Contains("GC"))
                {
                    // Crude Oil or Gold
                    return 1000.0; // Typical intraday margin
                }
                else if (symbol.Contains("ZB") || symbol.Contains("ZN"))
                {
                    // Treasury Bonds/Notes
                    return 750.0; // Typical intraday margin
                }
                else
                {
                    // Default calculation based on point value
                    // This is a very rough estimate - adjust as needed
                    return Math.Max(100.0, pointValue * 10);
                }
            }
            catch (Exception ex)
            {
                Print($"Error in GetEstimatedMarginRequirement: {ex.Message}");
                return 500.0; // Default fallback value
            }
        }
        #endregion

        #region Logging Methods
        /// <summary>
        /// Initializes the logging system
        /// </summary>
        private void InitializeLogging()
        {
            try
            {
                // Set log level based on user setting
                switch (LogLevelSetting?.ToUpper() ?? "INFO")
                {
                    case "CRITICAL":
                        CurrentLogLevel = LogLevel.Critical;
                        break;
                    case "ERROR":
                        CurrentLogLevel = LogLevel.Error;
                        break;
                    case "WARNING":
                        CurrentLogLevel = LogLevel.Warning;
                        break;
                    case "INFO":
                        CurrentLogLevel = LogLevel.Info;
                        break;
                    case "DEBUG":
                        CurrentLogLevel = LogLevel.Debug;
                        break;
                    case "VERBOSE":
                        CurrentLogLevel = LogLevel.Verbose;
                        break;
                    default:
                        CurrentLogLevel = LogLevel.Info;
                        break;
                }

                // Force debug level if indicator values or market context logging is enabled
                if ((LogIndicatorValuesSetting || LogMarketContextSetting) && CurrentLogLevel < LogLevel.Debug)
                {
                    LogContext(LogLevel.Info, LogCategory.SYSTEM,
                        $"Upgrading log level to Debug because indicator or market context logging is enabled");
                    CurrentLogLevel = LogLevel.Debug;
                }

                // Set file logging based on user setting
                // EnableFileLogging variable is no longer needed, use the setting directly

                if (EnableFileLoggingSetting)
                {
                    // Create logs directory if it doesn't exist
                    string logsDirectory = System.IO.Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs");
                    if (!System.IO.Directory.Exists(logsDirectory))
                        System.IO.Directory.CreateDirectory(logsDirectory);

                    // Create a log file with timestamp in the name
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    string instrumentName = Instrument != null ? Instrument.FullName.Replace("/", "-") : "Unknown";
                    string logLevel = CurrentLogLevel.ToString();

                    // Implement log rotation - delete old log files (older than 7 days)
                    try
                    {
                        string[] oldLogFiles = System.IO.Directory.GetFiles(logsDirectory, "MAKER_V1_*.log");
                        foreach (string oldLogFile in oldLogFiles)
                        {
                            System.IO.FileInfo fileInfo = new System.IO.FileInfo(oldLogFile);
                            if (fileInfo.CreationTime < DateTime.Now.AddDays(-7))
                            {
                                LogContext(LogLevel.Info, LogCategory.SYSTEM, $"Deleting old log file: {fileInfo.Name}");
                                System.IO.File.Delete(oldLogFile);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogContext(LogLevel.Warning, LogCategory.SYSTEM, $"Error cleaning up old log files: {ex.Message}");
                    }

                    LogFilePath = System.IO.Path.Combine(logsDirectory, $"MAKER_V1_{instrumentName}_{logLevel}_{timestamp}.log");

                    // Open the log file for writing
                    LogFileWriter = new System.IO.StreamWriter(LogFilePath, true);
                    LogFileWriter.AutoFlush = true;

                    // Write header to log file
                    LogToFile(LogLevel.Info, $"=== MAKER_V1 Strategy Log Started at {DateTime.Now} ===");
                    LogToFile(LogLevel.Info, $"Instrument: {instrumentName}");
                    LogToFile(LogLevel.Info, $"Log Level: {CurrentLogLevel}");
                    LogToFile(LogLevel.Info, $"Strategy Parameters:");
                    LogToFile(LogLevel.Info, $"  RSI Period: {RSIPeriod}, Smooth: {RSISmooth}");
                    LogToFile(LogLevel.Info, $"  Overbought: {OverboughtThreshold}, Oversold: {OversoldThreshold}");
                    LogToFile(LogLevel.Info, $"  Fast MA: {FastMAPeriod}, Slow MA: {SlowMAPeriod}");
                    LogToFile(LogLevel.Info, $"  Trend Filter: {EnableTrendFilter}, Only Trade With Trend: {OnlyTradeWithTrend}");
                    LogToFile(LogLevel.Info, $"  ATM Template: {AtmStrategyTemplate}");
                    LogToFile(LogLevel.Info, $"  ADX, Signal Quality, Volume Filter, Risk Management, Time Filters: All Disabled (simplified strategy)");
                    LogToFile(LogLevel.Info, $"  Log Indicator Values: {LogIndicatorValuesSetting}");
                    LogToFile(LogLevel.Info, $"  Log Market Context: {LogMarketContextSetting}");
                    LogToFile(LogLevel.Info, $"  Log Trade Details: {LogTradeDetailsSetting}");
                }

                // Log initialization to NinjaTrader output
                Print($"{Time[0]} [INFO] MAKER_V1 logging initialized. Level: {CurrentLogLevel}, File Logging: {EnableFileLoggingSetting}");
                if (EnableFileLoggingSetting)
                    Print($"{Time[0]} [INFO] Log file: {LogFilePath}");
            }
            catch (Exception ex)
            {
                Print($"Error initializing logging: {ex.Message}");
                EnableFileLoggingSetting = false; // Disable file logging if initialization fails
            }
        }

        /// <summary>
        /// Logs a message to both NinjaTrader output window and log file if enabled
        /// </summary>
        private void Log(LogLevel level, string message)
        {
            // Only log if the current level is less than or equal to the configured level
            if (level <= CurrentLogLevel)
            {
                // Always log to NinjaTrader output window
                string formattedMessage = $"{Time[0]} [{level}] {message}";
                Print(formattedMessage);

                // Log to file if enabled
                if (EnableFileLoggingSetting)
                    LogToFile(level, message);
            }
        }

        /// <summary>
        /// Logs a message with additional context information
        /// </summary>
        private void LogContext(LogLevel level, string area, string message)
        {
            Log(level, $"[{area}] {message}");
        }

        /// <summary>
        /// Logs detailed information about a filter condition
        /// </summary>
        private void LogFilterCondition(LogLevel level, string filterName, bool passed, string details)
        {
            string result = passed ? "PASSED" : "FAILED";
            LogContext(level, LogCategory.FILTER, $"{filterName} filter {result}: {details}");
        }



        /// <summary>
        /// Logs a trade signal with detailed information
        /// </summary>
        private void LogSignal(string signalType, string direction, string reason, Dictionary<string, object> details = null)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append($"[SIGNAL] {signalType} {direction}: {reason}");

            if (details != null && details.Count > 0)
            {
                sb.Append(" | Details: ");
                foreach (var kvp in details)
                {
                    // Format numeric values with 2 decimal places
                    string valueStr = kvp.Value is double ? ((double)kvp.Value).ToString("F2") : kvp.Value.ToString();
                    sb.Append($"{kvp.Key}={valueStr}, ");
                }
                // Remove trailing comma and space
                sb.Length -= 2;
            }

            Log(LogLevel.Info, sb.ToString());
        }

        /// <summary>
        /// Logs order-related information
        /// </summary>
        private void LogOrder(string action, string orderType, string details)
        {
            Log(LogLevel.Info, $"[ORDER] {action} {orderType}: {details}");
        }

        /// <summary>
        /// Logs a message to the log file only
        /// </summary>
        private void LogToFile(LogLevel level, string message)
        {
            try
            {
                if (EnableFileLoggingSetting && LogFileWriter != null)
                {
                    string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    LogFileWriter.WriteLine($"{timestamp} [{level}] {message}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error writing to log file: {ex.Message}");
                EnableFileLoggingSetting = false; // Disable file logging if writing fails
            }
        }

        /// <summary>
        /// Closes the log file
        /// </summary>
        private void CloseLogFile()
        {
            try
            {
                if (LogFileWriter != null)
                {
                    LogToFile(LogLevel.Info, $"=== MAKER_V1 Strategy Log Ended at {DateTime.Now} ===");
                    LogFileWriter.Flush();
                    LogFileWriter.Close();
                    LogFileWriter.Dispose();
                    LogFileWriter = null;
                }
            }
            catch (Exception ex)
            {
                Print($"Error closing log file: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs detailed indicator values for the current bar
        /// </summary>
        private void LogIndicatorDetails()
        {
            if (!LogIndicatorValuesSetting || CurrentBar < 1)
                return;

            try
            {
                // Create a dictionary to hold all indicator values
                Dictionary<string, object> indicatorValues = new Dictionary<string, object>
                {
                    // Price data
                    { "Open", Open[0] },
                    { "High", High[0] },
                    { "Low", Low[0] },
                    { "Close", Close[0] },
                    { "Volume", Volume[0] },

                    // RSI values
                    { "RSI", rsi[0] },
                    { "RSI_1", rsi[1] },
                    { "RSI_Oversold", OversoldThreshold },
                    { "RSI_Overbought", OverboughtThreshold },

                    // Moving Averages
                    { "FastMA", fastMA[0] },
                    { "FastMA_1", fastMA[1] },
                    { "SlowMA", slowMA[0] },
                    { "SlowMA_1", slowMA[1] },

                    // ADX and volume metrics removed as requested

                    // Signal quality metrics
                    { "SignalQuality", signalQuality[0] },

                    // Trend information
                    { "CurrentTrend", currentTrend }
                };

                // Format the indicator values as a string
                StringBuilder sb = new StringBuilder("Indicator values: ");
                foreach (var kvp in indicatorValues)
                {
                    // Format numeric values with 2 decimal places
                    string valueStr = kvp.Value is double ? ((double)kvp.Value).ToString("F2") : kvp.Value.ToString();
                    sb.Append($"{kvp.Key}={valueStr}, ");
                }

                // Remove trailing comma and space
                if (sb.Length > 2)
                    sb.Length -= 2;

                // Log the indicator values
                LogContext(LogLevel.Debug, LogCategory.SIGNAL, sb.ToString());
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error in LogIndicatorDetails: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs detailed market context information
        /// </summary>
        private void LogMarketContext()
        {
            if (!LogMarketContextSetting || CurrentBar < 1)
                return;

            try
            {
                // Get current session information
                sessionIterator.GetNextSession(Time[0], false);
                DateTime sessionBegin = sessionIterator.ActualSessionBegin;
                DateTime sessionEnd = sessionIterator.ActualSessionEnd;
                TimeSpan timeIntoSession = Time[0] - sessionBegin;
                TimeSpan timeLeftInSession = sessionEnd - Time[0];

                // Calculate volatility metrics
                double atr = ATR(14)[0]; // Use standard ATR with 14 period
                double dailyRange = High[0] - Low[0];

                // Create a series for the daily range and calculate its average
                Series<double> dailyRangeSeries = new Series<double>(this);
                dailyRangeSeries[0] = dailyRange;
                double averageDailyRange = SMA(dailyRangeSeries, 20)[0]; // Use built-in calculation
                double volatilityRatio = dailyRange / (averageDailyRange > 0 ? averageDailyRange : 1);

                // Create a dictionary to hold market context information
                Dictionary<string, object> contextInfo = new Dictionary<string, object>
                {
                    // Session information
                    { "SessionBegin", sessionBegin.ToString("HH:mm:ss") },
                    { "SessionEnd", sessionEnd.ToString("HH:mm:ss") },
                    { "TimeIntoSession", $"{timeIntoSession.TotalMinutes:F0} min" },
                    { "TimeLeftInSession", $"{timeLeftInSession.TotalMinutes:F0} min" },

                    // Volatility metrics
                    { "ATR", atr },
                    { "DailyRange", dailyRange },
                    { "AvgDailyRange", averageDailyRange },
                    { "VolatilityRatio", volatilityRatio },

                    // Account information
                    { "AccountBalance", Account.Get(AccountItem.CashValue, Currency.UsDollar) },
                    { "AvailableMargin", Account.Get(AccountItem.ExcessInitialMargin, Currency.UsDollar) },

                    // Position information
                    { "CurrentPosition", Position.MarketPosition },
                    { "PositionSize", Position.Quantity },
                    { "UnrealizedPnL", Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]) }
                };

                // Format the context information as a string
                StringBuilder sb = new StringBuilder("Market context: ");
                foreach (var kvp in contextInfo)
                {
                    // Format numeric values with 2 decimal places if they are doubles
                    string valueStr = kvp.Value is double ? ((double)kvp.Value).ToString("F2") : kvp.Value.ToString();
                    sb.Append($"{kvp.Key}={valueStr}, ");
                }

                // Remove trailing comma and space
                if (sb.Length > 2)
                    sb.Length -= 2;

                // Log the market context
                LogContext(LogLevel.Info, LogCategory.SYSTEM, sb.ToString());
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error in LogMarketContext: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs detailed trade decision information
        /// </summary>
        private void LogTradeDecision(string action, string direction, Dictionary<string, object> conditions)
        {
            if (!LogTradeDetailsSetting)
                return;

            try
            {
                StringBuilder sb = new StringBuilder($"Trade decision ({action} {direction}): ");

                // Add all conditions to the log message
                foreach (var kvp in conditions)
                {
                    // Format boolean values as "Yes"/"No" for readability
                    string valueStr;
                    if (kvp.Value is bool)
                        valueStr = (bool)kvp.Value ? "Yes" : "No";
                    else if (kvp.Value is double)
                        valueStr = ((double)kvp.Value).ToString("F2");
                    else
                        valueStr = kvp.Value.ToString();

                    sb.Append($"{kvp.Key}={valueStr}, ");
                }

                // Remove trailing comma and space
                if (sb.Length > 2)
                    sb.Length -= 2;

                // Log the trade decision
                LogContext(LogLevel.Info, LogCategory.TRADE, sb.ToString());
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error in LogTradeDecision: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs strategy performance metrics
        /// </summary>
        private void LogPerformanceMetrics()
        {
            try
            {
                if (SystemPerformance == null || SystemPerformance.AllTrades.Count == 0)
                {
                    LogContext(LogLevel.Info, LogCategory.SYSTEM, "No trades to report performance metrics");
                    return;
                }

                // Get overall performance metrics
                int totalTrades = SystemPerformance.AllTrades.Count;
                int winningTrades = SystemPerformance.AllTrades.WinningTrades.Count;
                int losingTrades = SystemPerformance.AllTrades.LosingTrades.Count;
                double winRate = totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;

                // Calculate gross profit and loss manually
                double grossProfit = 0;
                double grossLoss = 0;
                double netProfit = 0;

                foreach (Trade trade in SystemPerformance.AllTrades)
                {
                    if (trade.ProfitCurrency > 0)
                        grossProfit += trade.ProfitCurrency;
                    else
                        grossLoss += Math.Abs(trade.ProfitCurrency);

                    netProfit += trade.ProfitCurrency;
                }

                double profitFactor = grossLoss != 0 ? grossProfit / grossLoss : 0;

                // Log overall performance
                LogContext(LogLevel.Info, LogCategory.SYSTEM,
                    $"Performance metrics: Trades={totalTrades}, Win%={winRate:F1}, " +
                    $"ProfitFactor={profitFactor:F2}, NetProfit=${netProfit:F2}");

                // Log recent performance (last 10 trades or fewer)
                int recentTradeCount = Math.Min(10, totalTrades);
                if (recentTradeCount > 0)
                {
                    int recentWins = 0;
                    double recentNetProfit = 0;

                    for (int i = totalTrades - 1; i >= totalTrades - recentTradeCount; i--)
                    {
                        if (SystemPerformance.AllTrades[i].ProfitCurrency > 0)
                            recentWins++;
                        recentNetProfit += SystemPerformance.AllTrades[i].ProfitCurrency;
                    }

                    double recentWinRate = (double)recentWins / recentTradeCount * 100;

                    LogContext(LogLevel.Info, LogCategory.SYSTEM,
                        $"Recent performance (last {recentTradeCount} trades): Win%={recentWinRate:F1}, NetProfit=${recentNetProfit:F2}");
                }

                // Log hourly performance if available
                if (hourlyPerformance.Count > 0)
                {
                    StringBuilder hourlyStats = new StringBuilder("Hourly performance: ");
                    foreach (var kvp in hourlyPerformance.OrderBy(h => h.Key))
                    {
                        if (kvp.Value.TotalTrades >= 3) // Only include hours with at least 3 trades
                        {
                            hourlyStats.Append($"Hour {kvp.Key}={kvp.Value.WinRate:F1}% ({kvp.Value.TotalTrades}), ");
                        }
                    }

                    // Remove trailing comma and space
                    if (hourlyStats.Length > "Hourly performance: ".Length)
                    {
                        hourlyStats.Length -= 2;
                        LogContext(LogLevel.Info, LogCategory.SYSTEM, hourlyStats.ToString());
                    }
                }

                // Risk management status logging removed as requested
                LogContext(LogLevel.Info, LogCategory.RISK, "Risk management disabled as requested");
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error in LogPerformanceMetrics: {ex.Message}");
            }
        }
        #endregion

        #region Drawing Resource Management
        // Helper method to create/recreate SharpDX brushes using the current RenderTarget
        private void InitializeDrawingBrushes()
        {
            // Dispose existing brushes first
            DisposeDrawingBrushes();

             // Ensure RenderTarget is available
            if (RenderTarget == null) return;

            try
            {
                // Create brushes using the stored Color values
                panelBackgroundBrush = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(PanelBackgroundColor));
                textBrushDefault = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(PanelTextColor));
                textBrushProfit = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(ProfitColor));
                textBrushLoss = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(LossColor));

                // Additional fixed colors
                textBrushWhite = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(System.Windows.Media.Colors.WhiteSmoke));
                textBrushYellow = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(System.Windows.Media.Colors.Yellow));
                panelBorderBrush = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget, ToDxColor(System.Windows.Media.Colors.DarkGray));
            }
            catch (Exception ex)
            {
                Print($"Error initializing drawing brushes: {ex.Message}");
                // Prevent further drawing attempts if brushes failed
                DisposeDrawingBrushes();
            }
        }

        // Helper method to dispose of SharpDX brushes
        private void DisposeDrawingBrushes()
        {
            if (panelBackgroundBrush != null) { panelBackgroundBrush.Dispose(); panelBackgroundBrush = null; }
            if (panelBorderBrush != null) { panelBorderBrush.Dispose(); panelBorderBrush = null; }
            if (textBrushDefault != null) { textBrushDefault.Dispose(); textBrushDefault = null; }
            if (textBrushWhite != null) { textBrushWhite.Dispose(); textBrushWhite = null; }
            if (textBrushProfit != null) { textBrushProfit.Dispose(); textBrushProfit = null; }
            if (textBrushLoss != null) { textBrushLoss.Dispose(); textBrushLoss = null; }
            if (textBrushYellow != null) { textBrushYellow.Dispose(); textBrushYellow = null; }
        }

         // Helper method to dispose of all drawing resources
        private void DisposeDrawingResources()
        {
             DisposeDrawingBrushes(); // Dispose brushes first
             if (terminalTextFormat != null) { terminalTextFormat.Dispose(); terminalTextFormat = null; }
             if (dwFactory != null) { dwFactory.Dispose(); dwFactory = null; }
        }
        #endregion


        #region OnBarUpdate
        protected override void OnBarUpdate()
        {
            try
            {
                // Ensure we have enough bars for calculations
                if (CurrentBar < BarsRequiredToTrade) // Use BarsRequiredToTrade set in Configure/DataLoaded
                {
                    if (CurrentBar == 0)
                        Log(LogLevel.Debug, "Waiting for required bars to accumulate");
                    return;
                }

                // Check if indicators have valid data points - crucial!
                if (CurrentBar < 1 || rsi == null || fastMA == null || slowMA == null)
                {
                    Log(LogLevel.Warning, "Indicators not ready or initialized properly");
                    return;
                }

                // Log bar update at verbose level (only in detailed debugging)
                if (CurrentLogLevel == LogLevel.Verbose)
                    Log(LogLevel.Verbose, $"Processing bar {CurrentBar}: O={Open[0]}, H={High[0]}, L={Low[0]}, C={Close[0]}, V={Volume[0]}");

                // Update Trend
                currentTrend = DetectTrend();
                LogContext(LogLevel.Debug, LogCategory.TREND, $"Current trend detected: {currentTrend}, FastMA={fastMA[0]:F2}, SlowMA={slowMA[0]:F2}");

                // Update Market Structure Zones if enabled
                if (EnableMarketStructureZones)
                {
                    UpdateMarketStructureZones();
                }

                // Calculate signal quality score (0-100)
                double currentSignalQuality;
                if (EnableMarketStructureZones)
                {
                    // Use Market Structure Zones signal quality calculation
                    currentSignalQuality = CalculateMarketStructureSignalQuality();
                    LogContext(LogLevel.Debug, LogCategory.SIGNAL, $"Market Structure signal quality: {currentSignalQuality:F2}/100, RSI={rsi[0]:F2}");
                }
                else
                {
                    // Use original signal quality calculation
                    currentSignalQuality = CalculateSignalQuality();
                    LogContext(LogLevel.Debug, LogCategory.SIGNAL, $"Signal quality: {currentSignalQuality:F2}/100, RSI={rsi[0]:F2}");
                }
                signalQuality[0] = currentSignalQuality;

                // Calculate volume ratio for confirmation (for logging only)
                double currentVolumeRatio;
                if (EnableMarketStructureZones)
                {
                    // Use shorter lookback period for range charts as recommended
                    currentVolumeRatio = Volume[0] > 0 ? Volume[0] / this.SMA(Volume, 10)[0] : 0;
                }
                else
                {
                    currentVolumeRatio = Volume[0] > 0 ? Volume[0] / this.SMA(Volume, 20)[0] : 0;
                }
                // volumeRatio removed as requested
                LogContext(LogLevel.Debug, LogCategory.VOLUME, $"Volume ratio: {currentVolumeRatio:F2}, Current={Volume[0]}");

                // Log detailed indicator values if enabled
                LogIndicatorDetails();

                // Log market context periodically (every 15 bars or approximately every hour for 5-min charts)
                if (CurrentBar % 15 == 0)
                {
                    LogMarketContext();

                    // Also log performance metrics periodically
                    if (LogTradeDetailsSetting)
                    {
                        LogPerformanceMetrics();
                    }
                }

                // Update cached metrics on every bar close for the panel
                if (IsFirstTickOfBar || Calculate == Calculate.OnEachTick) // Only update once per bar if OnBarClose
                    UpdateCachedMetrics();

                // Entry Logic (only if not in historical state for real trading)
                if (State >= State.Realtime || State == State.Historical)
                {
                    // Check for a pending entry order for the *current* strategy instance
                    if (orderId.Length > 0)
                    {
                        LogContext(LogLevel.Debug, "ATM", $"Checking status of pending entry order: {orderId}");
                        string[] status = GetAtmStrategyEntryOrderStatus(orderId);
                        // Order status indicates a terminal state, clear orderId
                        if (status.GetLength(0) > 0 && (status[2] == "Filled" || status[2] == "Cancelled" || status[2] == "Rejected"))
                        {
                            LogOrder("STATUS", "ENTRY", $"Order {orderId} reached terminal state: {status[2]}. Clearing orderId.");
                            orderId = string.Empty;
                        }
                        // Handle cases where the order might be working but GetAtmStrategyEntryOrderStatus returns empty initially
                        // If an ATM IS active with our ID, assume the entry filled or is working, clear our local orderId
                        else if (atmStrategyId.Length > 0 && GetAtmStrategyMarketPosition(atmStrategyId) != MarketPosition.Flat)
                        {
                             // Only clear if the status array was actually empty, otherwise trust the status reported
                             if (status.GetLength(0) == 0)
                             {
                                LogContext(LogLevel.Warning, "ATM", $"ATM {atmStrategyId} is active but entry order {orderId} status not found/pending. Clearing local orderId to prevent re-entry.");
                                orderId = string.Empty;
                             }
                             else
                             {
                                LogContext(LogLevel.Debug, "ATM", $"Entry order {orderId} status: {status[2]}, ATM position: {GetAtmStrategyMarketPosition(atmStrategyId)}");
                             }
                        }
                        else
                        {
                            LogContext(LogLevel.Debug, "ATM", $"Entry order {orderId} status: {(status.GetLength(0) > 0 ? status[2] : "Unknown")}");
                        }
                    }

                    // If the ATM strategy has terminated (position flat and no active stops/targets), reset the atmStrategyId
                    if (atmStrategyId.Length > 0 && GetAtmStrategyMarketPosition(atmStrategyId) == MarketPosition.Flat)
                    {
                        LogContext(LogLevel.Debug, "ATM", $"Checking if ATM {atmStrategyId} is still active despite flat position");
                        bool isAtmStillActive = false;
                        // Check entry order status first (if orderId hasn't been cleared yet)
                        if (orderId.Length > 0)
                        {
                            string[] entryStatus = GetAtmStrategyEntryOrderStatus(orderId);
                            if (entryStatus.GetLength(0) > 0 && entryStatus[2] == "Working")
                            {
                                isAtmStillActive = true;
                                LogContext(LogLevel.Debug, "ATM", $"ATM still active: Entry order {orderId} is still Working");
                            }
                        }

                        // If entry order isn't working, check stops/targets
                        if (!isAtmStillActive)
                        {
                            LogContext(LogLevel.Debug, "ATM", $"Checking stop/target orders for ATM {atmStrategyId}");
                            string[,] stopStatusArray = GetAtmStrategyStopTargetOrderStatus(atmStrategyId, string.Empty); // Gets all stops/targets
                            // Check if any stop or target order is still working
                            if (stopStatusArray != null && stopStatusArray.GetLength(0) > 0)
                            {
                                LogContext(LogLevel.Debug, "ATM", $"Found {stopStatusArray.GetLength(0)} stop/target orders");
                                for (int i = 0; i < stopStatusArray.GetLength(0); i++)
                                {
                                    // Check if status column (index 2) exists and is "Working"
                                    if (stopStatusArray.GetLength(1) > 2 && stopStatusArray[i, 2] == "Working")
                                    {
                                        isAtmStillActive = true;
                                        LogContext(LogLevel.Debug, "ATM", $"ATM still active: Stop/target order {stopStatusArray[i, 0]} is still Working");
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                LogContext(LogLevel.Debug, "ATM", "No stop/target orders found");
                            }
                        }

                        // If no orders are working and position is flat, clear the ATM ID
                        if (!isAtmStillActive)
                        {
                            LogContext(LogLevel.Info, "ATM", $"ATM {atmStrategyId} is Flat and no active orders found. Clearing atmStrategyId.");
                            atmStrategyId = string.Empty;
                            // Explicitly clear orderId here too, in case it was missed earlier
                            orderId = string.Empty;
                        }
                    }

                    // --- Entry Signal Logic ---
                    // Only process entry signals if we're flat according to ATM AND no entry order pending
                    if (atmStrategyId.Length == 0 && orderId.Length == 0 && Position.MarketPosition == MarketPosition.Flat)
                    {
                        // Get current RSI value
                        double rsiValue = rsi[0];
                        double currentAdx = 0; // ADX removed as requested
                        double currentQuality = signalQuality[0];
                        double currentVolRatio = 0; // Volume ratio removed as requested

                        // Risk management removed as requested
                        bool riskAllowsEntry = true;
                        LogContext(LogLevel.Debug, LogCategory.RISK, "Risk management disabled as requested");

                        // Time filters removed as requested
                        bool timeAllowsEntry = true;
                        LogContext(LogLevel.Debug, LogCategory.TIME, "Time filters removed as requested");

                        // Volume filter, signal quality, ADX, and risk management checks removed as requested
                        // Setting all filters to always true to simplify the strategy
                        bool volumeAllowsEntry = true;
                        bool qualityAllowsEntry = true;
                        bool adxAllowsEntry = true;
                        riskAllowsEntry = true; // Using the existing variable instead of declaring a new one

                        // Log that filters have been removed
                        LogContext(LogLevel.Debug, LogCategory.SIGNAL, $"All filters (volume, signal quality, ADX, risk management) have been removed as requested");

                        // LONG condition: RSI crosses below OversoldThreshold
                        if (CrossBelow(rsi, OversoldThreshold, 1))
                        {
                            bool trendAllowsLong = !EnableTrendFilter ||
                                                   (OnlyTradeWithTrend && currentTrend == TrendDirection.Up) ||
                                                   (!OnlyTradeWithTrend && currentTrend != TrendDirection.Down); // Allow sideways/up if not strictly with trend

                            // Check for support zone proximity if Market Structure Zones are enabled
                            bool zoneAllowsLong = !EnableMarketStructureZones || isNearSupportZone;

                            // ADX check removed as requested
                            // Always true to simplify the strategy
                            bool adxCheck = true;

                            if (trendAllowsLong && zoneAllowsLong)
                            {
                                // Create detailed log of trade entry decision
                                Dictionary<string, object> longEntryDetails = new Dictionary<string, object>
                                {
                                    { "RSI", rsiValue },
                                    { "Trend", currentTrend },
                                    { "TrendAllows", trendAllowsLong }
                                };

                                // Add Market Structure Zone details if enabled
                                if (EnableMarketStructureZones)
                                {
                                    longEntryDetails.Add("NearSupport", isNearSupportZone);
                                    if (isNearSupportZone && nearestSupportZone != null)
                                    {
                                        longEntryDetails.Add("SupportPrice", nearestSupportZone.Price);
                                        longEntryDetails.Add("SupportStrength", nearestSupportZone.Strength);
                                    }
                                }
                                else
                                {
                                    longEntryDetails.Add("ADX", "Removed");
                                }

                                LogTradeDecision("EXECUTING", "LONG", longEntryDetails);

                                if (EnableMarketStructureZones)
                                {
                                    LogContext(LogLevel.Info, LogCategory.SIGNAL,
                                        $"Long signal triggered at support zone. RSI={rsiValue:F2}, Trend={currentTrend}, " +
                                        $"Support={nearestSupportZone?.Price:F2} (Strength: {nearestSupportZone?.Strength}). " +
                                        $"Submitting ATM entry.");
                                }
                                else
                                {
                                    LogContext(LogLevel.Info, LogCategory.SIGNAL,
                                        $"Long signal triggered. RSI={rsiValue:F2}, Trend={currentTrend}. Submitting ATM entry.");
                                }

                                EnterLongWithBracketOrder();
                            }
                            else
                            {
                                // Create detailed log of ignored trade
                                Dictionary<string, object> ignoredLongDetails = new Dictionary<string, object>
                                {
                                    { "RSI", rsiValue },
                                    { "Trend", trendAllowsLong }
                                };

                                // Add Market Structure Zone details if enabled
                                if (EnableMarketStructureZones)
                                {
                                    ignoredLongDetails.Add("NearSupport", zoneAllowsLong);
                                }
                                else
                                {
                                    ignoredLongDetails.Add("ADX", adxAllowsEntry);
                                }

                                LogTradeDecision("IGNORED", "LONG", ignoredLongDetails);

                                if (EnableMarketStructureZones && !zoneAllowsLong)
                                {
                                    LogContext(LogLevel.Warning, LogCategory.SIGNAL,
                                        $"Long signal ignored - not near support zone. RSI={rsiValue:F2}");
                                }
                                else
                                {
                                    LogContext(LogLevel.Warning, LogCategory.SIGNAL,
                                        $"Long signal ignored. RSI={rsiValue:F2}, Trend={trendAllowsLong}, " +
                                        $"{(EnableMarketStructureZones ? "NearSupport" : "Zone")}={(EnableMarketStructureZones ? zoneAllowsLong : true)}");
                                }
                            }
                        }
                        // SHORT condition: RSI crosses above OverboughtThreshold
                        else if (CrossAbove(rsi, OverboughtThreshold, 1))
                        {
                            bool trendAllowsShort = !EnableTrendFilter ||
                                                (OnlyTradeWithTrend && currentTrend == TrendDirection.Down) ||
                                                (!OnlyTradeWithTrend && currentTrend != TrendDirection.Up); // Allow sideways/down if not strictly with trend

                            // Check for resistance zone proximity if Market Structure Zones are enabled
                            bool zoneAllowsShort = !EnableMarketStructureZones || isNearResistanceZone;

                            // ADX check removed as requested
                            // Always true to simplify the strategy
                            bool adxCheck = true;

                            if (trendAllowsShort && zoneAllowsShort)
                            {
                                // Create detailed log of trade entry decision
                                Dictionary<string, object> shortEntryDetails = new Dictionary<string, object>
                                {
                                    { "RSI", rsiValue },
                                    { "Trend", currentTrend },
                                    { "TrendAllows", trendAllowsShort }
                                };

                                // Add Market Structure Zone details if enabled
                                if (EnableMarketStructureZones)
                                {
                                    shortEntryDetails.Add("NearResistance", isNearResistanceZone);
                                    if (isNearResistanceZone && nearestResistanceZone != null)
                                    {
                                        shortEntryDetails.Add("ResistancePrice", nearestResistanceZone.Price);
                                        shortEntryDetails.Add("ResistanceStrength", nearestResistanceZone.Strength);
                                    }
                                }
                                else
                                {
                                    shortEntryDetails.Add("ADX", "Removed");
                                }

                                LogTradeDecision("EXECUTING", "SHORT", shortEntryDetails);

                                if (EnableMarketStructureZones)
                                {
                                    LogContext(LogLevel.Info, LogCategory.SIGNAL,
                                        $"Short signal triggered at resistance zone. RSI={rsiValue:F2}, Trend={currentTrend}, " +
                                        $"Resistance={nearestResistanceZone?.Price:F2} (Strength: {nearestResistanceZone?.Strength}). " +
                                        $"Submitting ATM entry.");
                                }
                                else
                                {
                                    LogContext(LogLevel.Info, LogCategory.SIGNAL,
                                        $"Short signal triggered. RSI={rsiValue:F2}, Trend={currentTrend}. Submitting ATM entry.");
                                }

                                EnterShortWithBracketOrder();
                            }
                            else
                            {
                                // Create detailed log of ignored trade
                                Dictionary<string, object> ignoredShortDetails = new Dictionary<string, object>
                                {
                                    { "RSI", rsiValue },
                                    { "Trend", trendAllowsShort }
                                };

                                // Add Market Structure Zone details if enabled
                                if (EnableMarketStructureZones)
                                {
                                    ignoredShortDetails.Add("NearResistance", zoneAllowsShort);
                                }
                                else
                                {
                                    ignoredShortDetails.Add("ADX", adxAllowsEntry);
                                }

                                LogTradeDecision("IGNORED", "SHORT", ignoredShortDetails);

                                if (EnableMarketStructureZones && !zoneAllowsShort)
                                {
                                    LogContext(LogLevel.Warning, LogCategory.SIGNAL,
                                        $"Short signal ignored - not near resistance zone. RSI={rsiValue:F2}");
                                }
                                else
                                {
                                    LogContext(LogLevel.Warning, LogCategory.SIGNAL,
                                        $"Short signal ignored. RSI={rsiValue:F2}, Trend={trendAllowsShort}, " +
                                        $"{(EnableMarketStructureZones ? "NearResistance" : "Zone")}={(EnableMarketStructureZones ? zoneAllowsShort : true)}");
                                }
                            }
                        }
                    }
                    // --- Optional: Exit on Opposite Signal (if not using ATM or want custom logic) ---
                    else if (ExitExistingPositions && atmStrategyId.Length > 0 && Position.MarketPosition != MarketPosition.Flat)
                    {
                        // Exit LONG if RSI crosses above Overbought (counter-signal)
                        if (Position.MarketPosition == MarketPosition.Long && CrossAbove(rsi, OverboughtThreshold, 1))
                        {
                            Print($"{Time[0]} Exit Long signal (RSI crossed Overbought). Closing ATM {atmStrategyId}.");
                            AtmStrategyClose(atmStrategyId);
                            atmStrategyId = string.Empty; // Clear ID after requesting close
                            orderId = string.Empty;       // Clear any lingering entry order ID
                        }
                        // Exit SHORT if RSI crosses below Oversold (counter-signal)
                        else if (Position.MarketPosition == MarketPosition.Short && CrossBelow(rsi, OversoldThreshold, 1))
                        {
                             Print($"{Time[0]} Exit Short signal (RSI crossed Oversold). Closing ATM {atmStrategyId}.");
                             AtmStrategyClose(atmStrategyId);
                             atmStrategyId = string.Empty; // Clear ID after requesting close
                             orderId = string.Empty;       // Clear any lingering entry order ID
                        }
                    }

                } // End if (State >= State.Realtime || State == State.Historical)

            }
            catch (Exception ex)
            {
                Print($"Exception in OnBarUpdate: {ex.Message}");
                Print($"Stack Trace: {ex.StackTrace}");
                 // Consider stopping the strategy or taking other action on critical errors
                 if(State == State.Realtime && RealtimeErrorHandling == RealtimeErrorHandling.StopCancelClose)
                 {
                    Print("Stopping strategy due to error in OnBarUpdate.");
                    // Optional: Force close positions if desired before stopping
                    if (Position.MarketPosition != MarketPosition.Flat && !string.IsNullOrEmpty(atmStrategyId))
                        AtmStrategyClose(atmStrategyId);
                    // Set state to disable further processing internally
                 }
            }
        }
        #endregion

        #region Performance Caching
        private void UpdateCachedMetrics()
        {
            // Ensure SystemPerformance is available
            if (SystemPerformance == null || SystemPerformance.AllTrades == null) return;

            // Use SystemPerformance.AllTrades.TradesPerformance for cumulative stats shown on panel
            TradesPerformance allTradesPerf = SystemPerformance.AllTrades.TradesPerformance;

            cachedMetrics.TotalNetPnl = allTradesPerf.NetProfit;
            cachedMetrics.UnrealizedPnl = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]); // Current position unrealized P/L
            cachedMetrics.TotalTrades = SystemPerformance.AllTrades.Count; // Total count including entries/exits

            // Calculate winning and losing trades manually
            int winningTrades = 0;
            int losingTrades = 0;
            double totalProfit = 0;
            double totalWinProfit = 0;
            double totalLossProfit = 0;

            foreach (Trade trade in SystemPerformance.AllTrades)
            {
                totalProfit += trade.ProfitCurrency;

                if (trade.ProfitCurrency > 0)
                {
                    winningTrades++;
                    totalWinProfit += trade.ProfitCurrency;
                }
                else if (trade.ProfitCurrency < 0)
                {
                    losingTrades++;
                    totalLossProfit += trade.ProfitCurrency;
                }
            }

            cachedMetrics.WinningTrades = winningTrades;
            cachedMetrics.LosingTrades = losingTrades;

            // Calculate max drawdown from gross loss as an approximation
            cachedMetrics.MaxDrawdown = Math.Abs(allTradesPerf.GrossLoss);

            if (cachedMetrics.TotalTrades > 0)
            {
                // Calculate win rate manually
                cachedMetrics.WinRate = ((double)winningTrades / cachedMetrics.TotalTrades) * 100.0;

                // Use ProfitFactor directly from TradesPerformance
                cachedMetrics.ProfitFactor = allTradesPerf.ProfitFactor;

                // Calculate average values manually
                cachedMetrics.AvgTradePnl = totalProfit / cachedMetrics.TotalTrades;
                cachedMetrics.AvgWinPnl = winningTrades > 0 ? totalWinProfit / winningTrades : 0;
                cachedMetrics.AvgLossPnl = losingTrades > 0 ? totalLossProfit / losingTrades : 0;
            }
            else // Initialize if no trades yet
            {
                cachedMetrics.WinRate = 0;
                cachedMetrics.ProfitFactor = 0;
                cachedMetrics.AvgTradePnl = 0;
                cachedMetrics.AvgWinPnl = 0;
                cachedMetrics.AvgLossPnl = 0;
                cachedMetrics.MaxDrawdown = 0;
            }
        }

        // We already have PerformanceMetrics and PerformanceStats defined earlier in the file
        // Removing duplicate definitions
        #endregion

        #region Helper Methods
        // Detect market trend using moving averages
        private TrendDirection DetectTrend()
        {
            // Ensure MAs are valid before comparing
            if (CurrentBar < 1 || fastMA == null || slowMA == null)
                return TrendDirection.Sideways; // Not enough data yet

            if (fastMA[0] > slowMA[0]) return TrendDirection.Up;
            if (fastMA[0] < slowMA[0]) return TrendDirection.Down;
            return TrendDirection.Sideways;
        }

        // Calculate signal quality score (0-100) - Simplified as requested
        private double CalculateSignalQuality()
        {
            // Signal quality calculation removed as requested
            LogContext(LogLevel.Debug, LogCategory.SIGNAL, "Signal quality calculation removed as requested");
            return 100; // Always return maximum quality since filtering is disabled
        }

        // TrendDirection enum is already defined earlier in the file
        // Removing duplicate definition

        // Check if an order belongs to this specific strategy instance's ATM context
        // Refined to check against both the entry order ID and the active ATM strategy ID
        private bool IsMyOrderOrAtm(string orderIdToCheck)
        {
            if (string.IsNullOrEmpty(orderIdToCheck)) return false;

            // Check if it's our pending entry order
            if (orderIdToCheck == orderId) return true;

            // Check if it belongs to our active ATM strategy
            if (!string.IsNullOrEmpty(atmStrategyId))
            {
                 // Attempt to get the ATM strategy ID associated with the given orderId
                 // This relies on internal NinjaTrader mechanisms and might not be perfectly reliable,
                 // but checking against our stored atmStrategyId is the primary way.
                 // A simpler check: Does the order object's AtmStrategyId match ours? (If accessible)
                 // Or, are there active orders linked to our atmStrategyId?

                 // Using GetAtmStrategyXXX methods is the supported way.
                 // If GetAtmStrategyMarketPosition or OrderStatus links to our atmStrategyId, it's ours.
                 // However, we don't have the Order object here, only the ID.
                 // A practical approach: If an ATM is active (atmStrategyId is set), assume any order related to it
                 // might be relevant. The most reliable check is if the orderId IS the entry orderId OR if the atmStrategyId is active.
                 // We can't directly link an arbitrary orderId back to *our* atmStrategyId without iterating through all orders
                 // or using specific NinjaTrader internal knowledge.

                 // Let's stick to the most reliable checks:
                 // 1. Is it the specific entry order ID we stored?
                 // 2. Is our ATM Strategy ID currently active (non-empty)? (This implies subsequent stop/target orders are "ours")
                 // The OnOrderUpdate/OnExecutionUpdate filtering should rely on this.
                 return true; // If ATM is active, assume related orders might be ours for logging purposes.
            }

            return false;
        }

        // Helper method to check if an order is part of a specific ATM strategy
        private bool IsOrderPartOfAtmStrategy(Order order, string atmStrategyIdToCheck)
        {
            if (order == null || string.IsNullOrEmpty(atmStrategyIdToCheck))
                return false;

            // Check if this is our entry order
            if (order.OrderId == orderId)
                return true;

            // Check if the order name contains our ATM strategy ID
            // This is a common pattern in NinjaTrader where ATM orders include the strategy ID in their name
            if (order.Name.Contains(atmStrategyIdToCheck))
                return true;

            // Check if the order is linked to our ATM strategy through other means
            // For example, check if it's a stop or target order for our ATM strategy
            string[,] stopTargetOrders = GetAtmStrategyStopTargetOrderStatus(atmStrategyIdToCheck, string.Empty);
            if (stopTargetOrders != null && stopTargetOrders.GetLength(0) > 0)
            {
                for (int i = 0; i < stopTargetOrders.GetLength(0); i++)
                {
                    // Check if the order ID column exists and matches our order
                    if (stopTargetOrders.GetLength(1) > 0 && stopTargetOrders[i, 0] == order.OrderId)
                        return true;
                }
            }

            return false;
        }

         // Helper to convert System.Windows.Media.Color to SharpDX.Color
        private SharpDX.Color ToDxColor(MediaColor wpfColor)
        {
            return new SharpDX.Color(wpfColor.R, wpfColor.G, wpfColor.B, wpfColor.A);
        }

        // Helper to get SharpDX brush based on P/L value
        private SharpDX.Direct2D1.SolidColorBrush GetPnlBrush(double pnlValue)
        {
            if (pnlValue > 0) return textBrushProfit; // Use Profit Color Brush
            if (pnlValue < 0) return textBrushLoss;   // Use Loss Color Brush
            return textBrushWhite;                   // Neutral (WhiteSmoke)
        }

        // Helper to get SharpDX brush based on RSI level
         private SharpDX.Direct2D1.SolidColorBrush GetRsiBrush(double rsiValue)
        {
            if (rsiValue >= OverboughtThreshold) return textBrushLoss;   // Overbought -> Loss Color (Red)
            if (rsiValue <= OversoldThreshold) return textBrushProfit; // Oversold -> Profit Color (Green)
            return textBrushWhite; // Neutral zone -> White
        }
        #endregion

        #region Entry Methods
        private void EnterLongWithBracketOrder()
        {
            // Double-check conditions before submitting
            if (atmStrategyId.Length > 0 || orderId.Length > 0 || Position.MarketPosition != MarketPosition.Flat)
            {
                LogContext(LogLevel.Warning, LogCategory.ORDER,
                    $"Prevented duplicate long entry. ATM ID: '{atmStrategyId}', Order ID: '{orderId}', Position: {Position.MarketPosition}");
                return;
            }

            // Log diagnostic information about entry attempt
            LogEntryAttempt("LONG");

            // Get ATM template details for logging
            Dictionary<string, object> atmDetails = GetAtmTemplateDetails(AtmStrategyTemplate);
            LogContext(LogLevel.Info, LogCategory.ATM, $"Using ATM template: {AtmStrategyTemplate} with details: " +
                $"EntryQty={atmDetails["EntryQuantity"]}, " +
                $"Target1={atmDetails["Target1"]}, StopLoss1={atmDetails["StopLoss1"]}, " +
                $"BreakEven={atmDetails["BreakEvenTrigger"]}");

            try
            {
                isAtmStrategyCreated = false; // Reset check flag
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = GetAtmStrategyUniqueId(); // Use a separate ID for the entry order itself

                // Log entry details
                Dictionary<string, object> entryDetails = new Dictionary<string, object>
                {
                    { "Action", "Buy" },
                    { "OrderType", "Market" },
                    { "Price", Close[0] },
                    { "Time", Time[0] },
                    { "ATM_Template", AtmStrategyTemplate },
                    { "ATM_ID", atmStrategyId },
                    { "Order_ID", orderId },
                    { "RSI", rsi[0] },
                    { "ADX", "Removed" },
                    { "Trend", currentTrend },
                    { "SignalQuality", signalQuality[0] }
                };
                LogContext(LogLevel.Info, LogCategory.ORDER, "Submitting LONG entry order with details: " +
                    string.Join(", ", entryDetails.Select(kvp => $"{kvp.Key}={kvp.Value}")));

                // Submit the ATM Strategy Order
                AtmStrategyCreate(OrderAction.Buy, OrderType.Market, 0, 0, TimeInForce.Day,
                    orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                {
                    // This callback confirms the *creation request* was processed, not if it filled.
                    if (atmCallbackErrorCode == ErrorCode.NoError)
                    {
                        // Check if the callback ID matches the strategy ID we intended to create
                        if (atmCallBackId == atmStrategyId)
                        {
                            isAtmStrategyCreated = true; // Mark as successfully submitted
                            LogContext(LogLevel.Info, LogCategory.ATM,
                                $"ATM Strategy {atmStrategyId} submission acknowledged for LONG entry (Order ID: {orderId}) using template: {AtmStrategyTemplate}. Waiting for fill...");

                            // Log entry time for tracking
                            lastTradeTime = Time[0];
                        }
                        else
                        {
                            // This case should be rare, but log it if it happens
                            LogContext(LogLevel.Warning, LogCategory.ATM,
                                $"ATM Callback received for unexpected ATM ID: {atmCallBackId} (expected {atmStrategyId}) associated with Order ID {orderId}. Status: {atmCallbackErrorCode}.");
                        }
                    }
                    else
                    {
                        LogContext(LogLevel.Error, LogCategory.ATM,
                            $"Error submitting ATM Strategy {atmStrategyId} for LONG entry (Order ID: {orderId}): {atmCallbackErrorCode}. Clearing IDs.");
                        // Reset IDs if submission failed critically, allowing for retry on next signal
                        atmStrategyId = string.Empty;
                        orderId = string.Empty;
                    }
                });
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM,
                    $"Exception submitting long ATM: {ex.Message}\n{ex.StackTrace}");
                // Ensure IDs are cleared on exception to allow retry
                atmStrategyId = string.Empty;
                orderId = string.Empty;
            }
        }

        private void EnterShortWithBracketOrder()
        {
            // Double-check conditions before submitting
            if (atmStrategyId.Length > 0 || orderId.Length > 0 || Position.MarketPosition != MarketPosition.Flat)
            {
                LogContext(LogLevel.Warning, LogCategory.ORDER,
                    $"Prevented duplicate short entry. ATM ID: '{atmStrategyId}', Order ID: '{orderId}', Position: {Position.MarketPosition}");
                return;
            }

            // Log diagnostic information about entry attempt
            LogEntryAttempt("SHORT");

            // Get ATM template details for logging
            Dictionary<string, object> atmDetails = GetAtmTemplateDetails(AtmStrategyTemplate);
            LogContext(LogLevel.Info, LogCategory.ATM, $"Using ATM template: {AtmStrategyTemplate} with details: " +
                $"EntryQty={atmDetails["EntryQuantity"]}, " +
                $"Target1={atmDetails["Target1"]}, StopLoss1={atmDetails["StopLoss1"]}, " +
                $"BreakEven={atmDetails["BreakEvenTrigger"]}");

            try
            {
                isAtmStrategyCreated = false; // Reset check flag
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = GetAtmStrategyUniqueId(); // Use a separate ID for the entry order itself

                // Log entry details
                Dictionary<string, object> entryDetails = new Dictionary<string, object>
                {
                    { "Action", "Sell" },
                    { "OrderType", "Market" },
                    { "Price", Close[0] },
                    { "Time", Time[0] },
                    { "ATM_Template", AtmStrategyTemplate },
                    { "ATM_ID", atmStrategyId },
                    { "Order_ID", orderId },
                    { "RSI", rsi[0] },
                    { "ADX", "Removed" },
                    { "Trend", currentTrend },
                    { "SignalQuality", signalQuality[0] }
                };
                LogContext(LogLevel.Info, LogCategory.ORDER, "Submitting SHORT entry order with details: " +
                    string.Join(", ", entryDetails.Select(kvp => $"{kvp.Key}={kvp.Value}")));

                // Place the ATM Strategy Order
                AtmStrategyCreate(OrderAction.Sell, OrderType.Market, 0, 0, TimeInForce.Day,
                    orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                {
                    // This callback confirms the *creation request* was processed, not if it filled.
                    if (atmCallbackErrorCode == ErrorCode.NoError)
                    {
                        // Check if the callback ID matches the strategy ID we intended to create
                        if (atmCallBackId == atmStrategyId)
                        {
                            isAtmStrategyCreated = true; // Mark as successfully submitted
                            LogContext(LogLevel.Info, LogCategory.ATM,
                                $"ATM Strategy {atmStrategyId} submission acknowledged for SHORT entry (Order ID: {orderId}) using template: {AtmStrategyTemplate}. Waiting for fill...");

                            // Log entry time for tracking
                            lastTradeTime = Time[0];
                        }
                        else
                        {
                            // This case should be rare, but log it if it happens
                            LogContext(LogLevel.Warning, LogCategory.ATM,
                                $"ATM Callback received for unexpected ATM ID: {atmCallBackId} (expected {atmStrategyId}) associated with Order ID {orderId}. Status: {atmCallbackErrorCode}.");
                        }
                    }
                    else
                    {
                        LogContext(LogLevel.Error, LogCategory.ATM,
                            $"Error submitting ATM Strategy {atmStrategyId} for SHORT entry (Order ID: {orderId}): {atmCallbackErrorCode}. Clearing IDs.");
                        // Reset IDs if submission failed critically
                        atmStrategyId = string.Empty;
                        orderId = string.Empty;
                    }
                });
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM,
                    $"Exception submitting short ATM: {ex.Message}\n{ex.StackTrace}");
                // Ensure IDs are cleared on exception to allow retry
                atmStrategyId = string.Empty;
                orderId = string.Empty;
            }
        }
        #endregion

        #region ATM Template Helpers
        /// <summary>
        /// Gets details about an ATM template for logging purposes
        /// </summary>
        private Dictionary<string, object> GetAtmTemplateDetails(string templateName)
        {
            Dictionary<string, object> details = new Dictionary<string, object>();

            try
            {
                // Default values in case we can't read the template
                details["EntryQuantity"] = DefaultQuantity;
                details["Target1"] = 100;
                details["StopLoss1"] = 40;
                details["BreakEvenTrigger"] = 20;

                // Set known values based on template name
                if (templateName == "ATM_VOLATILE_OPTIMIZED")
                {
                    details["EntryQuantity"] = 6;
                    details["Target1"] = 125;
                    details["StopLoss1"] = 35;
                    details["BreakEvenTrigger"] = 15;
                    details["Description"] = "Optimized template for volatile markets";
                }
                else if (templateName == "ATM_VOLATILE")
                {
                    details["EntryQuantity"] = 5;
                    details["Target1"] = 150;
                    details["StopLoss1"] = 40;
                    details["BreakEvenTrigger"] = 20;
                    details["Description"] = "Standard template for volatile markets";
                }

                // Try to read actual template file if available
                string templatePath = System.IO.Path.Combine(
                    NinjaTrader.Core.Globals.UserDataDir,
                    "templates",
                    "AtmStrategy",
                    $"{templateName}.xml");

                if (System.IO.File.Exists(templatePath))
                {
                    LogContext(LogLevel.Debug, LogCategory.ATM, $"Found ATM template file: {templatePath}");
                    // We could parse the XML here, but that's complex and error-prone
                    // Instead, we'll use the known values above
                }
                else
                {
                    LogContext(LogLevel.Warning, LogCategory.ATM, $"ATM template file not found: {templatePath}");
                }
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error getting ATM template details: {ex.Message}");
            }

            return details;
        }
        #endregion

        #region Order/Execution Event Handlers
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string nativeError)
        {
            // Filter for relevance: Check if order belongs to our pending entry or active ATM
            bool isRelevant = (orderId == order.OrderId) || (!string.IsNullOrEmpty(atmStrategyId) && IsOrderPartOfAtmStrategy(order, atmStrategyId));

            if (isRelevant)
            {
                // Log detailed order information
                LogContext(LogLevel.Info, LogCategory.ORDER,
                    $"Update: {order.Name} ({order.OrderId}) - State: {orderState}, Filled: {filled}/{quantity} @ {averageFillPrice}, ATM: {atmStrategyId}, Error: {error}");

                // Additional details for debugging
                Dictionary<string, object> orderDetails = new Dictionary<string, object>
                {
                    { "OrderId", order.OrderId },
                    { "Name", order.Name },
                    { "State", orderState },
                    { "Filled", $"{filled}/{quantity}" },
                    { "AvgPrice", averageFillPrice },
                    { "LimitPrice", limitPrice },
                    { "StopPrice", stopPrice },
                    { "Time", time },
                    { "Error", error },
                    { "NativeError", nativeError },
                    { "IsEntry", order.OrderId == orderId },
                    { "AtmId", atmStrategyId }
                };

                // Log order details at debug level
                StringBuilder detailsBuilder = new StringBuilder("Order details: ");
                foreach (var kvp in orderDetails)
                {
                    if (kvp.Value != null && !string.IsNullOrEmpty(kvp.Value.ToString()))
                    {
                        detailsBuilder.Append($"{kvp.Key}={kvp.Value}, ");
                    }
                }
                if (detailsBuilder.Length > 2) detailsBuilder.Length -= 2; // Remove trailing comma
                LogContext(LogLevel.Debug, LogCategory.ORDER, detailsBuilder.ToString());

                // If our specific entry order is rejected or cancelled, clear the IDs to allow new entries.
                if (order.OrderId == orderId && (orderState == OrderState.Rejected || orderState == OrderState.Cancelled))
                {
                    LogContext(LogLevel.Error, LogCategory.ORDER,
                        $"Entry Order {order.OrderId} failed ({orderState}). Clearing ATM Strategy {atmStrategyId}. Error: {error} {nativeError}");

                    // Attempt to cancel the associated ATM strategy if it exists and wasn't fully cancelled by the entry failure.
                    // Check if the ATM ID is still set and wasn't cleared by the callback error handler.
                    if (!string.IsNullOrEmpty(atmStrategyId))
                    {
                        // Check if the ATM strategy still thinks it's active before trying to cancel
                        // (e.g., market position is not flat, or stop/target orders might exist)
                        // This prevents unnecessary cancel attempts if NT already cleaned it up.
                        // A simple check might be if GetAtmStrategyMarketPosition is not Flat,
                        // but rely primarily on the fact that the entry failed.
                        LogContext(LogLevel.Warning, LogCategory.ATM,
                            $"Attempting to close potentially orphaned ATM strategy {atmStrategyId} due to entry order failure.");
                        AtmStrategyClose(atmStrategyId); // Request closure of the ATM strategy
                    }

                    // Always clear local IDs after entry failure
                    atmStrategyId = string.Empty;
                    orderId = string.Empty;
                    LogContext(LogLevel.Info, LogCategory.ATM, "Cleared ATM and order IDs after entry failure");
                }

                // Log when an order is filled
                if (orderState == OrderState.Filled)
                {
                    LogContext(LogLevel.Info, LogCategory.ORDER,
                        $"Order FILLED: {order.Name} ({order.OrderId}) - {quantity} @ {averageFillPrice}, ATM: {atmStrategyId}");
                }
            }
            else if (TraceOrders)
            {
                // Log other orders at verbose level for complete audit trail
                LogContext(LogLevel.Verbose, LogCategory.ORDER,
                    $"Other Order: {order.Name} ({order.OrderId}) - State: {orderState}, Filled: {filled}/{quantity}");
            }
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            // Filter for relevance using the execution's order object
            bool isRelevant = execution.Order != null &&
                             ((this.orderId == execution.Order.OrderId) ||
                              (!string.IsNullOrEmpty(atmStrategyId) && IsOrderPartOfAtmStrategy(execution.Order, atmStrategyId)));

            if (isRelevant)
            {
                // Log detailed execution information
                LogContext(LogLevel.Info, LogCategory.TRADE,
                    $"Execution: {execution.Name} ({executionId}) - Pos: {marketPosition}, Qty: {quantity} @ {price}, Order: {orderId}, ATM: {atmStrategyId}");

                // Additional execution details for debugging
                Dictionary<string, object> executionDetails = new Dictionary<string, object>
                {
                    { "ExecutionId", executionId },
                    { "Name", execution.Name },
                    { "Position", marketPosition },
                    { "Quantity", quantity },
                    { "Price", price },
                    { "OrderId", orderId },
                    { "Time", time },
                    { "IsEntry", orderId == this.orderId },
                    { "AtmId", atmStrategyId },
                    { "Commission", execution.Commission },
                    { "Exchange", execution.Exchange }
                };

                // Log execution details at debug level
                StringBuilder detailsBuilder = new StringBuilder("Execution details: ");
                foreach (var kvp in executionDetails)
                {
                    if (kvp.Value != null && !string.IsNullOrEmpty(kvp.Value.ToString()))
                    {
                        detailsBuilder.Append($"{kvp.Key}={kvp.Value}, ");
                    }
                }
                if (detailsBuilder.Length > 2) detailsBuilder.Length -= 2; // Remove trailing comma
                LogContext(LogLevel.Debug, LogCategory.TRADE, detailsBuilder.ToString());

                // Update cached metrics immediately after any execution potentially affecting P/L or trade count
                UpdateCachedMetrics();

                // Track time-based performance for completed trades
                if (execution.Order != null && execution.Order.OrderState == OrderState.Filled)
                {
                    // Find the trade this execution belongs to
                    Trade trade = SystemPerformance.AllTrades.LastOrDefault();
                    if (trade != null && trade.ProfitCurrency != 0) // Only process completed trades with P/L
                    {
                        // Get the entry time (use execution time as fallback)
                        DateTime entryTime = trade.Entry != null && trade.Entry.Time != DateTime.MinValue ?
                                            trade.Entry.Time : time;

                        // Update hourly performance stats
                        int hour = entryTime.Hour;
                        if (!hourlyPerformance.ContainsKey(hour))
                            hourlyPerformance[hour] = new PerformanceStats();

                        hourlyPerformance[hour].AddTrade(trade.ProfitCurrency);

                        // Update daily performance stats
                        DayOfWeek day = entryTime.DayOfWeek;
                        if (!dailyPerformance.ContainsKey(day))
                            dailyPerformance[day] = new PerformanceStats();

                        dailyPerformance[day].AddTrade(trade.ProfitCurrency);

                        // Update risk management tracking
                        lastTradeTime = time; // Update last trade time for daily reset logic

                        // Risk management tracking removed as requested
                        if (trade.ProfitCurrency > 0)
                        {
                            Print($"{Time} Winning trade. P/L: {trade.ProfitCurrency}");
                        }
                        else if (trade.ProfitCurrency < 0)
                        {
                            Print($"{Time} Losing trade. P/L: {trade.ProfitCurrency}");
                        }

                        // Log performance stats for analysis
                        Print($"{Time} Trade completed at Hour {hour}, Day {day}. P/L: {trade.ProfitCurrency}. " +
                              $"Hour stats: {hourlyPerformance[hour].WinRate:F1}% win rate over {hourlyPerformance[hour].TotalTrades} trades.");
                    }
                }

                // Force chart redraw AFTER updating metrics to show the latest P/L etc. on the panel
                // Check if ChartControl is available before forcing refresh
                if (ChartControl != null) ForceRefresh();
            }
            // Optional: Log other executions if needed
            // else if (TraceOrders)
            // {
            //     Print($"{Time} Other Execution Update: {execution.Name} ({executionId}) - Pos: {marketPosition}");
            // }
        }

        // Use correct base class signature
        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            // Position updates are global for the account/instrument, not specific to this strategy instance's ATM.
            // However, they are crucial for knowing the overall state.

            // Create detailed position information dictionary
            Dictionary<string, object> positionDetails = new Dictionary<string, object>
            {
                { "Account", position.Account },
                { "Instrument", position.Instrument.FullName },
                { "MarketPosition", marketPosition },
                { "Quantity", quantity },
                { "AveragePrice", averagePrice },
                { "Time", Time[0] },
                { "UnrealizedPnL", position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]) },
                { "ATM_ID", atmStrategyId },
                { "Order_ID", orderId }
            };

            // Log position update with detailed information
            LogContext(LogLevel.Info, LogCategory.TRADE, "Position Update: " +
                string.Join(", ", positionDetails.Select(kvp => $"{kvp.Key}={kvp.Value}")));

            // Update cached metrics which depend on the current position (Unrealized P/L)
            UpdateCachedMetrics();

            // Force chart redraw when position changes to update panel (e.g., Unrealized PnL, Position line)
            // Check if ChartControl is available before forcing refresh
            if (ChartControl != null) ForceRefresh();

            // If position becomes flat unexpectedly (e.g., manual close, external event), clear our ATM IDs
            if (marketPosition == MarketPosition.Flat && !string.IsNullOrEmpty(atmStrategyId))
            {
                LogContext(LogLevel.Warning, LogCategory.ATM,
                    $"Position became Flat. Clearing active ATM ID {atmStrategyId} as a precaution.");

                // Log trade completion if this was an active trade
                if (LogTradeDetailsSetting)
                {
                    // Try to find the trade in SystemPerformance to get P/L
                    double tradePnL = 0;
                    if (SystemPerformance != null && SystemPerformance.AllTrades.Count > 0)
                    {
                        // Get the most recent trade
                        Trade lastTrade = SystemPerformance.AllTrades.LastOrDefault();
                        if (lastTrade != null)
                        {
                            tradePnL = lastTrade.ProfitCurrency;

                            // Log detailed trade information
                            Dictionary<string, object> tradeDetails = new Dictionary<string, object>
                            {
                                { "Entry", lastTrade.Entry.Price },
                                { "Exit", lastTrade.Exit.Price },
                                { "EntryTime", lastTrade.Entry.Time },
                                { "ExitTime", lastTrade.Exit.Time },
                                { "Direction", lastTrade.Entry.MarketPosition },
                                { "Quantity", lastTrade.Quantity },
                                { "PnL", tradePnL },
                                { "MAE", lastTrade.MaePercent },
                                { "MFE", lastTrade.MfePercent }
                            };

                            LogContext(LogLevel.Info, LogCategory.TRADE, "Trade Completed: " +
                                string.Join(", ", tradeDetails.Select(kvp => $"{kvp.Key}={kvp.Value}")));
                        }
                    }

                    // Risk management tracking removed as requested
                    if (tradePnL < 0)
                    {
                        LogContext(LogLevel.Warning, LogCategory.RISK, "Trade resulted in loss");
                    }
                    else if (tradePnL > 0)
                    {
                        LogContext(LogLevel.Info, LogCategory.RISK, "Trade resulted in profit");
                    }
                }

                // Clear ATM and order IDs
                atmStrategyId = string.Empty;
                orderId = string.Empty; // Also clear pending entry order ID
            }
        }

        #endregion

        #region OnRender (Metrics Panel Drawing)
        // Correct Access Modifier: protected
        protected override void OnRender(ChartControl chartControl, ChartScale chartScale)
        {
            // Always call the base implementation first
            base.OnRender(chartControl, chartScale);

            // Render market structure zones if enabled
            if (EnableMarketStructureZones)
            {
                RenderMarketStructureZones(chartControl, chartScale);
            }

             // Ensure drawing is enabled and basic requirements met for the metrics panel
            if (!ShowPanel || ChartBars == null || ChartBars.Count == 0 || CurrentBar < BarsRequiredToTrade || State < State.Historical)
                return;

            // Update cached metrics before rendering to ensure latest data
            UpdateCachedMetrics();

            // --- Ensure RenderTarget and Drawing Resources are Ready ---
            // RenderTarget can become null (e.g., chart closed, tab changed)
            // Brushes depend on RenderTarget, so initialize/reinitialize them here.
            if (RenderTarget == null || dwFactory == null || terminalTextFormat == null)
            {
                 // Attempt to re-acquire Factory and TextFormat if lost (though usually stable once created)
                 if (dwFactory == null) dwFactory = new SharpDX.DirectWrite.Factory();
                 if (terminalTextFormat == null && dwFactory != null)
                 {
                    terminalTextFormat = new SharpDX.DirectWrite.TextFormat(dwFactory, "Consolas", SharpDX.DirectWrite.FontWeight.Normal, SharpDX.DirectWrite.FontStyle.Normal, SharpDX.DirectWrite.FontStretch.Normal, PanelFontSize)
                        { TextAlignment = SharpDX.DirectWrite.TextAlignment.Leading, ParagraphAlignment = SharpDX.DirectWrite.ParagraphAlignment.Near };
                 }
                 // If still missing essential resources, exit
                 if (RenderTarget == null || dwFactory == null || terminalTextFormat == null) return;
            }

            // Initialize/Reinitialize Brushes if they are null (first run or after RenderTarget recreated)
            // This ensures brushes are always created with the *current* valid RenderTarget.
             if (panelBackgroundBrush == null) // Check one brush, assume all need init if one is null
             {
                InitializeDrawingBrushes();
                 // If brush initialization failed (e.g., RenderTarget issue), exit
                 if (panelBackgroundBrush == null) return;
             }


            // --- Declare panel dimension/position variables ---
            float panelWidth    = 300; // Adjust as needed
            float panelHeight   = 200; // Adjusted slightly for more lines
            float padding       = 10;
            float lineSpacing   = 3; // Extra space between lines
            float panelX = ChartPanel.X + padding;
            float panelY = ChartPanel.Y + ChartPanel.H - panelHeight - padding; // Bottom Left

            try // Keep the try block for drawing operations
            {
                // --- Draw Panel Background and Border ---
                var panelRect = new SharpDX.RectangleF(panelX, panelY, panelWidth, panelHeight);
                RenderTarget.FillRectangle(panelRect, panelBackgroundBrush);
                RenderTarget.DrawRectangle(panelRect, panelBorderBrush, 1.0f); // 1 pixel border

                // --- Build Panel Text ---
                StringBuilder panelText = new StringBuilder();

                 // Strategy Info
                panelText.AppendLine($"=== {Name} ({Instrument.FullName}) ===");
                panelText.AppendLine($"State: {State} | Time: {Time[0]:HH:mm:ss}"); // Current bar time

                 // Position Info (using Strategy's Position property)
                 string positionStr = "FLAT";
                 SharpDX.Direct2D1.SolidColorBrush posBrushDx = textBrushWhite; // Default to white
                 if (Position.MarketPosition == MarketPosition.Long)
                 {
                     positionStr = $"LONG {Position.Quantity} @ {Position.AveragePrice:F2}"; // Adjust F2 based on instrument TickSize later if needed
                     posBrushDx = textBrushProfit; // Use Profit DX Brush
                 }
                 else if (Position.MarketPosition == MarketPosition.Short)
                 {
                     positionStr = $"SHORT {Position.Quantity} @ {Position.AveragePrice:F2}";
                     posBrushDx = textBrushLoss; // Use Loss DX Brush
                 }
                 panelText.AppendLine($"Position: {positionStr}"); // Will color this line later

                // Performance Info (Using cached metrics)
                panelText.AppendLine("--- Performance (All Trades) ---");
                 string totalPnlStr = Core.Globals.FormatCurrency(cachedMetrics.TotalNetPnl);
                 string unrealizedPnlStr = Core.Globals.FormatCurrency(cachedMetrics.UnrealizedPnl);
                 panelText.AppendLine($"Total P/L: {totalPnlStr}");
                 panelText.AppendLine($"Unrealized: {unrealizedPnlStr}");
                 panelText.AppendLine($"Trades: {cachedMetrics.TotalTrades} | Win Rate: {cachedMetrics.WinRate:F1}%");
                 string pfStr = "N/A";
                 if (cachedMetrics.TotalTrades > 0)
                 {
                    pfStr = double.IsNaN(cachedMetrics.ProfitFactor) || double.IsInfinity(cachedMetrics.ProfitFactor)
                            ? "INF"
                            : cachedMetrics.ProfitFactor.ToString("F2");
                 }
                 panelText.AppendLine($"Profit Factor: {pfStr}");
                 // Use Core.Globals.FormatCurrency for Max Drawdown
                 panelText.AppendLine($"Max Drawdown: {Core.Globals.FormatCurrency(cachedMetrics.MaxDrawdown)}"); // MaxDrawdown is usually positive

                 // Indicator Info
                panelText.AppendLine("--- Indicators & Signal Quality ---");
                 string rsiStr = CurrentBar > 0 && rsi != null ? $"{rsi[0]:F2}" : "Calc...";
                 string trendStr = currentTrend.ToString();
                 string adxStr = "Removed"; // ADX removed as requested
                 string qualityStr = CurrentBar > 0 && signalQuality != null ? $"{signalQuality[0]:F1}" : "Calc...";

                 panelText.AppendLine($"this.RSI({RSIPeriod},{RSISmooth}): {rsiStr}");
                 panelText.AppendLine($"Trend (MA {FastMAPeriod}/{SlowMAPeriod}): {trendStr}");
                 panelText.AppendLine($"Signal Quality: {qualityStr}");

                 // Time-based performance
                 if (hourlyPerformance.Count > 0)
                 {
                     int currentHour = Time[0].Hour;
                     if (hourlyPerformance.ContainsKey(currentHour))
                     {
                         var stats = hourlyPerformance[currentHour];
                         if (stats.TotalTrades > 0)
                         {
                             panelText.AppendLine($"Hour {currentHour}: {stats.WinRate:F1}% win ({stats.TotalTrades} trades)");
                         }
                     }
                 }

                 // ATM Info
                string atmStatus = "Inactive";
                 if(!string.IsNullOrEmpty(atmStrategyId))
                 {
                    // Get current ATM position for display
                    MarketPosition atmPos = GetAtmStrategyMarketPosition(atmStrategyId);
                    int atmQty = GetAtmStrategyPositionQuantity(atmStrategyId);
                    double atmAvgPrice = GetAtmStrategyPositionAveragePrice(atmStrategyId);
                    string atmPosStr = atmPos == MarketPosition.Flat ? "FLAT" : $"{atmPos} {atmQty} @ {atmAvgPrice:F2}";
                    atmStatus = $"Active ({atmStrategyId.Split('_').LastOrDefault()}) Pos: {atmPosStr}"; // Show partial ID for brevity
                 }
                 else if (!string.IsNullOrEmpty(orderId))
                 {
                     string[] status = GetAtmStrategyEntryOrderStatus(orderId);
                     string entryStatus = status.GetLength(0) > 0 ? status[2] : "Unknown"; // Get status if available
                     atmStatus = $"Pending Entry ({orderId.Split('_').LastOrDefault()}) St: {entryStatus}";
                 }
                 panelText.AppendLine($"ATM Status: {atmStatus}");

                // --- Draw Text with Color Coding ---
                float currentY = panelY + padding / 2; // Start drawing text slightly padded
                float textX = panelX + padding / 2;
                float textWidth = panelWidth - padding; // Max width for text layout

                // Use DrawText line by line for easier color control
                string[] lines = panelText.ToString().Split(new[] { Environment.NewLine }, StringSplitOptions.None);

                 foreach (string line in lines)
                {
                    if (string.IsNullOrEmpty(line)) continue; // Skip empty lines

                    SharpDX.Direct2D1.SolidColorBrush currentBrush = textBrushDefault; // Default terminal color

                    // Apply color coding based on content (Heat Map using SharpDX Brushes)
                    if (line.StartsWith("Total P/L:"))
                        currentBrush = GetPnlBrush(cachedMetrics.TotalNetPnl);
                    else if (line.StartsWith("Unrealized:"))
                         currentBrush = GetPnlBrush(cachedMetrics.UnrealizedPnl);
                    else if (line.StartsWith("Win Rate:"))
                         currentBrush = cachedMetrics.WinRate >= 50 ? textBrushProfit : (cachedMetrics.TotalTrades > 0 && cachedMetrics.WinRate >= 40 ? textBrushYellow : (cachedMetrics.TotalTrades > 0 ? textBrushLoss : textBrushWhite));
                    else if (line.StartsWith("Profit Factor:"))
                         currentBrush = cachedMetrics.ProfitFactor >= 1.5 ? textBrushProfit : (cachedMetrics.ProfitFactor >= 1.0 ? textBrushYellow : (cachedMetrics.TotalTrades > 0 ? textBrushLoss : textBrushWhite));
                     else if (line.StartsWith("Position: LONG"))
                        currentBrush = textBrushProfit; // Use Profit Color Brush
                     else if (line.StartsWith("Position: SHORT"))
                         currentBrush = textBrushLoss; // Use Loss Color Brush
                     else if (line.Contains($"this.RSI({RSIPeriod},{RSISmooth}):"))
                         currentBrush = (CurrentBar > 0 && rsi != null) ? GetRsiBrush(rsi[0]) : textBrushWhite; // Color RSI value itself
                    else if (line.Contains("Trend: Up"))
                        currentBrush = textBrushProfit;
                    else if (line.Contains("Trend: Down"))
                        currentBrush = textBrushLoss;
                    else if (line.StartsWith("ATM Status: Pending"))
                        currentBrush = textBrushYellow;
                    else if (line.StartsWith("ATM Status: Active"))
                         currentBrush = textBrushWhite; // Could refine based on ATM PnL if accessible easily


                    // Ensure brush is not null before drawing (safety check)
                    if(currentBrush != null)
                    {
                         // Use a slightly larger layout rectangle height to prevent clipping descenders (like 'g', 'y')
                         RenderTarget.DrawText(line, terminalTextFormat, new SharpDX.RectangleF(textX, currentY, textWidth, PanelFontSize * 2.0f), currentBrush, DrawTextOptions.None);
                         currentY += PanelFontSize + lineSpacing; // Move Y down for the next line
                    }

                    // Optional: Check if text exceeds panel height and break if needed
                    if (currentY > panelY + panelHeight - padding) break;
                }
            }
            catch (Exception ex)
            {
                // Log error to NT output window - important for diagnosing render issues
                Print($"Error in OnRender: {ex.Message}\n{ex.StackTrace}");
                // Draw an error message on the panel itself if possible
                var errorRect = new SharpDX.RectangleF(panelX + 2, panelY + 2, panelWidth - 4, panelHeight - 4);
                // Ensure loss brush and format are initialized before drawing error text
                if(textBrushLoss != null && terminalTextFormat != null)
                    RenderTarget.DrawText("PANEL RENDER ERROR - See Output", terminalTextFormat, errorRect, textBrushLoss);

                 // Dispose potentially corrupted brushes so they are recreated next time
                 DisposeDrawingBrushes();
            }
        }
        #endregion

    } // End of Class MAKER_V1
} // End of Namespace