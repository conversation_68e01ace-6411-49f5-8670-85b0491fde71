﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <NinjaScriptEditor-f053885317c14245b179c99969eaa874>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.NinjaScript.Editor.EditorView</Class>
      <WindowState>Minimized</WindowState>
      <Location>130;130</Location>
      <Size>892;877</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <ExpandedTreeItems>
        <ArrayOfString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <string>Strategies</string>
          <string>Strategies.MARKET MAKER</string>
        </ArrayOfString>
      </ExpandedTreeItems>
      <SelectedTreeItems>
        <ArrayOfString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <string>Strategies.MARKET MAKER.MAKER</string>
        </ArrayOfString>
      </SelectedTreeItems>
      <ExplorerPinned>
        <boolean>true</boolean>
      </ExplorerPinned>
      <ExplorerPaneWidth>
        <double>195</double>
      </ExplorerPaneWidth>
      <ContentGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ContentGridHeight>
      <SeparatorGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </SeparatorGridHeight>
      <ErrorsGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ErrorsGridHeight>
      <ErrorsGridActualHeight>
        <double>-1.7976931348623157E+308</double>
      </ErrorsGridActualHeight>
      <Properties>
        <EditorProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <AlwaysOnTop>false</AlwaysOnTop>
          <AutoHideExplorer>false</AutoHideExplorer>
          <DebugMode>false</DebugMode>
          <InlineSyntaxChecking>true</InlineSyntaxChecking>
          <IsAutoBracketCompletionEnabled>false</IsAutoBracketCompletionEnabled>
          <ShowIndentationLines>false</ShowIndentationLines>
          <Font>
            <Bold>false</Bold>
            <FamilySerialize>Consolas</FamilySerialize>
            <Italic>false</Italic>
            <Size>13</Size>
          </Font>
          <ShouldShowWarnings>false</ShouldShowWarnings>
        </EditorProperties>
      </Properties>
      <OpenFiles>
        <ArrayOfScriptTabStateSerialize xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <ScriptTabStateSerialize>
            <Line>29</Line>
            <Character>57</Character>
            <FileName>C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\Strategies\MARKET MAKER\ALGOBASE 1.cs</FileName>
            <IsSelectedTab>false</IsSelectedTab>
          </ScriptTabStateSerialize>
          <ScriptTabStateSerialize>
            <Line>29</Line>
            <Character>57</Character>
            <FileName>C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\Strategies\MARKET MAKER\ALGOBASE 2.cs</FileName>
            <IsSelectedTab>true</IsSelectedTab>
          </ScriptTabStateSerialize>
          <ScriptTabStateSerialize>
            <Line>27</Line>
            <Character>52</Character>
            <FileName>C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\Strategies\MARKET MAKER\MAKER.cs</FileName>
            <IsSelectedTab>false</IsSelectedTab>
          </ScriptTabStateSerialize>
        </ArrayOfScriptTabStateSerialize>
      </OpenFiles>
    </NinjaScriptEditor-f053885317c14245b179c99969eaa874>
  </NTWindows>
</NinjaTrader>