﻿<?xml version="1.0" encoding="utf-8"?>
<StrategyTemplate>
  <StrategyType>NinjaTrader.NinjaScript.Strategies.ORB_Bot</StrategyType>
  <OptimizerType>NinjaTrader.NinjaScript.Optimizers.DefaultOptimizer</OptimizerType>
  <OptimizerParameters>
    <ArrayOfParameterWrapper xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <ParameterWrapper>
        <DisplayName>IsStrategyGenerator</DisplayName>
        <Name>IsStrategyGenerator</Name>
        <Value xsi:type="xsd:boolean">false</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Keep best # results</DisplayName>
        <Name>KeepBestResults</Name>
        <Value xsi:type="xsd:int">10</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>LogTypeName</DisplayName>
        <Name>LogTypeName</Name>
        <Value xsi:type="xsd:string">Optimizer</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Visible</DisplayName>
        <Name>IsVisible</Name>
        <Value xsi:type="xsd:boolean">true</Value>
      </ParameterWrapper>
    </ArrayOfParameterWrapper>
  </OptimizerParameters>
  <OptimizationFitness>NinjaTrader.NinjaScript.OptimizationFitnesses.MaxProfitFactor</OptimizationFitness>
  <OptimizationParameters>
    <ArrayOfParameter xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Backtest</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>PosSize1</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">2</Max>
        <Min xsi:type="xsd:int">2</Min>
        <Name>PosSize2</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>2</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">3</Max>
        <Min xsi:type="xsd:int">3</Min>
        <Name>PosSize3</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>3</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>TradeWindow1IsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Both</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>MyTradeDirection</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.Strategies.ORB_Bot+Trade, ORBBOT, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Both</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>EntryOrderTickOffset</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">20</Max>
        <Min xsi:type="xsd:double">20</Min>
        <Name>StopLossTicks</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>20</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">30</Max>
        <Min xsi:type="xsd:double">30</Min>
        <Name>ProfitTargetTicks1</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>30</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">60</Max>
        <Min xsi:type="xsd:double">60</Min>
        <Name>ProfitTargetTicks2</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>60</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">90</Max>
        <Min xsi:type="xsd:double">90</Min>
        <Name>ProfitTargetTicks3</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>90</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>BreakEvenIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">0</Max>
        <Min xsi:type="xsd:double">0</Min>
        <Name>BreakEvenOffset</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">30</Max>
        <Min xsi:type="xsd:double">30</Min>
        <Name>BreakEvenAfterTicks</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>30</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">true</Max>
        <Min xsi:type="xsd:boolean">true</Min>
        <Name>TrailIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>True</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">30</Max>
        <Min xsi:type="xsd:int">30</Min>
        <Name>TrailByTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>30</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">60</Max>
        <Min xsi:type="xsd:int">60</Min>
        <Name>StartTrailAfterTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>60</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">10</Max>
        <Min xsi:type="xsd:int">10</Min>
        <Name>TrailFrequency</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>10</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Martingale</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">2</Max>
        <Min xsi:type="xsd:int">2</Min>
        <Name>MartingaleMultiplier</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>2</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">5</Max>
        <Min xsi:type="xsd:int">5</Min>
        <Name>MaxMartingales</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>5</ValueSerializable>
      </Parameter>
    </ArrayOfParameter>
  </OptimizationParameters>
  <Strategy>
    <ORB_Bot xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2025-02-25T12:46:47</From>
      <Panel>-1</Panel>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>true</ShowTransparentPlotsInDataBox>
      <To>2025-03-03T12:46:47</To>
      <Calculate>OnBarClose</Calculate>
      <Displacement>0</Displacement>
      <IsAutoScale>true</IsAutoScale>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>ORB Bot - Vincere Trading V1.4</Name>
      <Plots>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDA70D6&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Dash</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>3</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Range High</Name>
          <PlotStyle>Hash</PlotStyle>
        </Plot>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDA70D6&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Dash</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>3</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Range Low</Name>
          <PlotStyle>Hash</PlotStyle>
        </Plot>
      </Plots>
      <SelectedValueSeries>0</SelectedValueSeries>
      <BarsPeriodParameter>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name />
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </BarsPeriodParameter>
      <BarsRequiredToTrade>0</BarsRequiredToTrade>
      <Category>NinjaScript</Category>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <DaysToLoad>5</DaysToLoad>
      <DefaultQuantity>1</DefaultQuantity>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <EntriesPerDirection>1</EntriesPerDirection>
      <EntryHandling>AllEntries</EntryHandling>
      <ExitOnSessionCloseSeconds>30</ExitOnSessionCloseSeconds>
      <IncludeCommission>false</IncludeCommission>
      <InstrumentOrInstrumentList>ES JUN25</InstrumentOrInstrumentList>
      <IsAggregated>false</IsAggregated>
      <IsExitOnSessionCloseStrategy>false</IsExitOnSessionCloseStrategy>
      <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
      <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <IsWaitUntilFlat>false</IsWaitUntilFlat>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <OptimizationPeriod>10</OptimizationPeriod>
      <OrderFillResolution>Standard</OrderFillResolution>
      <OrderFillResolutionType>Tick</OrderFillResolutionType>
      <OrderFillResolutionValue>1</OrderFillResolutionValue>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
      <SetOrderQuantity>Strategy</SetOrderQuantity>
      <Slippage>0</Slippage>
      <StartBehavior>WaitUntilFlat</StartBehavior>
      <StopTargetHandling>PerEntryExecution</StopTargetHandling>
      <SupportsOptimizationGraph>true</SupportsOptimizationGraph>
      <TestPeriod>28</TestPeriod>
      <TradingHoursSerializable />
      <Gtd>1800-01-01T00:00:00</Gtd>
      <Template />
      <TimeInForce>Gtc</TimeInForce>
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ZOrder>-**********</ZOrder>
      <LicenseKey>V-7D8D48-14995D48-C0A85EW</LicenseKey>
      <Backtest>false</Backtest>
      <PosSize1>5</PosSize1>
      <PosSize2>2</PosSize2>
      <PosSize3>1</PosSize3>
      <TradeWindow1IsOn>true</TradeWindow1IsOn>
      <TradeStart1>2020-01-01T06:35:00</TradeStart1>
      <TradeEnd1>2020-01-01T23:59:00</TradeEnd1>
      <RangeStart>2020-01-01T05:25:00</RangeStart>
      <RangeEnd>2020-01-01T06:35:00</RangeEnd>
      <RangeIsOvernight>false</RangeIsOvernight>
      <MyTradeDirection>Both</MyTradeDirection>
      <EntryOrderTickOffset>1</EntryOrderTickOffset>
      <StopLossTicks>150</StopLossTicks>
      <ProfitTargetTicks1>50</ProfitTargetTicks1>
      <ProfitTargetTicks2>80</ProfitTargetTicks2>
      <ProfitTargetTicks3>800</ProfitTargetTicks3>
      <BreakEvenIsOn>true</BreakEvenIsOn>
      <BreakEvenOffset>1</BreakEvenOffset>
      <BreakEvenAfterTicks>50</BreakEvenAfterTicks>
      <TrailIsOn>true</TrailIsOn>
      <TrailByTicks>50</TrailByTicks>
      <StartTrailAfterTicks>50</StartTrailAfterTicks>
      <TrailFrequency>1</TrailFrequency>
      <Martingale>false</Martingale>
      <MartingaleMultiplier>2</MartingaleMultiplier>
      <MaxMartingales>5</MaxMartingales>
    </ORB_Bot>
  </Strategy>
</StrategyTemplate>