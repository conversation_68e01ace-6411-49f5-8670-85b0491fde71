******************* Session Start (Version *******) *******************
2025-06-08 02:38:44:027 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-06-08 02:38:44:031 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-06-08 02:38:44:309 Core.Instrumentation.ActivitySource: enabled=True randomPercent=7.3212 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-06-08 02:38:44:328 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-06-08 02:38:45:192 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-06-08 02:38:45:197 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4801.0055336' renewSecs='2400.5027668'
2025-06-08 02:38:45:981 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-06-08 02:38:46:631 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-06-08 02:38:47:353 PrimaryMonitorWPFDPIScale=1.00
2025-06-08 02:38:47:696 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-06-08 02:38:47:853 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-06-08 02:38:47:853 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-06-08 02:38:47:853 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-06-08 02:38:47:854 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-06-08 02:38:47:854 OSLanguage='en-US'
2025-06-08 02:38:47:854 OSEnvironment='64bit'
2025-06-08 02:38:47:854 Processors=8
2025-06-08 02:38:47:854 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-06-08 02:38:48:904 ProcessorSpeed=2.4 GHz
2025-06-08 02:38:48:904 PhysicalMemory=8192 MB
2025-06-08 02:38:49:011 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-06-08 02:38:49:011 Monitors=2/1280x720|1920x1080
2025-06-08 02:38:49:011 .NET/CLR Version='4.8'/64bit
2025-06-08 02:38:49:077 SQLiteVersion='1.0.116.0'
2025-06-08 02:38:49:126 ApplicationTimezone=EST +0 hour(s)
2025-06-08 02:38:49:126 ApplicationTimezone=UTC -4 hour(s)
2025-06-08 02:38:49:126 LocalTimezone=EST +0 hour(s)
2025-06-08 02:38:49:126 LocalTimezone=UTC -4 hour(s)
2025-06-08 02:38:50:104 DirectXRenderingHW
2025-06-08 02:38:50:104 Copying custom assemblies...
2025-06-08 02:38:52:444 Loading custom assemblies...
2025-06-08 02:38:52:444 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-06-08 02:38:54:236 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-06-08 02:38:54:327 Deleting temporary files...
2025-06-08 02:38:55:230 Copying db and restoring templates...
2025-06-08 02:38:55:302 Loading third party assemblies...
2025-06-08 02:38:55:375 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-06-08 02:38:55:375 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-06-08 02:38:55:375 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-06-08 02:38:55:375 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-06-08 02:38:55:375 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-06-08 02:38:55:375 Initializing database...
2025-06-08 02:38:55:375 Loading master instruments...
2025-06-08 02:38:55:872 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-06-08 02:38:55:876 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-06-08 02:38:56:514 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-06-08 02:38:56:600 Loading instruments...
2025-06-08 02:38:56:898 Loading accounts...
2025-06-08 02:38:57:488 Loading users...
2025-06-08 02:38:57:580 Downloading server info...
2025-06-08 02:38:57:580 Starting instrument management...
2025-06-08 02:38:57:589 Starting timer...
2025-06-08 02:38:57:589 Creating file type watcher...
2025-06-08 02:38:57:590 Setting ATI...
2025-06-08 02:38:57:633 Connecting ATI server...
2025-06-08 02:38:57:652 Server.AtiServer.Connect0
2025-06-08 02:38:57:652 Starting adapter server...
2025-06-08 02:38:57:715 Server.AtiServer.Connect1: Port='36973'
2025-06-08 02:38:57:715 Starting bars dictionary...
2025-06-08 02:38:57:718 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-06-08 02:38:57:732 Starting recorder...
2025-06-08 02:38:57:739 Starting server(s)...
2025-06-08 02:38:57:763 Server.AtiServer.Connect2
2025-06-08 02:38:57:881 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3133
2025-06-08 02:38:57:881 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-06-08 02:38:57:881 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9783
2025-06-08 02:38:57:881 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=12115
2025-06-08 02:38:57:881 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5061
2025-06-08 02:38:57:979 Required resource key 'brushOrderWorking' is missing.
2025-06-08 02:38:57:979 Required resource key 'brushOrderAccepted' is missing.
2025-06-08 02:38:57:979 Required resource key 'brushOrderPartFilled' is missing.
2025-06-08 02:38:57:979 Required resource key 'brushOrderInitialized' is missing.
2025-06-08 02:38:58:669 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarTick='' SnapModeBarObject='' SnapModeDisabled='' SnapModeTick='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-06-08 02:38:58:680 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Replace='Ctrl+H' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-06-08 02:38:58:681 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-08 02:38:58:682 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-06-08 02:38:58:686 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='' SimulatedOrder=''
2025-06-08 02:38:58:687 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-08 02:38:58:687 OrderEntryHotKeys=disabled
2025-06-08 02:38:58:689 AutoClose=disabled
2025-06-08 02:39:01:182 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-06-08 02:39:03:222 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=1 RolloverCollection=260 TradingHours=0
2025-06-08 02:39:03:252 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:39:03:608 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:39:04:129 ERROR: Failed to restore Indicator 'NinjaTrader.NinjaScript.Indicators.TradeSaber.MultiSeriesHL'. Most likely (a) the implementation changed or (b) one or more properties have been renamed or removed or (c) the custom assembly which implements this Indicator no longer is there.
2025-06-08 02:39:04:291 ERROR: Failed to restore Indicator 'NinjaTrader.NinjaScript.Indicators.Prop_Trader_Tools.PropTraderAccountTool'. Most likely (a) the implementation changed or (b) one or more properties have been renamed or removed or (c) the custom assembly which implements this Indicator no longer is there.
2025-06-08 02:39:04:396 ERROR: Unable to restore strategy 'NinjaTrader.NinjaScript.Strategies.SampleAtmCandleStrategy' with ID '*********'. Most likely this strategy no longer is supported by the custom assemblies.
2025-06-08 02:39:04:397 ERROR: Unable to restore strategy 'NinjaTrader.NinjaScript.Strategies.MARKET_MAKER.MAKER' with ID '*********'. Most likely this strategy no longer is supported by the custom assemblies.
2025-06-08 02:39:05:330 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.07ms InstrumentLists=0.72ms MasterInstruments=0.06ms Messages=0.38ms Risks=8.02ms RolloverCollection=2098.00ms TradingHours=0.07ms
2025-06-08 02:39:05:331 Starting server message polling timer with interval 3600 seconds...
2025-06-08 02:39:06:274 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-06-08 02:39:06:504 1 Chart Chart1Tab1 1 Ser 5 Ind 0 DrawObj 
2025-06-08 02:39:26:338 (Playback) Gui.ControlCenter.OnConnect
2025-06-08 02:39:32:665 (Playback) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-06-08 runAsProcess=False
2025-06-08 02:39:32:825 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-08 02:39:32:828 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-08 02:39:32:833 (Playback) Cbi.Connection.Connect1
2025-06-08 02:39:32:870 (Playback) Cbi.Connection.Connect2
2025-06-08 02:39:32:870 (Playback) Cbi.Connection.Connect3
2025-06-08 02:39:32:877 (Playback) Cbi.Connection.CreateAccount: account='Playback101' displayName='Playback101' fcm='' denomination=UsDollar forexLotSize=10000
2025-06-08 02:39:32:986 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-06-08 02:39:33:055 (Playback) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Playback101'
2025-06-08 02:39:33:076 (Playback) Cbi.Connection.Connect4
2025-06-08 02:39:33:077 (Playback) Cbi.Connection.Connect5
2025-06-08 02:39:33:078 (Playback) Adapter.PlaybackAdapter.Connect
2025-06-08 02:39:33:264 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-08 02:39:33:266 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connecting priceStatus=Connecting
2025-06-08 02:39:33:862 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-06-08 02:39:33:862 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-06-08 02:39:35:375 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-08 02:39:35:378 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connected previousStatus=Connecting message=''
2025-06-08 02:39:35:379 (Playback) Core.Connection.Statistics: connectAttempts=1/2302.0ms
2025-06-08 02:39:35:380 (Playback) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-06-08 02:39:35:380 Server.HdsClient.Connect: type=HDS server='hds-us-nt-008.ninjatrader.com' port=31655 system='' useSsl=True
2025-06-08 02:39:35:474 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-08 02:39:35:474 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-08 02:39:35:474 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connected priceStatus=Connected
2025-06-08 02:39:35:478 (Playback) Cbi.Connection.Connect9 ok
2025-06-08 02:39:35:523 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-06-08 02:39:35:524 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-06-08 02:39:35:708 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-08 02:39:35:767 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-08 02:39:35:770 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-08 02:39:35:771 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-08 02:42:49:208 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:42:49:366 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:42:57:121 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-08 02:42:57:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-08 02:42:57:140 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-08 02:43:03:536 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-08 02:43:03:537 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-08 02:43:03:540 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-08 02:43:03:541 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-08 02:44:09:186 -- SetDefaults --
2025-06-08 02:44:38:492 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:44:38:617 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-08 02:44:50:137 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-08 02:44:50:139 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-08 02:44:50:140 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-08 02:44:50:140 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-08 02:44:50:140 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-08 02:44:59:621 Shutting down NinjaTrader
2025-06-08 02:44:59:624 Disconnecting 'Playback'
2025-06-08 02:44:59:636 (Playback) Cbi.Connection.Disconnect
2025-06-08 02:44:59:636 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-06-08 02:44:59:638 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-06-08 02:44:59:645 (Playback) Cbi.Connection.ConnectionStatusCallback.Close1
2025-06-08 02:44:59:663 (Playback) Cbi.Connection.ConnectionStatusCallback.Close3
2025-06-08 02:44:59:664 (Playback) Cbi.Connection.ConnectionStatusCallback.Close4
2025-06-08 02:44:59:664 (Playback) Cbi.Connection.ConnectionStatusCallback.Close5
2025-06-08 02:44:59:664 (Playback) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-06-08 02:44:59:667 (Playback) Cbi.Connection.ConnectionStatusCallback.Close7
2025-06-08 02:44:59:670 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-06-08 02:44:59:671 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-06-08 02:44:59:674 Flushing DB thread
2025-06-08 02:44:59:677 (Playback) Cbi.Connection.ConnectionStatusCallback.Close8
2025-06-08 02:44:59:678 Shutting down instrument management
2025-06-08 02:44:59:680 Shutting down instrument threads
2025-06-08 02:44:59:692 Shutting down recorder
2025-06-08 02:44:59:706 Shutting down file type watcher
2025-06-08 02:44:59:706 Shutting down ATI server
2025-06-08 02:44:59:714 Shutting down auto trade component
2025-06-08 02:44:59:740 Shutting down SMTP server
2025-06-08 02:44:59:748 Shutting down adapter server
2025-06-08 02:44:59:783 Shutting down server(s)
2025-06-08 02:44:59:841 Shutting down mail thread
2025-06-08 02:44:59:841 Shutting down sound thread
2025-06-08 02:44:59:841 Shutting down timer
2025-06-08 02:44:59:841 Shutting down alerts timer
2025-06-08 02:44:59:841 Shutting down message timer
2025-06-08 02:44:59:841 Shutting down db
2025-06-08 02:44:59:858 Flushing DB thread
2025-06-08 02:45:00:269 Shutting down bars dictionary
2025-06-08 02:45:00:304 Shutting down user entitlement threads
2025-06-08 02:45:00:315 Shutting down logs
2025-06-08 02:45:00:327 Shutting down bars types
2025-06-08 02:45:00:338 Shutting down UI threads
2025-06-08 02:45:00:358 ************************ Session End ************************
