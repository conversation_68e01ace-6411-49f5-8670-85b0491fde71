﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTTabPage>
    <InstrumentLink>0</InstrumentLink>
    <IntervalLink>0</IntervalLink>
    <ChartTraderVisibility>Visible</ChartTraderVisibility>
    <SeriesCount>1</SeriesCount>
    <DataSeries>
      <BarsProperties>
        <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <BarsPeriod>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
          <RangeType>Days</RangeType>
          <BarsBack>50</BarsBack>
          <DaysBack>5</DaysBack>
          <From>2025-05-25T00:00:00</From>
          <IsStableSession>true</IsStableSession>
          <To>2025-03-18T00:00:00</To>
          <TradingHoursSerializable />
          <AutoScale>true</AutoScale>
          <CenterPriceOnScale>false</CenterPriceOnScale>
          <DisplayInDataBox>true</DisplayInDataBox>
          <Label>NQ 06-25</Label>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <Panel>0</Panel>
          <PriceMarker>
            <BackgroundSerialize>DEFAULT</BackgroundSerialize>
            <IsVisible>true</IsVisible>
          </PriceMarker>
          <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
          <ScaleJustification>Right</ScaleJustification>
          <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
          <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
          <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
          <PlotExecutions>TextAndMarker</PlotExecutions>
          <MarkerSize>5</MarkerSize>
          <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
          <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
          <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
          <BarsSeriesId>c6482ccddd2043c18fa4bcf3488637af</BarsSeriesId>
          <Id>7dcd1b1f1a474f4192d86bce4412f4fc</Id>
          <Instrument>NQ JUN25</Instrument>
          <IsLinked>true</IsLinked>
          <IsPrimarySeries>true</IsPrimarySeries>
          <ZOrder>1</ZOrder>
        </BarsProperties>
        <ChartStyles>
          <ChartStyle>
            <CandleStyle>
              <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <BarWidth>1.5243171334768002</BarWidth>
                <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                <DownBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFF8F8FF&lt;/SolidColorBrush&gt;</DownBrushSerialize>
                <UpBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF00FF00&lt;/SolidColorBrush&gt;</UpBrushSerialize>
                <StrokeSerialize>&lt;Pen Brush="#FF000000" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StrokeSerialize>
                <Stroke2Serialize>DEFAULT</Stroke2Serialize>
              </CandleStyle>
            </CandleStyle>
          </ChartStyle>
        </ChartStyles>
      </BarsProperties>
    </DataSeries>
    <Indicators>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1" DisplayName="Price line(100,100,100,False,False,True)">
        <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <calculate2>OnPriceChange</calculate2>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <Calculate>OnPriceChange</Calculate>
          <Displacement>0</Displacement>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-03-13T00:00:00</From>
          <IsAutoScale>true</IsAutoScale>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>Price line</Name>
          <Panel>-1</Panel>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Ask line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Bid line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Last line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-03-18T00:00:00</To>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>1</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10005</ZOrder>
          <ShowAskLine>false</ShowAskLine>
          <ShowBidLine>false</ShowBidLine>
          <ShowLastLine>true</ShowLastLine>
          <AskLineLength>100</AskLineLength>
          <BidLineLength>100</BidLineLength>
          <LastLineLength>100</LastLineLength>
          <AskStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </AskStroke>
          <BidStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </BidStroke>
          <LastStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </LastStroke>
        </PriceLine>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1" DisplayName="Bar timer">
        <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <calculate2>OnEachTick</calculate2>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <Calculate>OnEachTick</Calculate>
          <Displacement>0</Displacement>
          <DisplayInDataBox>false</DisplayInDataBox>
          <From>2025-03-13T00:00:00</From>
          <IsAutoScale>true</IsAutoScale>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>Bar timer</Name>
          <Panel>-1</Panel>
          <Plots />
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-03-18T00:00:00</To>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>false</DrawOnPricePanel>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>4</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10006</ZOrder>
          <TextPositionFine>BottomRight</TextPositionFine>
        </BarTimer>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1" DisplayName="SMA(20)">
        <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <calculate2>OnBarClose</calculate2>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-03-13T00:00:00</From>
          <IsAutoScale>true</IsAutoScale>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>SMA</Name>
          <Panel>-1</Panel>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>SMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-03-18T00:00:00</To>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>7</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10008</ZOrder>
          <Period>20</Period>
        </SMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1" DisplayName="SMA(50)">
        <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <calculate2>OnBarClose</calculate2>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-03-13T00:00:00</From>
          <IsAutoScale>true</IsAutoScale>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>SMA</Name>
          <Panel>-1</Panel>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF1E90FF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>SMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-03-18T00:00:00</To>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>10</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10009</ZOrder>
          <Period>50</Period>
        </SMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BuySellPressure" Panel="1" DisplayName="Buy sell pressure">
        <BuySellPressure xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <calculate2>OnEachTick</calculate2>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <Calculate>OnEachTick</Calculate>
          <Displacement>0</Displacement>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-03-13T00:00:00</From>
          <IsAutoScale>true</IsAutoScale>
          <Lines>
            <Line>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <Name>Upper</Name>
              <Value>75</Value>
            </Line>
            <Line>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <Name>Lower</Name>
              <Value>25</Value>
            </Line>
          </Lines>
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>Buy sell pressure</Name>
          <Panel>1</Panel>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008B8B&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Buy pressure</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Sell pressure</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-03-18T00:00:00</To>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>false</IsOverlay>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>false</DrawOnPricePanel>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>13</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10004</ZOrder>
        </BuySellPressure>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
    </Indicators>
    <CrosshairType>Local</CrosshairType>
    <StayInDrawMode>False</StayInDrawMode>
    <Properties>
      <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AlwaysOnTop>false</AlwaysOnTop>
        <AreTabsVisible>true</AreTabsVisible>
        <AllowSelectionDragging>true</AllowSelectionDragging>
        <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
        <EquidistantBarSpacing>true</EquidistantBarSpacing>
        <LabelFont>
          <Bold>false</Bold>
          <FamilySerialize>#Montserrat</FamilySerialize>
          <Italic>false</Italic>
          <Size>11</Size>
        </LabelFont>
        <BarDistance>5.081057</BarDistance>
        <BarMarginRightUser>8</BarMarginRightUser>
        <ChartTraderVisibility>Visible</ChartTraderVisibility>
        <ShowDateRange>false</ShowDateRange>
        <ShowScrollBar>true</ShowScrollBar>
        <ShowSelectedDrawingMarkers>true</ShowSelectedDrawingMarkers>
        <SnapMode>Bar</SnapMode>
        <TabName>@INSTRUMENT_FULL</TabName>
        <LoadBackgroundImage>false</LoadBackgroundImage>
        <BackgroundImageStretch>Fill</BackgroundImageStretch>
        <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
        <ChartTextSerialize>DEFAULT</ChartTextSerialize>
        <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
        <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
        <SelectedMarkerBrushSerialize>DEFAULT</SelectedMarkerBrushSerialize>
        <AreHGridLinesVisible>true</AreHGridLinesVisible>
        <AreVGridLinesVisible>true</AreVGridLinesVisible>
        <AxisPenSerialize>DEFAULT</AxisPenSerialize>
        <CrosshairPen>DEFAULT</CrosshairPen>
        <CrosshairIsLocked>false</CrosshairIsLocked>
        <CrosshairCrosshairType>Local</CrosshairCrosshairType>
        <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
        <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
        <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
        <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
        <StartTime>2025-01-01T09:00:00</StartTime>
        <EndTime>2025-01-01T17:00:00</EndTime>
        <TimeHighBrushSerialize>DEFAULT</TimeHighBrushSerialize>
        <Opacity>25</Opacity>
        <IsVisible>false</IsVisible>
      </ChartControlProperties>
    </Properties>
    <ChartPanels>
      <ChartPanel>
        <Height>755</Height>
        <HoldChartTraderOrders>false</HoldChartTraderOrders>
        <IsMaximized>false</IsMaximized>
        <Right>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>19783.5</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>19628</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Right>
        <Left>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0.25</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Left>
        <Overlay>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Overlay>
      </ChartPanel>
      <ChartPanel>
        <Height>226</Height>
        <HoldChartTraderOrders>false</HoldChartTraderOrders>
        <IsMaximized>false</IsMaximized>
        <Right>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>51</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>50</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Right>
        <Left>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Left>
        <Overlay>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
            <IsInverted>false</IsInverted>
          </ChartScaleProperties>
        </Overlay>
      </ChartPanel>
    </ChartPanels>
    <ChartAlerts />
  </NTTabPage>
  <ChartTrader>
    <Properties>
      <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
        <AutoScale>true</AutoScale>
        <OrderDisplayBarLength>25</OrderDisplayBarLength>
        <PnLDisplayUnit>Points</PnLDisplayUnit>
        <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
        <ScaleQuantity>0</ScaleQuantity>
        <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
        <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
        <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
        <StopLimitOffsetValue>0</StopLimitOffsetValue>
        <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
        <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
        <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
        <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
        <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
        <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
        <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
        <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
        <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
        <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
      </ChartTraderProperties>
    </Properties>
    <ATM>ATM_IMPROVED</ATM>
    <Instrument>NQ JUN25</Instrument>
    <Quantity>1</Quantity>
    <TIF>Day</TIF>
  </ChartTrader>
</NinjaTrader>