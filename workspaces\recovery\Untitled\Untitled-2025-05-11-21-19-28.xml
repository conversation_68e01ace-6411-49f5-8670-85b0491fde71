﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <NinjaScriptEditor-13129608384b4fa08a829e46c5461675>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.NinjaScript.Editor.EditorView</Class>
      <WindowState>Minimized</WindowState>
      <Location>155;156</Location>
      <Size>892;706</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <ExplorerPinned>
        <boolean>true</boolean>
      </ExplorerPinned>
      <ExplorerPaneWidth>
        <double>195</double>
      </ExplorerPaneWidth>
      <ContentGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ContentGridHeight>
      <SeparatorGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </SeparatorGridHeight>
      <ErrorsGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ErrorsGridHeight>
      <ErrorsGridActualHeight>
        <double>-1.7976931348623157E+308</double>
      </ErrorsGridActualHeight>
      <Properties>
        <EditorProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <AlwaysOnTop>false</AlwaysOnTop>
          <AutoHideExplorer>false</AutoHideExplorer>
          <DebugMode>false</DebugMode>
          <InlineSyntaxChecking>true</InlineSyntaxChecking>
          <IsAutoBracketCompletionEnabled>false</IsAutoBracketCompletionEnabled>
          <ShowIndentationLines>false</ShowIndentationLines>
          <Font>
            <Bold>false</Bold>
            <FamilySerialize>Consolas</FamilySerialize>
            <Italic>false</Italic>
            <Size>13</Size>
          </Font>
          <ShouldShowWarnings>false</ShouldShowWarnings>
        </EditorProperties>
      </Properties>
    </NinjaScriptEditor-13129608384b4fa08a829e46c5461675>
    <NinjaScriptEditor-d5e624d75ecb423eb1a38cd59df740d1>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.NinjaScript.Editor.EditorView</Class>
      <WindowState>Normal</WindowState>
      <Location>208;208</Location>
      <Size>892;804</Size>
      <ZOrder>1</ZOrder>
      <Topmost>False</Topmost>
      <ExplorerPinned>
        <boolean>true</boolean>
      </ExplorerPinned>
      <ExplorerPaneWidth>
        <double>195</double>
      </ExplorerPaneWidth>
      <ContentGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ContentGridHeight>
      <SeparatorGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </SeparatorGridHeight>
      <ErrorsGridHeight>
        <GridLength xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
      </ErrorsGridHeight>
      <ErrorsGridActualHeight>
        <double>-1.7976931348623157E+308</double>
      </ErrorsGridActualHeight>
      <Properties>
        <EditorProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <AlwaysOnTop>false</AlwaysOnTop>
          <AutoHideExplorer>false</AutoHideExplorer>
          <DebugMode>false</DebugMode>
          <InlineSyntaxChecking>true</InlineSyntaxChecking>
          <IsAutoBracketCompletionEnabled>false</IsAutoBracketCompletionEnabled>
          <ShowIndentationLines>false</ShowIndentationLines>
          <Font>
            <Bold>false</Bold>
            <FamilySerialize>Consolas</FamilySerialize>
            <Italic>false</Italic>
            <Size>13</Size>
          </Font>
          <ShouldShowWarnings>false</ShouldShowWarnings>
        </EditorProperties>
      </Properties>
    </NinjaScriptEditor-d5e624d75ecb423eb1a38cd59df740d1>
    <Chart-0c2c782469a94a3dbdaaeb24563f0ca6>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>648;35</Location>
      <Size>948;724</Size>
      <ZOrder>2</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>149</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-4de69b345efc443aa35b608604fa5a45>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>2015</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>50</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>25</Value>
                  <Value2>100</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>2</DaysBack>
                <From>2025-05-06T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2025-04-29T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>MNQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>7109523fc98a41239ccd0866bf8ab205</BarsSeriesId>
                <Id>7109523fc98a41239ccd0866bf8ab205</Id>
                <Instrument>MNQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>2.0900473933649288</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies>
            <Strategy0 BarsIndex="0">357444287</Strategy0>
          </Strategies>
          <Indicators />
          <CrosshairType>Off</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>6.96682453</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Off</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>612</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Fixed</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21166.188190709196</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>20314.811809290804</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-4de69b345efc443aa35b608604fa5a45>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <ATM></ATM>
        <Instrument>MNQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Day</TIF>
      </ChartTrader>
    </Chart-0c2c782469a94a3dbdaaeb24563f0ca6>
  </NTWindows>
</NinjaTrader>