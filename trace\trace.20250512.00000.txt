******************* Session Start (Version *******) *******************
2025-05-12 04:33:22:811 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-05-12 04:33:22:816 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-05-12 04:33:23:091 Core.Instrumentation.ActivitySource: enabled=True randomPercent=87.11378 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-05-12 04:33:23:115 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-05-12 04:33:24:047 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-12 04:33:24:056 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.7040896' renewSecs='2399.8520448'
2025-05-12 04:33:25:166 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-05-12 04:33:25:872 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-05-12 04:33:26:763 PrimaryMonitorWPFDPIScale=1.00
2025-05-12 04:33:27:045 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab (Beta) Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-05-12 04:33:27:179 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-05-12 04:33:27:179 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-05-12 04:33:27:179 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-05-12 04:33:27:180 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-05-12 04:33:27:180 OSLanguage='en-US'
2025-05-12 04:33:27:180 OSEnvironment='64bit'
2025-05-12 04:33:27:180 Processors=8
2025-05-12 04:33:27:180 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-05-12 04:33:28:259 ProcessorSpeed=2.4 GHz
2025-05-12 04:33:28:260 PhysicalMemory=8192 MB
2025-05-12 04:33:28:461 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-05-12 04:33:28:461 Monitors=2/1280x720|1920x1080
2025-05-12 04:33:28:461 .NET/CLR Version='4.8'/64bit
2025-05-12 04:33:28:463 SQLiteVersion='1.0.116.0'
2025-05-12 04:33:28:463 ApplicationTimezone=EST +0 hour(s)
2025-05-12 04:33:28:465 ApplicationTimezone=UTC -4 hour(s)
2025-05-12 04:33:28:465 LocalTimezone=EST +0 hour(s)
2025-05-12 04:33:28:465 LocalTimezone=UTC -4 hour(s)
2025-05-12 04:33:28:534 DirectXRenderingHW
2025-05-12 04:33:28:535 Copying custom assemblies...
2025-05-12 04:33:28:556 Loading custom assemblies...
2025-05-12 04:33:28:556 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-05-12 04:33:28:680 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-05-12 04:33:28:690 Deleting temporary files...
2025-05-12 04:33:28:702 Copying db and restoring templates...
2025-05-12 04:33:28:735 Loading third party assemblies...
2025-05-12 04:33:28:796 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-05-12 04:33:28:796 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-05-12 04:33:28:796 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-05-12 04:33:28:796 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-05-12 04:33:28:797 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-05-12 04:33:28:797 Initializing database...
2025-05-12 04:33:28:797 Loading master instruments...
2025-05-12 04:33:29:015 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-05-12 04:33:29:015 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-05-12 04:33:29:283 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-05-12 04:33:29:380 Loading instruments...
2025-05-12 04:33:29:675 Loading accounts...
2025-05-12 04:33:29:724 Loading users...
2025-05-12 04:33:29:725 Downloading server info...
2025-05-12 04:33:29:725 Starting instrument management...
2025-05-12 04:33:29:731 Starting timer...
2025-05-12 04:33:29:731 Creating file type watcher...
2025-05-12 04:33:29:732 Setting ATI...
2025-05-12 04:33:29:736 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-12 04:33:29:750 Connecting ATI server...
2025-05-12 04:33:29:750 Server.AtiServer.Connect0
2025-05-12 04:33:29:751 Starting adapter server...
2025-05-12 04:33:29:751 Server.AtiServer.Connect1: Port='36973'
2025-05-12 04:33:29:752 Starting bars dictionary...
2025-05-12 04:33:29:752 Starting recorder...
2025-05-12 04:33:29:752 Starting server(s)...
2025-05-12 04:33:29:753 Server.AtiServer.Connect2
2025-05-12 04:33:29:773 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3117
2025-05-12 04:33:29:773 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-05-12 04:33:29:773 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9777
2025-05-12 04:33:29:773 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=11957
2025-05-12 04:33:29:773 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5059
2025-05-12 04:33:29:810 Required resource key 'brushOrderWorking' is missing.
2025-05-12 04:33:29:810 Required resource key 'brushOrderAccepted' is missing.
2025-05-12 04:33:29:810 Required resource key 'brushOrderPartFilled' is missing.
2025-05-12 04:33:29:810 Required resource key 'brushOrderInitialized' is missing.
2025-05-12 04:33:29:819 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarPrice='' SnapModeDisabled='' SnapModePrice='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-05-12 04:33:29:819 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-05-12 04:33:29:819 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-12 04:33:29:819 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-05-12 04:33:29:820 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='Ctrl+Z' SimulatedOrder=''
2025-05-12 04:33:29:820 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-12 04:33:29:820 OrderEntryHotKeys=disabled
2025-05-12 04:33:29:820 AutoClose=disabled
2025-05-12 04:33:30:082 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-05-12 04:33:34:139 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=259 TradingHours=0
2025-05-12 04:33:35:362 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.10ms InstrumentLists=0.62ms MasterInstruments=0.02ms Messages=0.54ms Risks=0.03ms RolloverCollection=1221.54ms TradingHours=0.02ms
2025-05-12 04:33:35:363 Starting server message polling timer with interval 3600 seconds...
2025-05-12 04:47:05:082 (Playback) Gui.ControlCenter.OnConnect
2025-05-12 04:47:07:327 (Playback) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-12 runAsProcess=False
2025-05-12 04:47:07:348 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-12 04:47:07:356 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-12 04:47:07:370 (Playback) Cbi.Connection.Connect1
2025-05-12 04:47:07:381 (Playback) Cbi.Connection.Connect2
2025-05-12 04:47:07:382 (Playback) Cbi.Connection.Connect3
2025-05-12 04:47:07:392 (Playback) Cbi.Connection.CreateAccount: account='Playback101' displayName='Playback101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-12 04:47:07:398 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-12 04:47:07:405 (Playback) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Playback101'
2025-05-12 04:47:07:411 (Playback) Cbi.Connection.Connect4
2025-05-12 04:47:07:412 (Playback) Cbi.Connection.Connect5
2025-05-12 04:47:07:414 (Playback) Adapter.PlaybackAdapter.Connect
2025-05-12 04:47:07:429 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-12 04:47:07:431 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connecting priceStatus=Connecting
2025-05-12 04:47:07:690 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-12 04:47:07:691 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-12 04:47:08:120 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-12 04:47:08:121 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-12 04:47:08:123 (Playback) Core.Connection.Statistics: connectAttempts=1/711.4ms
2025-05-12 04:47:08:123 (Playback) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-12 04:47:08:125 Server.HdsClient.Connect: type=HDS server='hds-us-nt-020.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-12 04:47:08:144 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-12 04:47:08:145 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-12 04:47:08:146 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connected priceStatus=Connected
2025-05-12 04:47:08:146 (Playback) Cbi.Connection.Connect9 ok
2025-05-12 04:47:08:207 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-12 04:47:08:208 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-12 04:47:08:371 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-12 04:47:08:420 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:08:423 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-12 04:47:18:685 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-12 04:47:19:001 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-12 04:47:23:898 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-12 04:47:23:900 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-12 04:47:23:900 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-12 04:47:23:900 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:23:901 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-12 04:47:27:239 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-12 04:47:27:240 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-12 04:48:05:844 ERROR: Insufficient historical data to calculate pivots. Increase chart look back period (DaysToLoad, BarsToLoad, or Start Date)
2025-05-12 04:48:05:852 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-05-12 05:13:23:934 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-12 05:13:24:131 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-12 05:13:24:560 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-12 05:13:24:564 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8279318' renewSecs='2399.9139659'
2025-05-12 05:33:35:458 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-12 05:33:36:794 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-12 05:33:36:970 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.01ms Messages=27.26ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.01ms
2025-05-12 05:44:50:599 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-12 05:44:50:883 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-12 05:52:07:093 Core.Instrumentation.LogActivity: activityType=Application errorCode=Panic errorMessage=''
2025-05-12 05:52:07:186 *************** unhandled exception trapped ***************
2025-05-12 05:52:07:186 Insufficient memory to continue the execution of the program.
2025-05-12 05:52:07:186 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:52:07:189 *************** unhandled exception trapped ***************
2025-05-12 05:52:07:189 Insufficient memory to continue the execution of the program.
2025-05-12 05:52:07:190 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.MediaContext.CompleteRender()
   at System.Windows.Interop.HwndTarget.OnResize()
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:52:07:199 *************** unhandled exception trapped ***************
2025-05-12 05:52:07:199 Insufficient memory to continue the execution of the program.
2025-05-12 05:52:07:199 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:52:07:199 *************** unhandled exception trapped ***************
2025-05-12 05:52:07:199 Insufficient memory to continue the execution of the program.
2025-05-12 05:52:07:199 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:52:07:201 *************** unhandled exception trapped ***************
2025-05-12 05:52:07:202 Insufficient memory to continue the execution of the program.
2025-05-12 05:52:07:202 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:53:24:496 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-12 05:53:24:500 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-12 05:53:29:790 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-12 05:53:29:791 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8239933' renewSecs='2399.91199665'
2025-05-12 05:54:18:774 *************** unhandled exception trapped ***************
2025-05-12 05:54:18:774 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:18:784 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:058 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:058 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:058 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:147 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:147 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:147 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:150 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:150 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:150 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.MediaContext.CompleteRender()
   at System.Windows.Interop.HwndTarget.OnResize()
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:209 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:209 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:209 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.MediaContext.CompleteRender()
   at System.Windows.Interop.HwndTarget.OnResize()
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:215 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:215 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:216 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:216 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:216 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:216 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:216 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:216 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:216 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.Composition.DUCE.Channel.SyncFlush()
   at System.Windows.Interop.HwndTarget.UpdateWindowSettings(Boolean enableRenderTarget, Nullable`1 channelSet)
   at System.Windows.Interop.HwndTarget.UpdateWindowPos(IntPtr lParam)
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-12 05:54:32:217 *************** unhandled exception trapped ***************
2025-05-12 05:54:32:217 Insufficient memory to continue the execution of the program.
2025-05-12 05:54:32:217 System.OutOfMemoryException: Insufficient memory to continue the execution of the program.
   at System.Windows.Media.MediaContext.CompleteRender()
   at System.Windows.Interop.HwndTarget.OnResize()
   at System.Windows.Interop.HwndTarget.HandleMessage(WindowMessage msg, IntPtr wparam, IntPtr lparam)
   at System.Windows.Interop.HwndSource.HwndTargetFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
