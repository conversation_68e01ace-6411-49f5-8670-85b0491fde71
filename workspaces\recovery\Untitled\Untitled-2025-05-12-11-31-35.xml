﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-0d90bae72e9c4392b35a6bd6b71066be>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>1072;-1</Location>
      <Size>849;1035</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>148</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-945082fab4dd4a3da191159806217295>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>15</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-10T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2099-12-01T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>107996ea3e324a0aa32463fae7db430a</BarsSeriesId>
                <Id>b96cf2f05faa492eb5d0560ddffac7c6</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>false</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>3.5697992270713543</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators />
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>11.8993311</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>923</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Fixed</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21115.318308573835</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>20396.931691426165</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <DrawingTools>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30004</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>129360e677d6431187f0e12864528bc5</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>305</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T03:30:00</Time>
                <Price>20931.987036836403</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>305</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T03:30:00</Time>
                <Price>20931.987036836403</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30005</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>1f3fb78c6a234e03aaf15bde25eac9d1</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 2</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>308</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T04:15:00</Time>
                <Price>20778.643799406993</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>308</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T04:15:00</Time>
                <Price>20778.643799406993</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <Line xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30006</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>73d427e328db4960b1ed7175b15587bf</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Line</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>336</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T11:15:00</Time>
                <Price>20882.008780291551</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>320</SlotIndex>
                <DisplayName>Start</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T07:15:00</Time>
                <Price>20882.747101583584</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.Line</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </Line>
          </DrawingTools>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-945082fab4dd4a3da191159806217295>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>TDYG150355451300000018</Account>
        <ATM></ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Day</TIF>
      </ChartTrader>
    </Chart-0d90bae72e9c4392b35a6bd6b71066be>
    <Chart-1c6dfa9e16d7461e8f6059a1bc4f365a>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>-8;8</Location>
      <Size>980;547</Size>
      <ZOrder>1</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>148</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-9dd6f377a5df4f5da1fb34910fba92c4>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-07T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2099-12-01T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>371885aeb5364dda95d3b5714600907c</BarsSeriesId>
                <Id>fb326f1e4fda41f29e6783f45b176820</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>4.2365693333988474</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1">
              <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-05-07T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-12T00:00:00</To>
                <Calculate>OnPriceChange</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Price line</Name>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Ask line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Bid line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Last line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>511</IndicatorId>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>30009</ZOrder>
                <ShowAskLine>false</ShowAskLine>
                <ShowBidLine>false</ShowBidLine>
                <ShowLastLine>true</ShowLastLine>
                <AskLineLength>100</AskLineLength>
                <BidLineLength>100</BidLineLength>
                <LastLineLength>100</LastLineLength>
                <AskStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </AskStroke>
                <BidStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </BidStroke>
                <LastStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </LastStroke>
              </PriceLine>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1">
              <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>false</DisplayInDataBox>
                <From>2025-05-07T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-12T00:00:00</To>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Bar timer</Name>
                <Plots />
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>1066</IndicatorId>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>30010</ZOrder>
              </BarTimer>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
          </Indicators>
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>14.1218987</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>435</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Fixed</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>20926.463537558273</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>20702.278559372826</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <DrawingTools>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30004</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>9a185db9f9f54760b590d49ec585b0d8</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 3</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>916</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T03:25:00</Time>
                <Price>20932.488302676076</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>916</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T03:25:00</Time>
                <Price>20932.488302676076</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30005</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>c9ed44a169ea41d288286ed9d681015d</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 4</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>930</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T04:35:00</Time>
                <Price>20778.377898240327</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>930</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T04:35:00</Time>
                <Price>20778.377898240327</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30006</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>9bcae2cc05f247788ed2adcdcc9f611b</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 5</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>962</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T07:15:00</Time>
                <Price>20992.680482549844</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>962</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T07:15:00</Time>
                <Price>20992.680482549844</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30007</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>0ea259fa3b9d4a7aac71fc50434b9252</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 6</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>973</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T08:10:00</Time>
                <Price>20883.341188758775</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>973</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T08:10:00</Time>
                <Price>20883.341188758775</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <Line xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30008</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>d4ec69ee70cc45d1ae92d5c3e947698e</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Line 2</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>1001</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T10:30:00</Time>
                <Price>20850.959198134162</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>993</SlotIndex>
                <DisplayName>Start</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T09:50:00</Time>
                <Price>20850.959198134162</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.Line</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </Line>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30012</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>a5c0cf6b46a2428c8fc839d552932dc7</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 7</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>986</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-12T09:15:00</Time>
                <Price>20746.259560708673</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>986</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-12T09:15:00</Time>
                <Price>20746.259560708673</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
          </DrawingTools>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-9dd6f377a5df4f5da1fb34910fba92c4>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>TDYG150355451300000018</Account>
        <ATM>NQ</ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>3</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-1c6dfa9e16d7461e8f6059a1bc4f365a>
  </NTWindows>
</NinjaTrader>