﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <PropTraderAccountTool>
    <PropTraderAccountTool xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <calculate2>OnEachTick</calculate2>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <Calculate>OnEachTick</Calculate>
      <Displacement>0</Displacement>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2025-03-13T00:00:00</From>
      <IsAutoScale>true</IsAutoScale>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>Prop Trader Account Tool</Name>
      <Panel>-1</Panel>
      <Plots />
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
      <To>2025-03-18T00:00:00</To>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <SelectedValueSeries>0</SelectedValueSeries>
      <InputPlot>0</InputPlot>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <PaintPriceMarkers>true</PaintPriceMarkers>
      <DrawHorizontalGridLines>false</DrawHorizontalGridLines>
      <DrawVerticalGridLines>false</DrawVerticalGridLines>
      <DrawOnPricePanel>true</DrawOnPricePanel>
      <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
      <IndicatorId>1112</IndicatorId>
      <MaxSerialized>0</MaxSerialized>
      <MinSerialized>0</MinSerialized>
      <ZOrder>50</ZOrder>
      <selectedAccountName>Playback101</selectedAccountName>
      <AccountStartValue>150000</AccountStartValue>
      <Prop_DD>5000</Prop_DD>
      <Prop_AutoLiquidationValue>150000</Prop_AutoLiquidationValue>
      <ResetAccountInfo>false</ResetAccountInfo>
      <stoplossExposureWarning>0.6</stoplossExposureWarning>
      <usePredSL>64</usePredSL>
      <usePredTP>64</usePredTP>
      <_set_BE>false</_set_BE>
      <_be_ticks_offset>10</_be_ticks_offset>
      <_set_Trailing_Stop>false</_set_Trailing_Stop>
      <_tr_start>16</_tr_start>
      <_tr_offset>10</_tr_offset>
      <Show_Trailing_DD_ChartLines>true</Show_Trailing_DD_ChartLines>
      <Show_Trailing_DD_flat>true</Show_Trailing_DD_flat>
      <Line_1>10</Line_1>
      <Line_2>30</Line_2>
      <Line_3>80</Line_3>
    </PropTraderAccountTool>
  </PropTraderAccountTool>
</NinjaTrader>