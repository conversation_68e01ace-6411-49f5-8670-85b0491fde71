#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion


namespace NinjaTrader.NinjaScript.Indicators
{
    public class AndeanOscillator : Indicator
    {
        private int length;
        private int sigLength;
        private double alpha;
        private double up1;
        private double up2;
        private double dn1;
        private double dn2;

		public Series<double> Bull { get { return Values[0]; } }
		public Series<double> Bear { get { return Values[1]; } }
		public Series<double> SignalLine { get { return Values[2]; } }

//        private Series<double> Bull;
//        private Series<double> Bear;
//        private Series<double> SignalLine;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Andean Oscillator";
                Name = "AndeanOscillator";
                IsOverlay = false;
                AddPlot(Brushes.LimeGreen, "Bullish Component");
                AddPlot(Brushes.Red, "Bearish Component");
                AddPlot(Brushes.Orange, "SignalLine");

                length = 50;
                sigLength = 9;
            }
            else if (State == State.DataLoaded)
            {
                alpha = 2.0 / (length + 1);
//                Bull = new Series<double>(this);
//                Bear = new Series<double>(this);
//                SignalLine = new Series<double>(this);
                up1 = 0.0;
                up2 = 0.0;
                dn1 = 0.0;
                dn2 = 0.0;
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar == 0)
            {
                up1 = Close[0];
                up2 = Close[0] * Close[0];
                dn1 = Close[0];
                dn2 = Close[0] * Close[0];
                return;
            }

            up1 = Math.Max(Close[0], Math.Max(Open[0], up1 - (up1 - Close[0]) * alpha));
            up2 = Math.Max(Close[0] * Close[0], Math.Max(Open[0] * Open[0], up2 - (up2 - Close[0] * Close[0]) * alpha));

            dn1 = Math.Min(Close[0], Math.Min(Open[0], dn1 + (Close[0] - dn1) * alpha));
            dn2 = Math.Min(Close[0] * Close[0], Math.Min(Open[0] * Open[0], dn2 + (Close[0] * Close[0] - dn2) * alpha));

            Bull[0] = Math.Sqrt(Math.Max(0, dn2 - dn1 * dn1));
            Bear[0] = Math.Sqrt(Math.Max(0, up2 - up1 * up1));

            //SignalLine[0] = EMA(Math.Max(Bull[0], Bear[0]), sigLength)[0];
			//SignalLine[0] = EMA(Bull[0]>Bear[0]?Bull[0]:Bear[0],sigLength)[0];
			if(Bull[0]>Bear[0])
				SignalLine[0] = EMA(Bull,sigLength)[0];
			else
				SignalLine[0] = EMA(Bear,sigLength)[0];

            Values[0][0] = Bull[0];
            Values[1][0] = Bear[0];
            Values[2][0] = SignalLine[0];
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Length", Order = 1, GroupName = "Parameters")]
        public int Length
        {
            get { return length; }
            set { length = value; }
        }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "SignalLine Length", Order = 2, GroupName = "Parameters")]
        public int SigLength
        {
            get { return sigLength; }
            set { sigLength = value; }
        }
        #endregion
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private AndeanOscillator[] cacheAndeanOscillator;
		public AndeanOscillator AndeanOscillator(int length, int sigLength)
		{
			return AndeanOscillator(Input, length, sigLength);
		}

		public AndeanOscillator AndeanOscillator(ISeries<double> input, int length, int sigLength)
		{
			if (cacheAndeanOscillator != null)
				for (int idx = 0; idx < cacheAndeanOscillator.Length; idx++)
					if (cacheAndeanOscillator[idx] != null && cacheAndeanOscillator[idx].Length == length && cacheAndeanOscillator[idx].SigLength == sigLength && cacheAndeanOscillator[idx].EqualsInput(input))
						return cacheAndeanOscillator[idx];
			return CacheIndicator<AndeanOscillator>(new AndeanOscillator(){ Length = length, SigLength = sigLength }, input, ref cacheAndeanOscillator);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.AndeanOscillator AndeanOscillator(int length, int sigLength)
		{
			return indicator.AndeanOscillator(Input, length, sigLength);
		}

		public Indicators.AndeanOscillator AndeanOscillator(ISeries<double> input , int length, int sigLength)
		{
			return indicator.AndeanOscillator(input, length, sigLength);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.AndeanOscillator AndeanOscillator(int length, int sigLength)
		{
			return indicator.AndeanOscillator(Input, length, sigLength);
		}

		public Indicators.AndeanOscillator AndeanOscillator(ISeries<double> input , int length, int sigLength)
		{
			return indicator.AndeanOscillator(input, length, sigLength);
		}
	}
}

#endregion
