******************* Session Start (Version *******) *******************
2025-05-25 20:10:54:975 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-05-25 20:10:55:003 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-05-25 20:10:55:367 Core.Instrumentation.ActivitySource: enabled=True randomPercent=7.79501 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-05-25 20:10:55:403 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-05-25 20:10:56:360 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-25 20:10:56:368 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.7380598' renewSecs='2399.8690299'
2025-05-25 20:10:57:220 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-05-25 20:10:57:842 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-05-25 20:10:58:674 PrimaryMonitorWPFDPIScale=1.00
2025-05-25 20:10:58:976 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab (Beta) Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-05-25 20:10:59:131 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-05-25 20:10:59:131 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-05-25 20:10:59:131 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-05-25 20:10:59:132 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-05-25 20:10:59:132 OSLanguage='en-US'
2025-05-25 20:10:59:132 OSEnvironment='64bit'
2025-05-25 20:10:59:132 Processors=8
2025-05-25 20:10:59:132 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-05-25 20:11:00:196 ProcessorSpeed=2.4 GHz
2025-05-25 20:11:00:197 PhysicalMemory=8192 MB
2025-05-25 20:11:00:415 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-05-25 20:11:00:415 Monitors=2/1280x720|1920x1080
2025-05-25 20:11:00:415 .NET/CLR Version='4.8'/64bit
2025-05-25 20:11:00:417 SQLiteVersion='1.0.116.0'
2025-05-25 20:11:00:418 ApplicationTimezone=EST +0 hour(s)
2025-05-25 20:11:00:418 ApplicationTimezone=UTC -4 hour(s)
2025-05-25 20:11:00:418 LocalTimezone=EST +0 hour(s)
2025-05-25 20:11:00:418 LocalTimezone=UTC -4 hour(s)
2025-05-25 20:11:00:489 DirectXRenderingHW
2025-05-25 20:11:00:490 Copying custom assemblies...
2025-05-25 20:11:00:518 Loading custom assemblies...
2025-05-25 20:11:00:518 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-05-25 20:11:00:678 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-05-25 20:11:00:689 Deleting temporary files...
2025-05-25 20:11:00:698 Copying db and restoring templates...
2025-05-25 20:11:00:744 Loading third party assemblies...
2025-05-25 20:11:00:806 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-05-25 20:11:00:807 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-05-25 20:11:00:807 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-05-25 20:11:00:807 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-05-25 20:11:00:807 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-05-25 20:11:00:807 Initializing database...
2025-05-25 20:11:00:807 Loading master instruments...
2025-05-25 20:11:01:003 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-05-25 20:11:01:005 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-05-25 20:11:01:314 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-05-25 20:11:01:389 Loading instruments...
2025-05-25 20:11:01:629 Loading accounts...
2025-05-25 20:11:01:711 Loading users...
2025-05-25 20:11:01:734 Downloading server info...
2025-05-25 20:11:01:734 Starting instrument management...
2025-05-25 20:11:01:742 Starting timer...
2025-05-25 20:11:01:742 Creating file type watcher...
2025-05-25 20:11:01:744 Setting ATI...
2025-05-25 20:11:01:766 Connecting ATI server...
2025-05-25 20:11:01:766 Server.AtiServer.Connect0
2025-05-25 20:11:01:767 Starting adapter server...
2025-05-25 20:11:01:772 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-25 20:11:01:774 Server.AtiServer.Connect1: Port='36973'
2025-05-25 20:11:01:780 Server.AtiServer.Connect2
2025-05-25 20:11:01:782 Starting bars dictionary...
2025-05-25 20:11:01:783 Starting recorder...
2025-05-25 20:11:01:785 Starting server(s)...
2025-05-25 20:11:01:818 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3133
2025-05-25 20:11:01:818 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-05-25 20:11:01:818 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9780
2025-05-25 20:11:01:818 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=12065
2025-05-25 20:11:01:818 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5059
2025-05-25 20:11:01:868 Required resource key 'brushOrderWorking' is missing.
2025-05-25 20:11:01:869 Required resource key 'brushOrderAccepted' is missing.
2025-05-25 20:11:01:869 Required resource key 'brushOrderPartFilled' is missing.
2025-05-25 20:11:01:869 Required resource key 'brushOrderInitialized' is missing.
2025-05-25 20:11:01:910 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarPrice='' SnapModeDisabled='' SnapModePrice='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-05-25 20:11:01:912 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-05-25 20:11:01:912 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-25 20:11:01:913 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-05-25 20:11:01:922 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='Ctrl+Z' SimulatedOrder=''
2025-05-25 20:11:01:922 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-25 20:11:01:922 OrderEntryHotKeys=disabled
2025-05-25 20:11:01:923 AutoClose=disabled
2025-05-25 20:11:02:402 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-05-25 20:11:03:524 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-25 20:11:03:811 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-25 20:11:04:030 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-25 20:11:05:963 1 Chart Chart1Tab1 1 Ser 0 Ind 0 DrawObj Chart1Tab2 1 Ser 0 Ind 0 DrawObj 
2025-05-25 20:11:06:032 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=1 RolloverCollection=260 TradingHours=0
2025-05-25 20:11:07:740 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.11ms InstrumentLists=0.39ms MasterInstruments=0.05ms Messages=0.64ms Risks=11.60ms RolloverCollection=1695.36ms TradingHours=0.07ms
2025-05-25 20:11:07:741 Starting server message polling timer with interval 3600 seconds...
2025-05-25 20:11:10:006 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-25 20:11:10:061 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-25 runAsProcess=False
2025-05-25 20:11:10:077 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-25 20:11:10:080 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-25 20:11:10:088 (TRADIFY) Cbi.Connection.Connect1
2025-05-25 20:11:10:100 (TRADIFY) Cbi.Connection.Connect2
2025-05-25 20:11:10:101 (TRADIFY) Cbi.Connection.Connect3
2025-05-25 20:11:10:107 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-25 20:11:10:113 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:10:116 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-25 20:11:10:120 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-25 20:11:10:120 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:10:120 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-25 20:11:10:120 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount3' displayName='SimAccount3' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount3'
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount4' displayName='SimAccount4' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount4'
2025-05-25 20:11:10:121 (TRADIFY) Cbi.Connection.Connect4
2025-05-25 20:11:10:136 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-25 20:11:10:137 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-25 20:11:10:150 (TRADIFY) Cbi.Connection.Connect5
2025-05-25 20:11:10:153 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-25 20:11:10:168 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-25 20:11:10:317 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-25 20:11:10:317 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-25 20:11:10:317 Core.Instrumentation.LogActivity: activityType=Adapter errorCode=NoError errorMessage=''
2025-05-25 20:11:10:864 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-25 20:11:10:864 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8690334' renewSecs='2399.9345167'
2025-05-25 20:11:10:893 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-25 20:11:11:266 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-25 20:11:11:267 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-25 20:11:11:268 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-25 20:11:11:271 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-25 20:11:12:126 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-25 20:11:12:422 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-25 20:11:12:425 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-25 20:11:12:433 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-25 20:11:12:457 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-25 20:11:12:723 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-25 20:11:12:723 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:12:723 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-25 20:11:12:726 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000019' displayName='TDYA150355451300000019' fcm='' denomination=UsDollar forexLotSize=1
2025-05-25 20:11:12:726 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-25 20:11:12:726 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000019'
2025-05-25 20:11:12:732 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:12:732 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:12:740 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-25 20:11:12:741 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-25 20:11:12:746 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:753 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-25 20:11:12:784 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-25 20:11:12:785 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-25 20:11:12:786 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-25 20:11:12:786 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-25 20:11:12:786 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount3' fcm=''
2025-05-25 20:11:12:786 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount4' fcm=''
2025-05-25 20:11:12:787 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000019' fcm=''
2025-05-25 20:11:13:134 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-25 20:11:13:387 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-25 20:11:13:885 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-25 20:11:13:904 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-25 20:11:13:935 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000019' fcm=''
2025-05-25 20:11:13:946 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount3' fcm=''
2025-05-25 20:11:13:946 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount4' fcm=''
2025-05-25 20:11:13:946 (TRADIFY) Core.Connection.Statistics: connectAttempts=1/3795.2ms
2025-05-25 20:11:13:946 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-25 20:11:13:948 Server.HdsClient.Connect: type=HDS server='hds-us-nt-017.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-25 20:11:14:091 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-25 20:11:14:091 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-25 20:11:14:091 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-25 20:11:14:116 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-25 20:11:14:133 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-25 20:11:14:133 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount3' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount4' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount1' fcm='' fcmDate='2025-05-25'
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount3' fcm='' fcmDate='2025-05-25'
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount4' fcm='' fcmDate='2025-05-25'
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='Sim101' fcm='' fcmDate='2025-05-25'
2025-05-25 20:11:14:979 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 20:11:14:980 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 20:11:14:980 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount2' fcm='' fcmDate='2025-05-25'
2025-05-25 20:11:25:463 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-25 20:11:25:535 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-25 20:11:25:700 Cbi.Instrument.RequestBars (to Provider): instrument='NQ MAR25' from='19/05/2025 12:00:00 AM' to='23/05/2025 12:00:00 AM' period='1 Minute'
2025-05-25 20:11:26:517 Cbi.Instrument.RequestBars (to Provider): instrument='NQ MAR25' from='25/05/2025 12:00:00 AM' to='25/05/2025 12:00:00 AM' period='1 Minute'
2025-05-25 20:11:45:127 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='23/05/2025 12:00:00 AM' to='23/05/2025 12:00:00 AM' period='1 Minute'
2025-05-25 20:11:45:814 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='25/05/2025 12:00:00 AM' to='25/05/2025 12:00:00 AM' period='1 Minute'
2025-05-25 20:34:50:645 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='25/05/2025 12:00:00 AM' to='25/05/2025 12:00:00 AM' period='1 Minute'
2025-05-25 20:34:51:160 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='23/05/2025 12:00:00 AM' to='26/05/2025 12:00:00 AM' period='Daily'
2025-05-25 20:34:51:160 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='23/05/2025 12:00:00 AM' to='26/05/2025 12:00:00 AM' period='Daily'
2025-05-25 20:34:51:162 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-05-25 20:34:51:471 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='25/05/2025 12:00:00 AM' to='26/05/2025 12:00:00 AM' period='Daily'
2025-05-25 20:34:51:706 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='25/05/2025 12:00:00 AM' to='26/05/2025 12:00:00 AM' period='Daily'
2025-05-25 20:45:03:574 (TRADIFY) Cbi.Account.CreateOrder: orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41955 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 20:45:03:640 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41955 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:03:641 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41955 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:03:644 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41955 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 20:45:03:661 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:03:822 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:03:823 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=3
2025-05-25 20:45:03:826 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='40acfc537bcf436ebbc297a2b69da930' maxFillQuantity=1 price=21195.25 thread=51
2025-05-25 20:45:03:826 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='40acfc537bcf436ebbc297a2b69da930' fillQuantity=1 price=21195.25
2025-05-25 20:45:03:826 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=21195.25 time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=4
2025-05-25 20:45:03:835 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='b8e4d3ded8694046b022061d93dc6209' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21195.25 quantity=1 marketPosition=Short operation=Add orderID='40acfc537bcf436ebbc297a2b69da930' isSod=False time='2025-05-25 20:45:03' statementDate='2025-05-25'
2025-05-25 20:45:03:867 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21195.25 quantity=1 marketPosition=Short operation=Add
2025-05-25 20:45:03:888 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='40acfc537bcf436ebbc297a2b69da930' maxFillQuantity=2 price=21195 thread=51
2025-05-25 20:45:03:889 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='40acfc537bcf436ebbc297a2b69da930' fillQuantity=2 price=21195
2025-05-25 20:45:03:889 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='40acfc537bcf436ebbc297a2b69da930' account='Sim101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=21195.********** time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:03:907 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='955e7b2835444ae2a4654548c2ff4c5a' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21195 quantity=2 marketPosition=Short operation=Add orderID='40acfc537bcf436ebbc297a2b69da930' isSod=False time='2025-05-25 20:45:03' statementDate='2025-05-25'
2025-05-25 20:45:03:930 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21195.********** quantity=3 marketPosition=Short operation=Update
2025-05-25 20:45:03:957 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Sim101' instrument='NQ JUN25' id='*********' filled=3 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='40acfc537bcf436ebbc297a2b69da930+=3 ' outstandingOrders='' thread=13
2025-05-25 20:45:03:959 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Sim101' instrument='NQ JUN25' id='*********' initialEntryOrderId='40acfc537bcf436ebbc297a2b69da930' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=3 exitOrders=''
2025-05-25 20:45:03:962 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='Sim101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=3 limitPrice=0 stopPrice=21225.25 oco='aa7d6986cde141d0b3b111846d320a00'
2025-05-25 20:45:03:962 (TRADIFY) Cbi.Account.CreateOrder: orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21225.25 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 20:45:03:965 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21225.25 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:03:966 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21225.25 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:03:967 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21225.25 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 20:45:03:967 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21225.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 20:45:03' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:03:999 (TRADIFY) Cbi.Account.CreateOrder: orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 20:45:03' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 20:45:04:008 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 20:45:04' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:04:008 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 20:45:04' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:45:04:008 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 20:45:04' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 20:45:04:008 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 20:45:04' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:04:099 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21225.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 20:45:04' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:04:122 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 20:45:04' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:45:04:123 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 20:45:04' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:47:40:926 (TRADIFY) Cbi.Account.CreateOrder: orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 20:47:40' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 20:47:40:933 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 20:47:40' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:47:40:933 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 20:47:40' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 20:47:40:933 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 20:47:40' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 20:47:40:933 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=21225.75 stopPrice=21225.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 20:47:40' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:47:41:052 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21225.75 stopPrice=21225.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 20:47:41' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 20:50:56:242 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-25 20:50:56:266 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-25 20:50:56:642 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-25 20:50:56:643 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.888868' renewSecs='2399.944434'
2025-05-25 20:51:10:804 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-25 20:51:10:804 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-25 20:51:11:182 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8681567' renewSecs='2399.********'
2025-05-25 21:00:26:492 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:26' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21256.25 stopPriceChanged=21256 quantityChanged=6
2025-05-25 21:00:26:492 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=21225.75 stopPrice=21225.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:26' statementDate='2025-05-25' error=NoError comment='' nr=3
2025-05-25 21:00:26:496 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21225.75 stopPrice=21225.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:26' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21256.25 quantityChanged=6 stopPriceChanged=21256
2025-05-25 21:00:26:499 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21256.25 stopPrice=21256 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:26' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged='21256.25' quantityChanged='6' stopPriceChanged='21256' delay=100
2025-05-25 21:00:26:499 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21256.25 stopPrice=21256 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:26' statementDate='2025-05-25' error=NoError comment='' nr=4
2025-05-25 21:00:26:608 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21256.25 stopPrice=21256 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:26' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:00:28:171 (TRADIFY) Gui.Chart.OrderStack.Cancel.Cancel1: realOrderState=Accepted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21256.25 stopPrice=21256 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:26' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:00:28:177 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21256.25 stopPrice=21256 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:28' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:00:28:177 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy limitPrice=21256.25 stopPrice=21256 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:28' statementDate='2025-05-25' error=NoError comment='' nr=6
2025-05-25 21:00:28:181 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21256.25 stopPrice=21256 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:28' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:00:28:188 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Buy orderType='Stop Limit' limitPrice=21256.25 stopPrice=21256 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41958 time='2025-05-25 21:00:28' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:00:28:188 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21256.25 stopPrice=21256 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:28' statementDate='2025-05-25' error=NoError comment='' nr=7
2025-05-25 21:00:28:299 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='0a9f8ea623b14cacbd919acea078544a' account='Sim101' name='Entry' orderState=Cancelled instrument='NQ JUN25' orderAction=Buy limitPrice=21256.25 stopPrice=21256 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:00:28' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:00:28:319 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:01:32:394 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21225.25 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 21:01:32' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21194.5 quantityChanged=3
2025-05-25 21:01:32:394 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21225.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:01:32' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:01:32:400 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21194.5 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 21:01:32' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 quantityChanged=3 stopPriceChanged=21194.5
2025-05-25 21:01:32:400 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21194.5 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41956 time='2025-05-25 21:01:32' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged='0' quantityChanged='3' stopPriceChanged='21194.5' delay=100
2025-05-25 21:01:32:400 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21194.5 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:01:32' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:01:32:510 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21194.5 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:01:32' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:11:07:752 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-25 21:11:09:016 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-25 21:11:09:018 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=0.55ms Risks=0.00ms RolloverCollection=0.01ms TradingHours=0.00ms
2025-05-25 21:12:44:171 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21194.5 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:172 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='0341e98702384b6a908177c9964b3969' maxFillQuantity=1 price=21194.75 thread=51
2025-05-25 21:12:44:172 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='0341e98702384b6a908177c9964b3969' fillQuantity=1 price=21194.75
2025-05-25 21:12:44:172 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21194.5 quantity=3 orderType='Stop Market' filled=1 averageFillPrice=21194.75 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21194.5 quantity=3 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=1 averageFillPrice=21194.75 onBehalfOf='' id=41956 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=3 order2.maxQuantitySeen=3 e.Filled=1 order2.QuantityChanged=2
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21165.25 stopPriceChanged=0 quantityChanged=2
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21165.25 quantityChanged=2 stopPriceChanged=0
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged='21165.25' quantityChanged='2' stopPriceChanged='0' delay=100
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:173 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='c3185c7b9c684f638d5a25195a92b1e7' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21194.75 quantity=1 marketPosition=Long operation=Add orderID='0341e98702384b6a908177c9964b3969' isSod=False time='2025-05-25 21:12:44' statementDate='2025-05-25'
2025-05-25 21:12:44:184 (TRADIFY) Cbi.Account.OnAddTrade: entryId='b8e4d3ded8694046b022061d93dc6209' exitId='c3185c7b9c684f638d5a25195a92b1e7' profitCurrencyBeforeCommissionAndFees=10
2025-05-25 21:12:44:184 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21195 quantity=2 marketPosition=Short operation=Update
2025-05-25 21:12:44:184 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='0341e98702384b6a908177c9964b3969' maxFillQuantity=2 price=21195 thread=51
2025-05-25 21:12:44:184 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='0341e98702384b6a908177c9964b3969' fillQuantity=2 price=21195
2025-05-25 21:12:44:185 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='0341e98702384b6a908177c9964b3969' account='Sim101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21194.5 quantity=3 orderType='Stop Market' filled=3 averageFillPrice=21194.********** time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:200 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=ChangeSubmitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:200 (TRADIFY) Cbi.Account.Cancel0: realOrderState=ChangeSubmitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:201 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=6
2025-05-25 21:12:44:201 (TRADIFY) Cbi.Account.QueueCancel: realOrderState=ChangeSubmitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:201 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='16fbce1fbd304d5f885e1906106b1ee1' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21195 quantity=2 marketPosition=Long operation=Add orderID='0341e98702384b6a908177c9964b3969' isSod=False time='2025-05-25 21:12:44' statementDate='2025-05-25'
2025-05-25 21:12:44:201 (TRADIFY) Cbi.Account.OnAddTrade: entryId='955e7b2835444ae2a4654548c2ff4c5a' exitId='16fbce1fbd304d5f885e1906106b1ee1' profitCurrencyBeforeCommissionAndFees=0
2025-05-25 21:12:44:201 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-25 21:12:44:208 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:12:44:211 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:12:44:223 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:12:44:223 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.OrderUpdateCallback.RetryCancel: orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=8
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21165.25 stopPrice=0 quantity=2 tif=Gtc oco='aa7d6986cde141d0b3b111846d320a00' filled=0 averageFillPrice=0 onBehalfOf='' id=41957 time='2025-05-25 21:12:44' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:12:44:279 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=9
2025-05-25 21:12:44:381 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='102ebede09724a4685260ed2018d0728' account='Sim101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21165.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:12:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:12:44:386 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:12:45:094 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 21:12:45:095 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:12:45:095 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:12:45:095 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:30:00:826 (TRADIFY) Cbi.Account.CreateOrder: orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41959 time='2025-05-25 21:30:00' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 21:30:00:833 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41959 time='2025-05-25 21:30:00' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:00:833 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41959 time='2025-05-25 21:30:00' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:00:833 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41959 time='2025-05-25 21:30:00' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:30:00:833 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=6 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 21:30:00' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:00:960 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=6 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 21:30:00' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=6 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-25 21:30:00' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='55f286a8834a4c5e93efd77c8689a91a' maxFillQuantity=1 price=21178 thread=51
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='55f286a8834a4c5e93efd77c8689a91a' fillQuantity=1 price=21178
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=6 orderType='Market' filled=1 averageFillPrice=21178 time='2025-05-25 21:30:00' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='4db5815ef13c4b5eb67213500ecd0ef0' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21178 quantity=1 marketPosition=Long operation=Add orderID='55f286a8834a4c5e93efd77c8689a91a' isSod=False time='2025-05-25 21:30:00' statementDate='2025-05-25'
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178 quantity=1 marketPosition=Long operation=Add
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='55f286a8834a4c5e93efd77c8689a91a' maxFillQuantity=5 price=21178.25 thread=51
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='55f286a8834a4c5e93efd77c8689a91a' fillQuantity=5 price=21178.25
2025-05-25 21:30:00:961 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='55f286a8834a4c5e93efd77c8689a91a' account='Sim101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=6 orderType='Market' filled=6 averageFillPrice=21178.********** time='2025-05-25 21:30:00' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:00:976 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='3384b760be784ecd8108e6f93017d293' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21178.25 quantity=5 marketPosition=Long operation=Add orderID='55f286a8834a4c5e93efd77c8689a91a' isSod=False time='2025-05-25 21:30:00' statementDate='2025-05-25'
2025-05-25 21:30:00:976 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.********** quantity=6 marketPosition=Long operation=Update
2025-05-25 21:30:01:000 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Sim101' instrument='NQ JUN25' id='*********' filled=6 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='55f286a8834a4c5e93efd77c8689a91a+=6 ' outstandingOrders='' thread=13
2025-05-25 21:30:01:000 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Sim101' instrument='NQ JUN25' id='*********' initialEntryOrderId='55f286a8834a4c5e93efd77c8689a91a' bracket=0 qty=6 stopOrdersOutstandingQuantity=0 quantity2Add=6 exitOrders=''
2025-05-25 21:30:01:000 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='Sim101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=6 limitPrice=0 stopPrice=21148 oco='bab26d89639a4773976485c83954823a'
2025-05-25 21:30:01:000 (TRADIFY) Cbi.Account.CreateOrder: orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 21:30:01:005 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:01:005 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:01:007 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:30:01:008 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=6 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:30:01' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:01:013 (TRADIFY) Cbi.Account.CreateOrder: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 21:30:01:017 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:01:017 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:01:017 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:30:01:017 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21208 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:01' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:01:125 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=6 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:30:01' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:01:125 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21208 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:01' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:01:125 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21208 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:01' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:33:037 (TRADIFY) Cbi.Account.CreateOrder: orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:30:33' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 21:30:33:044 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:30:33' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:33:044 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:30:33' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:33:044 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:30:33' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:30:33:044 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21148.25 stopPrice=21148.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:33' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:33:168 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21148.25 stopPrice=21148.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:33' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:44:304 (TRADIFY) Cbi.Account.CreateOrder: orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:30:44' gtd='2099-12-01' statementDate='2025-05-25' id=-1 comment=''
2025-05-25 21:30:44:312 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:30:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:44:312 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:30:44' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:30:44:312 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:30:44' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:30:44:312 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21148 stopPrice=21148.25 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:44:432 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21148 stopPrice=21148.25 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:30:44' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:30:56:593 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-25 21:30:56:593 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-25 21:30:56:926 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-25 21:30:56:927 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8994457' renewSecs='2399.********'
2025-05-25 21:31:11:132 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-25 21:31:11:132 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-25 21:31:11:447 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8986587' renewSecs='2399.********'
2025-05-25 21:35:56:420 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:30:01' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21208 stopPriceChanged=0 quantityChanged=6
2025-05-25 21:46:47:977 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21208 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:46:47' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21206.75 stopPriceChanged=0 quantityChanged=6
2025-05-25 21:46:47:977 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21208 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:46:47' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:46:47:981 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:46:47' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=21206.75 quantityChanged=6 stopPriceChanged=0
2025-05-25 21:46:47:981 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41961 time='2025-05-25 21:46:47' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged='21206.75' quantityChanged='6' stopPriceChanged='0' delay=100
2025-05-25 21:46:47:981 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:46:47' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:46:48:088 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:46:48' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:46:48:088 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:46:48' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=1 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=1 averageFillPrice=21206.75 onBehalfOf='' id=41961 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=6 order2.maxQuantitySeen=6 e.Filled=1 order2.QuantityChanged=5
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21148 quantityChanged=5
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=6 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 quantityChanged=5 stopPriceChanged=21148
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged='0' quantityChanged='5' stopPriceChanged='21148' delay=100
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:511 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='4fd0bc354d3f405cbda797deea0e6b89' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Account.OnAddTrade: entryId='4db5815ef13c4b5eb67213500ecd0ef0' exitId='4fd0bc354d3f405cbda797deea0e6b89' profitCurrencyBeforeCommissionAndFees=575
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.25 quantity=5 marketPosition=Long operation=Update
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=2 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=2 averageFillPrice=21206.75 onBehalfOf='' id=41961 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=6 order2.maxQuantitySeen=6 e.Filled=2 order2.QuantityChanged=4
2025-05-25 21:49:18:512 (TRADIFY) Cbi.Account.Change0: realOrderState=ChangeSubmitted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21148 quantityChanged=4
2025-05-25 21:49:18:515 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:516 (TRADIFY) Cbi.Account.QueueChange: realOrderState=ChangeSubmitted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:516 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='4c4ed2de0e904289ade5bd9b0e786bff' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:516 (TRADIFY) Cbi.Account.OnAddTrade: entryId='3384b760be784ecd8108e6f93017d293' exitId='4c4ed2de0e904289ade5bd9b0e786bff' profitCurrencyBeforeCommissionAndFees=570
2025-05-25 21:49:18:516 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.25 quantity=4 marketPosition=Long operation=Update
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=3 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=3 averageFillPrice=21206.75 onBehalfOf='' id=41961 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=6 order2.maxQuantitySeen=6 e.Filled=3 order2.QuantityChanged=3
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.Change0: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21148 quantityChanged=3
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.QueueChange: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='911da53800a94a829d30a0d348732418' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.OnAddTrade: entryId='3384b760be784ecd8108e6f93017d293' exitId='911da53800a94a829d30a0d348732418' profitCurrencyBeforeCommissionAndFees=570
2025-05-25 21:49:18:519 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.25 quantity=3 marketPosition=Long operation=Update
2025-05-25 21:49:18:523 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:523 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:523 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=4 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:523 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=4 averageFillPrice=21206.75 onBehalfOf='' id=41961 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=6 order2.maxQuantitySeen=6 e.Filled=4 order2.QuantityChanged=2
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.Change0: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21148 quantityChanged=2
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.QueueChange: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='674d44d467a940e8b49c36b7342149c4' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.OnAddTrade: entryId='3384b760be784ecd8108e6f93017d293' exitId='674d44d467a940e8b49c36b7342149c4' profitCurrencyBeforeCommissionAndFees=570
2025-05-25 21:49:18:524 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.25 quantity=2 marketPosition=Long operation=Update
2025-05-25 21:49:18:527 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:527 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:527 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=5 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21206.75 stopPrice=0 quantity=6 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=5 averageFillPrice=21206.75 onBehalfOf='' id=41961 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' order.MaxQuantitySeen=6 order2.maxQuantitySeen=6 e.Filled=5 order2.QuantityChanged=1
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.Change0: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' limitPriceChanged=0 stopPriceChanged=21148 quantityChanged=1
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.QueueChange: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='4a7af06fa7b54962b993ab3027afd411' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.OnAddTrade: entryId='3384b760be784ecd8108e6f93017d293' exitId='4a7af06fa7b54962b993ab3027afd411' profitCurrencyBeforeCommissionAndFees=570
2025-05-25 21:49:18:528 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=21178.25 quantity=1 marketPosition=Long operation=Update
2025-05-25 21:49:18:529 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' maxFillQuantity=1 price=21206.75 thread=51
2025-05-25 21:49:18:529 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf60794caf9c409bb68ba4e5746f07fe' fillQuantity=1 price=21206.75
2025-05-25 21:49:18:529 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='cf60794caf9c409bb68ba4e5746f07fe' account='Sim101' name='Target1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=21206.75 stopPrice=0 quantity=6 orderType='Limit' filled=6 averageFillPrice=21206.75 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.Cancel0: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=5 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.QueueCancel: realOrderState=ChangePending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=5 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='d64e25edc2d64edaae60c8d0b47b8fe4' account='Sim101' instrument='NQ JUN25' exchange=Globex price=21206.75 quantity=1 marketPosition=Short operation=Add orderID='cf60794caf9c409bb68ba4e5746f07fe' isSod=False time='2025-05-25 21:49:18' statementDate='2025-05-25'
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.OnAddTrade: entryId='3384b760be784ecd8108e6f93017d293' exitId='d64e25edc2d64edaae60c8d0b47b8fe4' profitCurrencyBeforeCommissionAndFees=570
2025-05-25 21:49:18:544 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Sim101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-25 21:49:18:550 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:550 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:550 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:49:18:550 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:49:18:568 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:568 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:568 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:49:18:568 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:49:18:582 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:582 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:582 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:49:18:582 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:49:18:600 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:600 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:600 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:49:18:600 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:49:18:614 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:614 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:49:18:614 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='Sim101' currentQuantity=0 signalName='Close'
2025-05-25 21:49:18:614 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='Sim101'
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.OrderUpdateCallback.RetryCancel: orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=1 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=1 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=1 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21148 quantity=1 tif=Gtc oco='bab26d89639a4773976485c83954823a' filled=0 averageFillPrice=0 onBehalfOf='' id=41960 time='2025-05-25 21:49:18' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:49:18:618 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:620 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=CashValue currency=UsDollar value=*****
2025-05-25 21:49:18:620 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:49:18:620 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:49:18:620 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-25 21:49:18:728 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='2db44625643b45adaae4e41ecada76f1' account='Sim101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21148 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-25 21:49:18' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:49:18:735 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:50:27:803 (TRADIFY) Gui.Chart.OrderStack.Cancel.Cancel1: realOrderState=Accepted orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:50:27' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:27:803 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:50:27' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:27:803 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21148 stopPrice=21148.25 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:27' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:27:807 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:50:27' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:27:807 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148 stopPrice=21148.25 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41963 time='2025-05-25 21:50:27' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:50:27:807 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21148 stopPrice=21148.25 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:27' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:27:916 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='b31396aaf2d44adcb37b439492f0be49' account='Sim101' name='Entry' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21148 stopPrice=21148.25 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:27' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:27:937 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Sim101' instrument='NQ JUN25' id='*********'
2025-05-25 21:50:28:334 (TRADIFY) Gui.Chart.OrderStack.Cancel.Cancel1: realOrderState=Accepted orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:50:28' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:28:334 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:50:28' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:28:334 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21148.25 stopPrice=21148.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:28' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:28:338 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:50:28' gtd='2099-12-01' statementDate='2025-05-25'
2025-05-25 21:50:28:338 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21148.25 stopPrice=21148.5 quantity=6 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=41962 time='2025-05-25 21:50:28' gtd='2099-12-01' statementDate='2025-05-25' delay=100
2025-05-25 21:50:28:338 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21148.25 stopPrice=21148.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:28' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:28:452 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='9b66e0acc55d4e17944f4f69ab4faf48' account='Sim101' name='Entry' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21148.25 stopPrice=21148.5 quantity=6 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-25 21:50:28' statementDate='2025-05-25' error=NoError comment='' nr=-1
2025-05-25 21:50:28:472 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Sim101' instrument='NQ JUN25' id='*********'
