﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTTabPage>
    <InstrumentLink>0</InstrumentLink>
    <IntervalLink>0</IntervalLink>
    <ChartTraderVisibility>Visible</ChartTraderVisibility>
    <SeriesCount>1</SeriesCount>
    <DataSeries>
      <BarsProperties>
        <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <BarsPeriod>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriod>
          <RangeType>Days</RangeType>
          <BarsBack>50</BarsBack>
          <DaysBack>5</DaysBack>
          <From>2025-04-26T00:00:00</From>
          <IsStableSession>true</IsStableSession>
          <To>2099-12-01T00:00:00</To>
          <TradingHoursSerializable />
          <AutoScale>true</AutoScale>
          <CenterPriceOnScale>false</CenterPriceOnScale>
          <DisplayInDataBox>true</DisplayInDataBox>
          <Label>MES JUN25</Label>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <Panel>0</Panel>
          <PriceMarker>
            <BackgroundSerialize>DEFAULT</BackgroundSerialize>
            <IsVisible>true</IsVisible>
          </PriceMarker>
          <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
          <ScaleJustification>Right</ScaleJustification>
          <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
          <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FFC0C0C0" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
          <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
          <PlotExecutions>TextAndMarker</PlotExecutions>
          <MarkerSize>5</MarkerSize>
          <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
          <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
          <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
          <BarsSeriesId>ea9fe998bbff47f2b2ad105215f4de23</BarsSeriesId>
          <Id>ea9fe998bbff47f2b2ad105215f4de23</Id>
          <Instrument>MES JUN25</Instrument>
          <IsLinked>true</IsLinked>
          <IsPrimarySeries>true</IsPrimarySeries>
          <ZOrder>1</ZOrder>
        </BarsProperties>
        <ChartStyles>
          <ChartStyle>
            <CandleStyle>
              <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <BarWidth>1.7466477341879996</BarWidth>
                <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                <StrokeSerialize>DEFAULT</StrokeSerialize>
                <Stroke2Serialize>DEFAULT</Stroke2Serialize>
              </CandleStyle>
            </CandleStyle>
          </ChartStyle>
        </ChartStyles>
      </BarsProperties>
    </DataSeries>
    <Indicators>
      <Indicator BarsIndex="0" Instrument="MES JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.EMA" Panel="-1" DisplayName="EMA(50)">
        <EMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-04-26T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-01T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>EMA</Name>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF1E90FF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>EMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>241</IndicatorId>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10006</ZOrder>
          <Period>50</Period>
        </EMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="MES JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.EMA" Panel="-1" DisplayName="EMA(20)">
        <EMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-04-26T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-01T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>EMA</Name>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF8A2BE2&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>EMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>242</IndicatorId>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10007</ZOrder>
          <Period>20</Period>
        </EMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
    </Indicators>
    <CrosshairType>Local</CrosshairType>
    <StayInDrawMode>False</StayInDrawMode>
    <Properties>
      <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AlwaysOnTop>false</AlwaysOnTop>
        <AreTabsVisible>true</AreTabsVisible>
        <AllowSelectionDragging>true</AllowSelectionDragging>
        <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
        <EquidistantBarSpacing>true</EquidistantBarSpacing>
        <LabelFont>
          <Bold>false</Bold>
          <FamilySerialize>#Montserrat</FamilySerialize>
          <Italic>false</Italic>
          <Size>11</Size>
        </LabelFont>
        <BarDistance>5.82215929</BarDistance>
        <BarMarginRightUser>8</BarMarginRightUser>
        <ChartTraderVisibility>Visible</ChartTraderVisibility>
        <ShowDateRange>false</ShowDateRange>
        <ShowScrollBar>true</ShowScrollBar>
        <SnapMode>Bar</SnapMode>
        <TabName>@INSTRUMENT_FULL</TabName>
        <LoadBackgroundImage>false</LoadBackgroundImage>
        <BackgroundImageStretch>Fill</BackgroundImageStretch>
        <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
        <ChartTextSerialize>DEFAULT</ChartTextSerialize>
        <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
        <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
        <AreHGridLinesVisible>true</AreHGridLinesVisible>
        <AreVGridLinesVisible>true</AreVGridLinesVisible>
        <AxisPenSerialize>DEFAULT</AxisPenSerialize>
        <CrosshairPen>DEFAULT</CrosshairPen>
        <CrosshairIsLocked>false</CrosshairIsLocked>
        <CrosshairCrosshairType>Local</CrosshairCrosshairType>
        <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
        <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
        <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
        <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
      </ChartControlProperties>
    </Properties>
    <ChartPanels>
      <ChartPanel>
        <Height>855</Height>
        <HoldChartTraderOrders>false</HoldChartTraderOrders>
        <IsMaximized>false</IsMaximized>
        <Right>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Fixed</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>5696.2089491523866</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>5600.7910508476134</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Right>
        <Left>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Left>
        <Overlay>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Overlay>
      </ChartPanel>
    </ChartPanels>
    <ChartAlerts />
  </NTTabPage>
  <ChartTrader>
    <Properties>
      <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
        <AutoScale>true</AutoScale>
        <OrderDisplayBarLength>25</OrderDisplayBarLength>
        <PnLDisplayUnit>Points</PnLDisplayUnit>
        <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
        <ScaleQuantity>0</ScaleQuantity>
        <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
        <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
        <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
        <StopLimitOffsetValue>0</StopLimitOffsetValue>
        <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
        <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
        <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
        <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
        <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
        <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
        <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
        <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
        <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
        <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
      </ChartTraderProperties>
    </Properties>
    <Account>FTDYFA150355451300000013</Account>
    <ATM>ES</ATM>
    <Instrument>MES JUN25</Instrument>
    <Quantity>30</Quantity>
    <TIF>Gtc</TIF>
  </ChartTrader>
</NinjaTrader>