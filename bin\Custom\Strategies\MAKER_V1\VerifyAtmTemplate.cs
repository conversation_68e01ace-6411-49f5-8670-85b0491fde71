using System;
using System.IO;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core;

namespace NinjaTrader.NinjaScript.Strategies
{
    public partial class MAKER_V1 : Strategy
    {
        // This method verifies that the ATM template exists and is accessible
        // Call this from OnStateChange() in the DataLoaded state to ensure template availability
        private bool VerifyAtmTemplateExists()
        {
            try
            {
                // Check if the template name is set
                if (string.IsNullOrEmpty(AtmStrategyTemplate))
                {
                    Print($"{Time[0]} ERROR: ATM Strategy Template name is not set.");
                    return false;
                }
                
                // Verify the template exists in NinjaTrader's templates
                string templatePath = Path.Combine(
                    Core.Globals.UserDataDir, 
                    "templates", 
                    "AtmStrategy", 
                    AtmStrategyTemplate + ".xml");
                
                if (!File.Exists(templatePath))
                {
                    // Check our local template directory as a fallback
                    string localTemplatePath = Path.Combine(
                        Path.GetDirectoryName(GetType().Assembly.Location),
                        "MAKER_V1",
                        "TEMPLATE",
                        AtmStrategyTemplate + ".xml");
                        
                    if (!File.Exists(localTemplatePath))
                    {
                        Print($"{Time[0]} ERROR: ATM Strategy Template '{AtmStrategyTemplate}' not found at:\n{templatePath}\nor at:\n{localTemplatePath}");
                        return false;
                    }
                    else
                    {
                        Print($"{Time[0]} ATM Strategy Template '{AtmStrategyTemplate}' found in local directory.");
                        // Optionally copy to NinjaTrader templates directory for better accessibility
                        try
                        {
                            Directory.CreateDirectory(Path.GetDirectoryName(templatePath));
                            File.Copy(localTemplatePath, templatePath, true);
                            Print($"{Time[0]} Copied ATM Strategy Template to NinjaTrader templates directory.");
                        }
                        catch (Exception copyEx)
                        {
                            Print($"{Time[0]} Warning: Could not copy template to NinjaTrader directory: {copyEx.Message}");
                            // Continue anyway since we found the local template
                        }
                    }
                }
                else
                {
                    Print($"{Time[0]} ATM Strategy Template '{AtmStrategyTemplate}' found in NinjaTrader templates directory.");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Print($"{Time[0]} Error verifying ATM template: {ex.Message}\n{ex.StackTrace}");
                return false;
            }
        }
    }
}