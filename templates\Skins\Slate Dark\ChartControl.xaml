<!-- Dark -->
<ResourceDictionary	xmlns		="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x		="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:po	="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options">

	<LinearGradientBrush	po:Freeze="true"	 x:Key="ChartTrader.ButtonBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66" >
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FFB3B3B3"/>
			<GradientStop Color="#FF000000" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	 x:Key="ChartTrader.ActionButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66" >
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FFB3B3B3"/>
			<GradientStop Color="#FF000000" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	 x:Key="ChartTrader.BuyButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66" >
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FFB3B3B3"/>
			<GradientStop Color="#FF000000" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	 x:Key="ChartTrader.SellButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66" >
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FFB3B3B3"/>
			<GradientStop Color="#FF000000" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<SolidColorBrush	x:Key="ChartControl.ChartBackground"				Color="Black"			po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.ChartText"						Color="White"			po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.InactivePriceMarkersBackground"	Color="LightGray"		po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.CrosshairLabelBackground"		Color="LightGray"		po:Freeze="true" />

	<Pen				x:Key="ChartControl.AxisPen"						Brush="White"			po:Freeze="true"	Thickness="1"/>
	<Pen				x:Key="ChartControl.CrosshairPen"					Brush="White"			po:Freeze="true"	Thickness="1"/>
	<Pen				x:Key="ChartControl.GridLineHPen"											po:Freeze="true"	Thickness="1">
		<Pen.Brush>
			<SolidColorBrush Color="Silver" Opacity="0.4" po:Freeze="true"></SolidColorBrush>
		</Pen.Brush>
	</Pen>
	<Pen				x:Key="ChartControl.GridLineVPen"											po:Freeze="true"	Thickness="1">
		<Pen.Brush>
			<SolidColorBrush Color="Silver" Opacity="0.4" po:Freeze="true"></SolidColorBrush>
		</Pen.Brush>
	</Pen>
	<Pen				x:Key="ChartControl.PanelSplitterPen"				Brush="White"			po:Freeze="true"	Thickness="1"/>

	<SolidColorBrush	x:Key="ChartControl.DataBoxPanelLabelBackground"	Color="#FF333333"		po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.DataBoxItemLabelBackground"		Color="#FF666666"		po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.DataBoxBackground"				Color="Black"			po:Freeze="true"	Opacity="0.759"/>
	<SolidColorBrush	x:Key="ChartControl.DataBoxForeground"				Color="White"			po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.TimeHighBrush"					Color="CornflowerBlue"	po:Freeze="true"		Opacity="0.25"/>
	<SolidColorBrush	x:Key="ChartControl.DropHighlight"					Color="CornflowerBlue"	po:Freeze="true"	Opacity="0.5"/>
	<SolidColorBrush	x:Key="ChartControl.SelectedMarkerBrush"			Color="CornflowerBlue"	po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.UpBrush"						Color="LimeGreen"		po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.DownBrush"						Color="Red"				po:Freeze="true" />
	<SolidColorBrush	x:Key="ChartControl.VolumetricText"					Color="White"			po:Freeze="true" />
	<Pen				x:Key="ChartControl.Stroke"							Brush="White"			po:Freeze="true"	Thickness="1"/>
	<Pen				x:Key="ChartControl.Stroke2"						Brush="White"			po:Freeze="true"	Thickness="1"/>
</ResourceDictionary> 