#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators.TradeSaber
{
    public class ORB_TradeSaber : Indicator
    {
        private double sessionHigh;
        private double sessionLow;
        private bool isInRange;
        private DateTime adjustedStartTime;
        private DateTime adjustedEndTime;
        private Series<double> highSeries;
        private Series<double> lowSeries;
        private DateTime lastBarDate;

        #region Properties
        
        [NinjaScriptProperty]
        [Display(Name = "Highlight Range", Description = "Highlight the Opening Range period", Order = 9, GroupName = "Display")]
        public bool HighlightRange { get; set; }

        [Range(0, 1)]
        [NinjaScriptProperty]
        [Display(Name = "Highlight Opacity", Description = "Opacity of the highlight (0 to 1)", Order = 10, GroupName = "Display")]
        public double HighlightOpacity { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name = "Opening Range Start Time", Description = "Start time of the Opening Range", Order = 0, GroupName = "Opening Range")]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        public DateTime StartTime { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Opening Range End Time", Description = "End time of the Opening Range", Order = 1, GroupName = "Opening Range")]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        public DateTime EndTime { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Time Zone", Description = "Select the time zone for the Opening Range", Order = 2, GroupName = "Opening Range")]
        [TypeConverter(typeof(TimeZoneConverter))]
        public string TimeZoneSelection { get; set; }
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
        [Display(Name = "Time Zone Note", Description = "", Order = 3, GroupName = "Opening Range")]
        public string TZNote { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Show Outer Arrows (+1/-1)", Description = "Show arrows for crossing above upper or below lower", Order = 1, GroupName = "Predator X Signals")]
        public bool ShowOuterArrows { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Above Upper Tag", Description = "Tag for arrow when crossing above upper range", Order = 2, GroupName = "Predator X Signals")]
        public string AboveUpperTag { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Below Lower Tag", Description = "Tag for arrow when crossing below lower range", Order = 3, GroupName = "Predator X Signals")]
        public string BelowLowerTag { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name = "Show Inner Arrows (+2/-2)", Description = "Show arrows for crossing above lower or below upper", Order = 4, GroupName = "Predator X Signals")]
        public bool ShowInnerArrows { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Above Lower Tag", Description = "Tag for arrow when crossing above lower range", Order = 7, GroupName = "Predator X Signals")]
        public string AboveLowerTag { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Below Upper Tag", Description = "Tag for arrow when crossing below upper range", Order = 8, GroupName = "Predator X Signals")]
        public string BelowUpperTag { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Show Day Start Arrows", Description = "Show arrows at midnight/new day", Order = 9, GroupName = "Predator X Signals")]
        public bool ShowDayStartArrows { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Show Range Start Arrows", Description = "Show arrows at start of ORB range", Order = 10, GroupName = "Predator X Signals")]
        public bool ShowRangeStartArrows { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Day/Range Start Up Arrow Tag", Description = "Tag for up arrow at session start", Order = 11, GroupName = "Predator X Signals")]
        public string SessionStartUpTag { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Day/Range Start Down Arrow Tag", Description = "Tag for down arrow at session start", Order = 12, GroupName = "Predator X Signals")]
        public string SessionStartDownTag { get; set; }
        
        #endregion

        #region Exposed Plots for Strategy Builder
        [Browsable(false)]
        [XmlIgnore]
        public Series<double> ORHigh
        {
            get { return Values[0]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> ORLow
        {
            get { return Values[1]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Signal
        {
            get { return Values[2]; }
        }
        #endregion

        #region Time Zone Converter
        private class TimeZoneConverter : TypeConverter
        {
            public override bool GetStandardValuesSupported(ITypeDescriptorContext context) => true;

            public override StandardValuesCollection GetStandardValues(ITypeDescriptorContext context)
            {
                return new StandardValuesCollection(new[] { 
                    "Local", "EST", "CST", "MST", "PST", "UTC", 
                    "GMT", "CET", "EET", "JST", "AEST" 
                });
            }

            public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
            {
                return sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);
            }

            public override object ConvertFrom(ITypeDescriptorContext context, System.Globalization.CultureInfo culture, object value)
            {
                if (value is string str)
                    return str;
                return base.ConvertFrom(context, culture, value);
            }
        }
        #endregion
        
        #region TradeSaber Social
        
        private Brush axisColor;
        
        private bool YouTubeButtonClicked;
        private bool DiscordButtonClicked;
        private bool tradeSaberButtonClicked;
        
        private System.Windows.Controls.Button YouTubeButton;
        private System.Windows.Controls.Button DiscordButton;
        private System.Windows.Controls.Button tradeSaberButton;
        private System.Windows.Controls.Button closeSocialsButton;
        
        private System.Windows.Controls.Grid myGrid29;
        
        #region TradeSaber Properties
    
        [NinjaScriptProperty]
        [Display(Name="Show Social Media Buttons", Description="", Order=1, GroupName="TradeSaber Socials")]
        public bool ShowSocials
        { get; set; }
        
        [ReadOnly(true)]
        [NinjaScriptProperty]
        [Display(Name="Author", Description="", Order=2, GroupName="TradeSaber Socials")]
        public string Author
        { get; set; }
        
        [ReadOnly(true)]
        [NinjaScriptProperty]
        [Display(Name="Version", Description="", Order=3, GroupName="TradeSaber Socials")]
        public string Version
        { get; set; }
        
        [ReadOnly(true)]
        [NinjaScriptProperty]
        [Display(Name="TradeSaber Link", Description="", Order=4, GroupName="TradeSaber Socials")]
        public string TradeSaber
        { get; set; }
        
        [ReadOnly(true)]
        [NinjaScriptProperty]
        [Display(Name="Discord", Description="", Order=5, GroupName="TradeSaber Socials")]
        public string Discord
        { get; set; }
        
        [ReadOnly(true)]
        [NinjaScriptProperty]
        [Display(Name="Youtube", Description="", Order=6, GroupName="TradeSaber Socials")]
        public string YouTube
        { get; set; }
        
        #endregion
        
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Plots the Opening Range High and Low with signals and customizable options.";
                Name = "ORB_TradeSaber";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;

                StartTime = new DateTime(2000, 1, 1, 6, 30, 0);
                EndTime = new DateTime(2000, 1, 1, 6, 50, 0);
                TimeZoneSelection = "Local"; // Default to Local
				TZNote = "Recommended that PC and \nNinjaTrader clocks match";
                ShowOuterArrows = true;
                ShowInnerArrows = true;
                AboveUpperTag = "AboveUpper";
                BelowLowerTag = "BelowLower";
                AboveLowerTag = "AboveLower";
                BelowUpperTag = "BelowUpper";
                HighlightRange = true;
                HighlightOpacity = 0.2;
                ShowDayStartArrows = false;
                ShowRangeStartArrows = false;
                SessionStartUpTag = "AboveLower";
                SessionStartDownTag = "BelowUpper";

                AddPlot(new Stroke(Brushes.Cyan), PlotStyle.Line, "ORHigh");
                AddPlot(new Stroke(Brushes.Yellow), PlotStyle.Line, "ORLow");
                AddPlot(new Stroke(Brushes.Transparent), PlotStyle.Dot, "Signal");
                
                #region TradeSaber Socials
                
                ShowSocials = true;
                Author = "TradeSaber - Built With Grok";
                Version = "Version 1.1 // March 2025";
                YouTube = "https://youtu.be/jUYT-Erzc_8";
                Discord = "https://Discord.gg/2YU9GDme8j";
                TradeSaber = "https://tradesaber.com/predator-guide/";
                
                #endregion
            }
            else if (State == State.Configure)
            {
                sessionHigh = double.MinValue;
                sessionLow = double.MaxValue;
                isInRange = false;
                highSeries = new Series<double>(this, MaximumBarsLookBack.Infinite);
                lowSeries = new Series<double>(this, MaximumBarsLookBack.Infinite);
                lastBarDate = DateTime.MinValue;
            }
            else if (State == State.DataLoaded)
            {
                if (ChartControl != null)
                {	
                    axisColor = ChartControl.Properties.AxisPen.Brush;
                }
            }
            
            else if (State == State.Historical)
            {
                if (ShowSocials)
                {
                    if (UserControlCollection.Contains(myGrid29))
                        return;
                    
                    Dispatcher.InvokeAsync((() =>
                    {
                        myGrid29 = new System.Windows.Controls.Grid
                        {
                            Name = "MyCustomGrid", 
                            HorizontalAlignment = HorizontalAlignment.Left, 
                            VerticalAlignment = VerticalAlignment.Bottom,
                        };
                        
                        System.Windows.Controls.ColumnDefinition column1 = new System.Windows.Controls.ColumnDefinition();
                        System.Windows.Controls.ColumnDefinition column2 = new System.Windows.Controls.ColumnDefinition();
                        System.Windows.Controls.ColumnDefinition column3 = new System.Windows.Controls.ColumnDefinition();
                        System.Windows.Controls.ColumnDefinition column4 = new System.Windows.Controls.ColumnDefinition();
                        
                        myGrid29.ColumnDefinitions.Add(column1);
                        myGrid29.ColumnDefinitions.Add(column2);
                        myGrid29.ColumnDefinitions.Add(column3);
                        myGrid29.ColumnDefinitions.Add(column4);
                        
                        YouTubeButton = new System.Windows.Controls.Button
                        {
                            Name = "YoutubeButton", 
                            Content = "Youtube", 
                            Foreground = Brushes.White, 
                            Background = Brushes.Red,
                            Margin = new Thickness(0,0,0,0),
                            Padding = new Thickness(0,0,0,0),
                            FontSize = 12,
                            Height = 18,
                            Width = 45
                        };
                        
                        DiscordButton = new System.Windows.Controls.Button
                        {
                            Name = "DiscordButton", 
                            Content = "Discord", 
                            Foreground = Brushes.White, 
                            Background = Brushes.RoyalBlue,
                            Margin = new Thickness(0,0,0,0),
                            Padding = new Thickness(0,0,0,0),
                            FontSize = 12,
                            Height = 18,
                            Width = 45
                        };
                        
                        tradeSaberButton = new System.Windows.Controls.Button
                        {
                            Name = "TradeSaberButton", 
                            Content = "TradeSaber", 
                            Foreground = Brushes.White, 
                            Background = Brushes.DarkOrange,
                            Margin = new Thickness(0,0,0,0),
                            Padding = new Thickness(0,0,0,0),
                            FontSize = 12,
                            Height = 18,
                            Width = 45
                        };
                        closeSocialsButton = new System.Windows.Controls.Button
                        {
                            Name = "CloseSocialsButton", 
                            Content = "❌", 
                            Foreground = axisColor, 
                            Background = Brushes.Transparent,
                            BorderBrush = Brushes.Transparent,
                            HorizontalContentAlignment = HorizontalAlignment.Left,
                            Margin = new Thickness(0,0,0,0),
                            Padding = new Thickness(0,0,0,0),
                            FontSize = 12,
                            Height = 18,
                            Width = 15
                        };
                        
                        YouTubeButton.Click += OnButtonClick;
                        DiscordButton.Click += OnButtonClick;
                        tradeSaberButton.Click += OnButtonClick;
                        closeSocialsButton.Click += OnButtonClick;
                        
                        System.Windows.Controls.Grid.SetColumn(YouTubeButton, 0);
                        System.Windows.Controls.Grid.SetColumn(DiscordButton, 1);
                        System.Windows.Controls.Grid.SetColumn(tradeSaberButton, 2);
                        System.Windows.Controls.Grid.SetColumn(closeSocialsButton, 3);
                        
                        myGrid29.Children.Add(YouTubeButton);
                        myGrid29.Children.Add(DiscordButton);
                        myGrid29.Children.Add(tradeSaberButton);
                        myGrid29.Children.Add(closeSocialsButton);
                        
                        UserControlCollection.Add(myGrid29);
                    }));
                }
            }	
            else if (State == State.Terminated)
            {
                if (ShowSocials)
                {
                    Dispatcher.InvokeAsync((() =>
                    {
                        if (myGrid29 != null)
                        {
                            if (YouTubeButton != null)
                            {
                                myGrid29.Children.Remove(YouTubeButton);
                                YouTubeButton.Click -= OnButtonClick;
                                YouTubeButton = null;
                            }
                            
                            if (DiscordButton != null)
                            {
                                myGrid29.Children.Remove(DiscordButton);
                                DiscordButton.Click -= OnButtonClick;
                                DiscordButton = null;
                            }
                            
                            if (tradeSaberButton != null)
                            {
                                myGrid29.Children.Remove(tradeSaberButton);
                                tradeSaberButton.Click -= OnButtonClick;
                                tradeSaberButton = null;
                            }	
                            if (closeSocialsButton != null)
                            {
                                myGrid29.Children.Remove(closeSocialsButton);
                                closeSocialsButton.Click -= OnButtonClick;
                                closeSocialsButton = null;
                            }
                        }
                    }));
                }
            }
        }

        private void OnButtonClick(object sender, RoutedEventArgs rea)
        {
            System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
            
            if (ShowSocials)
            {
                if (button == YouTubeButton && button.Name == "YoutubeButton" && button.Content == "Youtube")
                {
                    System.Diagnostics.Process.Start(YouTube);
                    return;
                }
                
                if (button == DiscordButton && button.Name == "DiscordButton" && button.Content == "Discord")
                {	
                    System.Diagnostics.Process.Start(Discord);
                    return;
                }
                
                if (button == tradeSaberButton && button.Name == "TradeSaberButton" && button.Content == "TradeSaber")
                {	
                    System.Diagnostics.Process.Start(TradeSaber);
                    return;
                }
                
                if (button == closeSocialsButton && button.Name == "CloseSocialsButton" && button.Content == "❌")
                {	
                    YouTubeButton.Visibility = Visibility.Collapsed;
                    DiscordButton.Visibility = Visibility.Collapsed;
                    tradeSaberButton.Visibility = Visibility.Collapsed;
                    closeSocialsButton.Visibility = Visibility.Collapsed;
                    return;
                }
            }
        }
    
        public override string DisplayName
        {
            get 
            { 
                if (State == State.SetDefaults) 
                    return Name; 
                else 
                    return "ORB - TradeSaber"; 
            }
        }
        
        protected override void OnBarUpdate()
        {
            TimeZoneInfo selectedTimeZone;
            try
            {
                switch (TimeZoneSelection)
                {
                    case "Local": selectedTimeZone = TimeZoneInfo.Local; break;
                    case "EST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"); break;
                    case "CST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time"); break;
                    case "MST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Mountain Standard Time"); break;
                    case "PST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time"); break;
                    case "UTC": selectedTimeZone = TimeZoneInfo.Utc; break;
                    case "GMT": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time"); break;
                    case "CET": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time"); break;
                    case "EET": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("E. Europe Standard Time"); break;
                    case "JST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Tokyo Standard Time"); break;
                    case "AEST": selectedTimeZone = TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"); break;
                    default: selectedTimeZone = TimeZoneInfo.Local; break;
                }
            }
            catch (TimeZoneNotFoundException)
            {
                selectedTimeZone = TimeZoneInfo.Local;
                Print("Warning: Selected time zone not found. Defaulting to local system timezone.");
            }

            DateTime barTime = Time[0];
            DateTime adjustedTime = TimeZoneInfo.ConvertTime(barTime, TimeZoneInfo.Local, selectedTimeZone);

            adjustedStartTime = new DateTime(adjustedTime.Year, adjustedTime.Month, adjustedTime.Day, StartTime.Hour, StartTime.Minute, StartTime.Second);
            adjustedEndTime = new DateTime(adjustedTime.Year, adjustedTime.Month, adjustedTime.Day, EndTime.Hour, EndTime.Minute, EndTime.Second);

            if (CurrentBar > 0)
            {
                DateTime previousAdjustedTime = TimeZoneInfo.ConvertTime(Time[1], TimeZoneInfo.Local, selectedTimeZone);
                if (adjustedTime.Date > previousAdjustedTime.Date)
                {
                    sessionHigh = double.MinValue;
                    sessionLow = double.MaxValue;
                    isInRange = false;
                    Print($"Reset triggered for {TimeZoneSelection} at {adjustedTime} - New day detected.");

                    if (ShowDayStartArrows)
                    {
                        Draw.ArrowUp(this, SessionStartUpTag + CurrentBar, false, 0, Low[0], Brushes.LightGreen);
                        Draw.ArrowDown(this, SessionStartDownTag + CurrentBar, false, 0, High[0], Brushes.Pink);
                    }
                }
            }

            if (adjustedTime == adjustedStartTime && ShowRangeStartArrows)
            {
                Draw.ArrowUp(this, SessionStartUpTag + CurrentBar, false, 0, Low[0], Brushes.LightGreen);
                Draw.ArrowDown(this, SessionStartDownTag + CurrentBar, false, 0, High[0], Brushes.Pink);
            }

            if (adjustedTime >= adjustedStartTime && adjustedTime <= adjustedEndTime)
            {
                isInRange = true;
                sessionHigh = Math.Max(sessionHigh, High[0]);
                sessionLow = Math.Min(sessionLow, Low[0]);
                highSeries[0] = sessionHigh;
                lowSeries[0] = sessionLow;

                if (HighlightRange && sessionHigh != double.MinValue && sessionLow != double.MaxValue)
                {
                    Draw.Region(this, "ORHighlight" + CurrentBar, adjustedStartTime, Time[0], highSeries, lowSeries, null, Brushes.Gray, (int)(HighlightOpacity * 100));
                }
            }
            else if (adjustedTime > adjustedEndTime && isInRange)
            {
                isInRange = false;
                if (HighlightRange && sessionHigh != double.MinValue && sessionLow != double.MaxValue)
                {
                    Draw.Region(this, "ORHighlight" + CurrentBar, adjustedStartTime, adjustedEndTime, highSeries, lowSeries, null, Brushes.Gray, (int)(HighlightOpacity * 100));
                }
            }

            if (sessionHigh != double.MinValue)
                Values[0][0] = sessionHigh;
            if (sessionLow != double.MaxValue)
                Values[1][0] = sessionLow;
            Values[2][0] = 0;

            if (CurrentBar >= 1)
            {
                double priorClose = Closes[0][1];
                double currentClose = Close[0];

                if (isInRange)
                    Values[2][0] = 0;
                else
                {
                    if (priorClose <= sessionHigh && currentClose > sessionHigh)
                    {
                        Values[2][0] = 1;
                        if (ShowOuterArrows)
                            Draw.ArrowUp(this, AboveUpperTag + CurrentBar, false, 0, Low[0], Brushes.Green);
                    }
                    else if (priorClose >= sessionLow && currentClose < sessionLow)
                    {
                        Values[2][0] = -1;
                        if (ShowOuterArrows)
                            Draw.ArrowDown(this, BelowLowerTag + CurrentBar, false, 0, High[0], Brushes.Red);
                    }
                    else if (priorClose <= sessionLow && currentClose > sessionLow && currentClose < sessionHigh)
                    {
                        Values[2][0] = 2;
                        if (ShowInnerArrows)
                            Draw.ArrowUp(this, AboveLowerTag + CurrentBar, false, 0, Low[0], Brushes.LightGreen);
                    }
                    else if (priorClose >= sessionHigh && currentClose < sessionHigh && currentClose > sessionLow)
                    {
                        Values[2][0] = -2;
                        if (ShowInnerArrows)
                            Draw.ArrowDown(this, BelowUpperTag + CurrentBar, false, 0, High[0], Brushes.Pink);
                    }
                }
            }

            lastBarDate = adjustedTime.Date;
        }
    }
}


//Draw.RegionHighlightY(this, "ORHighlight" + CurrentBar, true, sessionHigh, sessionLow, Brushes.Gray, Brushes.Gray, 20);

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private TradeSaber.ORB_TradeSaber[] cacheORB_TradeSaber;
		public TradeSaber.ORB_TradeSaber ORB_TradeSaber(bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			return ORB_TradeSaber(Input, highlightRange, highlightOpacity, startTime, endTime, timeZoneSelection, tZNote, showOuterArrows, aboveUpperTag, belowLowerTag, showInnerArrows, aboveLowerTag, belowUpperTag, showDayStartArrows, showRangeStartArrows, sessionStartUpTag, sessionStartDownTag, showSocials, author, version, tradeSaber, discord, youTube);
		}

		public TradeSaber.ORB_TradeSaber ORB_TradeSaber(ISeries<double> input, bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			if (cacheORB_TradeSaber != null)
				for (int idx = 0; idx < cacheORB_TradeSaber.Length; idx++)
					if (cacheORB_TradeSaber[idx] != null && cacheORB_TradeSaber[idx].HighlightRange == highlightRange && cacheORB_TradeSaber[idx].HighlightOpacity == highlightOpacity && cacheORB_TradeSaber[idx].StartTime == startTime && cacheORB_TradeSaber[idx].EndTime == endTime && cacheORB_TradeSaber[idx].TimeZoneSelection == timeZoneSelection && cacheORB_TradeSaber[idx].TZNote == tZNote && cacheORB_TradeSaber[idx].ShowOuterArrows == showOuterArrows && cacheORB_TradeSaber[idx].AboveUpperTag == aboveUpperTag && cacheORB_TradeSaber[idx].BelowLowerTag == belowLowerTag && cacheORB_TradeSaber[idx].ShowInnerArrows == showInnerArrows && cacheORB_TradeSaber[idx].AboveLowerTag == aboveLowerTag && cacheORB_TradeSaber[idx].BelowUpperTag == belowUpperTag && cacheORB_TradeSaber[idx].ShowDayStartArrows == showDayStartArrows && cacheORB_TradeSaber[idx].ShowRangeStartArrows == showRangeStartArrows && cacheORB_TradeSaber[idx].SessionStartUpTag == sessionStartUpTag && cacheORB_TradeSaber[idx].SessionStartDownTag == sessionStartDownTag && cacheORB_TradeSaber[idx].ShowSocials == showSocials && cacheORB_TradeSaber[idx].Author == author && cacheORB_TradeSaber[idx].Version == version && cacheORB_TradeSaber[idx].TradeSaber == tradeSaber && cacheORB_TradeSaber[idx].Discord == discord && cacheORB_TradeSaber[idx].YouTube == youTube && cacheORB_TradeSaber[idx].EqualsInput(input))
						return cacheORB_TradeSaber[idx];
			return CacheIndicator<TradeSaber.ORB_TradeSaber>(new TradeSaber.ORB_TradeSaber(){ HighlightRange = highlightRange, HighlightOpacity = highlightOpacity, StartTime = startTime, EndTime = endTime, TimeZoneSelection = timeZoneSelection, TZNote = tZNote, ShowOuterArrows = showOuterArrows, AboveUpperTag = aboveUpperTag, BelowLowerTag = belowLowerTag, ShowInnerArrows = showInnerArrows, AboveLowerTag = aboveLowerTag, BelowUpperTag = belowUpperTag, ShowDayStartArrows = showDayStartArrows, ShowRangeStartArrows = showRangeStartArrows, SessionStartUpTag = sessionStartUpTag, SessionStartDownTag = sessionStartDownTag, ShowSocials = showSocials, Author = author, Version = version, TradeSaber = tradeSaber, Discord = discord, YouTube = youTube }, input, ref cacheORB_TradeSaber);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.TradeSaber.ORB_TradeSaber ORB_TradeSaber(bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			return indicator.ORB_TradeSaber(Input, highlightRange, highlightOpacity, startTime, endTime, timeZoneSelection, tZNote, showOuterArrows, aboveUpperTag, belowLowerTag, showInnerArrows, aboveLowerTag, belowUpperTag, showDayStartArrows, showRangeStartArrows, sessionStartUpTag, sessionStartDownTag, showSocials, author, version, tradeSaber, discord, youTube);
		}

		public Indicators.TradeSaber.ORB_TradeSaber ORB_TradeSaber(ISeries<double> input , bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			return indicator.ORB_TradeSaber(input, highlightRange, highlightOpacity, startTime, endTime, timeZoneSelection, tZNote, showOuterArrows, aboveUpperTag, belowLowerTag, showInnerArrows, aboveLowerTag, belowUpperTag, showDayStartArrows, showRangeStartArrows, sessionStartUpTag, sessionStartDownTag, showSocials, author, version, tradeSaber, discord, youTube);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.TradeSaber.ORB_TradeSaber ORB_TradeSaber(bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			return indicator.ORB_TradeSaber(Input, highlightRange, highlightOpacity, startTime, endTime, timeZoneSelection, tZNote, showOuterArrows, aboveUpperTag, belowLowerTag, showInnerArrows, aboveLowerTag, belowUpperTag, showDayStartArrows, showRangeStartArrows, sessionStartUpTag, sessionStartDownTag, showSocials, author, version, tradeSaber, discord, youTube);
		}

		public Indicators.TradeSaber.ORB_TradeSaber ORB_TradeSaber(ISeries<double> input , bool highlightRange, double highlightOpacity, DateTime startTime, DateTime endTime, string timeZoneSelection, string tZNote, bool showOuterArrows, string aboveUpperTag, string belowLowerTag, bool showInnerArrows, string aboveLowerTag, string belowUpperTag, bool showDayStartArrows, bool showRangeStartArrows, string sessionStartUpTag, string sessionStartDownTag, bool showSocials, string author, string version, string tradeSaber, string discord, string youTube)
		{
			return indicator.ORB_TradeSaber(input, highlightRange, highlightOpacity, startTime, endTime, timeZoneSelection, tZNote, showOuterArrows, aboveUpperTag, belowLowerTag, showInnerArrows, aboveLowerTag, belowUpperTag, showDayStartArrows, showRangeStartArrows, sessionStartUpTag, sessionStartDownTag, showSocials, author, version, tradeSaber, discord, youTube);
		}
	}
}

#endregion
