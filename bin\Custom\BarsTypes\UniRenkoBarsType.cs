#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;

#endregion

namespace NinjaTrader.NinjaScript.BarsTypes
{
	public class UniRenkoBarsType : BarsType
	{
		private int		barDirection;
		private double	barHigh, barLow, barMax, barMin, fakeOpen, openOffset, reversalOffset, thisClose, tickSize, trendOffset;
		private long	barVolume;
		private bool	isNewSession, maxExceeded, minExceeded, calculatedOnce;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description							= @"UniRenko";
				Name								= "UniRenko";
				BarsPeriod							= new BarsPeriod { BarsPeriodType = (BarsPeriodType) 2018, BarsPeriodTypeName = "UniRenko(2018)", Value = 1 };
				BuiltFrom							= BarsPeriodType.Tick;
				DefaultChartStyle					= Gui.Chart.ChartStyleType.CandleStick;
				DaysToLoad							= 3;
				IsIntraday							= true;
				IsTimeBased							= false;
			}
			else if (State == State.Configure)
			{
				barDirection	= 0;
				openOffset		= 0;

				Properties.Remove(Properties.Find("BaseBarsPeriodType", true));
				Properties.Remove(Properties.Find("PointAndFigurePriceType", true));
				Properties.Remove(Properties.Find("ReversalType", true));

				SetPropertyName("BaseBarsPeriodValue", "Open Offset");
				SetPropertyName("Value", "Tick Trend");
				SetPropertyName("Value2", "Tick Reversal");

				Name			= string.Format("{0} UniRenko T{0}R{1}O{2}", BarsPeriod.Value, BarsPeriod.Value2, BarsPeriod.BaseBarsPeriodValue);
			}
		}

		public override void ApplyDefaultBasePeriodValue(BarsPeriod period) { }

		public override void ApplyDefaultValue(BarsPeriod period)
		{
			period.BaseBarsPeriodValue	= 2;
			period.Value				= 2;
			period.Value2				= 4;
		}

		public override string ChartLabel(DateTime dateTime)
		{
			return dateTime.ToString("T", Core.Globals.GeneralOptions.CurrentCulture);
		}

		public override int GetInitialLookBackDays(BarsPeriod barsPeriod, TradingHours tradingHours, int barsBack)
		{
			return 3;
		}

		public override double GetPercentComplete(Bars bars, DateTime now)
		{
			return 1.0d;
		}

		protected override void OnDataPoint(Bars bars, double open, double high, double low, double close, DateTime time, long volume, bool isBar, double bid, double ask)
		{
			if (SessionIterator == null)
				SessionIterator = new SessionIterator(bars);

			isNewSession	= SessionIterator.IsNewSession(time, isBar);

			if (isNewSession)
				SessionIterator.GetNextSession(time, isBar);

			// calculate values if Bars object is midsession
			if (!calculatedOnce)
			{
				tickSize 		= bars.Instrument.MasterInstrument.TickSize;
				trendOffset 	= bars.BarsPeriod.Value * tickSize;
				reversalOffset 	= bars.BarsPeriod.Value2 * tickSize;

				openOffset 		= Math.Ceiling((double)bars.BarsPeriod.BaseBarsPeriodValue * 1) * tickSize;

				barMax 			= close + (trendOffset * barDirection);
				barMin 			= close - (trendOffset * barDirection);
				calculatedOnce 	= true;
			}

			// First Bar
			if ((bars.Count == 0) || bars.IsResetOnNewTradingDay && isNewSession)
			{
				tickSize		= bars.Instrument.MasterInstrument.TickSize;
				trendOffset		= bars.BarsPeriod.Value * tickSize;
				reversalOffset	= bars.BarsPeriod.Value2 * tickSize;
				
				openOffset		= Math.Ceiling((double)bars.BarsPeriod.BaseBarsPeriodValue * 1) * tickSize;

				barMax			= close + (trendOffset * barDirection);
				barMin			= close - (trendOffset * barDirection);

				AddBar(bars, close, close, close, close, time, volume);
			}

			// Subsequent Bars
			else
			{
				//Data.Bar bar = (Bar)bars.Get(bars.Count - 1);
				maxExceeded		= bars.Instrument.MasterInstrument.Compare(close, barMax) > 0 ? true : false;
				minExceeded		= bars.Instrument.MasterInstrument.Compare(close, barMin) < 0 ? true : false;

				barHigh			= bars.GetHigh(bars.Count - 1);
				barLow			= bars.GetLow(bars.Count - 1);
				barVolume		= bars.GetVolume(bars.Count - 1);

				// Defined Range Exceeded?
				if (maxExceeded || minExceeded)
				{
					thisClose		= maxExceeded ? Math.Min(close, barMax) : minExceeded ? Math.Max(close, barMin) : close;
					barDirection	= maxExceeded ? 1 : minExceeded ? -1 : 0;
					fakeOpen		= thisClose - (openOffset * barDirection); // Fake Open is halfway down the bar

					// Close Current Bar
					UpdateBar(bars, (maxExceeded ? thisClose : barHigh), (minExceeded ? thisClose : barLow), thisClose, time, volume);

					// Add New Bar
					barMax			= thisClose + ((barDirection > 0 ? trendOffset : reversalOffset));
					barMin			= thisClose - ((barDirection > 0 ? reversalOffset : trendOffset));

					AddBar(bars, fakeOpen, (maxExceeded ? thisClose : fakeOpen), (minExceeded ? thisClose : fakeOpen), thisClose, time, volume);
				}

				// Current Bar Still Developing
				else
					UpdateBar(bars, (close > barHigh ? close : barHigh), (close < barLow ? close : barLow), close, time, volume);
			}

			bars.LastPrice	= close;
		}
	}
}
