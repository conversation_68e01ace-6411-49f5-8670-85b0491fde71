﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Acceleration" xml:space="preserve">
    <value>Ускорение</value>
  </data>
  <data name="AccelerationMax" xml:space="preserve">
    <value>Максимальное ускорение</value>
  </data>
  <data name="AccelerationStep" xml:space="preserve">
    <value>Шаг ускорения</value>
  </data>
  <data name="ADLAD" xml:space="preserve">
    <value>AD</value>
  </data>
  <data name="AlertOnBreak" xml:space="preserve">
    <value>Оповещение о перерыве</value>
  </data>
  <data name="AlertOnBreakSound" xml:space="preserve">
    <value>Оповещение по звуку перерыва</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_ModifiedSchiff" xml:space="preserve">
    <value>Modified Schiff</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_Schiff" xml:space="preserve">
    <value>Schiff</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_StandardPitchfork" xml:space="preserve">
    <value>Стандарт</value>
  </data>
  <data name="AskLineLength" xml:space="preserve">
    <value>Длина линии Ask (% от графика)</value>
  </data>
  <data name="AskLineStroke" xml:space="preserve">
    <value>Линия Ask</value>
  </data>
  <data name="AuthDisclosureText1" xml:space="preserve">
    <value>{0} об авторских правах &lt;sup&gt;©&lt;/sup&gt; . Все права защищены. NinjaTrader и логотип NinjaTrader. Рег. США Пат. amp; Тм. Выкл.</value>
  </data>
  <data name="AuthDisclosureText2" xml:space="preserve">
    <value>ПОЛНОЕ РАСКРЫТИЕ РИСКА: Торговля фьючерсами и форекс содержит существенный риск и не для каждого инвестора. Инвестор может потенциально потерять все или больше, чем первоначальные инвестиции. Рисковый капитал - это деньги, которые могут быть потеряны, не ставя под угрозу финансовую безопасность или образ жизни. Для торговли должен использоваться только рисковый капитал, и только те, у кого достаточно рисковый капитал, должны рассматривать торговлю. Прошлые показатели не обязательно свидетельствуют о будущих результатах.</value>
  </data>
  <data name="BandPct" xml:space="preserve">
    <value>Band процент</value>
  </data>
  <data name="BarCount" xml:space="preserve">
    <value>График количества</value>
  </data>
  <data name="BarDown" xml:space="preserve">
    <value>График вниз</value>
  </data>
  <data name="BarSpacing" xml:space="preserve">
    <value>Расстояние между барами</value>
  </data>
  <data name="BarsPeriodType" xml:space="preserve">
    <value>Тип периода баров</value>
  </data>
  <data name="BarsPeriodTypeNameDay" xml:space="preserve">
    <value>День</value>
  </data>
  <data name="BarsPeriodTypeNameHeikenAshi" xml:space="preserve">
    <value>Heiken-Ashi</value>
  </data>
  <data name="BarsPeriodTypeNameKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="BarsPeriodTypeNameLineBreak" xml:space="preserve">
    <value>Линия разрыва</value>
  </data>
  <data name="BarsPeriodTypeNameMinute" xml:space="preserve">
    <value>Минут</value>
  </data>
  <data name="BarsPeriodTypeNameMonth" xml:space="preserve">
    <value>Месяц</value>
  </data>
  <data name="BarsPeriodTypeNamePointAndFigure" xml:space="preserve">
    <value>Точка и Фигура</value>
  </data>
  <data name="BarsPeriodTypeNameRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="BarsPeriodTypeNameRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="BarsPeriodTypeNameSecond" xml:space="preserve">
    <value>Секунда</value>
  </data>
  <data name="BarsPeriodTypeNameTick" xml:space="preserve">
    <value>Тик</value>
  </data>
  <data name="BarsPeriodTypeNameVolume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="BarsPeriodTypeNameWeek" xml:space="preserve">
    <value>Неделя</value>
  </data>
  <data name="BarsPeriodTypeNameYear" xml:space="preserve">
    <value>Год</value>
  </data>
  <data name="BarsPeriodValue" xml:space="preserve">
    <value>Значение периода баров</value>
  </data>
  <data name="BarTimerDisconnectedError" xml:space="preserve">
    <value>Bar timer отключен, поскольку в настоящий момент нет подключения к данным.</value>
  </data>
  <data name="BarTimerSessionTimeError" xml:space="preserve">
    <value>Bar timer отключен, поскольку текущее время находится вне времени сессии или даты окончания графика</value>
  </data>
  <data name="BarTimerTimeBasedError" xml:space="preserve">
    <value>Bar timer работает только с интервалами, основанными на внутридневном времени</value>
  </data>
  <data name="BarTimerTimeRemaining" xml:space="preserve">
    <value>Оставшееся время =</value>
  </data>
  <data name="BarTimerWaitingOnDataError" xml:space="preserve">
    <value>Перед запуском Бар таймер ожидает данные в реальном времени</value>
  </data>
  <data name="BarUp" xml:space="preserve">
    <value>Бар вверх</value>
  </data>
  <data name="BasePeriod" xml:space="preserve">
    <value>Базовый периода</value>
  </data>
  <data name="BidLineLength" xml:space="preserve">
    <value>Длина линии Bid (% от графика)</value>
  </data>
  <data name="BidLineStroke" xml:space="preserve">
    <value>Линия Bid</value>
  </data>
  <data name="BlockTradeSize" xml:space="preserve">
    <value>Размер блочной сделки</value>
  </data>
  <data name="BollingerLowerBand" xml:space="preserve">
    <value>Нижняя полоса</value>
  </data>
  <data name="BollingerMiddleBand" xml:space="preserve">
    <value>Средняя полоса</value>
  </data>
  <data name="BollingerUpperBand" xml:space="preserve">
    <value>Верхняя полоса</value>
  </data>
  <data name="BuySellPressureBuyPressure" xml:space="preserve">
    <value>Давление покупки</value>
  </data>
  <data name="BuySellPressureSellPressure" xml:space="preserve">
    <value>Давление продаж</value>
  </data>
  <data name="BuySellVolumeBuys" xml:space="preserve">
    <value>Покупки</value>
  </data>
  <data name="BuySellVolumeSells" xml:space="preserve">
    <value>Продажи</value>
  </data>
  <data name="CandlestickPatternFound" xml:space="preserve">
    <value>Найден шаблон</value>
  </data>
  <data name="CCILevel1" xml:space="preserve">
    <value>Уровень 1</value>
  </data>
  <data name="CCILevel2" xml:space="preserve">
    <value>Уровень 2</value>
  </data>
  <data name="CCILevelMinus1" xml:space="preserve">
    <value>Уровень -1</value>
  </data>
  <data name="CCILevelMinus2" xml:space="preserve">
    <value>Уровень -2</value>
  </data>
  <data name="ChartSpan_Day" xml:space="preserve">
    <value>1 День</value>
  </data>
  <data name="ChartSpan_Min1" xml:space="preserve">
    <value>1 мин</value>
  </data>
  <data name="ChartSpan_Min15" xml:space="preserve">
    <value>15 мин</value>
  </data>
  <data name="ChartSpan_Min240" xml:space="preserve">
    <value>240 мин</value>
  </data>
  <data name="ChartSpan_Min30" xml:space="preserve">
    <value>30 мин</value>
  </data>
  <data name="ChartSpan_Min5" xml:space="preserve">
    <value>5 мин</value>
  </data>
  <data name="ChartSpan_Min60" xml:space="preserve">
    <value>60 мин</value>
  </data>
  <data name="ChartSpan_Month" xml:space="preserve">
    <value>1 Месяц</value>
  </data>
  <data name="ChartSpan_Week" xml:space="preserve">
    <value>1 Неделя</value>
  </data>
  <data name="ChartSpan_Year" xml:space="preserve">
    <value>1 Год</value>
  </data>
  <data name="ConstantLines1" xml:space="preserve">
    <value>Линия 1</value>
  </data>
  <data name="ConstantLines2" xml:space="preserve">
    <value>Линия 2</value>
  </data>
  <data name="ConstantLines3" xml:space="preserve">
    <value>Линия 3</value>
  </data>
  <data name="ConstantLines4" xml:space="preserve">
    <value>Линия 4</value>
  </data>
  <data name="COT1" xml:space="preserve">
    <value>КОТ 1</value>
  </data>
  <data name="COT2" xml:space="preserve">
    <value>КОТ 2</value>
  </data>
  <data name="COT3" xml:space="preserve">
    <value>КОТ 3</value>
  </data>
  <data name="COT4" xml:space="preserve">
    <value>COT 4</value>
  </data>
  <data name="COT5" xml:space="preserve">
    <value>КОТ 5</value>
  </data>
  <data name="CotDataError" xml:space="preserve">
    <value>Данные COT не поддерживаются для этого инструмента</value>
  </data>
  <data name="CotDataStillDownloading" xml:space="preserve">
    <value>Данные COT все еще загружаются. Пожалуйста, обновите индикатор в считанные мгновения.</value>
  </data>
  <data name="CotDataWarning" xml:space="preserve">
    <value>вы должны позволить "Загрузить данные COT при запуске" для получения самых последних данных COT</value>
  </data>
  <data name="CountDown" xml:space="preserve">
    <value>Обратный отсчет</value>
  </data>
  <data name="CountType_Trades" xml:space="preserve">
    <value>Сделки</value>
  </data>
  <data name="CountType_Volume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="CurrentDayOHLError" xml:space="preserve">
    <value>CurrentDayOHL только работает на внутридневных интервалах</value>
  </data>
  <data name="CurrentDayOHLHigh" xml:space="preserve">
    <value>Цена high сессии инструмента</value>
  </data>
  <data name="CurrentDayOHLLow" xml:space="preserve">
    <value>Цена low сессии инструмента</value>
  </data>
  <data name="CurrentDayOHLOpen" xml:space="preserve">
    <value>Цена на открытии сессии инструмента</value>
  </data>
  <data name="CustomWindowAddOnBuyMarket" xml:space="preserve">
    <value>Купить по рынку</value>
  </data>
  <data name="CustomWindowAddOnSellMarket" xml:space="preserve">
    <value>Продать по рынку</value>
  </data>
  <data name="CustomWindowSampleDescription" xml:space="preserve">
    <value>Описание окна пользователя</value>
  </data>
  <data name="CustomWindowSampleName" xml:space="preserve">
    <value>Пример окна пользователя</value>
  </data>
  <data name="DataBarsTypeDaily" xml:space="preserve">
    <value>Дневной</value>
  </data>
  <data name="DataBarsTypeDay" xml:space="preserve">
    <value>{0} День</value>
  </data>
  <data name="DataBarsTypeMinute" xml:space="preserve">
    <value>{0} Минута{1}</value>
  </data>
  <data name="DataBarsTypeMonth" xml:space="preserve">
    <value>{0} Месяц</value>
  </data>
  <data name="DataBarsTypeMonthly" xml:space="preserve">
    <value>Месячный</value>
  </data>
  <data name="DataBarsTypePointAndFigure" xml:space="preserve">
    <value>{0} Точка и Фигура</value>
  </data>
  <data name="DataBarsTypeRange" xml:space="preserve">
    <value>{0} Диапазон{1}</value>
  </data>
  <data name="DataBarsTypeRenko" xml:space="preserve">
    <value>{0} Renko</value>
  </data>
  <data name="DataBarsTypeSecond" xml:space="preserve">
    <value>{0} Секунда</value>
  </data>
  <data name="DataBarsTypeTick" xml:space="preserve">
    <value>{0} Тик{1}</value>
  </data>
  <data name="DataBarsTypeVolume" xml:space="preserve">
    <value>{0} Объем {1}</value>
  </data>
  <data name="DataBarsTypeWeek" xml:space="preserve">
    <value>{0} Неделя</value>
  </data>
  <data name="DataBarsTypeWeekly" xml:space="preserve">
    <value>Недельный</value>
  </data>
  <data name="DataBarsTypeYear" xml:space="preserve">
    <value>{0} Год</value>
  </data>
  <data name="DataBarsTypeYearly" xml:space="preserve">
    <value>Годовой</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>День</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Дней</value>
  </data>
  <data name="DeviationType" xml:space="preserve">
    <value>Тип отклонения</value>
  </data>
  <data name="DeviationValue" xml:space="preserve">
    <value>Значение отклонения</value>
  </data>
  <data name="DMMinusDI" xml:space="preserve">
    <value>-DI</value>
  </data>
  <data name="DMPlusDI" xml:space="preserve">
    <value>+DI</value>
  </data>
  <data name="DonchianChannelMean" xml:space="preserve">
    <value>Значит</value>
  </data>
  <data name="DownBarColor" xml:space="preserve">
    <value>Цвет графика вниз</value>
  </data>
  <data name="DrawingToolIndicatorDescription" xml:space="preserve">
    <value>Индикатор Drawing tool tile добавляет возможность иметь плавающую плитку на графике, которую можно настроить для быстрого доступа к наиболее часто используемым инструментам рисования.</value>
  </data>
  <data name="DrawingToolIndicatorName" xml:space="preserve">
    <value>Drawing tool tile</value>
  </data>
  <data name="DrawLines" xml:space="preserve">
    <value>Рисовать линии</value>
  </data>
  <data name="EMA1" xml:space="preserve">
    <value>EMA1 период</value>
  </data>
  <data name="EMA2" xml:space="preserve">
    <value>EMA2 период</value>
  </data>
  <data name="EmailSignature" xml:space="preserve">
    <value> Отправлено NinjaTrader</value>
  </data>
  <data name="EnvelopePercentage" xml:space="preserve">
    <value>Конверт процентов</value>
  </data>
  <data name="FacebookServiceName" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="FacebookSignature" xml:space="preserve">
    <value>Отправлено NinjaTrader</value>
  </data>
  <data name="Fast" xml:space="preserve">
    <value>Быстро</value>
  </data>
  <data name="FastLimit" xml:space="preserve">
    <value>Ограничение ускорения</value>
  </data>
  <data name="FastPeriod" xml:space="preserve">
    <value>Период ускорения</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeLeft" xml:space="preserve">
    <value>Крайняя левая</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeRight" xml:space="preserve">
    <value>Крайняя правая</value>
  </data>
  <data name="FibonacciTextAlignment_Left" xml:space="preserve">
    <value>Влево</value>
  </data>
  <data name="FibonacciTextAlignment_Off" xml:space="preserve">
    <value>Отключен</value>
  </data>
  <data name="FibonacciTextAlignment_Right" xml:space="preserve">
    <value>Вправо</value>
  </data>
  <data name="FileFilterAnyLoadingDialog" xml:space="preserve">
    <value>Любой (*.*)</value>
  </data>
  <data name="FileFilterAnyWinForms" xml:space="preserve">
    <value>Любой (*.*)|*.*</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>Название файла</value>
  </data>
  <data name="Font" xml:space="preserve">
    <value>Шрифт</value>
  </data>
  <data name="Forecast" xml:space="preserve">
    <value>Прогноз</value>
  </data>
  <data name="GannFanDirection_DownLeft" xml:space="preserve">
    <value>Вниз слева</value>
  </data>
  <data name="GannFanDirection_DownRight" xml:space="preserve">
    <value>Вниз справа</value>
  </data>
  <data name="GannFanDirection_UpLeft" xml:space="preserve">
    <value>Вверх слева</value>
  </data>
  <data name="GannFanDirection_UpRight" xml:space="preserve">
    <value>Вверх справа</value>
  </data>
  <data name="GuiAuthorize" xml:space="preserve">
    <value>Авторизовать</value>
  </data>
  <data name="GuiChartStyleDojiBrush" xml:space="preserve">
    <value>Color for doji bars</value>
  </data>
  <data name="HigherHigh" xml:space="preserve">
    <value>Более высокий high</value>
  </data>
  <data name="HigherLow" xml:space="preserve">
    <value>Более высокий low</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Currency" xml:space="preserve">
    <value>Валюта</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Percent" xml:space="preserve">
    <value>Процент</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Pips" xml:space="preserve">
    <value>Пипсы</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Price" xml:space="preserve">
    <value>Цена</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Ticks" xml:space="preserve">
    <value>Тики</value>
  </data>
  <data name="HLCCalculationMode" xml:space="preserve">
    <value>HLC режим вычесления</value>
  </data>
  <data name="HLCCalculationMode_CalcFromIntradayData" xml:space="preserve">
    <value>Пересчитано на основе внутридневных данных</value>
  </data>
  <data name="HLCCalculationMode_DailyBars" xml:space="preserve">
    <value>Использовать дневные бары</value>
  </data>
  <data name="HLCCalculationMode_UserDefinedValues" xml:space="preserve">
    <value>Использовать значения определенные пользователем</value>
  </data>
  <data name="HLCCalculationModeDescription" xml:space="preserve">
    <value>Приближен для вычесления предыдущего дня HLC значение.</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Импорт</value>
  </data>
  <data name="ImportTypeNinjaTraderBeginningOfBar" xml:space="preserve">
    <value>NinjaTrader (начало бара меток времени)</value>
  </data>
  <data name="ImportTypeNinjaTraderDateTimeFormatError" xml:space="preserve">
    <value>{0}: Дата/Время ошибка формата в строке {1}: {2}: '{3}'</value>
  </data>
  <data name="ImportTypeNinjaTraderEndOfBar" xml:space="preserve">
    <value>NinjaTrader (конец бара меток времени)</value>
  </data>
  <data name="ImportTypeNinjaTraderFieldSeparatorNotIdentified" xml:space="preserve">
    <value>{0}: Импорт разделителя полей не может быть идентифицирован.</value>
  </data>
  <data name="ImportTypeNinjaTraderFormatError" xml:space="preserve">
    <value>{0}: Ошибка формата в строке {1}: {2}: '{3}'</value>
  </data>
  <data name="ImportTypeNinjaTraderInstrumentNotSupported" xml:space="preserve">
    <value>Невозможно импортировать файл '{0}'. Инструмент не поддерживается репозитарием.</value>
  </data>
  <data name="ImportTypeNinjaTraderNumericPriceFormatError" xml:space="preserve">
    <value>{0}: числовой формат цены не поддерживается.</value>
  </data>
  <data name="ImportTypeNinjaTraderUnableReadData" xml:space="preserve">
    <value>Невозможно прочитать данные из файла '{0}': {1}</value>
  </data>
  <data name="ImportTypeNinjaTraderUnexpectedFieldNumber" xml:space="preserve">
    <value>{0}: Неожиданное количество полей в строке '{1}' должно быть 3, 5 или 6</value>
  </data>
  <data name="ImportTypeTickData" xml:space="preserve">
    <value>Tick Data, LLC</value>
  </data>
  <data name="IncrementalPeriod" xml:space="preserve">
    <value>Инкрементальный период</value>
  </data>
  <data name="Intermediate" xml:space="preserve">
    <value>Промежуточный</value>
  </data>
  <data name="Interval" xml:space="preserve">
    <value>Интервал</value>
  </data>
  <data name="KeltnerChannelMidline" xml:space="preserve">
    <value>Средняя линия</value>
  </data>
  <data name="KeyReversalPlot0" xml:space="preserve">
    <value>Постоитель 0</value>
  </data>
  <data name="LastLineLength" xml:space="preserve">
    <value>Длина линии Last (% от графика)</value>
  </data>
  <data name="LastLineStroke" xml:space="preserve">
    <value>Линия Last</value>
  </data>
  <data name="LegendLocation" xml:space="preserve">
    <value>Расположение легенды</value>
  </data>
  <data name="LegendLocation_BottomLeft" xml:space="preserve">
    <value>Внизу Слева</value>
  </data>
  <data name="LegendLocation_BottomRight" xml:space="preserve">
    <value>Внизу Справа</value>
  </data>
  <data name="LegendLocation_Disabled" xml:space="preserve">
    <value>Нетрудоспособный</value>
  </data>
  <data name="LegendLocation_TopLeft" xml:space="preserve">
    <value>Вверху Слева</value>
  </data>
  <data name="LegendLocation_TopRight" xml:space="preserve">
    <value>Вверху Справа</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Длина</value>
  </data>
  <data name="Line1Value" xml:space="preserve">
    <value>Значение линии 1 </value>
  </data>
  <data name="Line2Value" xml:space="preserve">
    <value>Значение линии 2</value>
  </data>
  <data name="Line3Value" xml:space="preserve">
    <value>Значение линии 3</value>
  </data>
  <data name="Line4Value" xml:space="preserve">
    <value>Значение линии 4</value>
  </data>
  <data name="LineColor" xml:space="preserve">
    <value>Цветная линия</value>
  </data>
  <data name="Load" xml:space="preserve">
    <value>Загрузить</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Место нахождени</value>
  </data>
  <data name="LowerHigh" xml:space="preserve">
    <value>Менее высокий high</value>
  </data>
  <data name="LowerLow" xml:space="preserve">
    <value>Более низкий low</value>
  </data>
  <data name="MailCcAddress" xml:space="preserve">
    <value>КУБОВЫЙ:</value>
  </data>
  <data name="MailCcAddressDescription" xml:space="preserve">
    <value>Адрес электронной почты получателя копии. Разделите несколько адресов с помощью ', или ';'</value>
  </data>
  <data name="MailServiceMailAddress" xml:space="preserve">
    <value>Адрес электронной почты</value>
  </data>
  <data name="MailServiceName" xml:space="preserve">
    <value>Электронная почта</value>
  </data>
  <data name="MailServicePort" xml:space="preserve">
    <value>Соединение - Порт</value>
  </data>
  <data name="MailServiceSenderDisplayName" xml:space="preserve">
    <value>От имени</value>
  </data>
  <data name="MailServiceServer" xml:space="preserve">
    <value>Соединение - Сервер</value>
  </data>
  <data name="MailServiceSSL" xml:space="preserve">
    <value>Соединение - SSL</value>
  </data>
  <data name="MailSubject" xml:space="preserve">
    <value>Тема:</value>
  </data>
  <data name="MailSubjectDescription" xml:space="preserve">
    <value>Тема сообщения</value>
  </data>
  <data name="MailToAddress" xml:space="preserve">
    <value>Кому:</value>
  </data>
  <data name="MailToAddressDescription" xml:space="preserve">
    <value>E-mail получателя. Разделите адреса ',' или ';'</value>
  </data>
  <data name="MAMAFAMA" xml:space="preserve">
    <value>FAMA</value>
  </data>
  <data name="MAPeriod" xml:space="preserve">
    <value>Период Среднее скользящее</value>
  </data>
  <data name="MAType" xml:space="preserve">
    <value>Тип Среднее скользящее</value>
  </data>
  <data name="MovingAverage" xml:space="preserve">
    <value>Среднее скользящее</value>
  </data>
  <data name="MovingAverageRibbonPlot1" xml:space="preserve">
    <value>Среднее скользящее 1</value>
  </data>
  <data name="MovingAverageRibbonPlot2" xml:space="preserve">
    <value>Среднее скользящее 2</value>
  </data>
  <data name="MovingAverageRibbonPlot3" xml:space="preserve">
    <value>Среднее скользящее 3</value>
  </data>
  <data name="MovingAverageRibbonPlot4" xml:space="preserve">
    <value>Среднее скользящее 4</value>
  </data>
  <data name="MovingAverageRibbonPlot5" xml:space="preserve">
    <value>Среднее скользящее 5</value>
  </data>
  <data name="MovingAverageRibbonPlot6" xml:space="preserve">
    <value>Среднее скользящее 6</value>
  </data>
  <data name="MovingAverageRibbonPlot7" xml:space="preserve">
    <value>Среднее скользящее 7</value>
  </data>
  <data name="MovingAverageRibbonPlot8" xml:space="preserve">
    <value>Среднее скользящее 8</value>
  </data>
  <data name="NBarsDownTrigger" xml:space="preserve">
    <value>Триггер</value>
  </data>
  <data name="NegativeColor" xml:space="preserve">
    <value>Отрицательный цвет</value>
  </data>
  <data name="NetChangePosition_BottomLeft" xml:space="preserve">
    <value>Внизу Слева</value>
  </data>
  <data name="NetChangePosition_BottomRight" xml:space="preserve">
    <value>Внизу Справа</value>
  </data>
  <data name="NetChangePosition_TopLeft" xml:space="preserve">
    <value>Вверху Слева</value>
  </data>
  <data name="NetChangePosition_TopRight" xml:space="preserve">
    <value>Вверху Справа</value>
  </data>
  <data name="NinjaScriptBackground" xml:space="preserve">
    <value>Справочная информация</value>
  </data>
  <data name="NinjaScriptBarsTypeDay" xml:space="preserve">
    <value>День</value>
  </data>
  <data name="NinjaScriptBarsTypeHeikenAshi" xml:space="preserve">
    <value>Heiken Ashi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagiReversal" xml:space="preserve">
    <value>Реверсивный бар</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreak" xml:space="preserve">
    <value>Линия разрыва</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreakLineBreaks" xml:space="preserve">
    <value>Линия разрывов</value>
  </data>
  <data name="NinjaScriptBarsTypeMinute" xml:space="preserve">
    <value>Минут</value>
  </data>
  <data name="NinjaScriptBarsTypeMonth" xml:space="preserve">
    <value>Месяц</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigure" xml:space="preserve">
    <value>Точка и Фигура</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureBoxSize" xml:space="preserve">
    <value>Размер Box</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureReversal" xml:space="preserve">
    <value>Реверсивный бар</value>
  </data>
  <data name="NinjaScriptBarsTypeRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="NinjaScriptBarsTypeRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="NinjaScriptBarsTypeRenkoBrickSize" xml:space="preserve">
    <value>Размер Brick </value>
  </data>
  <data name="NinjaScriptBarsTypeSecond" xml:space="preserve">
    <value>Секунда</value>
  </data>
  <data name="NinjaScriptBarsTypeTick" xml:space="preserve">
    <value>Тик</value>
  </data>
  <data name="NinjaScriptBarsTypeVolume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="NinjaScriptBarsTypeWeek" xml:space="preserve">
    <value>Неделя</value>
  </data>
  <data name="NinjaScriptBorder" xml:space="preserve">
    <value>Граница</value>
  </data>
  <data name="NinjaScriptChartStyleBarWidth" xml:space="preserve">
    <value>Ширина бара</value>
  </data>
  <data name="NinjaScriptChartStyleBox" xml:space="preserve">
    <value>Бокс</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsColor" xml:space="preserve">
    <value>Цвет для баров вниз</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsOutline" xml:space="preserve">
    <value>Контур графиков вниз</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsColor" xml:space="preserve">
    <value>Цвет для баров вверх</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsOutline" xml:space="preserve">
    <value>Контур баров вверх</value>
  </data>
  <data name="NinjaScriptChartStyleCandleDownBarsColor" xml:space="preserve">
    <value>Цвет для баров вниз</value>
  </data>
  <data name="NinjaScriptChartStyleCandleOutline" xml:space="preserve">
    <value>Контур тела свечи</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestick" xml:space="preserve">
    <value>Свечи</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestickHollow" xml:space="preserve">
    <value>Hollow candlestick</value>
  </data>
  <data name="NinjaScriptChartStyleCandleUpBarsColor" xml:space="preserve">
    <value>Цвет для баров вверх</value>
  </data>
  <data name="NinjaScriptChartStyleCandleWick" xml:space="preserve">
    <value>"Хвост" свечи</value>
  </data>
  <data name="NinjaScriptChartStyleEquivolume" xml:space="preserve">
    <value>Equivolume</value>
  </data>
  <data name="NinjaScriptChartStyleHeikenAshi" xml:space="preserve">
    <value>Heiken Ashi</value>
  </data>
  <data name="NinjaScriptChartStyleKagi" xml:space="preserve">
    <value>Линия Kagi </value>
  </data>
  <data name="NinjaScriptChartStyleKagiThickLine" xml:space="preserve">
    <value>Толстая линия</value>
  </data>
  <data name="NinjaScriptChartStyleKagiThinLine" xml:space="preserve">
    <value>Тонкая линия</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnClose" xml:space="preserve">
    <value>Линия по закрытию</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseColor" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseWidth" xml:space="preserve">
    <value>Ширина линии</value>
  </data>
  <data name="NinjaScriptChartStyleLineWidth" xml:space="preserve">
    <value>Ширина линии</value>
  </data>
  <data name="NinjaScriptChartStyleMountain" xml:space="preserve">
    <value>Гора</value>
  </data>
  <data name="NinjaScriptChartStyleMountainColor" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="NinjaScriptChartStyleMountainOutline" xml:space="preserve">
    <value>Контур</value>
  </data>
  <data name="NinjaScriptChartStyleOHLC" xml:space="preserve">
    <value>OHLC</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcDownBarsColor" xml:space="preserve">
    <value>Цвет для баров вниз</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcUpBarsColor" xml:space="preserve">
    <value>Цвет для баров вверх</value>
  </data>
  <data name="NinjaScriptChartStyleOpenClose" xml:space="preserve">
    <value>Open/Close</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsColor" xml:space="preserve">
    <value>Цвет для баров вниз</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsOutline" xml:space="preserve">
    <value>Контур графиков вниз</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsColor" xml:space="preserve">
    <value>Цвет для баров вверх</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsOutline" xml:space="preserve">
    <value>Контур баров вверх</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigure" xml:space="preserve">
    <value>Точка и Фигура</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureDownColor" xml:space="preserve">
    <value>Цвет вниз</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureUpColor" xml:space="preserve">
    <value>Цвет вверх</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchor" xml:space="preserve">
    <value>Якорь</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorEnd" xml:space="preserve">
    <value>Конец</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorExtension" xml:space="preserve">
    <value>Расширение</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorMiddle" xml:space="preserve">
    <value>Средний</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorStart" xml:space="preserve">
    <value>Начать</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorText" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchfork" xml:space="preserve">
    <value>Andrews pitchfork</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCalculationMethod" xml:space="preserve">
    <value>Метод вычесления</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCategoryStrokes" xml:space="preserve">
    <value>Штрихи</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkDescription" xml:space="preserve">
    <value>Описание Andrews Pitchfork</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtendLinesBack" xml:space="preserve">
    <value>Расширить линии назад</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtensionStroke" xml:space="preserve">
    <value>Расширение линии хода</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkRetracement" xml:space="preserve">
    <value>Откат</value>
  </data>
  <data name="NinjaScriptDrawingToolArc" xml:space="preserve">
    <value>Arc</value>
  </data>
  <data name="NinjaScriptDrawingToolAreaOpacity" xml:space="preserve">
    <value>Непрозрачность - области (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolArrowLine" xml:space="preserve">
    <value>Линия стрелки</value>
  </data>
  <data name="NinjaScriptDrawingToolBackgroundOpacity" xml:space="preserve">
    <value>Непрозрачность фона (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolEllipse" xml:space="preserve">
    <value>Эллипс</value>
  </data>
  <data name="NinjaScriptDrawingToolExtendedLine" xml:space="preserve">
    <value>Расширенная линия</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciCircle" xml:space="preserve">
    <value>Цикл Фибоначчи</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciExtensions" xml:space="preserve">
    <value>Расширения Фибоначчи</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciLevelsBaseAnchorLineStroke" xml:space="preserve">
    <value>Якорь</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracements" xml:space="preserve">
    <value>Откаты Фибоначчи</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesLeft" xml:space="preserve">
    <value>Расширение линий влево</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesRight" xml:space="preserve">
    <value>Расширение линий вправо</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextAlignment" xml:space="preserve">
    <value>Выравнивание текста</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextLocation" xml:space="preserve">
    <value>Место текста</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeCircleDivideTimeSeparately" xml:space="preserve">
    <value>Разделить время/цена раздельно</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensions" xml:space="preserve">
    <value>Расширения времени Фибоначчи</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensionsShowText" xml:space="preserve">
    <value>Показать текст</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFan" xml:space="preserve">
    <value>Веер Ганна</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanDisplayText" xml:space="preserve">
    <value>Отобразить текст</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanFanDirection" xml:space="preserve">
    <value>Направление веера</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanPointsPerBar" xml:space="preserve">
    <value>Точки каждого графика</value>
  </data>
  <data name="NinjaScriptDrawingToolHorizontalLine" xml:space="preserve">
    <value>Горизонтальная линия</value>
  </data>
  <data name="NinjaScriptDrawingToolLine" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="NinjaScriptDrawingToolPath" xml:space="preserve">
    <value>Путь</value>
  </data>
  <data name="NinjaScriptDrawingToolPathBegin" xml:space="preserve">
    <value>Начало пути</value>
  </data>
  <data name="NinjaScriptDrawingToolPathEnd" xml:space="preserve">
    <value>Конец пути</value>
  </data>
  <data name="NinjaScriptDrawingToolPathSegment" xml:space="preserve">
    <value>Сегмент</value>
  </data>
  <data name="NinjaScriptDrawingToolPathShowCount" xml:space="preserve">
    <value>Show count</value>
  </data>
  <data name="NinjaScriptDrawingToolPolygon" xml:space="preserve">
    <value>Многоугольник</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceLevelsOpacity" xml:space="preserve">
    <value>Уровень цен Непрозрачность (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolRay" xml:space="preserve">
    <value>Луч</value>
  </data>
  <data name="NinjaScriptDrawingToolRectangle" xml:space="preserve">
    <value>Прямоугольник</value>
  </data>
  <data name="NinjaScriptDrawingToolRegion" xml:space="preserve">
    <value>Регион</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirection" xml:space="preserve">
    <value>Направление</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirectionStroke" xml:space="preserve">
    <value>Направление Хода</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightHorizontalTextFormat" xml:space="preserve">
    <value>{0} Время баров: {1}</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalRangeUnit" xml:space="preserve">
    <value>Вертикальный диапазон единиц</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalTextFormat" xml:space="preserve">
    <value>Значение Range: {0} {1}</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightX" xml:space="preserve">
    <value>Выделение области X</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightY" xml:space="preserve">
    <value>Выделение области Y</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannel" xml:space="preserve">
    <value>Регрессионный канал</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannel" xml:space="preserve">
    <value>Нижний канал</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannelColor" xml:space="preserve">
    <value>Цвет нижнего канала</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelPriceType" xml:space="preserve">
    <value>Тип цены</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelRegressionChannel" xml:space="preserve">
    <value>Регрессия</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendLeft" xml:space="preserve">
    <value>Расширение влево</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendRight" xml:space="preserve">
    <value>Расширение вправо</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationLowerDistance" xml:space="preserve">
    <value>Дистанция к нижнему каналу</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationUpperDistance" xml:space="preserve">
    <value>Дистанция к верхнему каналу</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelType" xml:space="preserve">
    <value>Режим</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannel" xml:space="preserve">
    <value>Верхний канал</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannelColor" xml:space="preserve">
    <value>Цвет верхнего канала</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorEntry" xml:space="preserve">
    <value>Anchor входа</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorLineStroke" xml:space="preserve">
    <value>Якорь</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorReward" xml:space="preserve">
    <value>Reward anchor</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorRisk" xml:space="preserve">
    <value>Риск якорь</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardCategoryColors" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardDescription" xml:space="preserve">
    <value>Автоматическое вычесление цели основано вне определенного пользователем стоп-лосс</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeEntry" xml:space="preserve">
    <value>Расширение входа</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeReward" xml:space="preserve">
    <value>Reward extension</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeRisk" xml:space="preserve">
    <value>Risk extension</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardName" xml:space="preserve">
    <value>Risk Reward</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardRatio" xml:space="preserve">
    <value>Соотношение</value>
  </data>
  <data name="NinjaScriptDrawingToolRuler" xml:space="preserve">
    <value>Правило</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerDaysFormat" xml:space="preserve">
    <value>{0} дней</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerNumberBarsText" xml:space="preserve">
    <value># бары:</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerTimeText" xml:space="preserve">
    <value>Время</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueDisplayUnit" xml:space="preserve">
    <value>Значение Y показывает единицу</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueText" xml:space="preserve">
    <value>Значение Y:</value>
  </data>
  <data name="NinjaScriptDrawingTools" xml:space="preserve">
    <value>Инструменты для рисования</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowDownMarkerName" xml:space="preserve">
    <value>Стрелка вниз</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowUpMarkerName" xml:space="preserve">
    <value>Стрелка вверх</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDiamondMarkerName" xml:space="preserve">
    <value>Бриллиант</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDotMarkerName" xml:space="preserve">
    <value>Точка</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartSquareMarkerName" xml:space="preserve">
    <value>Квадрат</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleDownMarkerName" xml:space="preserve">
    <value>Треугольник вниз</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleUpMarkerName" xml:space="preserve">
    <value>Треугольник вверх</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioX" xml:space="preserve">
    <value>Соотношение времени</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioY" xml:space="preserve">
    <value>Соотношение цены</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngles" xml:space="preserve">
    <value>Углы Ганна</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAnglesPrompt" xml:space="preserve">
    <value>1 Угол Ганна|{0} Углы Ганны|Добавить угол Ганна..|Изменить угол Ганна...|Изменить углы Ганна...</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesAreaBrush" xml:space="preserve">
    <value>Цвет - места</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesOutlineBrush" xml:space="preserve">
    <value>Цвет - контура</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelIsVisible" xml:space="preserve">
    <value>Видимый</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelLineStroke" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevels" xml:space="preserve">
    <value>Уровни</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelsPrompt" xml:space="preserve">
    <value>1 уровень цены|{0} уровни цен|Добавить уровень цены..|Изменить уровень цены...|Изменить уровени цен...</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelUnset" xml:space="preserve">
    <value>Отозвать</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelValue" xml:space="preserve">
    <value>Значение (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolStroke" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="NinjaScriptDrawingToolText" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="NinjaScriptDrawingToolTextAlignment" xml:space="preserve">
    <value>Выравнивание текста</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBackBrush" xml:space="preserve">
    <value>Кисть фона текста</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBrush" xml:space="preserve">
    <value>Цвет - шрифта</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixed" xml:space="preserve">
    <value>Фиксированный текст</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixedTextPosition" xml:space="preserve">
    <value>Позиция текста</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFont" xml:space="preserve">
    <value>Шрифт</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineStroke" xml:space="preserve">
    <value>Контур</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineVisible" xml:space="preserve">
    <value>Контур - разрешен</value>
  </data>
  <data name="NinjaScriptDrawingToolTimeCycles" xml:space="preserve">
    <value>Циклы времени</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannel" xml:space="preserve">
    <value>Канал тренда</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelDescription" xml:space="preserve">
    <value>Начертить канал тренда используя параллельные линии</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelEnd1AnchorDisplayName" xml:space="preserve">
    <value>Окончание тренда</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelParallelStroke" xml:space="preserve">
    <value>Параллель</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart1AnchorDisplayName" xml:space="preserve">
    <value>Начало тренда</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart2AnchorDisplayName" xml:space="preserve">
    <value>Параллель</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelTrendStroke" xml:space="preserve">
    <value>Тренд</value>
  </data>
  <data name="NinjaScriptDrawingToolTriangle" xml:space="preserve">
    <value>Треугольник</value>
  </data>
  <data name="NinjaScriptDrawingToolVerticalLine" xml:space="preserve">
    <value>Вертикальная линия</value>
  </data>
  <data name="NinjaScriptGeneral" xml:space="preserve">
    <value>Главная</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerAveragePerformanceOffsetPercent" xml:space="preserve">
    <value>Среднее смещение по анализу торговли (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerConvergenceThreshold" xml:space="preserve">
    <value>Порог сходимости</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverIndex" xml:space="preserve">
    <value>Индекс Crosshair</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverRatePercent" xml:space="preserve">
    <value>Перекрестная норма прибыли (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerFastGenerations" xml:space="preserve">
    <value>Быстрое генерирование</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerations" xml:space="preserve">
    <value>Генерирования</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerationSize" xml:space="preserve">
    <value>Размер генерирования</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMinimumPerformance" xml:space="preserve">
    <value>Минимальная эффективность</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationRatePercent" xml:space="preserve">
    <value>Скорость мутации (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationStrengthPercent" xml:space="preserve">
    <value>Сила мутации (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerResetSizePercent" xml:space="preserve">
    <value>Сбросить размер (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerSlowGenerations" xml:space="preserve">
    <value>Медленное генерирование</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerStabilitySizePercent" xml:space="preserve">
    <value>Стабильный размер (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerThresholdGenerations" xml:space="preserve">
    <value>Threshold generations</value>
  </data>
  <data name="NinjaScriptIndicator" xml:space="preserve">
    <value>Индикатор</value>
  </data>
  <data name="NinjaScriptIndicatorAvg" xml:space="preserve">
    <value>Avg</value>
  </data>
  <data name="NinjaScriptIndicatorCount" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="NinjaScriptIndicatorDefault" xml:space="preserve">
    <value>По умолчанию</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADL" xml:space="preserve">
    <value>Изучение накопления/распределения (AD) пытается определить сумму объема текущих входов или выходов по инструменту путем определения периода закрытия позиции в отношении диапазона high/low этого периода .</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADX" xml:space="preserve">
    <value>Average Directional Index измеряет силу преобладающего тренда также как и выявляет существует ли движение на рынке. ADX измеряется по шкале от 0  100. Низкое значение ADX (как правило, меньше, чем 20) может указывать на не-трендовый рынок с низкими объемами, и если выше 20, может указывать на начало тренда (вверх или вниз). Если ADX выше 40 и начинает падать, это может указывать на замедление текущего тренда.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADXR" xml:space="preserve">
    <value>Average Directional Movement Rating определяет количественное изменение импульса в ADX. Он рассчитывается путем добавления двух значений ADX (текущее значение и значение N периодов назад), затем делится на два. Это дополнительное сглаживание делает ADXR менее чувствительным, по сравнению с ADX. Интерпретация такая же как ADX; чем выше значение, тем сильнее тренд.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAPZ" xml:space="preserve">
    <value>APZ (Adaptive Prize Zone) формирует устойчивый канал, основанный на двойных сглаженных экспоненциальных скользящих средних вокруг средней цены. Посмотрите S/C, Сентябрь 2006, стр.28.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroon" xml:space="preserve">
    <value>Aroon индикатор разработан Tushar Chande. Состоит из двух участков: один измеряет число периодов с момента последнего x-периода high (Aroon Вверх) и другой измеряет количество периодов с момента последнего х-периода low (Aroon Вниз).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroonOscillator" xml:space="preserve">
    <value>Aroon Oscillator основан на Индикаторе Aroon. Подобно индикатору Aroon, Aroon Oscillator измеряет силу тренда.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionATR" xml:space="preserve">
    <value>Average True Range (ATR) это мера волатильности. Он был введен Welles Wilder в книге 'New Concepts in Technical Trading Systems' и с тех пор используется как компонента многих индикаторов и торговых систем.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBarTimer" xml:space="preserve">
    <value>Отображает оставшееся время на основе времени бар</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBlockVolume" xml:space="preserve">
    <value>Block volume обнаруживает блочные сделки и отображает, сколько проишло за бар. Это может быть отображено в виде сделок или объема. Иторические тиковые данные необходимы для построения графика.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBollinger" xml:space="preserve">
    <value>Bollinger Bands строятся на стандартном отклонении уровней выше и ниже скользящей средней. Поскольку стандарт отклонения это измерение волатильности, полосы сами регулируются: расширяются когда рыноки волатильны, и сжимаются когда спокойные периоды.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBOP" xml:space="preserve">
    <value>Идикатор balance of power измеряет силу быков против медведей, оценивая способность каждого толкать цену до предельного уровня.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellPressure" xml:space="preserve">
    <value>Показывает процент давления текущей покупка или продажа. Это тик так индикатор. Если 'Вычесление' установлено в "На бар закрытие", значения индикатора всегда будет 100.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellVolume" xml:space="preserve">
    <value>Построители гистограм разделяют значение между торговлей на ask или выше и торговлей на bid и ниже. Работает только на данных с историей если используется Тик Проиграть.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCamarillaPivots" xml:space="preserve">
    <value>Camarilla pivots являются ценовым анализом, который генерирует потенциальные уровни поддержки и сопротивления, умножая предыдущий диапазон, затем добавляя или вычитая его из закрытия.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCandlestickPattern" xml:space="preserve">
    <value>Обнаруживает общие закономерности подсвечника и отмечает их на графике</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCCI" xml:space="preserve">
    <value>Commodity Channel Index (CCI) измеряет отклонение цены ценной бумаги от ее статистического значения. Значения high показывают, что цены необычно высоки по сравнению со средними ценами в то время как значения low указывают, что цены необычно низкие.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinMoneyFlow" xml:space="preserve">
    <value>Вычесление значения количества денежного потока сверх n бар.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinOscillator" xml:space="preserve">
    <value>Рассчитывает моментум накопления распределения линии, используя разницу между двумя экспоненциальными скользящими средними.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinVolatility" xml:space="preserve">
    <value>Сравнивает разницу между текущими инструментами и диапазоном истории используя экспоненциальные скользящие среднии.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChoppinessIndex" xml:space="preserve">
    <value>Индекс Choppiness предназначен для определения, является ли рынок изменчивым (торгуется в боковом направлении) или не изменчивым (торгуется в тренде в любом направлении)</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCMO" xml:space="preserve">
    <value>CMO отличается от других осцилляторов моментума, таких как Relative Strength Index, (RSI) и Stochastics. Он использует дневные данные Вверх и Вниз, в числителе расчета для измерения моментума напрямую. В первую очередь используется для поиска экстремальных состояний перекупок и перепродаж, CMO может также быть использован для поиска трендов.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionConstantLines" xml:space="preserve">
    <value>Построители линий на значениях определенных пользователем.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCorrelation" xml:space="preserve">
    <value>Индикатор корреляции будет спланировать корреляцию серии данных с желаемым инструментом. Значения, близкие к 1, указывают на движение в одном направлении. Значения, близкие к -1, указывают на движение в противоположных направлениях. Значения около 0 указывают на отсутствие корреляции.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCOT" xml:space="preserve">
    <value>Обязательство трейдеров</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCurrentDayOHL" xml:space="preserve">
    <value>Наносит значения сессии в текущем дне: открытие, high, low</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDarvas" xml:space="preserve">
    <value>Darvas Boxes были взяты со страниц книги Nicolas Darvas "How I Made $2,000,000 in the Stock Market". Boxes используются для нормализации тренда. Сигнал купить будет указывать, когда цена акции превышает верх box. Сигнал продать будет указывать когда цена акции падает ниже дна box.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDEMA" xml:space="preserve">
    <value>Double Exponential Moving Average (DEMA) представляет собой комбинацию одного экспоненциального скользящего среднего и двойной экспоненциальной скользящей средней.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDisparityIndex" xml:space="preserve">
    <value>Индекс несоответствия измеряет разницу между ценой и экспоненциальной скользящей средней. Значение большее может указывать на бычий импульс, тогда как значение, меньшее нуля, может указывать на медвежий импульс.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDM" xml:space="preserve">
    <value>Directional Movement, DM. Это такой же индикатор как ADX, с добавлением двух индикаторов направленния движения +DI и -DI. +DI и -DI измеряет моментум повышения и снижения. Сигнал покупки возникает когда +DI пересекает -DI в сторону повышения. Сигнал к продаже возникает когда -DI пересекает +DI в сторону снижения.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMI" xml:space="preserve">
    <value>Directional Movement Index. Индекс очень похож на Welles Wilder's Relative Strength Index. Разница в том, DMI использует переменные периоды времени (от 3 до 30) против фиксированных периодов RSI.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMIndex" xml:space="preserve">
    <value>Dynamic Momentum Index это переменный термин RSI. Термин RSI варьирует от 3 до 30. Переменный период времени делает RSI более восприимчивым к краткосрочным движениям. Чем волатильнее цена, тем короче период времени. Это интерпретируется также как RSI, но обеспечивает сигналы ранее.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDonchianChannel" xml:space="preserve">
    <value>Индикатор Donchian Channel был создан Ричардом Дончианом. Он использует самый высокий максимум и самый низкий минимум из периода времени для построения канала.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDoubleStochastics" xml:space="preserve">
    <value>Индикатор Double Stochastics является вариацией показателя Stochastics, разработанного William Blau.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEaseOfMovement" xml:space="preserve">
    <value>Индикатор Ease of Movement (EMV) подчеркивает дни в которые акции двигаются легко и минимизирует дни в которые акции двигаются с трудом. Сигнал покупки возникает когда EMV переходит выше нуля, и сигнал продажи, когда он переходит ниже нуля. Когда EMV зависает около нуля, тогда движение цены и/или объема high небольшие, который должен сказать, цена не движется легко.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEMA" xml:space="preserve">
    <value>Exponential Moving Average (EMA) индикатор, который показывает среднее значение цены ценной бумаги за период времени. Когда вычисляется скользящяя средняя. EMA делает более весомыми последние цены чем SMA.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFibonacciPivots" xml:space="preserve">
    <value>Fibonacci pivots представляют собой анализ цен, который генерирует потенциальные уровни поддержки и сопротивления, умножая предыдущий диапазон на значения Фибоначчи, затем прибавляя или вычитая его из среднего значения предыдущего high, low и закрытия.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFisherTransform" xml:space="preserve">
    <value>Fisher Transform имеет острые и различные точки поворота, которые происходят своевременно. Полученные пик колебания, используются для определения ценовых разворотов.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFOSC" xml:space="preserve">
    <value>Forecast Oscillator (FOSC) это расширение линейной регрессии базовых индикаторов и популярным его сделал Tushar Chande. Forecast Oscillator строит процентную разницу между ценой прогноза (генерируется линией х-период линейной регрессии) и фактической ценой. Oscillator выше нуля когда прогноз цены больше, чем фактическая цена. И наоборот, меньше чем ноль, если прогноз ниже. В редких случаях прогноз цены и фактическая цена одинаковые, осцилятор будет строить ноль. Фактические цены, которые ниже цены прогноза, предлагают низкие цены впереди. Также, фактические цены, которые выше цены прогноза, предлагают высокие цены впереди. Краткосрочные сделки должны использовать короткие периоды времени и возможно более гибкие стандарты для необходимой длины времени выше или ниже цены прогноза. Долгосрочные сделки должны использовать длинные периоды времени и возможно строгие стандарты для необходимой длины времени выше или ниже цены прогноза. Chande также предлагает строить трех-дневную скользящую среднюю триггер линию Forecast Oscillator для генерации раннего предупреждения изменений в тренде. Когда осциллятор пересекает ниже линии триггера, предлагаются низкие цены. Когда осциллятор пересекает выше линии триггера, предлагаются высокие цены.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionHMA" xml:space="preserve">
    <value>Hull Moving Average (НМА) использует взвешенные расчеты MA, предлагая превосходное сглаживание, и гораздо меньший лаг (отставание), по сравнению с традиционными индикаторами SMA.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKAMA" xml:space="preserve">
    <value>Разработан Перри Кауфман, этот индикатор ЕМА использует Эффективное Соотношение для изменения постоянной сглаживания, которая пролегает от минимума Быстрого Отрезка (Fast  Length) до максимума Медленного Отрезка (Slow  Length). Поскольку эта скользящая средняя адаптивна, она стремится следовать за ценами более близко чем другие MA's.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeltnerChannel" xml:space="preserve">
    <value>Индикатор Keltner Channel подобен индикатору Bollinger Bands. Здесь средняя линия, это стандарт скользящей средней с верхних и нижних полос, смещается за счет SMA разницы между high и low предыдущими барами. Множитель отступа настраивается также как SMA период.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalDown" xml:space="preserve">
    <value>Возвращает значение 1 когда текущее закрытие меньше чем предыдущее закрытие после проникновения самого высокого из последних n баров.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalUp" xml:space="preserve">
    <value>Возвращает значение 1 когда текущее закрытие больше чем предыдущее закрытие после проникновения самого низкого из последних n баров.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinReg" xml:space="preserve">
    <value>Linear Regression это индикатор, предсказывающий значение цены ценной бумаги.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegIntercept" xml:space="preserve">
    <value>Linear Regression Intercept обеспечивает значение перехвата линии тренда линейной регрессии.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegSlope" xml:space="preserve">
    <value>Linear Regression Slope обеспечивает значение наклона линии линейной регрессии.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMACD" xml:space="preserve">
    <value>MACD (Moving Average Convergence/Divergence) -следующий тренду индикатор моментума, показывает соотношение между ценами двух скользящих средних.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAEnvelopes" xml:space="preserve">
    <value>Построение % конвертов вокруг скользящей средней</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAMA" xml:space="preserve">
    <value>MAMA (MESA Adaptive Moving Average) была разработана John Ehlers. Она адаптируется к движению цены в новом и уникальном пути. Адаптация основана на Hilbert Transform Discriminator. Преимущества этого метода: особенности быстрой атаки средней и медленный спад средней. МАМА + FAMA (Following Adaptive Moving Average) линии только пересекающие крупные рыночные развороты.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAX" xml:space="preserve">
    <value>Максимум показывает максимум последнего n бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMcClellanOscillator" xml:space="preserve">
    <value>McClellan Oscillator представляет собой разницу между двумя экспоненциальными скользящими средними в распространении спада на NYSE. Этот показатель требует данных индекса ADV и DECL.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMFI" xml:space="preserve">
    <value>MFI (Money Flow Index) это индикатор моментума, измеряющий силу входящего и исходящего денежного потока ценной бумаги.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMIN" xml:space="preserve">
    <value>Минимум показывает минимум последнего n бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMomentum" xml:space="preserve">
    <value>Momentum indicator измеряет сумму изменения цены ценной бумаги за заданный период времени.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMoneyFlowOscillator" xml:space="preserve">
    <value>Money Flow Oscillator измеряет объем денежного потока за определенный период. Переход на положительную территорию указывает на давление на покупку, в то время как переход на отрицательную территорию указывает на давление продажи.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMovingAverageRibbon" xml:space="preserve">
    <value>Moving Average Ribbon представляет собой ряд увеличивающихся скользящих средних.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsDown" xml:space="preserve">
    <value>Этот индикатор возвращает 1 когда мы имеем n последовательных баров вниз, по-другому возвращает 0. Вниз бар определяется как бар где закрытие ниже открытия и бары делаются ниже high и ниже low. Вы можете настроить специфичные требуемые опции индикатора.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsUp" xml:space="preserve">
    <value>Этот индикатор возвращает 1 когда мы имеем n последовательных баров вверх, по-другому возвращает 0. Вверх бар определяется как бар где закрытие выше открытия и бары делаются выше high и выше low. Вы можете настроить специфичные требуемые опции индикатора.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNetChangeDisplay" xml:space="preserve">
    <value>Отображает изменение на графике.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionOBV" xml:space="preserve">
    <value>OBV, On Balance Volume управляет общим объемом. Показывает если объем течет в или из ценных бумаг. Когда ценные бумаги закрываются выше чем предыдущее закрытие, весь дневной объем считается вверх-объем. Когда бумаги закрываются ниже чем предыдущее закрытие, весь дневной объем считается вниз-объем.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionParabolicSAR" xml:space="preserve">
    <value>Parabolic SAR согласно журналу Акции и Сырьевые Товары V 11:11 (477-479).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPFE" xml:space="preserve">
    <value>PFE (Polarized Fractal Efficiency) индикатор, который использует фрактальную геометрию для определения насколько эффективно движется цена.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPivots" xml:space="preserve">
    <value>Индикатор Pivots (Pivot Points) отображает средние значения High, Low и Close предыдущей сессии или группы предыдущих сессий. Это основано на исторических данных, предоставленных поставщиком рыночных данных.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPPO" xml:space="preserve">
    <value>PPO (Percentage Price Oscillator) базируется на двух скользящих средних выраженных в процентах. РРО находится из вычитания длинной MA от короткого MA и затем делением разности на длинный MA.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceLine" xml:space="preserve">
    <value>Отображает ask, bid, и/или линии last на графике.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceOscillator" xml:space="preserve">
    <value>Индикатор Price Oscillator показвает вариацию между двумя скользящими средними от цены ценной бумаги.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriorDayOHLC" xml:space="preserve">
    <value>Наносит значения сессии начиная от предыдущего дня: открытие, high, low, закрытие</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPsychologicalLine" xml:space="preserve">
    <value>Psychological Line - это отношение числа восходящих баров к указанному количеству баров.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRange" xml:space="preserve">
    <value>Вычисление диапазона бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRangeCounter" xml:space="preserve">
    <value>Отображение подсчитаного диапазона бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRegressionChannel" xml:space="preserve">
    <value>Линейная регрессия используется для расчета подходящей линии для данных о цене. В дополнение верхние и нижнии полосы добавляются из вычисления стандартного отклонения цен от линии регрессии.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRelativeVigorIndex" xml:space="preserve">
    <value>Relative Vigor Index измеряет силу тренда, сравнивая цену закрытия инструментов с ценовым диапазоном. Это основано на том, что цены, как правило, выше, чем они открываются вверх по тенденциям, и ближе к минимуму, чем они открываются в нисходящем тренде.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRIND" xml:space="preserve">
    <value>RIND, Range Indicator сравнивает внутридневной диапазон (high - low) с между-дневным (закрытие - предыдущее закрытие) диапазоном. Когда внутридневной диапазон больше чем между-дневной, индикатор диапазона показывает высокое значение. Это сигнализирует конец текущему тренду. Когда индикатор диапазона на низком уровне, новый тренд собирается начаться.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionROC" xml:space="preserve">
    <value>Индикатор ROC (Rate-of-Change) отображает процентное изменение между текущей ценой и ценой х-периодов времени назад.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSI" xml:space="preserve">
    <value>RSI (Relative Strength Index) следующий за ценой осциллятор, который колеблется между 0 и 100.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSquared" xml:space="preserve">
    <value>Индикатор R-Squared вычисляет, насколько хорошо цена приближается к линейной линии регрессии. Индикатор получает свое имя от вычисления, т. Е. Квадрата коэффициента корреляции (в математике указывается греческая буква rho или r). Диапазон R-Squared от нуля до единицы.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSS" xml:space="preserve">
    <value>Относительная сила спрэда между двумя средними скользящими. TASC, октябрь 2006, стр. 16.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRVI" xml:space="preserve">
    <value>Индекс Относительной Волатильности (RVI, Relative Volatility Index) был разработан Дональдом Дорси как комплимент и подтверждение индикаторам на основе момента. Используется для подтверждения других сигналов, только покупать когда RVI превышает 50 и только продавать когда RVI ниже 50.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSampleCustomRender" xml:space="preserve">
    <value>Пример скрипта для показа OnRender () возможности</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSMA" xml:space="preserve">
    <value>SMA (Simple Moving Average) индикатор, который показывает среднее значение цены ценной бумаги за период времени.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdDev" xml:space="preserve">
    <value>Standard Deviation это статистическая мера волатильности. Стандартное Отклонение обычно используется как компонент других индикаторов, охотнее чем самостоятельный индикатор. Например, Полосы Боллинджера расчитываются добавляя Стандартное Отклонение ценых бумаг к скользящей средней.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdError" xml:space="preserve">
    <value>Standard Error показывает как близко цены идут вокруг линии линеной регрессии. Ближе цены к линии линейной регрессии, сильнее тренд.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochastics" xml:space="preserve">
    <value>Stochastic Oscillator сделан из двух линии, которые осциллируют между вертикальной шкалой от 0 до 100. %К это основная линия и рисуется как сплошная линия. Вторая это %D линия и скользящая средняя от %К. %D линия рисуется как пунктирная линия. Используется как генератор сигнала купить/продать, покупая когда быстрое движение выше медленного, и продавая когда быстрое движение ниже медленного.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochasticsFast" xml:space="preserve">
    <value>Stochastic Oscillator сделан из двух линии, которые осциллируют между вертикальной шкалой от 0 до 100. %К это основная линия и рисуется как сплошная линия. Вторая это %D линия и скользящая средняя от %К. %D линия рисуется как пунктирная линия. Используется как генератор сигнала купить/продать, покупая когда быстрое движение выше медленного, и продавая когда быстрое движение ниже медленного.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochRSI" xml:space="preserve">
    <value>StochRSI осцилятор, похожий в вычислении на измерение stochastic, но вместо значения цены как вход, СтохRSI использует значения RSI. СтохRSI вычисляет текущую позицию RSI относительно high и  low значений RSI за указанное число дней. Цель этого измерения, разработанного Tushar Chande и Stanley Kroll, представить больше информации о перекупленности/перепроданности характера RSI. СтохRSI колеблется между 0,0 и 1,0. Значения выше 0,8 обычно расматриваются для обнаружения уровней перекупленности и значения ниже 0,2 считаются указанием состояния перепроданности.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSUM" xml:space="preserve">
    <value>Сумма показывает суммирование последних n указателей данных.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSwing" xml:space="preserve">
    <value>Индикатор Swing отображает линии, которые представляют верхние и нижние точки swing.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionT3" xml:space="preserve">
    <value>T3 тип скользящей средней или функция сглаживания. Он основан на DEMA. T3 принимает расчет DEMA и добавляет vfactor, который находится между нулем и 1. Полученная функция называется GD или Generalized DEMA. GD с vfactor of 1 такой же, как DEMA. GD с vfactor нуля совпадает с экспоненциальным скользящим средним. T3 обычно использует vfactor 0.7.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTEMA" xml:space="preserve">
    <value>TEMA является индикатором сглаживания. Он был разработан Patrick Mulloy и в январе 1994 описан в его статье Technical Analysis of Stocks and Commodities.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTickCounter" xml:space="preserve">
    <value>Отображает подсчет тиков бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTMA" xml:space="preserve">
    <value>ТМА (Triangular Moving Average) это взвешенная скользящая средняя. По сравнению с WMA, которая делает более весомым последний бар цены, ТМА делает более весомыми данные в середине указанного периода.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTrendLines" xml:space="preserve">
    <value>Когда за высоким swing следует более низкий, автоматически строится линия максимума. Когда за низким swing следует более высокий, автоматически строится линия минимума тренда.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTRIX" xml:space="preserve">
    <value>TRIX (Triple Exponential Average) отображает процент Rate of Change (ROC) из тройного EMA. TRIX колеблется выше и ниже нулевой величины. Индикатор применяет трехкратное сглаживание в попытке устранить незначительные ценовые движения в пределах тренда, который пытаетесь изолировать.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSF" xml:space="preserve">
    <value>TSF (Time Series Forecast) вычисляет возможные будущие значения для цены путем подгонки линии линейной регрессии сверх определенного количества баров цены и следуя за этой линией вперед в будущее. .Линия линейной регрессии это прямая линия, расположенная близко ко всем точкам цены. Также можно посмотреть индикатор Linear Regression.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSI" xml:space="preserve">
    <value>TSI (True Strength Index) индикатор основанный на моментум, разработан William Blau. Предназначен определять состояние тренда и перепокупок/перепродаж, TSI применим к внутредневной и долгосрочной торговле.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionUltimateOscillator" xml:space="preserve">
    <value>Ultimate Oscillator взвешенная сумма трех осцилляторов различных периодов времени. Типичные периоды времени 7, 14 и 28. Значения диапазона Ultimate Oscillator от нуля до 100. Значения сверх 70 указывают на состояние перепокупок, и значения до 30 указывают на состояние перепродаж. Также смотри на соглашение/расхождение с ценой как подтверждение тренда или сигнал об окончании тренда.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVMA" xml:space="preserve">
    <value>VMA (Variable Moving Average), так же известен как VIDYA (Variable Index Dynamic Average), это экспоненциальная скользящая средняя, которая автоматически регулирует сглаживание веса в зависимости от волатильности рядов данных. VMA решает проблему с большинством скользящих средних. В периоды низкой волатильности, например когда цена находится в тренде, период времени скользящей средней должен быть короче, чтобы быть чувствительным к неизбежному перерыву в тренде. Тогда как, в более волатильные не трендовые времена, период времени скользящей средней должен быть длиннее чтобы отфильтровать Период для VMA и CMO являются регулируемыми.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOL" xml:space="preserve">
    <value>Объем это просто число акций (или контрактов) торгуемых в течении определенных временных рамок (например час, день, неделя, месяц, и т.д.)</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOLMA" xml:space="preserve">
    <value>VOLMA (Volume Moving Average) строит экспоненциальную скользящую среднюю (EMA) от объема.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeCounter" xml:space="preserve">
    <value>Отображает подсчитанный объем кождого бара.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeOscillator" xml:space="preserve">
    <value>Volume Oscillator измеряет объем путем расчета разницы от быстрого и медленного скользящего среднего объема. Volume Oscillator может обеспечить понимание сильного или слабово ценового тренда. Положительное значение предполагает, что есть достаточная поддержка рынка продолжить движение ценовой активности в направлении текущего тренда. Отрицательное значение означает недостаток поддержки и последующее замедление ценового движения или его разворот.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeProfile" xml:space="preserve">
    <value>Наносит горизонтальные гистограммы объема от цены.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeUpDown" xml:space="preserve">
    <value>Изменение индикатора VOL (Volume) раскрашивает объем гистограммы в разные цвета в зависимости если текущий бар идет вверх или вниз</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeZones" xml:space="preserve">
    <value>Volume zones строят горизонтальную гистограмму которая перекрывает ценовой график. Бары гистограмм растянуты слева направо, начиная с левой стороны графика. Длина каждого бара определяется нарастающим итогом объема всех баров за периоды в течение которого цена упала в вертикальном диапазоне бара гистограммы.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVortex" xml:space="preserve">
    <value>Индикатор Vortex - это осциллятор, используемый для определения трендов. Бычий сигнал срабатывает, когда линия VIPlus пересекает линию VIMinus. Медвежий сигнал срабатывает, когда линия VIMinus пересекает линию VIPlus</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVROC" xml:space="preserve">
    <value>VROC (Volume Rate-of-Change) показывает развивается ли тренд объема в большую или меньшую сторону. Это похоже на индикатор ROC, но применяется к объему.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVWMA" xml:space="preserve">
    <value>VWMA (Volume-Weighted Moving Average) возвращает объем-взвешенного скользящего среднего для указанного ценового ряда и периода. VWMA похожа на Simple Moving Average (SMA), но каждый бар данных взвешивается по объему бара. VWMA становиться более значим в дни с наибольшим объемом и менее, в дни с наименьшим объемом для указанного периода.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWilliamsR" xml:space="preserve">
    <value>Williams %R индикатор моментума, который спроектирован определять области перекупок и перепродаж в нетрендовом рынке.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWMA" xml:space="preserve">
    <value>WMA (Weighted Moving Average) индикатор Moving Average, который показывает среднее значение цены за некоторый период времени с особым акцентом на последние части периода времени при анализе, в противоположность ранним.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZigZag" xml:space="preserve">
    <value>Индикатор ZigZag показывает тренд линии, отфильтровывания изменения ниже определенного уровня.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZLEMA" xml:space="preserve">
    <value>ZLEMA (Zero-Lag Exponential Moving Average) это вариант ЕМА, который попытается корректировать задержку.</value>
  </data>
  <data name="NinjaScriptIndicatorDiff" xml:space="preserve">
    <value>Diff</value>
  </data>
  <data name="NinjaScriptIndicatorDisparityLine" xml:space="preserve">
    <value>Линия несоответствия</value>
  </data>
  <data name="NinjaScriptIndicatorDown" xml:space="preserve">
    <value>Down</value>
  </data>
  <data name="NinjaScriptIndicatorLower" xml:space="preserve">
    <value>Ниже</value>
  </data>
  <data name="NinjaScriptIndicatorMcClellanOscillatorLine" xml:space="preserve">
    <value>Линия осциллятора McClellan</value>
  </data>
  <data name="NinjaScriptIndicatorMiddle" xml:space="preserve">
    <value>Средний</value>
  </data>
  <data name="NinjaScriptIndicatorMoneyFlowLine" xml:space="preserve">
    <value>Линия денежных потоков</value>
  </data>
  <data name="NinjaScriptIndicatorNameADL" xml:space="preserve">
    <value>ADL</value>
  </data>
  <data name="NinjaScriptIndicatorNameADX" xml:space="preserve">
    <value>ADX</value>
  </data>
  <data name="NinjaScriptIndicatorNameADXR" xml:space="preserve">
    <value>ADXR</value>
  </data>
  <data name="NinjaScriptIndicatorNameAPZ" xml:space="preserve">
    <value>APZ</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroon" xml:space="preserve">
    <value>Aroon</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroonOscillator" xml:space="preserve">
    <value>Aroon oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameATR" xml:space="preserve">
    <value>ATR</value>
  </data>
  <data name="NinjaScriptIndicatorNameBarTimer" xml:space="preserve">
    <value>Bar timer</value>
  </data>
  <data name="NinjaScriptIndicatorNameBlockVolume" xml:space="preserve">
    <value>Block volume</value>
  </data>
  <data name="NinjaScriptIndicatorNameBollinger" xml:space="preserve">
    <value>Bollinger</value>
  </data>
  <data name="NinjaScriptIndicatorNameBOP" xml:space="preserve">
    <value>BOP</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellPressure" xml:space="preserve">
    <value>Buy sell pressure</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellVolume" xml:space="preserve">
    <value>Buy sell volume</value>
  </data>
  <data name="NinjaScriptIndicatorNameCamarillaPivots" xml:space="preserve">
    <value>Camarilla pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNameCandlestickPattern" xml:space="preserve">
    <value>Candlestick pattern</value>
  </data>
  <data name="NinjaScriptIndicatorNameCCI" xml:space="preserve">
    <value>CCI</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinMoneyFlow" xml:space="preserve">
    <value>Chaikin money flow</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinOscillator" xml:space="preserve">
    <value>Chaikin oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinVolatility" xml:space="preserve">
    <value>Chaikin volatility</value>
  </data>
  <data name="NinjaScriptIndicatorNameChoppinessIndex" xml:space="preserve">
    <value>Choppiness index</value>
  </data>
  <data name="NinjaScriptIndicatorNameCMO" xml:space="preserve">
    <value>CMO</value>
  </data>
  <data name="NinjaScriptIndicatorNameConstantLines" xml:space="preserve">
    <value>Constant lines</value>
  </data>
  <data name="NinjaScriptIndicatorNameCorrelation" xml:space="preserve">
    <value>Correlation</value>
  </data>
  <data name="NinjaScriptIndicatorNameCOT" xml:space="preserve">
    <value>COT</value>
  </data>
  <data name="NinjaScriptIndicatorNameCurrentDayOHL" xml:space="preserve">
    <value>Current day OHL</value>
  </data>
  <data name="NinjaScriptIndicatorNameDarvas" xml:space="preserve">
    <value>Darvas</value>
  </data>
  <data name="NinjaScriptIndicatorNameDEMA" xml:space="preserve">
    <value>DEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameDisparityIndex" xml:space="preserve">
    <value>Disparity index</value>
  </data>
  <data name="NinjaScriptIndicatorNameDM" xml:space="preserve">
    <value>DM</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMI" xml:space="preserve">
    <value>DMI</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMIndex" xml:space="preserve">
    <value>DM index</value>
  </data>
  <data name="NinjaScriptIndicatorNameDonchianChannel" xml:space="preserve">
    <value>Donchian channel</value>
  </data>
  <data name="NinjaScriptIndicatorNameDoubleStochastics" xml:space="preserve">
    <value>Double stochastics</value>
  </data>
  <data name="NinjaScriptIndicatorNameEaseOfMovement" xml:space="preserve">
    <value>Ease of movement</value>
  </data>
  <data name="NinjaScriptIndicatorNameEMA" xml:space="preserve">
    <value>EMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameFibonacciPivots" xml:space="preserve">
    <value>Fibonacci pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNameFisherTransform" xml:space="preserve">
    <value>Fisher transform</value>
  </data>
  <data name="NinjaScriptIndicatorNameFOSC" xml:space="preserve">
    <value>FOSC</value>
  </data>
  <data name="NinjaScriptIndicatorNameHMA" xml:space="preserve">
    <value>HMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKAMA" xml:space="preserve">
    <value>KAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKelterChannel" xml:space="preserve">
    <value>Keltner channel</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalDown" xml:space="preserve">
    <value>Key reversal down</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalUp" xml:space="preserve">
    <value>Key reversal up</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinReg" xml:space="preserve">
    <value>Lin. reg.</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegIntercept" xml:space="preserve">
    <value>Lin. reg. intercept</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegSlope" xml:space="preserve">
    <value>Lin. reg. slope</value>
  </data>
  <data name="NinjaScriptIndicatorNameMACD" xml:space="preserve">
    <value>MACD</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAEnvelopes" xml:space="preserve">
    <value>MA envelopes</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAMA" xml:space="preserve">
    <value>MAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAX" xml:space="preserve">
    <value>MAX</value>
  </data>
  <data name="NinjaScriptIndicatorNameMcClellanOscillator" xml:space="preserve">
    <value>McClellan oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameMFI" xml:space="preserve">
    <value>MFI</value>
  </data>
  <data name="NinjaScriptIndicatorNameMIN" xml:space="preserve">
    <value>MIN</value>
  </data>
  <data name="NinjaScriptIndicatorNameMomentum" xml:space="preserve">
    <value>Momentum</value>
  </data>
  <data name="NinjaScriptIndicatorNameMoneyFlowOscillator" xml:space="preserve">
    <value>Money flow oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameMovingAverageRibbon" xml:space="preserve">
    <value>Moving average ribbon</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsDown" xml:space="preserve">
    <value>N bars down</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsUp" xml:space="preserve">
    <value>N bars up</value>
  </data>
  <data name="NinjaScriptIndicatorNameNetChangeDisplay" xml:space="preserve">
    <value>Net change display</value>
  </data>
  <data name="NinjaScriptIndicatorNameOBV" xml:space="preserve">
    <value>OBV</value>
  </data>
  <data name="NinjaScriptIndicatorNameParabolicSAR" xml:space="preserve">
    <value>Parabolic SAR</value>
  </data>
  <data name="NinjaScriptIndicatorNamePFE" xml:space="preserve">
    <value>PFE</value>
  </data>
  <data name="NinjaScriptIndicatorNamePivots" xml:space="preserve">
    <value>Pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNamePPO" xml:space="preserve">
    <value>PPO</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceLine" xml:space="preserve">
    <value>Price line</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceOscillator" xml:space="preserve">
    <value>Price oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriorDayOHLC" xml:space="preserve">
    <value>Prior day OHLC</value>
  </data>
  <data name="NinjaScriptIndicatorNamePsychologicalLine" xml:space="preserve">
    <value>Psychological line</value>
  </data>
  <data name="NinjaScriptIndicatorNameRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="NinjaScriptIndicatorNameRangeCounter" xml:space="preserve">
    <value>Range counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameRegressionChannel" xml:space="preserve">
    <value>Regression channel</value>
  </data>
  <data name="NinjaScriptIndicatorNameRelativeVigorIndex" xml:space="preserve">
    <value>Relative vigor index</value>
  </data>
  <data name="NinjaScriptIndicatorNameRIND" xml:space="preserve">
    <value>RIND</value>
  </data>
  <data name="NinjaScriptIndicatorNameROC" xml:space="preserve">
    <value>ROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSI" xml:space="preserve">
    <value>RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSquared" xml:space="preserve">
    <value>R squared</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSS" xml:space="preserve">
    <value>RSS</value>
  </data>
  <data name="NinjaScriptIndicatorNameRVI" xml:space="preserve">
    <value>RVI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSampleCustomRender" xml:space="preserve">
    <value>Sample custom render</value>
  </data>
  <data name="NinjaScriptIndicatorNameSMA" xml:space="preserve">
    <value>SMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdDev" xml:space="preserve">
    <value>Std. dev.</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdError" xml:space="preserve">
    <value>Std. error</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochastics" xml:space="preserve">
    <value>Stochastics</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochasticsFast" xml:space="preserve">
    <value>Stochastics fast</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochRSI" xml:space="preserve">
    <value>Stoch RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSUM" xml:space="preserve">
    <value>SUM</value>
  </data>
  <data name="NinjaScriptIndicatorNameSwing" xml:space="preserve">
    <value>Swing</value>
  </data>
  <data name="NinjaScriptIndicatorNameT3" xml:space="preserve">
    <value>T3</value>
  </data>
  <data name="NinjaScriptIndicatorNameTEMA" xml:space="preserve">
    <value>TEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTickCounter" xml:space="preserve">
    <value>Tick counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameTMA" xml:space="preserve">
    <value>TMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTrendLines" xml:space="preserve">
    <value>Trend lines</value>
  </data>
  <data name="NinjaScriptIndicatorNameTRIX" xml:space="preserve">
    <value>TRIX</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSF" xml:space="preserve">
    <value>TSF</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSI" xml:space="preserve">
    <value>TSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameUltimateOscillator" xml:space="preserve">
    <value>Ultimate oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameVMA" xml:space="preserve">
    <value>VMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOL" xml:space="preserve">
    <value>VOL</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOLMA" xml:space="preserve">
    <value>VOLMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeCounter" xml:space="preserve">
    <value>Volume counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeOscillator" xml:space="preserve">
    <value>Volume oscillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeProfile" xml:space="preserve">
    <value>Volume profile</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumesZones" xml:space="preserve">
    <value>Volume zones</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeUpDown" xml:space="preserve">
    <value>Volume up down</value>
  </data>
  <data name="NinjaScriptIndicatorNameVortex" xml:space="preserve">
    <value>Vortex</value>
  </data>
  <data name="NinjaScriptIndicatorNameVROC" xml:space="preserve">
    <value>VROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameVWMA" xml:space="preserve">
    <value>VWMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameWilliamsR" xml:space="preserve">
    <value>Williams R</value>
  </data>
  <data name="NinjaScriptIndicatorNameWMA" xml:space="preserve">
    <value>WMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameZigZag" xml:space="preserve">
    <value>Zig zag</value>
  </data>
  <data name="NinjaScriptIndicatorNameZLEMA" xml:space="preserve">
    <value>ZLEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNeutral" xml:space="preserve">
    <value>Нейтральный</value>
  </data>
  <data name="NinjaScriptIndicatorOverbought" xml:space="preserve">
    <value>Overbought</value>
  </data>
  <data name="NinjaScriptIndicatorOverBoughtLine" xml:space="preserve">
    <value>Линия Overbought</value>
  </data>
  <data name="NinjaScriptIndicatorOversold" xml:space="preserve">
    <value>Oversold</value>
  </data>
  <data name="NinjaScriptIndicatorOverSoldLine" xml:space="preserve">
    <value>Линия Oversold</value>
  </data>
  <data name="NinjaScriptIndicatorRelativeVigorIndex" xml:space="preserve">
    <value>Relative Vigor Index</value>
  </data>
  <data name="NinjaScriptIndicatorSignal" xml:space="preserve">
    <value>Сигнал</value>
  </data>
  <data name="NinjaScriptIndicatorUp" xml:space="preserve">
    <value>Вверх</value>
  </data>
  <data name="NinjaScriptIndicatorUpper" xml:space="preserve">
    <value>Верхний</value>
  </data>
  <data name="NinjaScriptIndicatorVIMinus" xml:space="preserve">
    <value>VIMinus</value>
  </data>
  <data name="NinjaScriptIndicatorVIPlus" xml:space="preserve">
    <value>VIPlus</value>
  </data>
  <data name="NinjaScriptIndicatorVisualGroup" xml:space="preserve">
    <value>Визуальный</value>
  </data>
  <data name="NinjaScriptIndicatorZeroLine" xml:space="preserve">
    <value>Нулевая линия</value>
  </data>
  <data name="NinjaScriptIsVisibleOnlyFocused" xml:space="preserve">
    <value>Видимо только в фокусе</value>
  </data>
  <data name="NinjaScriptLine" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="NinjaScriptLines" xml:space="preserve">
    <value>Линии</value>
  </data>
  <data name="NinjaScriptLoadingData" xml:space="preserve">
    <value>  Загрузка данных... {0}</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskPrice" xml:space="preserve">
    <value>Текущая цена ask</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskSize" xml:space="preserve">
    <value>Текущий размер ask</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAverageDailyVolume" xml:space="preserve">
    <value>Средний дневной объем</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBeta" xml:space="preserve">
    <value>Мера волатильности, или систематического риска, ценных бумаг или портфель в сравнении с рынком в целом.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidAskSpread" xml:space="preserve">
    <value>Разница между текущим ценами bid и ask</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidPrice" xml:space="preserve">
    <value>Текущая цена bid</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidSize" xml:space="preserve">
    <value>Текущий размер bid</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHigh" xml:space="preserve">
    <value>Самая высокая цена текущего года</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHighDate" xml:space="preserve">
    <value>Дата, когда цена достигла самого высокого значения за текущего календарный год </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLow" xml:space="preserve">
    <value>Самая низкая цена текущего года</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLowDate" xml:space="preserve">
    <value>Дата, когда цена достигла самого низкого значения за текущего календарный год </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartMini" xml:space="preserve">
    <value>В этой колонке Обзор рынка отображается мини-диаграмма для входных свойств.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartNetChange" xml:space="preserve">
    <value>В этой колонке Обзор рынка отображается мини-диаграмма для входных свойств.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCurrentRatio" xml:space="preserve">
    <value>Текущие активы разделены текущими обязательствами</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyHigh" xml:space="preserve">
    <value>Сегодняшняя high</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyLow" xml:space="preserve">
    <value>Сегодняшняя low</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyVolume" xml:space="preserve">
    <value>Сегодняшний объем</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDaysUntilRollover" xml:space="preserve">
    <value>Показывает, сколько дней от ролловер на следующий контракта.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDescription" xml:space="preserve">
    <value>Описание инструмента</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendAmount" xml:space="preserve">
    <value>Размер дивидендов</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendPayDate" xml:space="preserve">
    <value>Дата выплаты дивидентов</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendYield" xml:space="preserve">
    <value>Соотношение, которое показывает сколько компания выплачивает дивидентов каждый год относительно цен акций.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionEarningsPerShare" xml:space="preserve">
    <value>Доля дохода компании назначенной каждой выделенной доли обыкновенной акции.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Пять лет процент роста</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52Weeks" xml:space="preserve">
    <value>Самое высокое значение за последние 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52WeeksDate" xml:space="preserve">
    <value>Дата, когда цена достигла самого высокого значения за последние 52 недели.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHistoricalVolatility" xml:space="preserve">
    <value>Реализован инструмент волатильности сверх времени</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionInstrument" xml:space="preserve">
    <value>Название инструмента</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastClose" xml:space="preserve">
    <value>Закрытие последней торговой сессии</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastPrice" xml:space="preserve">
    <value>Цена последней сделки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastSize" xml:space="preserve">
    <value>Размер последней сделки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52Weeks" xml:space="preserve">
    <value>Самое низкое значение за последние 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52WeeksDate" xml:space="preserve">
    <value>Дата, когда цена достигла самого низкого значения за последние 52 недели.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketCap" xml:space="preserve">
    <value>Рыночная капитализация. Общий объем выпущенных акций.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketPrice" xml:space="preserve">
    <value>Текущая цена и изменение</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChange" xml:space="preserve">
    <value>Текущая цена в сравнении с последней ценой закрытия </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxDown" xml:space="preserve">
    <value>Текущий low по сравнению с последней ценой закрытия</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxUp" xml:space="preserve">
    <value>Текущий high по сравнению с последней ценой закрытия</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNextYearsEarningsPerShare" xml:space="preserve">
    <value>Проект дохода на каждую акцию</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNotes" xml:space="preserve">
    <value>Поле определяется пользователем. Двойной щелчок на колонке применяемой заметки создает или редактирует заметку.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpening" xml:space="preserve">
    <value>Цена открытия для текущей торговой сессии</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpenInterest" xml:space="preserve">
    <value>Общее число опционов и\или фьючерсных контрактов, которые не закрыты или поставлены в определенный день</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPercentHeldByInstitutions" xml:space="preserve">
    <value>Процент акций, держателями которых являются институциональные инвесторы</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionAvgPrice" xml:space="preserve">
    <value>Начальная средняя цена текущей позиции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionSize" xml:space="preserve">
    <value>Размер текущей позиции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPriceEarningsRatio" xml:space="preserve">
    <value>Текущая цена акции в сравнении с доходом каждой акции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionProfitLoss" xml:space="preserve">
    <value>Общий нереализованный и реализованный доход и убыток</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRealizedProfitLoss" xml:space="preserve">
    <value>Реализованная Прибыль/Убыток</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRevenuePerShare" xml:space="preserve">
    <value>Соотношение дохода к цене акции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSettlement" xml:space="preserve">
    <value>Сегодняшняя цена Settlement</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSharesOutstanding" xml:space="preserve">
    <value>Число выделенных акций</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterest" xml:space="preserve">
    <value>Количество акций, проданных инвесторами в short, но еще не покрытых или закрытых.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterestRatio" xml:space="preserve">
    <value>Короткий интерес разделенный средним дневным объемом</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTimeLastTick" xml:space="preserve">
    <value>Время последней сделки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTradedContracts" xml:space="preserve">
    <value>Сегодняшние заполненные контракты</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTSTrend" xml:space="preserve">
    <value>This columndisplays a colored bar that represents the incoming ticks with the same colors that the T &amp; S window uses</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionUnrealizedProfitLoss" xml:space="preserve">
    <value>Прибыль/убыток для текущей позиции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionVwap" xml:space="preserve">
    <value>Взвешенное значение средней цены</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskPrice" xml:space="preserve">
    <value>Цена Ask</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskSize" xml:space="preserve">
    <value>Размер Ask</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAverageDailyVolume" xml:space="preserve">
    <value>Средний дневной объем</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBeta" xml:space="preserve">
    <value>Бета</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidAskSpread" xml:space="preserve">
    <value>Спред Bid Ask </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidPrice" xml:space="preserve">
    <value>Цена Bid</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidSize" xml:space="preserve">
    <value>Размер Bid</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHigh" xml:space="preserve">
    <value>High календарного года</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHighDate" xml:space="preserve">
    <value>День high за календарный год</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLow" xml:space="preserve">
    <value>Low календарного года</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLowDate" xml:space="preserve">
    <value>День low за календарный год</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartMini" xml:space="preserve">
    <value>Chart - Mini</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartNetChange" xml:space="preserve">
    <value>Chart - Net change</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCurrentRatio" xml:space="preserve">
    <value>Текущее соотношение</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyHigh" xml:space="preserve">
    <value>Дневная high</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyLow" xml:space="preserve">
    <value>Дневная low</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyVolume" xml:space="preserve">
    <value>Дневной объем</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDaysUntilRollover" xml:space="preserve">
    <value>Дней до ролловера</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDescription" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendAmount" xml:space="preserve">
    <value>Размер дивидендов</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendPayDate" xml:space="preserve">
    <value>Дата выплаты дивидентов</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendYield" xml:space="preserve">
    <value>Дивидендная доходность</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameEarningsPerShare" xml:space="preserve">
    <value>Доход каждой акции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Пять лет процент роста</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52Weeks" xml:space="preserve">
    <value>Самое высокое значение за 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52WeeksDate" xml:space="preserve">
    <value>День, когда достигла самогого высокого значения за последние 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHistoricalVolatility" xml:space="preserve">
    <value>История волатильности</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameInstrument" xml:space="preserve">
    <value>Инструмент</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastClose" xml:space="preserve">
    <value>Последнее закрытие</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastPrice" xml:space="preserve">
    <value>Цена последней сделки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastSize" xml:space="preserve">
    <value>Размер последней сделки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52Weeks" xml:space="preserve">
    <value>Самое низкое значение за 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52WeeksDate" xml:space="preserve">
    <value>День, когда достигла самого низкого значения за последние 52 недели</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketCap" xml:space="preserve">
    <value>Рыночная капитализация</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketPrice" xml:space="preserve">
    <value>Цена Market</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChange" xml:space="preserve">
    <value>Изменение net</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxDown" xml:space="preserve">
    <value>Изменение net макс вниз</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxUp" xml:space="preserve">
    <value>Изменение net макс вверх</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNextYearsEarningsPerShare" xml:space="preserve">
    <value>Доход следующего года каждой акции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNotes" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpening" xml:space="preserve">
    <value>Открытие</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpenInterest" xml:space="preserve">
    <value>Открытый интерес</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePercentHeldByInstitutions" xml:space="preserve">
    <value>Процент владения институционалами</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionAvgPrice" xml:space="preserve">
    <value>Средняя цена позиции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionSize" xml:space="preserve">
    <value>Размер позиции</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePriceEarningsRatio" xml:space="preserve">
    <value>Сотношение цены к доходу</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameProfitLoss" xml:space="preserve">
    <value>Прибыль/убыток</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRealizedProfitLoss" xml:space="preserve">
    <value>Реализованная Прибыль/Убыток</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRevenuePerShare" xml:space="preserve">
    <value>Выручка за акцию</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSettlement" xml:space="preserve">
    <value>Цена Settlement</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSharesOutstanding" xml:space="preserve">
    <value>Акции выделенные</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterest" xml:space="preserve">
    <value>Короткий интерес</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterestRatio" xml:space="preserve">
    <value>Коэффициент короткого интереса</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTimeLastTick" xml:space="preserve">
    <value>Время последнего тика</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTradedContracts" xml:space="preserve">
    <value>Проторговано контрактов</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTSTrend" xml:space="preserve">
    <value>T &amp; S тренд</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameUnrealizedProfitLoss" xml:space="preserve">
    <value>Нереализованная прибыль/убыток</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameVwap" xml:space="preserve">
    <value>VWAP</value>
  </data>
  <data name="NinjaScriptNumberOfRows" xml:space="preserve">
    <value>Строк</value>
  </data>
  <data name="NinjaScriptOnBarCloseError" xml:space="preserve">
    <value>{0} зависит от обновлений тика bid/ask, ожидает Пересчет 'На каждый тик'</value>
  </data>
  <data name="NinjaScriptOnPriceChangeError" xml:space="preserve">
    <value>{0} зависит от обновлений объема, ожидает Пересчет 'На каждый тик' или 'На закрытии бара'</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfe" xml:space="preserve">
    <value>Максимальное среднее благоприятное отклонение за время удерживания позиции</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeLong" xml:space="preserve">
    <value>Максимальное среднее благоприятное отклонение за время удерживания позиции (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeShort" xml:space="preserve">
    <value>Максимальное среднее благоприятное отклонение за время удерживания позиции (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfit" xml:space="preserve">
    <value>Максимальная средняя прибыль</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitLong" xml:space="preserve">
    <value>Максимальная средняя прибыль (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitShort" xml:space="preserve">
    <value>Максимальная средняя прибыль (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfit" xml:space="preserve">
    <value>Максимальная чистая прибыль</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitLong" xml:space="preserve">
    <value>Максимальная чистая прибыль (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitShort" xml:space="preserve">
    <value>Максимальная чистая прибыль (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitable" xml:space="preserve">
    <value>Максимальный % прибыльных сделок</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableLong" xml:space="preserve">
    <value>Максимальный % прибыльных сделок (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableShort" xml:space="preserve">
    <value>Максимальный % прибыльных сделок (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablity" xml:space="preserve">
    <value>Максимальная вероятность</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityLong" xml:space="preserve">
    <value>Максимальная вероятность (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityShort" xml:space="preserve">
    <value>Максимальная вероятность (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactor" xml:space="preserve">
    <value>Фактор максимальной прибыли</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorLong" xml:space="preserve">
    <value>Фактор максимальной прибыли (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorShort" xml:space="preserve">
    <value>Фактор максимальной прибыли (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2" xml:space="preserve">
    <value>Максимальный R^2</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Long" xml:space="preserve">
    <value>Максимальный R^2 (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Short" xml:space="preserve">
    <value>Максимальный R^2 (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatio" xml:space="preserve">
    <value>Максимальный коэффициент Шарпа</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioLong" xml:space="preserve">
    <value>Максимальный коэффициент Шарпа (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioShort" xml:space="preserve">
    <value>Максимальный коэффициент Шарпа (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatio" xml:space="preserve">
    <value>Максимальный коэффициент Сортино</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioLong" xml:space="preserve">
    <value>Максимальный коэффициент Сортино (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioShort" xml:space="preserve">
    <value>Максимальный коэффициент Сортино (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrength" xml:space="preserve">
    <value>Максимальная сила</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthLong" xml:space="preserve">
    <value>Максимальная сила (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthShort" xml:space="preserve">
    <value>Максимальная сила (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatio" xml:space="preserve">
    <value>Максимальный коэффициент Ulcer</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioLong" xml:space="preserve">
    <value>Максимальный коэффициент Ulcer (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioShort" xml:space="preserve">
    <value>Максимальный коэффициент Ulcer (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatio" xml:space="preserve">
    <value>Максимальное соотношение прибыльных/убыточных сделок</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioLong" xml:space="preserve">
    <value>Максимальное соотношение прибыльных/убыточных сделок (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioShort" xml:space="preserve">
    <value>Максимальное соотношение прибыльных/убыточных сделок (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMae" xml:space="preserve">
    <value>Минимальное неблагоприятное отклонение за время удерживания позиции</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeLong" xml:space="preserve">
    <value>Минимальное неблагоприятное отклонение за время удерживания позиции (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeShort" xml:space="preserve">
    <value>Минимальное неблагоприятное отклонение за время удерживания позиции (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDown" xml:space="preserve">
    <value>Минимальная просадка</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownLong" xml:space="preserve">
    <value>Минимальная просадка (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownShort" xml:space="preserve">
    <value>Минимальная просадка (short)</value>
  </data>
  <data name="NinjaScriptOptimizerDefault" xml:space="preserve">
    <value>По умолчанию</value>
  </data>
  <data name="NinjaScriptOptimizerGenetic" xml:space="preserve">
    <value>Генетический</value>
  </data>
  <data name="NinjaScriptParameters" xml:space="preserve">
    <value>Параметры</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleATMStrategy" xml:space="preserve">
    <value>Образец стратегии расширенного управления сделками.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleCustomPerformance" xml:space="preserve">
    <value>Пример для демонстрации использования анализа торговли, установленного пользователем</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleFramework" xml:space="preserve">
    <value>Эта стратегия демонстрирует некоторые из возможностей  NinjaTrader Development Framework</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMACrossOver" xml:space="preserve">
    <value>Простая скользящая средняя пересечение стратегии.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiInstrument" xml:space="preserve">
    <value>Много-инструментный пример стратегии.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiTimeFrame" xml:space="preserve">
    <value>Много-временной пакет пример стратегии.</value>
  </data>
  <data name="NinjaScriptStrategyGenerator" xml:space="preserve">
    <value>Генератор стратегий</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorCandleStickPatternPrompt" xml:space="preserve">
    <value>1 candle stick pattern|{0} candle stick patterns|Add candle stick pattern...|Configure candle stick pattern...|Configure candle stick patterns...</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntries" xml:space="preserve">
    <value>Условия входа</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntriesOrExits" xml:space="preserve">
    <value>Вам нужен хотя бы один ордер на вход чтобы установить условия для выхода.</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorException" xml:space="preserve">
    <value>Исключение в выражении: {0} {1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorsPrompt" xml:space="preserve">
    <value>1 индикатор | {0} индикаторов | Добавить индикатор ... | Настроить индикатор ... | Настроить индикатор ...</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorPeformance" xml:space="preserve">
    <value>Анализ торговли для {0} = {1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorProperties" xml:space="preserve">
    <value>AI Generate Properties</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorTerminated" xml:space="preserve">
    <value>Генератор стратегий прервался после {0} генерорований, так как не было улучшения производительности для {1} генерирования</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseCandleStickPattern" xml:space="preserve">
    <value>Candle stick pattern</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseIndicators" xml:space="preserve">
    <value>Индикаторы</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleATMStrategy" xml:space="preserve">
    <value>Образец АТМ стратегии</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleCustomPerformance" xml:space="preserve">
    <value>Пример анализа торговли, установленного пользователем</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleFramework" xml:space="preserve">
    <value>Пример фреймворка</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMACrossOver" xml:space="preserve">
    <value>Пример МА пересечения</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiInstrument" xml:space="preserve">
    <value>Пример много-инструментного</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiTimeFrame" xml:space="preserve">
    <value>Пример много-временного пакета</value>
  </data>
  <data name="NinjaScriptStrategyParameters" xml:space="preserve">
    <value>Параметры стратегии</value>
  </data>
  <data name="NinjaScriptSuperDomColumnApq" xml:space="preserve">
    <value>APQ</value>
  </data>
  <data name="NinjaScriptSuperDomColumnBaseInitializeBarsPoolError" xml:space="preserve">
    <value>Ошибка загрузки серии баров для '{0}/{1}': {2}</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionApq" xml:space="preserve">
    <value>Индикатор Approximate Position In Queue (APQ) дает вам консервативную оценку текущей позиции в очереди ордеров, которые вы разместили.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionNotes" xml:space="preserve">
    <value>Столбец Заметки предоставляет текстовую запись в точках цены непосредственно в SuperDOM и может использоваться для добавления заметок на уровень цены.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionPnl" xml:space="preserve">
    <value>Столбец Прибыль и Убыток (PnL) покажет потенциальную прибыль и убыток, как только вы будете в сделке.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionVolume" xml:space="preserve">
    <value>В столбце «Объем» будут использоваться исторические данные тика, чтобы отобразить количество контрактов, торгуемых на каждом уровне цен. Вы можете по желанию покрасить бары, основываясь на том, произошли ли сделки Ask или Bid.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnNotes" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="NinjaScriptSuperDomColumnProfitAndLoss" xml:space="preserve">
    <value>PnL</value>
  </data>
  <data name="NinjaScriptSuperDomColumnVolume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="NinjaScriptTileError" xml:space="preserve">
    <value>Ошибка при загрузке инструмента рисования {0}: {1}</value>
  </data>
  <data name="NinjaScriptYOffset" xml:space="preserve">
    <value>Y pixel offset</value>
  </data>
  <data name="NumberOfCotPlots" xml:space="preserve">
    <value>Количество участков COT</value>
  </data>
  <data name="NumberOfTrendLines" xml:space="preserve">
    <value>Количество линий тренда</value>
  </data>
  <data name="NumStdDev" xml:space="preserve">
    <value>Число стандартых отклонений</value>
  </data>
  <data name="OffsetMultiplier" xml:space="preserve">
    <value>Множитель отступа</value>
  </data>
  <data name="OldTrendsOpacity" xml:space="preserve">
    <value>Old trends opacity</value>
  </data>
  <data name="Opacity" xml:space="preserve">
    <value>Непрозрачность</value>
  </data>
  <data name="PathCapMode_Arrow" xml:space="preserve">
    <value>Стрелка</value>
  </data>
  <data name="PathCapMode_Line" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="PathToolCapMode_Arrow" xml:space="preserve">
    <value>Стрелка</value>
  </data>
  <data name="PathToolCapMode_Line" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="PerformanceMetricSampleCumProfit" xml:space="preserve">
    <value>Пример показателя анализа совокупной прибыли</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Период</value>
  </data>
  <data name="PeriodD" xml:space="preserve">
    <value>Период D</value>
  </data>
  <data name="PeriodK" xml:space="preserve">
    <value>Период K</value>
  </data>
  <data name="PeriodQ" xml:space="preserve">
    <value>Период Q</value>
  </data>
  <data name="PFEZero" xml:space="preserve">
    <value>Ноль</value>
  </data>
  <data name="PiviotsDailyBarsError" xml:space="preserve">
    <value>Внутридневные или дневные бары должны использоваться для Pivots</value>
  </data>
  <data name="PiviotsDailyDataError" xml:space="preserve">
    <value>Недостаточно данных за день для расчета Pivots</value>
  </data>
  <data name="PiviotsInsufficentDataError" xml:space="preserve">
    <value>Недостаточно исторических данных для расчета Pivots. Добавьте в настройках чарта количество дней для загрузки.</value>
  </data>
  <data name="PiviotsPeriodTypeError" xml:space="preserve">
    <value>Тип периода должен быть Дневной со значением 1</value>
  </data>
  <data name="PiviotsWeeklyBarsError" xml:space="preserve">
    <value>Дневные бары требуют использования недельного или месячного сводного диапазона</value>
  </data>
  <data name="PivotRange" xml:space="preserve">
    <value>Pivot range</value>
  </data>
  <data name="PivotRange_Daily" xml:space="preserve">
    <value>Дневной</value>
  </data>
  <data name="PivotRange_Monthly" xml:space="preserve">
    <value>Месячный</value>
  </data>
  <data name="PivotRange_Weekly" xml:space="preserve">
    <value>Месячный</value>
  </data>
  <data name="PivotsPP" xml:space="preserve">
    <value>PP</value>
  </data>
  <data name="PivotsR1" xml:space="preserve">
    <value>R1</value>
  </data>
  <data name="PivotsR2" xml:space="preserve">
    <value>R2</value>
  </data>
  <data name="PivotsR3" xml:space="preserve">
    <value>R3</value>
  </data>
  <data name="PivotsR4" xml:space="preserve">
    <value>R4</value>
  </data>
  <data name="PivotsS1" xml:space="preserve">
    <value>S1</value>
  </data>
  <data name="PivotsS2" xml:space="preserve">
    <value>S2</value>
  </data>
  <data name="PivotsS3" xml:space="preserve">
    <value>S3</value>
  </data>
  <data name="PivotsS4" xml:space="preserve">
    <value>S4</value>
  </data>
  <data name="PlotCurrentValue" xml:space="preserve">
    <value>Нанести текущее значение только</value>
  </data>
  <data name="PositiveColor" xml:space="preserve">
    <value>Положительный цвет</value>
  </data>
  <data name="PPOSmoothed" xml:space="preserve">
    <value>Сглаженный</value>
  </data>
  <data name="PriceLinePlotAsk" xml:space="preserve">
    <value>Линия Ask</value>
  </data>
  <data name="PriceLinePlotBid" xml:space="preserve">
    <value>Линия Bid</value>
  </data>
  <data name="PriceLinePlotLast" xml:space="preserve">
    <value>Линия Last</value>
  </data>
  <data name="PriorDayOHLCClose" xml:space="preserve">
    <value>Prior close</value>
  </data>
  <data name="PriorDayOHLCHigh" xml:space="preserve">
    <value>Prior high</value>
  </data>
  <data name="PriorDayOHLCIntradayError" xml:space="preserve">
    <value>PriorDayOHLC работает только с внутридневными интервалами</value>
  </data>
  <data name="PriorDayOHLCLow" xml:space="preserve">
    <value>Prior low</value>
  </data>
  <data name="PriorDayOHLCOpen" xml:space="preserve">
    <value>Prior open</value>
  </data>
  <data name="RangeCounterBarError" xml:space="preserve">
    <value>Range counter работает только на барах Range</value>
  </data>
  <data name="RangeCounterRemaing" xml:space="preserve">
    <value>Оставшийся Range = {0}</value>
  </data>
  <data name="RangerCounterCount" xml:space="preserve">
    <value>Range count = {0}</value>
  </data>
  <data name="RangeValue" xml:space="preserve">
    <value>Значение Range</value>
  </data>
  <data name="RegionHighlightDirection_Horizontal" xml:space="preserve">
    <value>Горизонтальная</value>
  </data>
  <data name="RegionHighlightDirection_Vertical" xml:space="preserve">
    <value>Вертикальный</value>
  </data>
  <data name="RegressionChannelType_Segment" xml:space="preserve">
    <value>Сегмент</value>
  </data>
  <data name="RegressionChannelType_StandardDeviation" xml:space="preserve">
    <value>Стандартная дистанция отклонения</value>
  </data>
  <data name="ROCPeriod" xml:space="preserve">
    <value>Скорость изменения периода</value>
  </data>
  <data name="RVISignalLine" xml:space="preserve">
    <value>Сигнальная линия</value>
  </data>
  <data name="SampleAddOnDescription" xml:space="preserve">
    <value>Пример описания названия</value>
  </data>
  <data name="SampleAddOnHiThere" xml:space="preserve">
    <value>Привет!</value>
  </data>
  <data name="SampleAddOnName" xml:space="preserve">
    <value>Пример AddOn имени</value>
  </data>
  <data name="SampleCumProfit" xml:space="preserve">
    <value>Пример совокупной прибыли</value>
  </data>
  <data name="SampleCumProfitDescription" xml:space="preserve">
    <value>Совокупная прибыль как пример показателя анализа торговли, установленного пользователем</value>
  </data>
  <data name="SampleCustomPlotLowerRightCorner" xml:space="preserve">
    <value>Нижний правый угол</value>
  </data>
  <data name="SampleCustomPlotUpperLeftCorner" xml:space="preserve">
    <value>Верхний левый угол</value>
  </data>
  <data name="SelectPattern" xml:space="preserve">
    <value>Выбранный шаблон</value>
  </data>
  <data name="SelectPatternDescription" xml:space="preserve">
    <value>Выбор шаблона обнаружения</value>
  </data>
  <data name="SendAlerts" xml:space="preserve">
    <value>Послать уведомления</value>
  </data>
  <data name="SendAlertsDescription" xml:space="preserve">
    <value>Установить Верно для отправки сообщения оповещения окну Оповещения</value>
  </data>
  <data name="ShareArgsException" xml:space="preserve">
    <value>Возникла проблема с вызовом OnShare with arguments: {0}</value>
  </data>
  <data name="ShareBadGatewayError" xml:space="preserve">
    <value>Провайдер возвратил ошибку неправильного шлюза: '{0}'</value>
  </data>
  <data name="ShareBadRequestError" xml:space="preserve">
    <value>Провайдер возвратил ошибку неправильного запроса: '{0}'</value>
  </data>
  <data name="ShareException" xml:space="preserve">
    <value>WebException было сброшено. Статус: '{0}' Сообщение: '{1}'</value>
  </data>
  <data name="ShareFacebookCouldNotRetrieveUser" xml:space="preserve">
    <value>Пользователь не найден</value>
  </data>
  <data name="ShareFacebookCouldNotVerifyToken" xml:space="preserve">
    <value>Facebook не удалось проверить маркер для пользователя</value>
  </data>
  <data name="ShareFacebookNoResult" xml:space="preserve">
    <value>Не удалось получить ответ от Facebook</value>
  </data>
  <data name="ShareFacebookPermissionDenied" xml:space="preserve">
    <value>Нужные Facebook разрешения отказаны пользователем</value>
  </data>
  <data name="ShareFacebookScopesNotFound" xml:space="preserve">
    <value>Не удалось проверить разрешения Facebook </value>
  </data>
  <data name="ShareFacebookSentSuccessfully" xml:space="preserve">
    <value>{0} - Сообщение успешно отправлено</value>
  </data>
  <data name="ShareForbidden" xml:space="preserve">
    <value>Провайдер возвратил сообщение запрета: '{0}'</value>
  </data>
  <data name="ShareGatewayTimeoutError" xml:space="preserve">
    <value>Провайдер возвратил ошибку шлюза времени окончания: '{0}'</value>
  </data>
  <data name="ShareImageNoLongerExists" xml:space="preserve">
    <value>Изображение в '{0}' не может быть найдено.</value>
  </data>
  <data name="ShareInternalServerError" xml:space="preserve">
    <value>Провайдер возвратил ошибку внутреннего сервера: '{0}'</value>
  </data>
  <data name="ShareMailException" xml:space="preserve">
    <value>Обнаружена ошибка при отправке сообщения: {0}</value>
  </data>
  <data name="ShareMailPreconfiguredAol" xml:space="preserve">
    <value>AOL</value>
  </data>
  <data name="ShareMailPreconfiguredComcast" xml:space="preserve">
    <value>Comcast</value>
  </data>
  <data name="ShareMailPreconfiguredGmail" xml:space="preserve">
    <value>Gmail</value>
  </data>
  <data name="ShareMailPreconfiguredICloud" xml:space="preserve">
    <value>iCloud</value>
  </data>
  <data name="ShareMailPreconfiguredManual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="ShareMailPreconfiguredOutlook" xml:space="preserve">
    <value>Outlook</value>
  </data>
  <data name="ShareMailPreconfiguredYahoo" xml:space="preserve">
    <value>Yahoo</value>
  </data>
  <data name="ShareMailSendError" xml:space="preserve">
    <value>Обнаружена ошибка при отправке сообщения: {0}</value>
  </data>
  <data name="ShareMailSentSuccessfully" xml:space="preserve">
    <value>{0} - Сообщение успешно отправлено</value>
  </data>
  <data name="ShareNonSuccessCode" xml:space="preserve">
    <value>Провайдер возвратил {0} сообщение об ошибке: '{1}'</value>
  </data>
  <data name="ShareNotAuthorized" xml:space="preserve">
    <value>Провайдер возвратил сообщение не авторизации: '{0}'</value>
  </data>
  <data name="ShareServiceParameters" xml:space="preserve">
    <value>Полномочия</value>
  </data>
  <data name="ShareServicePassword" xml:space="preserve">
    <value>Пароль</value>
  </data>
  <data name="ShareServiceSignature" xml:space="preserve">
    <value>Исключение в сервисе: '{0}'</value>
  </data>
  <data name="ShareServiceUserName" xml:space="preserve">
    <value>Имя пользователя</value>
  </data>
  <data name="ShareStockTwitsNoAccount" xml:space="preserve">
    <value>Счет StockTwits не может быть проверен</value>
  </data>
  <data name="ShareStockTwitsSentSuccessfully" xml:space="preserve">
    <value>{0} - Сообщение успешно отправлено</value>
  </data>
  <data name="ShareTextMessageEmail" xml:space="preserve">
    <value>Электронная почта</value>
  </data>
  <data name="ShareTextMessageEmailRequired" xml:space="preserve">
    <value>Чтобы настроить текстовое сообщение по электронной почте 'Поделиться в соц сетях' , вы должны сначала настроить электронную почту 'Поделиться в соц сетях'.</value>
  </data>
  <data name="ShareTextMessageErrorOnShare" xml:space="preserve">
    <value>Произошла ошибка при отправке сообщения через службу электронной почты {0}: '{1}'</value>
  </data>
  <data name="ShareTextMessageMmsAddress" xml:space="preserve">
    <value>MMS address</value>
  </data>
  <data name="ShareTextMessageName" xml:space="preserve">
    <value>Текстовое сообщение по электронной почте</value>
  </data>
  <data name="ShareTextMessagePhoneNumber" xml:space="preserve">
    <value>Телефонный номер</value>
  </data>
  <data name="ShareTextMessagePreconfiguredAtt" xml:space="preserve">
    <value>AT&amp;T</value>
  </data>
  <data name="ShareTextMessagePreconfiguredManual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="ShareTextMessagePreconfiguredSprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="ShareTextMessagePreconfiguredTMobile" xml:space="preserve">
    <value>T-Mobile</value>
  </data>
  <data name="ShareTextMessagePreconfiguredVerizon" xml:space="preserve">
    <value>Verizon</value>
  </data>
  <data name="ShareTextMessageSentSuccessfully" xml:space="preserve">
    <value>{0} - текстовое сообщение отправлено</value>
  </data>
  <data name="ShareTextMessageSmsAddress" xml:space="preserve">
    <value>SMS адрес</value>
  </data>
  <data name="ShareTooManyRequests" xml:space="preserve">
    <value>Провайдер возвратил сообщение слишком много запросов: '{0}'</value>
  </data>
  <data name="ShareTwitterSentSuccessfully" xml:space="preserve">
    <value>{0} - Tweet успешно отправлен</value>
  </data>
  <data name="ShowAskLine" xml:space="preserve">
    <value>Показать линию ask</value>
  </data>
  <data name="ShowBidLine" xml:space="preserve">
    <value>Показать линию bid</value>
  </data>
  <data name="ShowClose" xml:space="preserve">
    <value>Показать закрытие</value>
  </data>
  <data name="ShowHigh" xml:space="preserve">
    <value>Показать high</value>
  </data>
  <data name="ShowLastLine" xml:space="preserve">
    <value>Показать линию last</value>
  </data>
  <data name="ShowLow" xml:space="preserve">
    <value>Показать low</value>
  </data>
  <data name="ShowOpen" xml:space="preserve">
    <value>Показать открытие</value>
  </data>
  <data name="ShowPatternCount" xml:space="preserve">
    <value>Показать количество паттерн</value>
  </data>
  <data name="ShowPatternCountDescription" xml:space="preserve">
    <value>Установить Верно для отображения на графике найденного количества паттерн</value>
  </data>
  <data name="ShowPercent" xml:space="preserve">
    <value>Показать проценты</value>
  </data>
  <data name="SignalPeriod" xml:space="preserve">
    <value>Сигнал периода</value>
  </data>
  <data name="Slow" xml:space="preserve">
    <value>Медленно</value>
  </data>
  <data name="SlowLimit" xml:space="preserve">
    <value>Медленный лимит</value>
  </data>
  <data name="SlowPeriod" xml:space="preserve">
    <value>Показать период</value>
  </data>
  <data name="SmallAreaColor" xml:space="preserve">
    <value>Цвет малой области</value>
  </data>
  <data name="Smooth" xml:space="preserve">
    <value>Гладкий</value>
  </data>
  <data name="Smoothing" xml:space="preserve">
    <value>Сглаживание</value>
  </data>
  <data name="StochasticsD" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="StochasticsK" xml:space="preserve">
    <value>K</value>
  </data>
  <data name="StockTwitsSentiment" xml:space="preserve">
    <value>Sentiment:</value>
  </data>
  <data name="StockTwitsSentimentDescription" xml:space="preserve">
    <value>Выбрать Медвежий, Нейтральный, или Бычий для этого сообщения</value>
  </data>
  <data name="StockTwitsServiceName" xml:space="preserve">
    <value>StockTwits</value>
  </data>
  <data name="StockTwitsSignature" xml:space="preserve">
    <value> Отправлено NinjaTrader</value>
  </data>
  <data name="Strength" xml:space="preserve">
    <value>Сила</value>
  </data>
  <data name="SuperDomColumnException" xml:space="preserve">
    <value>SuperDOM столбец '{0}': ошибка метода '{1}': {2}</value>
  </data>
  <data name="SwingHigh" xml:space="preserve">
    <value>Свинг high</value>
  </data>
  <data name="SwingLow" xml:space="preserve">
    <value>Свинг low</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}.SwingHighBar: barsAgo должно быть выше/равно 0 но было {1}</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}.SwingHighBar: barsAgo за пределами допустимого диапазона от 0 до {1}, было {2}. </value>
  </data>
  <data name="SwingSwingHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}.SwingHighBar: instance должно быть выше/равно 0 но было {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}.SwingLowBar: barsAgo должно быть выше/равно 0 но было {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}.SwingLowBar: barsAgo за пределами допустимого диапазона от 0 до {1}, было {2}.</value>
  </data>
  <data name="SwingSwingLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}.SwingLowBar: instance должно быть выше/равно 0 но было {1}</value>
  </data>
  <data name="TCount" xml:space="preserve">
    <value>T count</value>
  </data>
  <data name="TextColor" xml:space="preserve">
    <value>Цвет текста</value>
  </data>
  <data name="TextFont" xml:space="preserve">
    <value>Цвет шрифта</value>
  </data>
  <data name="TextFontDescription" xml:space="preserve">
    <value>Выберите шрифт, стиль, размер для отображения на графике</value>
  </data>
  <data name="TextPosition_BottomLeft" xml:space="preserve">
    <value>Внизу Слева</value>
  </data>
  <data name="TextPosition_BottomRight" xml:space="preserve">
    <value>Внизу Справа</value>
  </data>
  <data name="TextPosition_Center" xml:space="preserve">
    <value>Центровка</value>
  </data>
  <data name="TextPosition_TopLeft" xml:space="preserve">
    <value>Вверху Слева</value>
  </data>
  <data name="TextPosition_TopRight" xml:space="preserve">
    <value>Вверху Справа</value>
  </data>
  <data name="TickCounterBarError" xml:space="preserve">
    <value>Tick counter работает только на барах, построенных с заданным количеством тиков</value>
  </data>
  <data name="TickCounterTickCount" xml:space="preserve">
    <value>Tick Count = </value>
  </data>
  <data name="TickCounterTicksRemaining" xml:space="preserve">
    <value>Ticks Remaining = </value>
  </data>
  <data name="TrendLinesCurrentTrendLine" xml:space="preserve">
    <value>Линия текущего тренда</value>
  </data>
  <data name="TrendLinesNotVisible" xml:space="preserve">
    <value>Индикатор TrendLines не виден в анализаторе стратегий</value>
  </data>
  <data name="TrendLinesTrendLineBroken" xml:space="preserve">
    <value>{0} сломан</value>
  </data>
  <data name="TrendLinesTrendLineHigh" xml:space="preserve">
    <value>Линия тренда high</value>
  </data>
  <data name="TrendLinesTrendLineLow" xml:space="preserve">
    <value>Линия тренда low</value>
  </data>
  <data name="TrendStrength" xml:space="preserve">
    <value>Сила тренда</value>
  </data>
  <data name="TrendStrengthDescription" xml:space="preserve">
    <value>Число баров необходимых для определения тренда когда паттерну требуется преобладающий тренд. \nA значение нуля запрещенно требованием тренда. </value>
  </data>
  <data name="TRIXSignal" xml:space="preserve">
    <value>Сигнал</value>
  </data>
  <data name="TwitterAuthHeader" xml:space="preserve">
    <value>Учетная запись успешно авторизована</value>
  </data>
  <data name="TwitterAuthText1" xml:space="preserve">
    <value>Вы успешно авторизовали {0} для доступа к вашей учетной записи Twitter.</value>
  </data>
  <data name="TwitterAuthText2" xml:space="preserve">
    <value>Вы можете закрыть это окно и вернуться к {0}.</value>
  </data>
  <data name="TwitterServiceName" xml:space="preserve">
    <value>Twitter</value>
  </data>
  <data name="TwitterSignature" xml:space="preserve">
    <value> #NinjaTrader</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Единиц</value>
  </data>
  <data name="UpBarColor" xml:space="preserve">
    <value>Цвет бара вверх</value>
  </data>
  <data name="UseHighLow" xml:space="preserve">
    <value>Использовать high low</value>
  </data>
  <data name="UserDefinedClose" xml:space="preserve">
    <value>Close определенный пользователем</value>
  </data>
  <data name="UserDefinedHigh" xml:space="preserve">
    <value>High определенный пользователем</value>
  </data>
  <data name="UserDefinedLow" xml:space="preserve">
    <value>Low определенный пользователем</value>
  </data>
  <data name="VFactor" xml:space="preserve">
    <value>Фактор V</value>
  </data>
  <data name="VolatilityPeriod" xml:space="preserve">
    <value>Период волатильности</value>
  </data>
  <data name="VolumeCounterBarError" xml:space="preserve">
    <value>Volume counter работает только с интервалами по объему</value>
  </data>
  <data name="VolumeCounterVolumeCount" xml:space="preserve">
    <value>Объем = </value>
  </data>
  <data name="VolumeCounterVolumeRemaining" xml:space="preserve">
    <value>Volume remaining = </value>
  </data>
  <data name="VolumeDivisor" xml:space="preserve">
    <value>Делитель объема</value>
  </data>
  <data name="VolumeDown" xml:space="preserve">
    <value>Down volume</value>
  </data>
  <data name="VolumeDownColor" xml:space="preserve">
    <value>Цвет объема вниз</value>
  </data>
  <data name="VolumeNeutralColor" xml:space="preserve">
    <value>Нейтральный цвет объема</value>
  </data>
  <data name="VolumeUp" xml:space="preserve">
    <value>Объем вверх</value>
  </data>
  <data name="VolumeUpColor" xml:space="preserve">
    <value>Цвет объема вверх</value>
  </data>
  <data name="VOLVolume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Ширина</value>
  </data>
  <data name="WilliamsPercentR" xml:space="preserve">
    <value>Williams %R</value>
  </data>
  <data name="ZigZagDeviationValueError" xml:space="preserve">
    <value>"ZigZag не может отображать значения, так как значение отклонения слишком большое. Пожалуйста, уменьшите его".</value>
  </data>
  <data name="ZigZagHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}.HighBar: barsAgo out of valid range 0 through {1}, was {2}</value>
  </data>
  <data name="ZigZagHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}.HighBar: instance must be greater/equal 1 but was {1}</value>
  </data>
  <data name="ZigZagLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}.LowBar: barsAgo out of valid range 0 through {1}, was {2}</value>
  </data>
  <data name="ZigZagLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}.LowBar: instance must be greater/equal 1 but was {1}</value>
  </data>
  <data name="ZigZigHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}.HighBar: barsAgo must be greater/equal 0 but was {1}</value>
  </data>
  <data name="ZigZigLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}.HighBar: instance must be greater/equal 1 but was {1}</value>
  </data>
  <data name="PullingStackingDisplayType_Ask" xml:space="preserve">
    <value>Просить</value>
  </data>
  <data name="RecentResetWhen_PriceReturns" xml:space="preserve">
    <value>Цена возвращается</value>
  </data>
  <data name="RecentResetWhen_BidAskChange" xml:space="preserve">
    <value>Изменение спроса/предложения</value>
  </data>
  <data name="RecentDisplayType_Bid" xml:space="preserve">
    <value>Делать ставку</value>
  </data>
  <data name="RecentDisplayType_BidAsk" xml:space="preserve">
    <value>Спрос и предложение</value>
  </data>
  <data name="RecentDisplayType_Ask" xml:space="preserve">
    <value>Просить</value>
  </data>
  <data name="PropertyCategoryVisual" xml:space="preserve">
    <value>Визуальный</value>
  </data>
  <data name="NinjaScriptSetup" xml:space="preserve">
    <value>Настраивать</value>
  </data>
  <data name="NinjaScriptRecentColumnResetWhen" xml:space="preserve">
    <value>Сбросить когда</value>
  </data>
  <data name="NinjaScriptRecentColumnResetTolerance" xml:space="preserve">
    <value>Сбросить толерантность</value>
  </data>
  <data name="NinjaScriptRecentColumnDiplay" xml:space="preserve">
    <value>дисплей</value>
  </data>
  <data name="NinjaScriptRecentColumnBidForeground" xml:space="preserve">
    <value>Ставка переднего плана</value>
  </data>
  <data name="NinjaScriptRecentColumnBidBackground" xml:space="preserve">
    <value>Предыстория предложения</value>
  </data>
  <data name="NinjaScriptRecentColumnAskForeground" xml:space="preserve">
    <value>Спросите передний план</value>
  </data>
  <data name="NinjaScriptRecentColumnAskBackground" xml:space="preserve">
    <value>Спросите фон</value>
  </data>
  <data name="PullingStackingDisplayType_BidAsk" xml:space="preserve">
    <value>Спрос и предложение</value>
  </data>
  <data name="PullingStackingDisplayType_Bid" xml:space="preserve">
    <value>Делать ставку</value>
  </data>
  <data name="PullingStackingResetWhen_BidAskChange" xml:space="preserve">
    <value>Изменение спроса/предложения</value>
  </data>
  <data name="PullingStackingResetWhen_NoMoreData" xml:space="preserve">
    <value>Больше не получаю данные о глубине</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceMarker" xml:space="preserve">
    <value>Маркировка цены</value>
  </data>
</root>