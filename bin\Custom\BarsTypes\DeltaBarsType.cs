#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
#endregion

namespace NinjaTrader.NinjaScript.BarsTypes
{
	public class DeltaBarsType : BarsType
	{
		int    lastT = 0;
		bool   nSess = false;
		double upVol = 0.0;
		double dnVol = 0.0;
		double added = 0.0;
		double currD = 0.0;
		double restV = 0.0;
		double currR = 0.0;
		
		protected override void OnStateChange()
		{
			if(State == State.SetDefaults)
			{
				Description			= @"";
				Name				= "DeltaBarsType";
				BarsPeriod			= new BarsPeriod { BarsPeriodType = (BarsPeriodType) 93, BarsPeriodTypeName = "DeltaBarsType(93)", Value = 1 };
				BuiltFrom			= BarsPeriodType.Tick;
				DaysToLoad			= 5;
				IsIntraday			= true;
				IsTimeBased			= false;
			}
			else if(State == State.Configure)
			{
				Properties.Remove(Properties.Find("BaseBarsPeriodType",			true));
				Properties.Remove(Properties.Find("BaseBarsPeriodValue",		true));
				Properties.Remove(Properties.Find("PointAndFigurePriceType",	true));
				Properties.Remove(Properties.Find("ReversalType",				true));
				Properties.Remove(Properties.Find("Value2",						true));
				
				SetPropertyName("Value", "Period");
				
				Name = "Delta "+BarsPeriod.Value;
			}
		}
		
		public override int GetInitialLookBackDays(BarsPeriod barsPeriod, TradingHours tradingHours, int barsBack)
		{
			return 5;
		}
		
		protected override void OnDataPoint(Bars bars, double open, double high, double low, double close, DateTime time, long volume, bool isBar, double bid, double ask)
		{
			if(SessionIterator == null)
			{
				SessionIterator = new SessionIterator(bars);
			}
			
			nSess = SessionIterator.IsNewSession(time, isBar);
			
			if(nSess)
			{
				SessionIterator.GetNextSession(time, isBar);
			}
			
			// ---
			
			if(bars.Count == 0 || bars.IsResetOnNewTradingDay && nSess)
			{
				AddBar(bars, close, close, close, close, time, volume);
				
				upVol = 0.0;
				dnVol = 0.0;
				
				return;
			}
			
			// ---
			
			if(close >= ask)
			{
				lastT = 1;
				upVol += volume;
			}
			
			if(close <= bid)
			{
				lastT = -1;
				dnVol += volume;
			}
			
			added = volume;
			currD = Math.Abs(upVol - dnVol);
			
			if(currD >= (double)bars.BarsPeriod.Value)
			{
				currR = currD - (double)bars.BarsPeriod.Value;
				restV = added - currR;
				
				if(restV > 0.0)
				{
					UpdateBar(bars, high, low, close, time, (long)restV);
				}
				
				while(currR > 0.0)
				{
					AddBar(bars, open, high, low, close, time, (long)Math.Min((double)bars.BarsPeriod.Value, currR));
					
					upVol = (lastT > 0) ? currR : 0.0;
					dnVol = (lastT < 0) ? currR : 0.0;
					
					currR -= Math.Min((double)bars.BarsPeriod.Value, currR);
				}
			}
			else
			{
				UpdateBar(bars, high, low, close, time, volume);
			}
		}
		
		public override void ApplyDefaultBasePeriodValue(BarsPeriod period) {}
		
		public override void ApplyDefaultValue(BarsPeriod period)
		{
			period.BarsPeriodTypeName = "Delta Bars";
			period.Value 			  = 300;
		}
		
		public override string ChartLabel(DateTime dateTime)
		{
			return "Delta Bars";
		}
		
		public override double GetPercentComplete(Bars bars, DateTime now)
		{
			return ((100.0 / (double)bars.BarsPeriod.Value) * currD);
		}
	}
}