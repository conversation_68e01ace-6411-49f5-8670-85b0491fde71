﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTTabPage>
    <InstrumentLink>0</InstrumentLink>
    <IntervalLink>0</IntervalLink>
    <ChartTraderVisibility>Collapsed</ChartTraderVisibility>
    <SeriesCount>1</SeriesCount>
    <DataSeries>
      <BarsProperties>
        <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <BarsPeriod>
            <BarsPeriodTypeSerialize>5</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
          <RangeType>Days</RangeType>
          <BarsBack>50</BarsBack>
          <DaysBack>365</DaysBack>
          <From>2024-04-30T00:00:00</From>
          <IsStableSession>true</IsStableSession>
          <To>2099-12-01T00:00:00</To>
          <TradingHoursSerializable />
          <AutoScale>false</AutoScale>
          <CenterPriceOnScale>false</CenterPriceOnScale>
          <DisplayInDataBox>false</DisplayInDataBox>
          <Label>ES JUN25</Label>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <Panel>0</Panel>
          <PriceMarker>
            <BackgroundSerialize>DEFAULT</BackgroundSerialize>
            <IsVisible>false</IsVisible>
          </PriceMarker>
          <ShowGlobalDrawObjects>false</ShowGlobalDrawObjects>
          <ScaleJustification>Overlay</ScaleJustification>
          <TradingHoursVisibility>Off</TradingHoursVisibility>
          <TradingHoursBreakPenSerialize>&lt;Pen Thickness="1" Brush="#FFC0C0C0" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
          <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
          <PlotExecutions>DoNotPlot</PlotExecutions>
          <MarkerSize>5</MarkerSize>
          <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
          <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
          <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
          <BarsSeriesId>7bc53c2d886344e2801b64509296ca36</BarsSeriesId>
          <Id>7bc53c2d886344e2801b64509296ca36</Id>
          <Instrument>ES JUN25</Instrument>
          <IsLinked>true</IsLinked>
          <IsPrimarySeries>true</IsPrimarySeries>
          <ZOrder>1</ZOrder>
        </BarsProperties>
        <ChartStyles>
          <ChartStyle>
            <CandleStyle>
              <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <BarWidth>3</BarWidth>
                <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                <DownBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</DownBrushSerialize>
                <UpBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</UpBrushSerialize>
                <StrokeSerialize>&lt;Pen Thickness="1" Brush="#00FFFFFF" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StrokeSerialize>
                <Stroke2Serialize>&lt;Pen Thickness="1" Brush="#00FFFFFF" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</Stroke2Serialize>
              </CandleStyle>
            </CandleStyle>
          </ChartStyle>
        </ChartStyles>
      </BarsProperties>
    </DataSeries>
    <Indicators>
      <Indicator BarsIndex="0" Instrument="ES JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.aiDuplicateAccountActions" Panel="-1" DisplayName="aiDuplicateAccountActions(3)">
        <aiDuplicateAccountActions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>false</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>5</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>false</DisplayInDataBox>
          <From>2024-04-30T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-04-30T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>false</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>aiDuplicateAccountActions</Name>
          <Plots />
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>798</IndicatorId>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>0</ZOrder>
          <SelectedColumn>Account</SelectedColumn>
          <ChartTraderNeedsDisabled>false</ChartTraderNeedsDisabled>
          <WindowPrivacyAccounts>true</WindowPrivacyAccounts>
          <WindowPrivacyCurrency>true</WindowPrivacyCurrency>
          <CurrencyPrivacy>Balances</CurrencyPrivacy>
          <RejectedOrderHandling>false</RejectedOrderHandling>
          <CheckBeforeSubmitting>true</CheckBeforeSubmitting>
          <RejectedSubmit>Limit</RejectedSubmit>
          <RejectedSubmitOff>0</RejectedSubmitOff>
          <MatchStopLimit>true</MatchStopLimit>
          <ResubmitMaster>true</ResubmitMaster>
          <ExitShieldFeaturesEnabled>false</ExitShieldFeaturesEnabled>
          <ExitShieldButtonEnabled>true</ExitShieldButtonEnabled>
          <ExitShieldStopLoss>true</ExitShieldStopLoss>
          <ExitShieldProfitTarget>false</ExitShieldProfitTarget>
          <UseOriginalLocation>true</UseOriginalLocation>
          <ExitShieldMessages>true</ExitShieldMessages>
          <ExitShieldIsEnabled>false</ExitShieldIsEnabled>
          <HideAccountsIsEnabled>false</HideAccountsIsEnabled>
          <HideCurrencyIsEnabled>false</HideCurrencyIsEnabled>
          <CopierIsEnabled>false</CopierIsEnabled>
          <SelectedAscending>true</SelectedAscending>
          <AllInstruments>true</AllInstruments>
          <IsCrossEnabled>false</IsCrossEnabled>
          <IsXEnabled>true</IsXEnabled>
          <IsATMSelectEnabled>false</IsATMSelectEnabled>
          <IsFadeEnabled>false</IsFadeEnabled>
          <IsBuildMode>true</IsBuildMode>
          <IsXColumnEnabled>true</IsXColumnEnabled>
          <MultiplierMode>Multiplier</MultiplierMode>
          <LastMultiplierMode>Multiplier</LastMultiplierMode>
          <IsATMColumnEnabled>true</IsATMColumnEnabled>
          <TheCopierMode>Executions</TheCopierMode>
          <IsCrossColumnEnabled>true</IsCrossColumnEnabled>
          <FDAXMini>FDXM</FDAXMini>
          <FDAXMicro>FDXS</FDAXMicro>
          <IsFadeColumnEnabled>true</IsFadeColumnEnabled>
          <ShowStatusMessages>true</ShowStatusMessages>
          <FontSizeTopRow3>0</FontSizeTopRow3>
          <ForceAllSelectedAccountsToTop>false</ForceAllSelectedAccountsToTop>
          <CloseButtonLocation>Left</CloseButtonLocation>
          <ShowHideButton>true</ShowHideButton>
          <CloseButtonEnabled>true</CloseButtonEnabled>
          <CancelButtonEnabled>true</CancelButtonEnabled>
          <OneTradeAll>true</OneTradeAll>
          <OneTradeEvalEnabled>true</OneTradeEvalEnabled>
          <OneTradePAEnabled>true</OneTradePAEnabled>
          <OneTradeShow>Totals</OneTradeShow>
          <HideExtraAccountsOnLock>true</HideExtraAccountsOnLock>
          <LockAccountsOnLock>true</LockAccountsOnLock>
          <LockHideAccountsOnLock>true</LockHideAccountsOnLock>
          <ShowRefreshPositions>true</ShowRefreshPositions>
          <RPString>Refresh Positions</RPString>
          <KeyboardEnabled>true</KeyboardEnabled>
          <KeyScrollUp>NumPad8</KeyScrollUp>
          <KeyScrollDn>NumPad2</KeyScrollDn>
          <UseNumberForMultiplier>true</UseNumberForMultiplier>
          <RefreshButtonBS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF8B4513&lt;/SolidColorBrush&gt;</RefreshButtonBS>
          <ShowFRPMessage>true</ShowFRPMessage>
          <ShowFlattenEverything>true</ShowFlattenEverything>
          <FLString>FLATTEN ALL</FLString>
          <FlattenButtonBS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF800000&lt;/SolidColorBrush&gt;</FlattenButtonBS>
          <ShowFEMessage>true</ShowFEMessage>
          <PrivacyEnabled>false</PrivacyEnabled>
          <AccountPrivacy1 />
          <AccountReplace1 />
          <AccountPrivacy2 />
          <AccountReplace2 />
          <AccountPrivacy3 />
          <AccountReplace3 />
          <AccountPrivacy4 />
          <AccountReplace4 />
          <AccountPrivacy5 />
          <AccountReplace5 />
          <AccountPrivacy6 />
          <AccountReplace6 />
          <AccountPrivacy7 />
          <AccountReplace7 />
          <AccountPrivacy8 />
          <AccountReplace8 />
          <AccountPrivacy9 />
          <AccountReplace9 />
          <AccountPrivacy10 />
          <AccountReplace10 />
          <AccountPrivacy11 />
          <AccountReplace11 />
          <AccountPrivacy12 />
          <AccountReplace12 />
          <APEXCoupon>DVUDKBFF</APEXCoupon>
          <BluSkyCoupon />
          <BulenoxCoupon />
          <DayTradersCoupon />
          <EliteTraderFundingCoupon />
          <FundedFuturesNetworkCoupon />
          <LeeLooCoupon />
          <TheLegendsTradingCoupon />
          <LifeUpTradingCoupon />
          <MyFundedFuturesCoupon />
          <PhidiasCoupon />
          <PurdiaCoupon />
          <TakeProfitTraderCoupon />
          <TickTickCoupon />
          <TopStepCoupon />
          <TradeDayCoupon />
          <TradeFundrrCoupon />
          <TradeifyCoupon />
          <ShowDiscountLinks>true</ShowDiscountLinks>
          <ActionsHideColumnEnabled>true</ActionsHideColumnEnabled>
          <ActionsButtonLocation>Left</ActionsButtonLocation>
          <TextFont3>
            <Bold>false</Bold>
            <FamilySerialize>Arial</FamilySerialize>
            <Italic>false</Italic>
            <Size>12</Size>
          </TextFont3>
          <CompDNColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF4682B4&lt;/SolidColorBrush&gt;</CompDNColorS>
          <CompMinOpacity>25</CompMinOpacity>
          <CompMinOpacityH>50</CompMinOpacityH>
          <BackMasterAccountColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF1E1E1E&lt;/SolidColorBrush&gt;</BackMasterAccountColorS>
          <BackSlaveAccountColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</BackSlaveAccountColorS>
          <CompMinOpacityHAcc>25</CompMinOpacityHAcc>
          <BackBuyColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008000&lt;/SolidColorBrush&gt;</BackBuyColorS>
          <BackSellColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</BackSellColorS>
          <BackBuyColor3S>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</BackBuyColor3S>
          <BackSellColor3S>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</BackSellColor3S>
          <CompPOSOpacity>40</CompPOSOpacity>
          <pConnectedOnS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</pConnectedOnS>
          <pConnectedOffS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</pConnectedOffS>
          <ConnectedLostS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFA500&lt;/SolidColorBrush&gt;</ConnectedLostS>
          <pConnectedOtherS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFD700&lt;/SolidColorBrush&gt;</pConnectedOtherS>
          <HighlightHoverCells>true</HighlightHoverCells>
          <HighlightAmount>10</HighlightAmount>
          <HighlightHoverButtons>true</HighlightHoverButtons>
          <ButtonHighlightO>40</ButtonHighlightO>
          <LeftRightPad>8</LeftRightPad>
          <TopBottomPad>5</TopBottomPad>
          <RemoveIcons>true</RemoveIcons>
          <AffiliateLinkDone1>false</AffiliateLinkDone1>
          <AffiliateLinkDone2>false</AffiliateLinkDone2>
          <FirstLoadIsDone>true</FirstLoadIsDone>
          <IsCopyBasicFunctionsEnabled>true</IsCopyBasicFunctionsEnabled>
          <IsRiskFunctionsEnabled>false</IsRiskFunctionsEnabled>
          <IsCopyBasicFunctionsPermission>true</IsCopyBasicFunctionsPermission>
          <IsRiskFunctionsPermission>true</IsRiskFunctionsPermission>
          <IsCopyBasicFunctionsChecked>true</IsCopyBasicFunctionsChecked>
          <IsRiskFunctionsChecked>false</IsRiskFunctionsChecked>
          <FillFundedCells>true</FillFundedCells>
          <IsFundedColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</IsFundedColorS>
          <ColumnFromFund>true</ColumnFromFund>
          <IsEvalCloseEnabled>true</IsEvalCloseEnabled>
          <DollarsExceedFunded>50</DollarsExceedFunded>
          <PAFundedAmouont>0</PAFundedAmouont>
          <DisableFundedFollowers>true</DisableFundedFollowers>
          <ColumnAutoLiquidate>true</ColumnAutoLiquidate>
          <ColumnRemaining>true</ColumnRemaining>
          <FillTrailingCells>true</FillTrailingCells>
          <PercentWarning1>60</PercentWarning1>
          <PercentWarning2>30</PercentWarning2>
          <TrailingGoodColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</TrailingGoodColorS>
          <TrailingWarningColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</TrailingWarningColorS>
          <TrailingBadColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFA500&lt;/SolidColorBrush&gt;</TrailingBadColorS>
          <TrailingBlownColorS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA52A2A&lt;/SolidColorBrush&gt;</TrailingBlownColorS>
          <ColumnDailyGoal>true</ColumnDailyGoal>
          <ColumnDailyLoss>true</ColumnDailyLoss>
          <ColumnProfitRequested>false</ColumnProfitRequested>
          <OnMasterGoalHitCloseFollowers>false</OnMasterGoalHitCloseFollowers>
          <DisableGoalFollowers>true</DisableGoalFollowers>
          <DailyGoalLossBuffer>20</DailyGoalLossBuffer>
          <AdjustmentDollars>100</AdjustmentDollars>
          <RestrictToZero>true</RestrictToZero>
          <DailyGoalDisplayMode>Update</DailyGoalDisplayMode>
          <DailyLossDisplayMode>Update</DailyLossDisplayMode>
          <SelectedLanguage>Default</SelectedLanguage>
          <ShowFollowerColumnButtons>true</ShowFollowerColumnButtons>
          <CopierButtonOn2S>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</CopierButtonOn2S>
          <LockButtonOnS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF800000&lt;/SolidColorBrush&gt;</LockButtonOnS>
          <AreaBrushSerialize>&lt;SolidColorBrush Color="#FF808080" Opacity="0.2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" /&gt;</AreaBrushSerialize>
          <AreaOpacity>20</AreaOpacity>
          <FontSizeTopRow>2</FontSizeTopRow>
          <FontSizeB>0</FontSizeB>
          <ThisMasterAccount>FTDYFA150355451300000013</ThisMasterAccount>
          <ColumnAudioE>true</ColumnAudioE>
          <ColumnEMAE>true</ColumnEMAE>
          <ColumnTimeStatusE>true</ColumnTimeStatusE>
          <ColumnTrendStatusE>true</ColumnTrendStatusE>
          <ColumnLastE>true</ColumnLastE>
          <ColumnEntryE>true</ColumnEntryE>
          <ColumnStopE>true</ColumnStopE>
          <ColumnT1E>true</ColumnT1E>
          <ColumnT2E>true</ColumnT2E>
          <ColumnT3E>true</ColumnT3E>
          <ShowAllPriceColE>true</ShowAllPriceColE>
          <ShowAvgPrice>false</ShowAvgPrice>
          <UnrealizedColumn>true</UnrealizedColumn>
          <RealizedColumn>true</RealizedColumn>
          <GrossRealizedColumn>false</GrossRealizedColumn>
          <CashValueColumn>false</CashValueColumn>
          <NetLiquidationColumn>true</NetLiquidationColumn>
          <QtyColumn2>true</QtyColumn2>
          <CommissionsColumn>false</CommissionsColumn>
          <TotalPNLColumn>true</TotalPNLColumn>
          <AlertLogEnabled>true</AlertLogEnabled>
          <AudioEnabled>false</AudioEnabled>
          <WAVFileName>Alert2.wav</WAVFileName>
          <ArrowUpFBrushS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008000&lt;/SolidColorBrush&gt;</ArrowUpFBrushS>
          <WAVFileName2>Alert2.wav</WAVFileName2>
          <ArrowDownFBrushS>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</ArrowDownFBrushS>
          <SpeechEnabled>true</SpeechEnabled>
          <ThisIndyName>BOT</ThisIndyName>
          <VoiceMode>Name</VoiceMode>
          <PreviousVersionName>25. 1. 16. 1</PreviousVersionName>
          <ShowTotalRows>true</ShowTotalRows>
          <Total1Name2>PA-APEX</Total1Name2>
          <Total2Name2>APEX</Total2Name2>
          <Total3Name2 />
          <Total5Name2 />
          <Total6Name2 />
          <Total7Name2 />
          <Total1Filter>PA-APEX</Total1Filter>
          <Total2Filter>APEX</Total2Filter>
          <Total3Filter />
          <Total5Filter />
          <Total6Filter />
          <Total7Filter />
          <ShowGrandTotal>true</ShowGrandTotal>
          <Total4Name2>Total</Total4Name2>
          <DisableOnStart>false</DisableOnStart>
          <DisableOnNewSession>false</DisableOnNewSession>
          <DisableInActiveWorkspace>true</DisableInActiveWorkspace>
          <CopierMode>Executions</CopierMode>
          <ShowAccountsMain>All</ShowAccountsMain>
          <AccountFilter />
          <ShowAccounts>All</ShowAccounts>
          <AccountsSimEnabled>true</AccountsSimEnabled>
          <AccountsLiveEnabled>true</AccountsLiveEnabled>
          <ProtectSec>3</ProtectSec>
          <SupportCode />
          <AllAccountData>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string>FTDYFA150355451300000013|None|1|No|No|No|No|No|Default</string>
            <string>Sim101|None|1|No|No|No|No|No|Default</string>
            <string>TDYA150355451300000017|Slave|2|No|No|No|No|No|Default</string>
            <string>|None|1|No|No|No|No|No|Default</string>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountData>
          <AllAccountCashValue>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string>FTDYFA150355451300000013|150000</string>
            <string>Sim101|100000</string>
            <string>TDYA150355451300000017|150000</string>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountCashValue>
          <AllAccountNetLiquidation>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string>FTDYFA150355451300000013|150000</string>
            <string>Sim101|100000</string>
            <string>TDYA150355451300000017|150000</string>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountNetLiquidation>
          <AllAccountAutoLiquidate>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string>FTDYFA150355451300000013|150500</string>
            <string>TDYA150355451300000017|150500</string>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountAutoLiquidate>
          <AllAccountDailyGoal>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountDailyGoal>
          <AllAccountDailyLoss>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountDailyLoss>
          <AllAccountPayouts>
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
            <string />
          </AllAccountPayouts>
          <OldColumnDailyLoss>false</OldColumnDailyLoss>
          <EventToSend>Execution Update</EventToSend>
          <EnableTradingViewDetections>true</EnableTradingViewDetections>
          <TradingViewExitDetection>Limit</TradingViewExitDetection>
          <MatchStopLossPrices>true</MatchStopLossPrices>
        </aiDuplicateAccountActions>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
    </Indicators>
    <CrosshairType>Off</CrosshairType>
    <StayInDrawMode>False</StayInDrawMode>
    <Properties>
      <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AlwaysOnTop>false</AlwaysOnTop>
        <AreTabsVisible>true</AreTabsVisible>
        <AllowSelectionDragging>true</AllowSelectionDragging>
        <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
        <EquidistantBarSpacing>true</EquidistantBarSpacing>
        <LabelFont>
          <Bold>false</Bold>
          <FamilySerialize>Arial</FamilySerialize>
          <Italic>false</Italic>
          <Size>1</Size>
        </LabelFont>
        <BarDistance>2000</BarDistance>
        <BarMarginRightUser>8</BarMarginRightUser>
        <ChartTraderVisibility>Collapsed</ChartTraderVisibility>
        <ShowDateRange>false</ShowDateRange>
        <ShowScrollBar>false</ShowScrollBar>
        <SnapMode>Bar</SnapMode>
        <TabName>@INSTRUMENT_FULL</TabName>
        <LoadBackgroundImage>false</LoadBackgroundImage>
        <BackgroundImageStretch>Fill</BackgroundImageStretch>
        <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
        <ChartTextSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</ChartTextSerialize>
        <CrosshairLabelBackgroundSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</CrosshairLabelBackgroundSerialize>
        <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
        <AreHGridLinesVisible>false</AreHGridLinesVisible>
        <AreVGridLinesVisible>false</AreVGridLinesVisible>
        <AxisPenSerialize>&lt;Pen Thickness="1" Brush="#00FFFFFF" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</AxisPenSerialize>
        <CrosshairPen>DEFAULT</CrosshairPen>
        <CrosshairIsLocked>false</CrosshairIsLocked>
        <CrosshairCrosshairType>Off</CrosshairCrosshairType>
        <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
        <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
        <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
        <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
      </ChartControlProperties>
    </Properties>
    <ChartPanels>
      <ChartPanel>
        <Height>562</Height>
        <HoldChartTraderOrders>false</HoldChartTraderOrders>
        <IsMaximized>false</IsMaximized>
        <Right>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>1</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>1</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Right>
        <Left>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Left>
        <Overlay>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>5583</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>5524.25</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Overlay>
      </ChartPanel>
    </ChartPanels>
    <ChartAlerts />
  </NTTabPage>
  <ChartTrader>
    <Properties>
      <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
        <AutoScale>true</AutoScale>
        <OrderDisplayBarLength>25</OrderDisplayBarLength>
        <PnLDisplayUnit>Points</PnLDisplayUnit>
        <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
        <ScaleQuantity>0</ScaleQuantity>
        <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
        <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
        <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
        <StopLimitOffsetValue>0</StopLimitOffsetValue>
        <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
        <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
        <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
        <LimitSerialize>&lt;Pen Thickness="2" Brush="#FF00FFFF" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
        <MitSerialize>&lt;Pen Thickness="2" Brush="#FF00FF7F" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
        <PositionEntryPriceSerialize>&lt;Pen Thickness="2" Brush="#FFDEB887" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
        <ProfitTargetSerialize>&lt;Pen Thickness="2" Brush="#FF32CD32" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
        <StopLimitSerialize>&lt;Pen Thickness="2" Brush="#FFEE82EE" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
        <StopLossSerialize>&lt;Pen Thickness="2" Brush="#FFFF0000" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
        <StopMarketSerialize>&lt;Pen Thickness="2" Brush="#FFFFC0CB" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
      </ChartTraderProperties>
    </Properties>
    <Account>Sim101</Account>
    <ATM></ATM>
    <Instrument>ES JUN25</Instrument>
    <Quantity>1</Quantity>
    <TIF>Day</TIF>
  </ChartTrader>
</NinjaTrader>