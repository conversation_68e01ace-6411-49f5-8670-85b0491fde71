﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Acceleration" xml:space="preserve">
    <value>Beschleunigung</value>
  </data>
  <data name="AccelerationMax" xml:space="preserve">
    <value>Max. Beschleunigung</value>
  </data>
  <data name="AccelerationStep" xml:space="preserve">
    <value>Beschleunigung Schrittweite</value>
  </data>
  <data name="ADLAD" xml:space="preserve">
    <value>AD</value>
  </data>
  <data name="AlertOnBreak" xml:space="preserve">
    <value>Warnung bei Unterbrechungen</value>
  </data>
  <data name="AlertOnBreakSound" xml:space="preserve">
    <value>Warnungston bei Unterbrechungen</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_ModifiedSchiff" xml:space="preserve">
    <value>Modified Schiff</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_Schiff" xml:space="preserve">
    <value>Schiff</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_StandardPitchfork" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="AskLineLength" xml:space="preserve">
    <value>Ask Zeilenlänge (% des Charts)</value>
  </data>
  <data name="AskLineStroke" xml:space="preserve">
    <value>Ask Linie</value>
  </data>
  <data name="AuthDisclosureText1" xml:space="preserve">
    <value>Urheberrecht &lt;sup&gt;©&lt;/sup&gt; {0}. Alle Rechte vorbehalten. NinjaTrader und das NinjaTrader Logo. U.S. Pat. amp; Tm. Aus.</value>
  </data>
  <data name="AuthDisclosureText2" xml:space="preserve">
    <value>VOLLSTÄNDIGE RISIKOOFFENLEGUNG: Der Handel mit Futures und Forex birgt ein erhebliches Risiko und ist nicht für jeden Anleger geeignet. Ein Investor könnte möglicherweise alles oder mehr als die ursprüngliche Investition verlieren. Risikokapital ist Geld, das verloren gehen kann, ohne die finanzielle Sicherheit oder den Lebensstil zu gefährden. Für den Handel sollte nur Risikokapital verwendet werden, und nur Personen mit ausreichendem Risikokapital sollten den Handel in Betracht ziehen. Die Wertentwicklung in der Vergangenheit ist nicht unbedingt ein Indikator für zukünftige Ergebnisse.</value>
  </data>
  <data name="BandPct" xml:space="preserve">
    <value>Band Prozent</value>
  </data>
  <data name="BarCount" xml:space="preserve">
    <value>Kerzenanzahl</value>
  </data>
  <data name="BarDown" xml:space="preserve">
    <value>Abwärtskerze</value>
  </data>
  <data name="BarSpacing" xml:space="preserve">
    <value>Kerzenabstand</value>
  </data>
  <data name="BarsPeriodType" xml:space="preserve">
    <value>Typ Kerzenperiode</value>
  </data>
  <data name="BarsPeriodTypeNameDay" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="BarsPeriodTypeNameHeikenAshi" xml:space="preserve">
    <value>Heiken-Ashi</value>
  </data>
  <data name="BarsPeriodTypeNameKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="BarsPeriodTypeNameLineBreak" xml:space="preserve">
    <value>Line Break</value>
  </data>
  <data name="BarsPeriodTypeNameMinute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="BarsPeriodTypeNameMonth" xml:space="preserve">
    <value>Monat</value>
  </data>
  <data name="BarsPeriodTypeNamePointAndFigure" xml:space="preserve">
    <value>Point &amp; Figure</value>
  </data>
  <data name="BarsPeriodTypeNameRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="BarsPeriodTypeNameRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="BarsPeriodTypeNameSecond" xml:space="preserve">
    <value>Sekunde</value>
  </data>
  <data name="BarsPeriodTypeNameTick" xml:space="preserve">
    <value>Tick</value>
  </data>
  <data name="BarsPeriodTypeNameVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="BarsPeriodTypeNameWeek" xml:space="preserve">
    <value>Woche</value>
  </data>
  <data name="BarsPeriodTypeNameYear" xml:space="preserve">
    <value>Jahr</value>
  </data>
  <data name="BarsPeriodValue" xml:space="preserve">
    <value>Wert Kerzenperiode</value>
  </data>
  <data name="BarTimerDisconnectedError" xml:space="preserve">
    <value>Bar-Timer deaktiviert, da Sie derzeit von einem Datenprovider getrennt sind</value>
  </data>
  <data name="BarTimerSessionTimeError" xml:space="preserve">
    <value>Bar-Timer deaktiviert, da die aktuelle Zeit außerhalb der Session-Zeit oder Chart Enddatum ist</value>
  </data>
  <data name="BarTimerTimeBasedError" xml:space="preserve">
    <value>Bar-Timer funktioniert nur in Intraday-Zeitintervallen</value>
  </data>
  <data name="BarTimerTimeRemaining" xml:space="preserve">
    <value>Verbleibende Zeit =</value>
  </data>
  <data name="BarTimerWaitingOnDataError" xml:space="preserve">
    <value>BarTimer wartet auf Echtzeitdaten vor dem Start</value>
  </data>
  <data name="BarUp" xml:space="preserve">
    <value>Aufwärtskerze</value>
  </data>
  <data name="BasePeriod" xml:space="preserve">
    <value>Basisperiode</value>
  </data>
  <data name="BidLineLength" xml:space="preserve">
    <value>Bid Zeilenlänge (% des Charts)</value>
  </data>
  <data name="BidLineStroke" xml:space="preserve">
    <value>Bid Linie</value>
  </data>
  <data name="BlockTradeSize" xml:space="preserve">
    <value>Blocktrade Größe</value>
  </data>
  <data name="BollingerLowerBand" xml:space="preserve">
    <value>Unteres Band</value>
  </data>
  <data name="BollingerMiddleBand" xml:space="preserve">
    <value>Mittleres Band</value>
  </data>
  <data name="BollingerUpperBand" xml:space="preserve">
    <value>Oberes Band</value>
  </data>
  <data name="BuySellPressureBuyPressure" xml:space="preserve">
    <value>Kaufdruck</value>
  </data>
  <data name="BuySellPressureSellPressure" xml:space="preserve">
    <value>Verkaufsdruck</value>
  </data>
  <data name="BuySellVolumeBuys" xml:space="preserve">
    <value>Käufe</value>
  </data>
  <data name="BuySellVolumeSells" xml:space="preserve">
    <value>Verkäufe</value>
  </data>
  <data name="CandlestickPatternFound" xml:space="preserve">
    <value>Pattern gefunden</value>
  </data>
  <data name="CCILevel1" xml:space="preserve">
    <value>Level 1</value>
  </data>
  <data name="CCILevel2" xml:space="preserve">
    <value>Level 2</value>
  </data>
  <data name="CCILevelMinus1" xml:space="preserve">
    <value>Level -1</value>
  </data>
  <data name="CCILevelMinus2" xml:space="preserve">
    <value>Level -2</value>
  </data>
  <data name="ChartSpan_Day" xml:space="preserve">
    <value>1 Tag</value>
  </data>
  <data name="ChartSpan_Min1" xml:space="preserve">
    <value>1 min</value>
  </data>
  <data name="ChartSpan_Min15" xml:space="preserve">
    <value>15 min</value>
  </data>
  <data name="ChartSpan_Min240" xml:space="preserve">
    <value>240 min</value>
  </data>
  <data name="ChartSpan_Min30" xml:space="preserve">
    <value>30 min</value>
  </data>
  <data name="ChartSpan_Min5" xml:space="preserve">
    <value>5 min</value>
  </data>
  <data name="ChartSpan_Min60" xml:space="preserve">
    <value>60 min</value>
  </data>
  <data name="ChartSpan_Month" xml:space="preserve">
    <value>1 Monat</value>
  </data>
  <data name="ChartSpan_Week" xml:space="preserve">
    <value>1 Woche</value>
  </data>
  <data name="ChartSpan_Year" xml:space="preserve">
    <value>1 Jahr</value>
  </data>
  <data name="ConstantLines1" xml:space="preserve">
    <value>Linie 1</value>
  </data>
  <data name="ConstantLines2" xml:space="preserve">
    <value>Linie 2</value>
  </data>
  <data name="ConstantLines3" xml:space="preserve">
    <value>Linie 3</value>
  </data>
  <data name="ConstantLines4" xml:space="preserve">
    <value>Linie 4</value>
  </data>
  <data name="COT1" xml:space="preserve">
    <value>COT 1</value>
  </data>
  <data name="COT2" xml:space="preserve">
    <value>COT 2</value>
  </data>
  <data name="COT3" xml:space="preserve">
    <value>COT 3</value>
  </data>
  <data name="COT4" xml:space="preserve">
    <value>COT 4</value>
  </data>
  <data name="COT5" xml:space="preserve">
    <value>COT 5</value>
  </data>
  <data name="CotDataError" xml:space="preserve">
    <value>COT-Daten werden für dieses Instrument nicht unterstützt</value>
  </data>
  <data name="CotDataStillDownloading" xml:space="preserve">
    <value>COT-Daten werden noch heruntergeladen. Bitte aktualisieren Sie den Indikator in wenigen Augenblicken.</value>
  </data>
  <data name="CotDataWarning" xml:space="preserve">
    <value>müssen "COT-Daten beim Start herunterladen" aktivieren, um die neuesten COT-Daten</value>
  </data>
  <data name="CountDown" xml:space="preserve">
    <value>Count Down</value>
  </data>
  <data name="CountType_Trades" xml:space="preserve">
    <value>Trades</value>
  </data>
  <data name="CountType_Volume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="CurrentDayOHLError" xml:space="preserve">
    <value>CurrentDayOHL funktioniert nur in Intraday Darstellungen</value>
  </data>
  <data name="CurrentDayOHLHigh" xml:space="preserve">
    <value>Aktuelles Hoch</value>
  </data>
  <data name="CurrentDayOHLLow" xml:space="preserve">
    <value>Aktuelles Tief</value>
  </data>
  <data name="CurrentDayOHLOpen" xml:space="preserve">
    <value>Aktueller Open</value>
  </data>
  <data name="CustomWindowAddOnBuyMarket" xml:space="preserve">
    <value>Markt kaufen</value>
  </data>
  <data name="CustomWindowAddOnSellMarket" xml:space="preserve">
    <value>Markt verkaufen</value>
  </data>
  <data name="CustomWindowSampleDescription" xml:space="preserve">
    <value>Individuelle Beschreibung des Fensters</value>
  </data>
  <data name="CustomWindowSampleName" xml:space="preserve">
    <value>Beispiel individuelles Fenster</value>
  </data>
  <data name="DataBarsTypeDaily" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="DataBarsTypeDay" xml:space="preserve">
    <value>{0} Tag</value>
  </data>
  <data name="DataBarsTypeMinute" xml:space="preserve">
    <value>{0} Min{1}</value>
  </data>
  <data name="DataBarsTypeMonth" xml:space="preserve">
    <value>{0} Monat</value>
  </data>
  <data name="DataBarsTypeMonthly" xml:space="preserve">
    <value>Monatlich</value>
  </data>
  <data name="DataBarsTypePointAndFigure" xml:space="preserve">
    <value>{0} Point and Figure</value>
  </data>
  <data name="DataBarsTypeRange" xml:space="preserve">
    <value>{0} Range{1}</value>
  </data>
  <data name="DataBarsTypeRenko" xml:space="preserve">
    <value>{0} Renko</value>
  </data>
  <data name="DataBarsTypeSecond" xml:space="preserve">
    <value>{0} Sekunde</value>
  </data>
  <data name="DataBarsTypeTick" xml:space="preserve">
    <value>{0} Tick{1}</value>
  </data>
  <data name="DataBarsTypeVolume" xml:space="preserve">
    <value>{0} Volumen {1}</value>
  </data>
  <data name="DataBarsTypeWeek" xml:space="preserve">
    <value>{0} Woche</value>
  </data>
  <data name="DataBarsTypeWeekly" xml:space="preserve">
    <value>Woche</value>
  </data>
  <data name="DataBarsTypeYear" xml:space="preserve">
    <value>{0} Jahr</value>
  </data>
  <data name="DataBarsTypeYearly" xml:space="preserve">
    <value>Jährlich</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="DeviationType" xml:space="preserve">
    <value>Abweichungstyp</value>
  </data>
  <data name="DeviationValue" xml:space="preserve">
    <value>Abweichungswert</value>
  </data>
  <data name="DMMinusDI" xml:space="preserve">
    <value>-DI</value>
  </data>
  <data name="DMPlusDI" xml:space="preserve">
    <value>+DI</value>
  </data>
  <data name="DonchianChannelMean" xml:space="preserve">
    <value>Mitte</value>
  </data>
  <data name="DownBarColor" xml:space="preserve">
    <value>Farbe Abwärtskerze</value>
  </data>
  <data name="DrawingToolIndicatorDescription" xml:space="preserve">
    <value>Die Indikatorkachel Zeichenwerkzeuge fügt die Möglichkeit hinzu, eine anpassbare schwebende Kachel im Chart zu haben, um schnell auf die am häufigsten verwendeten Zeichenwerkzeuge zuzugreifen.</value>
  </data>
  <data name="DrawingToolIndicatorName" xml:space="preserve">
    <value>Zeichenwerkzeugkachel</value>
  </data>
  <data name="DrawLines" xml:space="preserve">
    <value>Linie zeichnen</value>
  </data>
  <data name="EMA1" xml:space="preserve">
    <value>EMA1 Periode</value>
  </data>
  <data name="EMA2" xml:space="preserve">
    <value>EMA2 Periode</value>
  </data>
  <data name="EmailSignature" xml:space="preserve">
    <value>Gesendet von NinjaTrader</value>
  </data>
  <data name="EnvelopePercentage" xml:space="preserve">
    <value>Bänder Prozentsatz</value>
  </data>
  <data name="FacebookServiceName" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="FacebookSignature" xml:space="preserve">
    <value>Gesendet von NinjaTrader</value>
  </data>
  <data name="Fast" xml:space="preserve">
    <value>Fast</value>
  </data>
  <data name="FastLimit" xml:space="preserve">
    <value>Fast Limit</value>
  </data>
  <data name="FastPeriod" xml:space="preserve">
    <value>Schnelle Periode</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeLeft" xml:space="preserve">
    <value>Extrem Links</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeRight" xml:space="preserve">
    <value>Extrem Rechts</value>
  </data>
  <data name="FibonacciTextAlignment_Left" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="FibonacciTextAlignment_Off" xml:space="preserve">
    <value>Aus</value>
  </data>
  <data name="FibonacciTextAlignment_Right" xml:space="preserve">
    <value>Rechts</value>
  </data>
  <data name="FileFilterAnyLoadingDialog" xml:space="preserve">
    <value>Jede (*.*)</value>
  </data>
  <data name="FileFilterAnyWinForms" xml:space="preserve">
    <value>Any (*.*)|*.*</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>Dateiname</value>
  </data>
  <data name="Font" xml:space="preserve">
    <value>Schrift</value>
  </data>
  <data name="Forecast" xml:space="preserve">
    <value>Prognose</value>
  </data>
  <data name="GannFanDirection_DownLeft" xml:space="preserve">
    <value>Unten Links</value>
  </data>
  <data name="GannFanDirection_DownRight" xml:space="preserve">
    <value>Unten Rechts</value>
  </data>
  <data name="GannFanDirection_UpLeft" xml:space="preserve">
    <value>Oben Links</value>
  </data>
  <data name="GannFanDirection_UpRight" xml:space="preserve">
    <value>Oben Rechts</value>
  </data>
  <data name="GuiAuthorize" xml:space="preserve">
    <value>Autorisieren</value>
  </data>
  <data name="GuiChartStyleDojiBrush" xml:space="preserve">
    <value>Farbe für Doji-Balken</value>
  </data>
  <data name="HigherHigh" xml:space="preserve">
    <value>Höheres Hoch</value>
  </data>
  <data name="HigherLow" xml:space="preserve">
    <value>Höheres Tief</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Currency" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Percent" xml:space="preserve">
    <value>Prozent</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Pips" xml:space="preserve">
    <value>Pips</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Ticks" xml:space="preserve">
    <value>Ticks</value>
  </data>
  <data name="HLCCalculationMode" xml:space="preserve">
    <value>HLC Berechnungsmodus (Hoch - Tief - Schluss)</value>
  </data>
  <data name="HLCCalculationMode_CalcFromIntradayData" xml:space="preserve">
    <value>Berechnet aus intraday Daten</value>
  </data>
  <data name="HLCCalculationMode_DailyBars" xml:space="preserve">
    <value>Tagesbalken verwenden</value>
  </data>
  <data name="HLCCalculationMode_UserDefinedValues" xml:space="preserve">
    <value>Benutzerdefinierte Werte verwenden</value>
  </data>
  <data name="HLCCalculationModeDescription" xml:space="preserve">
    <value>Methode zur Berechnung des vorherigen HLC Werte</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="ImportTypeNinjaTraderBeginningOfBar" xml:space="preserve">
    <value>NinjaTrader (Zeitstempel am Begin der Kerze)</value>
  </data>
  <data name="ImportTypeNinjaTraderDateTimeFormatError" xml:space="preserve">
    <value>{0}: Datum/Uhrzeit Format Fehler in Zeile {1}: {2}: '{3}'</value>
  </data>
  <data name="ImportTypeNinjaTraderEndOfBar" xml:space="preserve">
    <value>NinjaTrader (Zeitstempel am Ende der Kerze)</value>
  </data>
  <data name="ImportTypeNinjaTraderFieldSeparatorNotIdentified" xml:space="preserve">
    <value>{0}: Import Feldtrennzeichen konnten nicht identifiziert werden.</value>
  </data>
  <data name="ImportTypeNinjaTraderFormatError" xml:space="preserve">
    <value>{0}: Formatfehler in Zeile {1}: {2}: '{3}'</value>
  </data>
  <data name="ImportTypeNinjaTraderInstrumentNotSupported" xml:space="preserve">
    <value>Importieren von Datei '{0}' nicht möglich. Instrument wird von dem Archiv nicht unterstützt.</value>
  </data>
  <data name="ImportTypeNinjaTraderNumericPriceFormatError" xml:space="preserve">
    <value>{0}: numerisches Preisformat nicht unterstützt.</value>
  </data>
  <data name="ImportTypeNinjaTraderUnableReadData" xml:space="preserve">
    <value>Nicht möglich Daten aus Datei '{0}' zu lesen: {1}</value>
  </data>
  <data name="ImportTypeNinjaTraderUnexpectedFieldNumber" xml:space="preserve">
    <value>{0}: unerwartete Anzahl der Felder in Zeile '{1}', sollten 3, 5 oder 6 sein</value>
  </data>
  <data name="ImportTypeTickData" xml:space="preserve">
    <value>Tick Data, LLC</value>
  </data>
  <data name="IncrementalPeriod" xml:space="preserve">
    <value>Inkrementelle Periode</value>
  </data>
  <data name="Intermediate" xml:space="preserve">
    <value>Intermediär</value>
  </data>
  <data name="Interval" xml:space="preserve">
    <value>Intervall</value>
  </data>
  <data name="KeltnerChannelMidline" xml:space="preserve">
    <value>Mittellinie</value>
  </data>
  <data name="KeyReversalPlot0" xml:space="preserve">
    <value>Plot 0</value>
  </data>
  <data name="LastLineLength" xml:space="preserve">
    <value>Letzte Zeilenlänge (% des Diagramms)</value>
  </data>
  <data name="LastLineStroke" xml:space="preserve">
    <value>Linie Letzter Kurs</value>
  </data>
  <data name="LegendLocation" xml:space="preserve">
    <value>Legendenstandort</value>
  </data>
  <data name="LegendLocation_BottomLeft" xml:space="preserve">
    <value>Unten Links</value>
  </data>
  <data name="LegendLocation_BottomRight" xml:space="preserve">
    <value>Unten Rechts</value>
  </data>
  <data name="LegendLocation_Disabled" xml:space="preserve">
    <value>Arbeitsunfähig</value>
  </data>
  <data name="LegendLocation_TopLeft" xml:space="preserve">
    <value>Oben Links</value>
  </data>
  <data name="LegendLocation_TopRight" xml:space="preserve">
    <value>Oben Rechts</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Länge</value>
  </data>
  <data name="Line1Value" xml:space="preserve">
    <value>Linie 1 Wert</value>
  </data>
  <data name="Line2Value" xml:space="preserve">
    <value>Linie 2 Wert</value>
  </data>
  <data name="Line3Value" xml:space="preserve">
    <value>Linie 3 Wert</value>
  </data>
  <data name="Line4Value" xml:space="preserve">
    <value>Linie 4 Wert</value>
  </data>
  <data name="LineColor" xml:space="preserve">
    <value>Farbe Linie</value>
  </data>
  <data name="Load" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Ort</value>
  </data>
  <data name="LowerHigh" xml:space="preserve">
    <value>Tieferes Hoch</value>
  </data>
  <data name="LowerLow" xml:space="preserve">
    <value>Tieferes Tief</value>
  </data>
  <data name="MailCcAddress" xml:space="preserve">
    <value>CC:</value>
  </data>
  <data name="MailCcAddressDescription" xml:space="preserve">
    <value>Die E-Mail-Adresse Ihres Kopierempfängers. Trennen Sie mehrere Adressen durch ',' oder ';'</value>
  </data>
  <data name="MailServiceMailAddress" xml:space="preserve">
    <value>Email Adresse</value>
  </data>
  <data name="MailServiceName" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="MailServicePort" xml:space="preserve">
    <value>Verbindung - Port</value>
  </data>
  <data name="MailServiceSenderDisplayName" xml:space="preserve">
    <value>Von Name</value>
  </data>
  <data name="MailServiceServer" xml:space="preserve">
    <value>Verbindung - Server</value>
  </data>
  <data name="MailServiceSSL" xml:space="preserve">
    <value>Verbindung - SSL</value>
  </data>
  <data name="MailSubject" xml:space="preserve">
    <value>Betreff:</value>
  </data>
  <data name="MailSubjectDescription" xml:space="preserve">
    <value>Der Betreff Ihrer Email Nachricht</value>
  </data>
  <data name="MailToAddress" xml:space="preserve">
    <value>an:</value>
  </data>
  <data name="MailToAddressDescription" xml:space="preserve">
    <value>Die email Adresse Ihres Empfängers. Mehrere Adressen mit ´,´oder ´;´</value>
  </data>
  <data name="MAMAFAMA" xml:space="preserve">
    <value>FAMA</value>
  </data>
  <data name="MAPeriod" xml:space="preserve">
    <value>Gleitender Durchschnitt Periode</value>
  </data>
  <data name="MAType" xml:space="preserve">
    <value>Gleitender Durchschnitt Typ</value>
  </data>
  <data name="MovingAverage" xml:space="preserve">
    <value>Gleitender Durchschnitt</value>
  </data>
  <data name="MovingAverageRibbonPlot1" xml:space="preserve">
    <value>Gleitender Durchschnitt 1</value>
  </data>
  <data name="MovingAverageRibbonPlot2" xml:space="preserve">
    <value>Gleitender Durchschnitt 2</value>
  </data>
  <data name="MovingAverageRibbonPlot3" xml:space="preserve">
    <value>Gleitender Durchschnitt 3</value>
  </data>
  <data name="MovingAverageRibbonPlot4" xml:space="preserve">
    <value>Gleitender Durchschnitt 4</value>
  </data>
  <data name="MovingAverageRibbonPlot5" xml:space="preserve">
    <value>Gleitender Durchschnitt 5</value>
  </data>
  <data name="MovingAverageRibbonPlot6" xml:space="preserve">
    <value>Gleitender Durchschnitt 6</value>
  </data>
  <data name="MovingAverageRibbonPlot7" xml:space="preserve">
    <value>Gleitender Durchschnitt 7</value>
  </data>
  <data name="MovingAverageRibbonPlot8" xml:space="preserve">
    <value>Gleitender Durchschnitt 8</value>
  </data>
  <data name="NBarsDownTrigger" xml:space="preserve">
    <value>Trigger</value>
  </data>
  <data name="NegativeColor" xml:space="preserve">
    <value>Negative Farbe</value>
  </data>
  <data name="NetChangePosition_BottomLeft" xml:space="preserve">
    <value>Unten Links</value>
  </data>
  <data name="NetChangePosition_BottomRight" xml:space="preserve">
    <value>Unten Rechts</value>
  </data>
  <data name="NetChangePosition_TopLeft" xml:space="preserve">
    <value>Oben Links</value>
  </data>
  <data name="NetChangePosition_TopRight" xml:space="preserve">
    <value>Oben Rechts</value>
  </data>
  <data name="NinjaScriptBackground" xml:space="preserve">
    <value>Hintergrund</value>
  </data>
  <data name="NinjaScriptBarsTypeDay" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="NinjaScriptBarsTypeHeikenAshi" xml:space="preserve">
    <value>Heikin Ashi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagiReversal" xml:space="preserve">
    <value>Reversal</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreak" xml:space="preserve">
    <value>Line Break</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreakLineBreaks" xml:space="preserve">
    <value>Line breaks</value>
  </data>
  <data name="NinjaScriptBarsTypeMinute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="NinjaScriptBarsTypeMonth" xml:space="preserve">
    <value>Monat</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigure" xml:space="preserve">
    <value>Point &amp; Figure</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureBoxSize" xml:space="preserve">
    <value>Box Grösse</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureReversal" xml:space="preserve">
    <value>Reversal</value>
  </data>
  <data name="NinjaScriptBarsTypeRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="NinjaScriptBarsTypeRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="NinjaScriptBarsTypeRenkoBrickSize" xml:space="preserve">
    <value>Brick Grösse</value>
  </data>
  <data name="NinjaScriptBarsTypeSecond" xml:space="preserve">
    <value>Sekunde</value>
  </data>
  <data name="NinjaScriptBarsTypeTick" xml:space="preserve">
    <value>Tick</value>
  </data>
  <data name="NinjaScriptBarsTypeVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="NinjaScriptBarsTypeWeek" xml:space="preserve">
    <value>Woche</value>
  </data>
  <data name="NinjaScriptBorder" xml:space="preserve">
    <value>Rand</value>
  </data>
  <data name="NinjaScriptChartStyleBarWidth" xml:space="preserve">
    <value>Kerzenbreite</value>
  </data>
  <data name="NinjaScriptChartStyleBox" xml:space="preserve">
    <value>Box</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsColor" xml:space="preserve">
    <value>Farbe für fallende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsOutline" xml:space="preserve">
    <value>Abwärtskerzen Outline</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsColor" xml:space="preserve">
    <value>Farbe für steigende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsOutline" xml:space="preserve">
    <value>Aufwärtskerzen Outline</value>
  </data>
  <data name="NinjaScriptChartStyleCandleDownBarsColor" xml:space="preserve">
    <value>Farbe für fallende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleCandleOutline" xml:space="preserve">
    <value>Kerzenkörper Outline</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestick" xml:space="preserve">
    <value>Candlestick</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestickHollow" xml:space="preserve">
    <value>Hollow Candlestick</value>
  </data>
  <data name="NinjaScriptChartStyleCandleUpBarsColor" xml:space="preserve">
    <value>Farbe für steigende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleCandleWick" xml:space="preserve">
    <value>Kerzen Docht</value>
  </data>
  <data name="NinjaScriptChartStyleEquivolume" xml:space="preserve">
    <value>Equivolumen</value>
  </data>
  <data name="NinjaScriptChartStyleHeikenAshi" xml:space="preserve">
    <value>Heikin Ashi</value>
  </data>
  <data name="NinjaScriptChartStyleKagi" xml:space="preserve">
    <value>Kagi Linie</value>
  </data>
  <data name="NinjaScriptChartStyleKagiThickLine" xml:space="preserve">
    <value>Dicke Linie</value>
  </data>
  <data name="NinjaScriptChartStyleKagiThinLine" xml:space="preserve">
    <value>Dünne Linie</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnClose" xml:space="preserve">
    <value>Schlusskurslinie</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseColor" xml:space="preserve">
    <value>Farbe</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseWidth" xml:space="preserve">
    <value>Linien - Dicke</value>
  </data>
  <data name="NinjaScriptChartStyleLineWidth" xml:space="preserve">
    <value>Linienbreite</value>
  </data>
  <data name="NinjaScriptChartStyleMountain" xml:space="preserve">
    <value>Mountain</value>
  </data>
  <data name="NinjaScriptChartStyleMountainColor" xml:space="preserve">
    <value>Farbe</value>
  </data>
  <data name="NinjaScriptChartStyleMountainOutline" xml:space="preserve">
    <value>Outline</value>
  </data>
  <data name="NinjaScriptChartStyleOHLC" xml:space="preserve">
    <value>OHLC</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcDownBarsColor" xml:space="preserve">
    <value>Farbe für fallende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcUpBarsColor" xml:space="preserve">
    <value>Farbe für steigende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleOpenClose" xml:space="preserve">
    <value>Öffnen/Schließen</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsColor" xml:space="preserve">
    <value>Farbe für fallende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsOutline" xml:space="preserve">
    <value>Abwärtskerzen Outline</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsColor" xml:space="preserve">
    <value>Farbe für steigende Kerzen</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsOutline" xml:space="preserve">
    <value>Aufwärtskerzen Outline</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigure" xml:space="preserve">
    <value>Point &amp; Figure</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureDownColor" xml:space="preserve">
    <value>Farbe fallend</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureUpColor" xml:space="preserve">
    <value>Farbe steigend</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchor" xml:space="preserve">
    <value>Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorEnd" xml:space="preserve">
    <value>Ende</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorExtension" xml:space="preserve">
    <value>Extension</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorMiddle" xml:space="preserve">
    <value>Mitte</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorStart" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchfork" xml:space="preserve">
    <value>Andrews Pitchfork</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCalculationMethod" xml:space="preserve">
    <value>Berechnungsmethode</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCategoryStrokes" xml:space="preserve">
    <value>Striche</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkDescription" xml:space="preserve">
    <value>Beschreibung Andrews Pitchfork </value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtendLinesBack" xml:space="preserve">
    <value>Zeilen zurück verlängern</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtensionStroke" xml:space="preserve">
    <value>Extension Linie - Stroke</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkRetracement" xml:space="preserve">
    <value>Retracement</value>
  </data>
  <data name="NinjaScriptDrawingToolArc" xml:space="preserve">
    <value>Bogen</value>
  </data>
  <data name="NinjaScriptDrawingToolAreaOpacity" xml:space="preserve">
    <value>Opacity - area (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolArrowLine" xml:space="preserve">
    <value>Pfeil</value>
  </data>
  <data name="NinjaScriptDrawingToolBackgroundOpacity" xml:space="preserve">
    <value>Hintergrund Deckkraft (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolEllipse" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="NinjaScriptDrawingToolExtendedLine" xml:space="preserve">
    <value>Erweiterte Linie</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciCircle" xml:space="preserve">
    <value>Fibonacci Kreis</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciExtensions" xml:space="preserve">
    <value>Fibonacci Extensions</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciLevelsBaseAnchorLineStroke" xml:space="preserve">
    <value>Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracements" xml:space="preserve">
    <value>Fibonacci Retracements</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesLeft" xml:space="preserve">
    <value>Linie nach Links erweitern</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesRight" xml:space="preserve">
    <value>Linie nach Rechts erweitern</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextAlignment" xml:space="preserve">
    <value>Abgleich Text</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextLocation" xml:space="preserve">
    <value>Ort für Text</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeCircleDivideTimeSeparately" xml:space="preserve">
    <value>Unterteile Preis/Zeit seperiert</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensions" xml:space="preserve">
    <value>Fibnonacci Time Extensions</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensionsShowText" xml:space="preserve">
    <value>Zeige Text</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFan" xml:space="preserve">
    <value>Gann Fächer</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanDisplayText" xml:space="preserve">
    <value>Textanzeige</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanFanDirection" xml:space="preserve">
    <value>Fächerrichtung</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanPointsPerBar" xml:space="preserve">
    <value>Punkte je Kerze</value>
  </data>
  <data name="NinjaScriptDrawingToolHorizontalLine" xml:space="preserve">
    <value>Horizonale Linie</value>
  </data>
  <data name="NinjaScriptDrawingToolLine" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="NinjaScriptDrawingToolPath" xml:space="preserve">
    <value>Pfad</value>
  </data>
  <data name="NinjaScriptDrawingToolPathBegin" xml:space="preserve">
    <value>Pfadbeginn</value>
  </data>
  <data name="NinjaScriptDrawingToolPathEnd" xml:space="preserve">
    <value>Pfadende</value>
  </data>
  <data name="NinjaScriptDrawingToolPathSegment" xml:space="preserve">
    <value>Segment</value>
  </data>
  <data name="NinjaScriptDrawingToolPathShowCount" xml:space="preserve">
    <value>Zählung anzeigen</value>
  </data>
  <data name="NinjaScriptDrawingToolPolygon" xml:space="preserve">
    <value>Vieleck</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceLevelsOpacity" xml:space="preserve">
    <value>Preislevel Deckkraft (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolRay" xml:space="preserve">
    <value>Gerade</value>
  </data>
  <data name="NinjaScriptDrawingToolRectangle" xml:space="preserve">
    <value>Rechteck</value>
  </data>
  <data name="NinjaScriptDrawingToolRegion" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirection" xml:space="preserve">
    <value>Richtung</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirectionStroke" xml:space="preserve">
    <value>Richtung Stroke</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightHorizontalTextFormat" xml:space="preserve">
    <value>{0} Zeit Kerze: ´{1}´</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalRangeUnit" xml:space="preserve">
    <value>Vertikale Range</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalTextFormat" xml:space="preserve">
    <value>Wert Spanne {0} {1}</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightX" xml:space="preserve">
    <value>Region Highlight X</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightY" xml:space="preserve">
    <value>Region Highlight Y</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannel" xml:space="preserve">
    <value>Regressionskanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannel" xml:space="preserve">
    <value>Unterer Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannelColor" xml:space="preserve">
    <value>Farbe unterer Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelPriceType" xml:space="preserve">
    <value>Preis Typ</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelRegressionChannel" xml:space="preserve">
    <value>Regression</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendLeft" xml:space="preserve">
    <value>Nach Links erweitern</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendRight" xml:space="preserve">
    <value>Nach Rechts erweitern</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationLowerDistance" xml:space="preserve">
    <value>Distanz zum unteren Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationUpperDistance" xml:space="preserve">
    <value>Distanz zum oberen Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelType" xml:space="preserve">
    <value>Modus</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannel" xml:space="preserve">
    <value>Oberer Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannelColor" xml:space="preserve">
    <value>Farbe oberer Kanal</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorEntry" xml:space="preserve">
    <value>Anker festlegen</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorLineStroke" xml:space="preserve">
    <value>Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorReward" xml:space="preserve">
    <value>Anker - Gewinnziel</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorRisk" xml:space="preserve">
    <value>Anker - Risiko</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardCategoryColors" xml:space="preserve">
    <value>Farben</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardDescription" xml:space="preserve">
    <value>Automatisches Berechnen der Targets anhand des selbst definierten Stop Loss</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeEntry" xml:space="preserve">
    <value>Erweiterung Einstieg</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeReward" xml:space="preserve">
    <value>Erweiterung Reward</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeRisk" xml:space="preserve">
    <value>Erweiterung Risiko</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardName" xml:space="preserve">
    <value>Risiko vs Reward</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardRatio" xml:space="preserve">
    <value>Ratio</value>
  </data>
  <data name="NinjaScriptDrawingToolRuler" xml:space="preserve">
    <value>Lineal</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerDaysFormat" xml:space="preserve">
    <value>{0} Tage</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerNumberBarsText" xml:space="preserve">
    <value># Kerzen</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerTimeText" xml:space="preserve">
    <value>Zeit:</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueDisplayUnit" xml:space="preserve">
    <value>Y Wert Anzeige</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueText" xml:space="preserve">
    <value>Y Wert</value>
  </data>
  <data name="NinjaScriptDrawingTools" xml:space="preserve">
    <value>Zeichenwerkzeuge</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowDownMarkerName" xml:space="preserve">
    <value>Pfeil Unten</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowUpMarkerName" xml:space="preserve">
    <value>Pfeil Oben</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDiamondMarkerName" xml:space="preserve">
    <value>Diamant</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDotMarkerName" xml:space="preserve">
    <value>Punkt</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartSquareMarkerName" xml:space="preserve">
    <value>Quadrat</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleDownMarkerName" xml:space="preserve">
    <value>Dreieck fallend</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleUpMarkerName" xml:space="preserve">
    <value>Dreieck oben</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioX" xml:space="preserve">
    <value>Ratio Zeit</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioY" xml:space="preserve">
    <value>Ratio Preis</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngles" xml:space="preserve">
    <value>Gann Winkel</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAnglesPrompt" xml:space="preserve">
    <value>1 Gann Winkel | {0} Gann Winkel | Gann Winkel hinzufügen | Editiere Gann Winkel.. |</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesAreaBrush" xml:space="preserve">
    <value>Farbe ´-´Fläche</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesOutlineBrush" xml:space="preserve">
    <value>Farbe ´-´ Outline</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelIsVisible" xml:space="preserve">
    <value>Sichtbar</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelLineStroke" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevels" xml:space="preserve">
    <value>Levels</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelsPrompt" xml:space="preserve">
    <value>1 Preis Level|{0} Preis Levels|Hinzufügen eines Preis Levels..|Editiere des Preis level...|Editieren von Preis Levels...</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelUnset" xml:space="preserve">
    <value>Unset</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelValue" xml:space="preserve">
    <value>Wert (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolStroke" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="NinjaScriptDrawingToolText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="NinjaScriptDrawingToolTextAlignment" xml:space="preserve">
    <value>Abgleich Text</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBackBrush" xml:space="preserve">
    <value>Hintergrund Text Pinsel</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBrush" xml:space="preserve">
    <value>Farbe Schrift</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixed" xml:space="preserve">
    <value>Text fixiert</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixedTextPosition" xml:space="preserve">
    <value>Position Text</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFont" xml:space="preserve">
    <value>Schrift</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineStroke" xml:space="preserve">
    <value>Outline</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineVisible" xml:space="preserve">
    <value>Outline aktiv</value>
  </data>
  <data name="NinjaScriptDrawingToolTimeCycles" xml:space="preserve">
    <value>Time Cycles</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannel" xml:space="preserve">
    <value>Trendkanal</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelDescription" xml:space="preserve">
    <value>Zeichnet einen Trendkanal mit parallelen Linien</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelEnd1AnchorDisplayName" xml:space="preserve">
    <value>Ende 1 Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelParallelStroke" xml:space="preserve">
    <value>Ende 2 Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart1AnchorDisplayName" xml:space="preserve">
    <value>Start 1 Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart2AnchorDisplayName" xml:space="preserve">
    <value>Ende 2 Anker</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelTrendStroke" xml:space="preserve">
    <value>Trend</value>
  </data>
  <data name="NinjaScriptDrawingToolTriangle" xml:space="preserve">
    <value>Dreieck</value>
  </data>
  <data name="NinjaScriptDrawingToolVerticalLine" xml:space="preserve">
    <value>Vertikale Linie</value>
  </data>
  <data name="NinjaScriptGeneral" xml:space="preserve">
    <value>Allgemein</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerAveragePerformanceOffsetPercent" xml:space="preserve">
    <value>Durchschnittliches Performanz Offset (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerConvergenceThreshold" xml:space="preserve">
    <value>Konvergenz Schwelle</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverIndex" xml:space="preserve">
    <value>Crossover Index</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverRatePercent" xml:space="preserve">
    <value>Crossoverrate (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerFastGenerations" xml:space="preserve">
    <value>Schnelle Generationen</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerations" xml:space="preserve">
    <value>Generationen</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerationSize" xml:space="preserve">
    <value>Größe der Generation</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMinimumPerformance" xml:space="preserve">
    <value>Mindest Performanz</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationRatePercent" xml:space="preserve">
    <value>Mutationsrate (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationStrengthPercent" xml:space="preserve">
    <value>Mutation Stärke (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerResetSizePercent" xml:space="preserve">
    <value>Reset Größe (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerSlowGenerations" xml:space="preserve">
    <value>Langsame Generationen</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerStabilitySizePercent" xml:space="preserve">
    <value>Stabilität Größe (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerThresholdGenerations" xml:space="preserve">
    <value>Grenzwertgenerationen</value>
  </data>
  <data name="NinjaScriptIndicator" xml:space="preserve">
    <value>Indikator</value>
  </data>
  <data name="NinjaScriptIndicatorAvg" xml:space="preserve">
    <value>Durchschnitt</value>
  </data>
  <data name="NinjaScriptIndicatorCount" xml:space="preserve">
    <value>Zählung</value>
  </data>
  <data name="NinjaScriptIndicatorDefault" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADL" xml:space="preserve">
    <value>Die Accumulation/Distribution (AD) Studie versucht, die Menge des in ein Instrument herein oder heraus fließenden Volumens zu messen, indem die Position des Schlusskurses am Ende der Periode in Bezug zu den Hochs und Tiefs des Zeitraums gesetzt wird.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADX" xml:space="preserve">
    <value>Der Average Directional Index misst die Stärke eines vorherrschenden Trends sowie ob Bewegung in dem Markt vorhanden ist. Der ADX wird auf einer Skala von 0-100 gemessen. Ein niedriger ADX Wert (in der Regel weniger als 20) kann einen nicht-trendenden Markt mit geringem Volumen anzeigen, wohingegen ein Kreuz über die 20 auf den Beginn eines Trends (auf- oder abwärts) hindeutet. Wenn der ADX über 40 ist und dann beginnt zu fallen, kann er auf die Verlangsamung eines aktuellen Trends hinweisen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADXR" xml:space="preserve">
    <value>Das Average Directional Movement Rating quantifiziert eine Momentum  Veränderung des ADX. Es wird berechnet, indem zwei ADX Werte addiert (der aktuelle Wert und der Wert vor n Perioden) und dann durch zwei geteilt werden. Diese zusätzliche Glättung macht den ADXR etwas langsamer als den ADX. Die Interpretation entspricht der des ADX: je höher der Wert, desto stärker der Trend.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAPZ" xml:space="preserve">
    <value>APZ (Adaptive Price Zone) bildet einen auf doppelt geglätteten, exponentiellen gleitenden Durchschnitten basierenden stetigen Kanal um den Durchschnittspreis. Siehe S&amp;C, September 2006, Seite 28.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroon" xml:space="preserve">
    <value>Der Aroon Indikator wurde von Tushar Chande entwickelt. Er beinhaltet zwei Darstellungen: die eine misst die Anzahl der Perioden seit dem letzten X-Periodenhoch (Aroon Up) und die andere misst die Anzahl der Perioden seit der letzten X-Periode Tief (Aroon Down).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroonOscillator" xml:space="preserve">
    <value>Der Aroon Oszillator beruht auf dem Aroon Indikator. Ähnlich dem Aroon Indikator misst der Aroon Oszillator die Stärke eines Trends.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionATR" xml:space="preserve">
    <value>Die Average True Range (ATR) ist ein Maß für die Volatilität. Von Welles Wilder in seinem Buch "New Concepts in Technical Trading Systems" eingeführt und wird seitdem als Komponente in vielen Indikatoren und Handelssystemen eingesetzt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBarTimer" xml:space="preserve">
    <value>Displays remaining time of the time based bar</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBlockVolume" xml:space="preserve">
    <value>Blockvolumen erkennt Blocktrades und zeigt an, wie viele pro Balken aufgetreten sind. Dies kann entweder als Trades oder als Volumen angezeigt werden. Historische Tickdaten sind für die historische Darstellung erforderlich.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBollinger" xml:space="preserve">
    <value>Bollinger Bänder werden als Standardabweichungs Level oberhalb und unterhalb eines gleitenden Durchschnitts dargestellt. Da die Standardabweichung ein Maß für die Volatilität ist, sind die Bänder selbstanpassend: Verbreiterung findet während volatiler Märkte und Verengung in ruhigeren Zeiten statt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBOP" xml:space="preserve">
    <value>Der Balance of Power Indikator misst die Stärke der Bullen gegen die der Bären durch Bewertung der Fähigkeit einer jeden Seite den Preis auf ein extremen Niveau zu drücken.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellPressure" xml:space="preserve">
    <value>Zeigt den aktuellen Kauf- oder Verkaufsdruck in Prozent an. Dies ist ein Echzeit Indikator. Wenn 'Berechnen auf' auf 'Bei Barschluss' gesetzt ist, werden die Indikator Werte immer 100 anzeigen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellVolume" xml:space="preserve">
    <value>Plots a histogram splitting volume between trades at the ask or higher and trades at the bid and lower.  Only works on historical data if using Tick Replay</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCamarillaPivots" xml:space="preserve">
    <value>Camarilla-Pivots sind auch eine Preisanalyse, die potenzielle Unterstützungs- und Widerstandsebenen generiert, indem sie den vorherigen Bereich multiplizieren und dann addieren oder vom Close abziehen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCandlestickPattern" xml:space="preserve">
    <value>Erkennt weit verbreitete Candletick Patterns und zeichnet diese im Chart ein</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCCI" xml:space="preserve">
    <value>Der Commodity Channel Index (CCI) misst die Variation eines Wertpapierpreises von seinem statistischen Mittelwert. Hohe Werte zeigen, dass die Preise ungewöhnlich hoch im Vergleich zu den durchschnittlichen Preisen sind, während niedrige Werte zeigen, dass die Preise ungewöhnlich niedrig sind.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinMoneyFlow" xml:space="preserve">
    <value>Berechnet den Wert des Geldfluss Volumens über n Bars.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinOscillator" xml:space="preserve">
    <value>Berechnet das Momentum der Akkumulation/Distribution Linie mit Hilfe der Differenz zwischen zwei exponentiellen, gleitenden Durchschnitten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinVolatility" xml:space="preserve">
    <value>Vergleicht die Differenz zwischen der aktuellen und historischen Preisspanne eines Instruments mit Hilfe von exponentiellen, gleitenden Durchschnitten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChoppinessIndex" xml:space="preserve">
    <value>Der Choppiness Index wurde entwickelt, um festzustellen, ob der Markt schwankend ist (seitwärts handeln) oder nicht (innerhalb eines Trends in beide Richtungen handeln)</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCMO" xml:space="preserve">
    <value>Der CMO unterscheidet sich von anderen Momentum Oszillatoren wie Relative Strength Index (RSI) und Stochastik. Er verwendet Daten über Auf- und Abwärts Tage im Zähler der Berechnung um die Dynamik direkt zu messen. In erster Linie zur Bestimmung extremer überkauft / überverkauft Bedingungen eingesetzt, kann der CMO auch helfen Trends zu identifizieren.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionConstantLines" xml:space="preserve">
    <value>Zeichnet Linien an benutzerdefinierten Werten ein</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCorrelation" xml:space="preserve">
    <value>Der Korrelationsindikator wird die Korrelation der Datenreihen zu einem gewünschten Instrument darstellen. Werte nahe 1 zeigen Bewegung in die gleiche Richtung an. Werte nahe -1 zeigen Bewegung in entgegengesetzte Richtungen an. Werte nahe 0 deuten auf keine Korrelation hin.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCOT" xml:space="preserve">
    <value>Verpflichtungen der Händler</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCurrentDayOHL" xml:space="preserve">
    <value>Stellt die Eröffnung, sowie die Hochs und Tiefs der aktuellen Handelssitzung dar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDarvas" xml:space="preserve">
    <value>Die Darvas Kästen wurden von den Seiten von Nicolas Darvas Buch"How I Made $ 2.000.000 in the stock market". Die Kästen werden verwendet, um einen Trend zu normalisieren. Ein Kaufsignal wird angezeigt werden wenn der Kurs des Instruments bei der Oberseite der Box liegt. Ein Verkaufsignall wird angezeigt wenn der Kurs unter der Box liegt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDEMA" xml:space="preserve">
    <value>Doppelter exponentieller gleitender Durchschnitt</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDisparityIndex" xml:space="preserve">
    <value>Der Disparitätsindex misst die Differenz zwischen dem Preis und einem exponentiellen gleitenden Durchschnitt. Ein Wert größer könnte auf bullisches Momentum hindeuten, während ein Wert unter Null ein bärisches Momentum vermuten lässt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDM" xml:space="preserve">
    <value>Directional Movement (DM). Dies ist der gleiche Indikator wie der ADX, mit dem Zusatz der zwei 'directional Movement' Indikatoren +DI und -DI. +DI und -DI messen die Auf- und Abwärts-Dynamik. Ein Kaufsignal wird generiert, wenn +DI den -DI nach oben, ein Verkaufssignal, wenn -DI den + DI nach unten kreuzt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMI" xml:space="preserve">
    <value>Directional Movement Index. Der Directional Movement Index ist dem Relative Strength Index von Welles Wilder sehr ähnlich. Der Unterschied ist, dass der DMI variable Zeitperioden (von 3 bis 30) gegenüber den statischen RSI Perioden verwendet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMIndex" xml:space="preserve">
    <value>Der Dynamic Momentum Index ist ein variabler RSI. Die RSI Komponente schwankt zwischen 3 und 30. Die variable Zeitperiode macht den RSI sensibler für kurzfristige Bewegungen. Je volatiler der Preis ist, desto kürzer wird der Berechnungszeitraum Er wird auf die gleiche Weise wie der RSI interpretiert, aber kann frühere Signale bieten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDonchianChannel" xml:space="preserve">
    <value>Der Donchian Channel Indikator wurde von Richard Donchian geschaffen. Es verwendet das höchste Hoch und das niedrigste Tief des Zeitraumes, um den Kanal darzustellen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDoubleStochastics" xml:space="preserve">
    <value>Doppel Stochastik</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEaseOfMovement" xml:space="preserve">
    <value>Der Ease of Movement (EMV) Indikator betont Tage, in denen sich die Aktie leicht bewegt und minimiert die Tage, in denen es der Aktie schwerfällt sich zu bewegen. Eine Kaufsignal wird generiert, wenn der EMV über Null, ein Verkaufssignal wenn er unter Null kreuzt. Wenn der EMV um Null tendiert, gibt es kleine Preisbewegungen und/oder hohes Volumen: das heißt, der Preis bewegt sich nicht leicht.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEMA" xml:space="preserve">
    <value>Der Exponential Moving Average ist ein Indikator der den durchschnittlichen Wert eines Wertpapierkurses über einen Zeitraum zeigt. Bei der Berechnung des gleitenden Durchschnitts EMA gilt mehr Gewicht auf die jüngsten Preise als bei dem SMA.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFibonacciPivots" xml:space="preserve">
    <value>Fibonacci-Pivots sind auch eine Preisanalyse, die potenzielle Unterstützungs- und Widerstandsebenen generiert, indem sie den vorherigen Bereich mit den Fibonacci-Werten multiplizieren und ihn dann addieren oder vom Durchschnitt des vorherigen hohen, niedrigen und engen Bereichs subtrahieren.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFisherTransform" xml:space="preserve">
    <value>Die Fisher Transformation hat scharfe und klare Wendepunkte, die in zeitnaher Weise erfolgen. Die resultierenden Extreme werden verwendet, um Preis Umkehren zu identifizieren.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFOSC" xml:space="preserve">
    <value>Der Forecast Oszillator (FOSC) ist eine Erweiterung der auf Linearer Regression basierenden Indikatoren, die von Tushar Chande populär gemacht wurden. Der Forecast Oszillator plottet die prozentuale Differenz zwischen dem prognostizierten Preis (erzeugt von einer linearen Regressionsgeraden auf x-Perioden) und dem tatsächlichen Preis. Der Oszillator ist über Null, wenn der prognostizierte Preis größer als der tatsächliche Preis ist. Umgekehrt ist er weniger als Null, wenn er kleiner ist. In dem seltenen Fall, das der prognostizierte Preis und der tatsächliche Preis gleich sind, würde der Oszillator Null anzeigen. Aktuelle Preise, die dauerhaft unter dem prognostizierten Preis liegen suggeriere in Zukunft niedrigere Preise. Ebenso suggerieren aktuelle Preise, die dauerhaft über dem prognostizierten Preis liegen, höhere Preise in Zukunft. Kurzfristige Trader sollten kürzere Zeiträume verwenden und vielleicht lockere Standards für die erforderlichen Zeitperioden über / unter dem prognostizierten Preis verwenden. Langfristige Trader sollten längere Zeiträume und vielleicht strengere Standards für die erforderlichen Zeitperioden über / unter dem prognostizierten Preis verwenden. Chande schlägt auch das Plotten ein dreitägigen gleitenden Durchschnitts Trigger Linie des Forecast Oszillators vor, um Frühwarnungen von Trendänderungen zu generieren. Wenn der Oszillator unter diese Trigger Linie kreuzt, werden niedrigere Preise angedeutet. Wenn der Oszillator über die Trigger Linie kreuzt, dagegen höhere Preise.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionHMA" xml:space="preserve">
    <value>Der Hull Moving Average (HMA) setzt gewichtete MA Berechnungen ein, um eine überlegene Glättung und wesentlich weniger Verzögerung als traditionelle SMA Indikatoren zu bieten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKAMA" xml:space="preserve">
    <value>Perry Kaufman entwickelte diesen Indikator, der einen EMA darstellt, der ein Efficieny Ratio benutzt um die Glättungskonstante anzupassen; diese kann von einer minimalen Schnell Länge bis zu einer maximalen Langsamen Länge reichen. Da dieser gleitenden Durchschnitt adaptiv ist, folgt er den Preisen tendenziell stärker als andere MA's.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeltnerChannel" xml:space="preserve">
    <value>Der Keltner Channel ist ein den Bollinger Bändern ähnlicher Indikator, hier ist die Mittellinie ein normaler gleitender Durchschnitt mit den oberen und unteren Bändern durch den SMA der Differenz zwischen Hochs und Tiefs der vorhergehenden Bars verschoben. Der Offset-Multiplikator sowie die SMA Periode ist konfigurierbar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalDown" xml:space="preserve">
    <value>Gibt den Wert 1 zurück, wenn der aktuelle Schlusskurs kleiner als der vorherige ist, nachdem das höchste Hoch der letzten n Bars durchbrochen wurde.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalUp" xml:space="preserve">
    <value>Gibt den Wert 1 zurück, wenn der aktuelle Schlusskurs größer als der vorherige ist, nachdem das tiefste Tief der letzten n Bars durchbrochen wurde.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinReg" xml:space="preserve">
    <value>Der Lineare Regression Indikator versucht den Wert eines Wertpapierskurses vorauszuberechnen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegIntercept" xml:space="preserve">
    <value>Schnittpunkt der Lineare Regression</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegSlope" xml:space="preserve">
    <value>Steigung der Linearen Regression</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMACD" xml:space="preserve">
    <value>Der MACD (Moving Average Convergence/Divergence) ist ein trendfolgender Momentum Indikator, der die Relation zwischen zwei gleitenden Durchschnitten der Preise zeigt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAEnvelopes" xml:space="preserve">
    <value>Zeichnet % Bänder um einen gleitenden Durchschnitt</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAMA" xml:space="preserve">
    <value>Der MAMA (MESA Adaptive Moving Average) wurde von John Ehlers entwickelt. Er passt sich den Kursbewegungen in einer neuen und einzigartigen Weise an. Die Anpassung basiert auf dem Hilbert Transformation Discriminator. Der Vorteil dieser Methode ist ein schneller Durchschnitt, sowie ein langsam gedämpfter Durchschnitt. Die MAMA &amp; FAMA (Following Adaptive Moving Average) Linien kreuzen nur bei wichtigen Umkehren des Preises.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAX" xml:space="preserve">
    <value>Das Maximum zeigt den maximalen Wert der letzten n bars.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMcClellanOscillator" xml:space="preserve">
    <value>McClellan Oscillator ist der Unterschied zwischen zwei exponentiellen gleitenden Durchschnitten des NYSE Advance-Decline Spreads. Dieser Indikator benötigt ADV- und DECL-Indexdaten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMFI" xml:space="preserve">
    <value>Der MFI (Money Flow Index) ist ein Momentum Indikator, der die Stärke des Geldflusses in / aus einem Wertpapier misst.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMIN" xml:space="preserve">
    <value>Das Minimum zeigt den minimalen Wert der letzten n bars.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMomentum" xml:space="preserve">
    <value>Der Momentum Indikator misst den Betrag, den sich der Preis eines Wertpapiers über einen bestimmten Zeitraum verändert hat.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMoneyFlowOscillator" xml:space="preserve">
    <value>Der Money Flow Oszillator misst die Menge des Geldflussvolumens über einen bestimmten Zeitraum. Ein Wechsel in den positiven Bereich deutet auf Kaufdruck hin, während ein Wechsel in den negativen Bereich auf Verkaufsdruck hindeutet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMovingAverageRibbon" xml:space="preserve">
    <value>Das Moving Average Ribbon ist eine Serie von steigenden Moving Averages.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsDown" xml:space="preserve">
    <value>Dieser Indikator gibt 1 zurück, wenn wir n aufeinander folgende Abwärts Bars haben, sonst gibt er 0 zurück. Ein Abwärts Bar wird definiert, als ein Bar, das unter dem Eröffnungskurs schliesst sowie über ein tieferes Hoch und tieferes Tief verfügt. Sie können die spezifischen Anforderungen in den Indikator Optionen anpassen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsUp" xml:space="preserve">
    <value>Dieser Indikator gibt 1 zurück, wenn wir n aufeinander folgende Aufwärts Bars haben, sonst gibt er 0 zurück. Ein Aufwärts Bar wird definiert, als ein Bar, das über dem Eröffnungskurs schliesst sowie über ein höheres Hoch und höheres Tief verfügt. Sie können die spezifischen Anforderungen in den Indikator Optionen anpassen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNetChangeDisplay" xml:space="preserve">
    <value>Zeigt die Nettoveränderung im Chart an.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionOBV" xml:space="preserve">
    <value>OBV (On Balance Volume) ist eine laufende Summe des Volumens. Es zeigt, ob Volumen in oder aus einem Wertpapier fließt. Wenn das Wertpapier höher schließt als der Vortag, wird das gesamte Volumen als aufwärts Volumen gesehen. Beim niedrigeren Schließen als abwärts Volumen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionParabolicSAR" xml:space="preserve">
    <value>Parabolic SAR nach Stocks and Commodities Magazin V 11:11 (477 – 479).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPFE" xml:space="preserve">
    <value>PFE (Polarized Fractal Efficiency) ist ein Indikator, der fraktale Geometrie verwendet, um festzustellen, wie effizient sich der Preis bewegt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPivots" xml:space="preserve">
    <value>Pivot Punkte</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPPO" xml:space="preserve">
    <value>Der PPO (Percentage Price Oszillator) basiert auf zwei gleitende Durchschnitten, ausgedrückt als Prozentsatz. Der PPO Wert findet sich durch Subtraktion des längeren MA vom kürzeren MA und dann die Differenz durch den längeren MA dividiert.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceLine" xml:space="preserve">
    <value>Zeigt ask, bid, und/oder last Linien im Chart.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceOscillator" xml:space="preserve">
    <value>Der Preis Oszillator Indikator zeigt die Variation zwischen zwei gleitenden Durchschnitten für den Preis eines Wertpapiers.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriorDayOHLC" xml:space="preserve">
    <value>Stellt die Eröffnung, sowie die Hochs und Tiefs der vorigen Handelssitzung dar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPsychologicalLine" xml:space="preserve">
    <value>Die Psychologische Linie ist das Verhältnis der Anzahl der aufsteigenden Balken zu der angegebenen Anzahl von Balken.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRange" xml:space="preserve">
    <value>Berechnet die Spanne eines Bars.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRangeCounter" xml:space="preserve">
    <value>Displays the range count of a bar"</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRegressionChannel" xml:space="preserve">
    <value>Lineare Regression wird verwendet, um eine am Besten passende Trendlinie für die Preisdaten zu berechnen. Darüber hinaus wird ein oberes und unteres Band hinzugefügt, die durch die Berechnung der Standardabweichung der Preise von der Regressionsgeraden entstehen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRelativeVigorIndex" xml:space="preserve">
    <value>Der Relative Vigor Index misst die Stärke eines Trends, indem er den Schlusskurs eines Instruments mit dessen Preisspanne vergleicht. Es basiert auf der Tatsache, dass die Preise tendenziell höher schließen, als sie sich bei Aufwärtstrends öffnen, und näher bei ihnen als bei Abwärtstrends.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRIND" xml:space="preserve">
    <value>RIND (Range Indicator) vergleicht die Intraday Preisspanne (Hoch - Tief) mit der Interday Preisspanne (Schlusskurs - voriger Schlusskurs)  Wenn die Intraday Spanne größer als die Interday Spanne ist, hat der Range Indikator einen hohen Wert. Dies signalisiert ein Ende des aktuellen Trends. Wenn der Range Indikator auf einem niedrigen Niveau ist, kann ein neuer Trend starten.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionROC" xml:space="preserve">
    <value>Der ROC (Rate of Change) Indikator zeigt die prozentuale Veränderung zwischen dem aktuellen Preis und den Preis x-Zeitperioden vorher.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSI" xml:space="preserve">
    <value>Der RSI (Relative Strength Index) ist ein dem Preis folgender Oszillator, der zwischen 0 und 100 liegt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSquared" xml:space="preserve">
    <value>R-Squared Indikator</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSS" xml:space="preserve">
    <value>Relative Spread Strength ist die Stärke der Spanne zwischen zwei gleitenden Durchschnitten. TASC, Oktober 2006, S. 16.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRVI" xml:space="preserve">
    <value>Der RVI (Relative Volatility Index) wurde von Donald Dorsey als ein Kompliment und eine Bestätigung der Basis Momentum Indikatoren entwickelt. Wenn als Bestätigung anderer Signale verwendet, wird nur gekauft wenn der RVI über 50 ist und nur verkauft, wenn unter 50.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSampleCustomRender" xml:space="preserve">
    <value>Beispielsscript um die OnRender() Möglichkeiten zu zeigen</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSMA" xml:space="preserve">
    <value>Der SMA (Simple Moving Average) ist ein Indikator, der den durchschnittlichen Wert eines Wertpapierkurses über einen Zeitraum von Zeit zeigt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdDev" xml:space="preserve">
    <value>Die Standardabweichung ist ein statistisches Maß der Volatilität. Die Standardabweichung wird normalerweise als eine Komponente von anderen Indikatoren und nicht als eigenständiger Indikator verwendet. Beispielsweise werden Bollinger-Bänder berechnet, indem die Standardabweichung eines Wertpapiers um einen gleitenden Durchschnitt addiert wird.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdError" xml:space="preserve">
    <value>Der Standard Error zeigt wie nah die Preise sich um eine lineare Regressionslinie verteilen. Je näher die Preise an der linearen Regressionsgeraden liegen, desto stärker ist der Trend.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochastics" xml:space="preserve">
    <value>Der Stochastic Oszillator besteht aus zwei Linien, die zwischen einer vertikalen Skala von 0 bis 100 oszillieren. Der %K ist der Hauptwert und es wird als eine durchgezogene Linie gezeichnet. Der zweite ist der %D Wert und dies ist ein gleitender Durchschnitt des %K. %D wird als eine gepunktete Linie gezeichnet. Verwenden Sie ihn als Signalgeber; kaufend, wenn die schnelle Linie die langsame nach oben kreuzt und verkaufend, wenn die langsame die schnell nach unten kreuzt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochasticsFast" xml:space="preserve">
    <value>Der Stochastic Oszillator besteht aus zwei Linien, die zwischen einer vertikalen Skala von 0 bis 100 oszillieren. Der %K ist der Hauptwert und es wird als eine durchgezogene Linie gezeichnet. Der zweite ist der %D Wert und dies ist ein gleitender Durchschnitt des %K. %D wird als eine gepunktete Linie gezeichnet. Verwenden Sie ihn als Signalgeber; kaufend, wenn die schnelle Linie die langsame nach oben kreuzt und verkaufend, wenn die langsame die schnell nach unten kreuzt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochRSI" xml:space="preserve">
    <value>Der StochRSI ist ein Oszillator, der in der Berechnung der Stochastik ähnelt, allerdings statt dem Preis als Eingangsdaten verwendet der StochRSI RSI Daten. Der StochRSI berechnet die aktuelle Position des RSI im Verhältnis zu den Hoch / Tief RSI Werten über die angegebene Anzahl von Tagen. Tushar Chande und Stanley Kroll verfolgen mit dieser Maßnahme die Absicht, die überkaufte/überverkaufte Natur des RSI herauszustellen. Der StochRSI erstreckt sich zwischen 0,0 und 1,0. Werte über 0,8 werden normalerweise als überkauft angesehen, Werte unter 0,2 als überverkauft.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSUM" xml:space="preserve">
    <value>Zeigt die Summe der letzten n Datenpunkte an.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSwing" xml:space="preserve">
    <value>Der Swing Indikator zeichnet Linien, die Hoch- und Tiefpunkte der Preiswings darstellen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionT3" xml:space="preserve">
    <value>T3 Gleitender Durchschnitt</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTEMA" xml:space="preserve">
    <value>Dreifacher exponentieller gleitender Durchschnitt</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTickCounter" xml:space="preserve">
    <value>Zeigt die Anzahl von Ticks der Kerze</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTMA" xml:space="preserve">
    <value>Der TMA (Triangular Moving Average) ist ein gewichteter gleitender Durchschnitt. Im Vergleich zum WMA, der mehr Gewicht auf die neueren Bars legt, hat der TMA mehr Gewicht auf den Daten in der Mitte des Zeitraums.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTrendLines" xml:space="preserve">
    <value>Wenn auf ein Swinghigh ein niedrigeres Swinghigh folgt, wird automatisch eine Trendlinie dargestellt. Wenn auf ein Swinglow ein höheres Swinglow folgt, wird automatisch eine Trendlinie aufgezeichnet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTRIX" xml:space="preserve">
    <value>Der TRIX (Triple Exponential Average) zeigt die prozentuale Rate of Change (ROC) von einem dreifachen EMA an. TRIX schwingt oberhalb und unterhalb der Null Linie. Der Indikator wendet eine dreifache Glättung an, um unbedeutende Preisbewegungen innerhalb des Trend zu beseitigen, damit Sie ihn besser isolieren können.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSF" xml:space="preserve">
    <value>Der TSF (Time Series Forecast) berechnet die wahrscheinliche zukünftige Werte für den Preis durch den Einbau einer linearen Regressionsgeraden über eine bestimmte Anzahl von Preis-Bars und folgende, die vorwärts in die Zukunft zu zeichnen. Eine lineare Regression-Linie ist eine gerade Linie, die so nah an alle gegebenen Preis Punkte wie möglich ist. Siehe auch Indikator Lineare Regression.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSI" xml:space="preserve">
    <value>Der TSI (True Strength Index) ist ein auf Momentum basierter und von William Blau entwickelter Indikator. Sowohl zur Trendbestimmung als auch für überkauft/überverkauft Bedingungen konstruiert, ist der TSI für Intraday Zeitfenster genauso wie für den langfristigen Handel anwendbar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionUltimateOscillator" xml:space="preserve">
    <value>Der Ultimate Oszillator ist die gewichtete Summe dreier Oszillatoren für unterschiedliche Zeiträume. Die typischen Zeiträume sind 7, 14 und 28. Die Werte des Ultimate Oszillators reichen von null bis 100. Werte über 70 zeigen überkauft und Werte unter 30 überverkauft Bedingungen an. Suchen Sie auch nach Übereinstimmung/Divergenz mit dem Preis, um zu einen Trend oder eine Trendwende zu bestätigen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVMA" xml:space="preserve">
    <value>Der VMA (Variable Moving Average, auch bekannt als VIDYA oder Variable Index Dynamic Average) ist ein exponentiell gleitender Durchschnitt, der die Glättungsgewichte anhand der Volatilität der Datenreihe automatisch anpasst. Der VMA löst ein Problem der meisten gleitenden Durchschnitte - in Zeiten niedriger Volatilität, z. B. wenn der Preis trendet, sollte die Periode des gleitenden Durchschnitts kürzer werden, um empfindlicher auf unvermeidbare Pausen im Trend zu reagieren. Wohingegen in volatilen Phasen ohne Trend, eine längere Periode für den gleitenden Durchschnitt vorzuziehen ist, damit die Unbeständigkeit herausgefiltert wird. VIDYA verwendet den CMO Indikator für seine internen Volatilitätsberechnungen Die VMA und die CMO Periode sind beide einstellbar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOL" xml:space="preserve">
    <value>Volumen ist einfach die Anzahl der Aktien (oder Kontrakte), die während eines vorgegebenen Zeitraums (z.B. Stunde, Tag, Woche, Monat, etc.) gehandelt werden.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOLMA" xml:space="preserve">
    <value>Die VOLMA (Volume Moving Average) stellt einen exponentiell gleitenden Durchschnitt (EMA) des Volumens dar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeCounter" xml:space="preserve">
    <value>Volumen der einzelnen Kerzen</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeOscillator" xml:space="preserve">
    <value>Der Volume Oszillator misst das Volumen durch die Berechnung des Unterschiedes eines schnellen und eines langsamen gleitenden Durchschnitts der Volumens. Der Volume Oszillator bieten Einblick in die Stärke oder Schwäche des Preistrends Ein positiver Wert deutet darauf hin, das es gibt genug Unterstützung im Markt gibt die Preise in Richtung des aktuellen Trends zu bewegen. Ein negativer Wert deutet darauf hin, das es einen Mangel an Unterstützung gibt; die Preise könnten stagnieren oder eine Umkehr vollziehen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeProfile" xml:space="preserve">
    <value>Zeichnet ein horizontales Histogramm des Volumens anhand des Preises</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeUpDown" xml:space="preserve">
    <value>Variation of the VOL (Volume) indicator that colors the volume histogram different color depending if the current bar is up or down bar</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeZones" xml:space="preserve">
    <value>Volume Zones zeichnet eine horizontales Histogramm, das einen Chart überlagert. Die Histogrammbalken erstrecken sich von links nach rechts beginnend an der linken Seite des Diagramms. Die Länge der einzelnen Balken wird durch die Gesamtsumme des Volumens für den dargestellten Zeitraum berechnet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVortex" xml:space="preserve">
    <value>Der Vortex-Indikator ist ein Oszillator, der verwendet wird, um Trends zu identifizieren. Ein bullisches Signal löst aus, wenn die VIPlus-Linie die VIMinus-Linie überschreitet. Ein rückläufiges Signal löst aus, wenn die VIMinus-Linie die VIPlus-Linie überschreitet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVROC" xml:space="preserve">
    <value>Der VROC (Volume Rate of Change) zeigt, ob sich ein Volumen Trend entwickelt (auf- oder abwärts gerichtet). Er ist vergleichbar mit dem ROC Indikator, aber stattdessen auf das Volumen angewendet.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVWMA" xml:space="preserve">
    <value>Der VWMA (Volume Weighted Moving Average) gibt den volumengewichteten gleitenden Durchschnitt für die angegebenen Preisreihe und Zeitraum an. Der VWMA ist dem Simple Moving Average (SMA) ähnlich, aber jedes Bar der Daten wird hier durch das Volumen des Bars gewichtet. Der VWMA misst den Tagen mit dem größten Volumen eine hoehere Bedeutung bei als denen mit dem niedrigsten Volumen für den angegebenen Zeitraum.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWilliamsR" xml:space="preserve">
    <value>Williams %R ist ein Momentum Indikator, der entworfen wurde, um überkaufte und überverkaufte Bereiche in einem nicht trendenden Markt zu identifizieren.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWMA" xml:space="preserve">
    <value>Der WMA (Weighted Moving Average) ist ein gleitender Durchschnitt Indikator, der den Durchschnittswert des Wertpapierpreises über einen Zeitraum mit besonderem Schwerpunkt auf den neueren Daten des Analysezeitraums anzeigt.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZigZag" xml:space="preserve">
    <value>Der Zig Zag Indikator zeigt Trendlinien an, die Preisveränderungen unter einem definierten Niveau herausfiltern.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZLEMA" xml:space="preserve">
    <value>Der ZLEMA (Zero Lag Exponential Moving Average) ist eine EMA Variante, die versucht, die Verzögerung zu kompensieren.</value>
  </data>
  <data name="NinjaScriptIndicatorDiff" xml:space="preserve">
    <value>Differenz</value>
  </data>
  <data name="NinjaScriptIndicatorDisparityLine" xml:space="preserve">
    <value>Disparitätslinie</value>
  </data>
  <data name="NinjaScriptIndicatorDown" xml:space="preserve">
    <value>nach unten</value>
  </data>
  <data name="NinjaScriptIndicatorLower" xml:space="preserve">
    <value>Lower</value>
  </data>
  <data name="NinjaScriptIndicatorMcClellanOscillatorLine" xml:space="preserve">
    <value>McClellan-Oszillatorlinie</value>
  </data>
  <data name="NinjaScriptIndicatorMiddle" xml:space="preserve">
    <value>Mitte</value>
  </data>
  <data name="NinjaScriptIndicatorMoneyFlowLine" xml:space="preserve">
    <value>Money Flow Linie</value>
  </data>
  <data name="NinjaScriptIndicatorNameADL" xml:space="preserve">
    <value>ADL</value>
  </data>
  <data name="NinjaScriptIndicatorNameADX" xml:space="preserve">
    <value>ADX</value>
  </data>
  <data name="NinjaScriptIndicatorNameADXR" xml:space="preserve">
    <value>ADXR</value>
  </data>
  <data name="NinjaScriptIndicatorNameAPZ" xml:space="preserve">
    <value>APZ</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroon" xml:space="preserve">
    <value>Aroon</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroonOscillator" xml:space="preserve">
    <value>Aroon Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameATR" xml:space="preserve">
    <value>ATR</value>
  </data>
  <data name="NinjaScriptIndicatorNameBarTimer" xml:space="preserve">
    <value>Bar Timer</value>
  </data>
  <data name="NinjaScriptIndicatorNameBlockVolume" xml:space="preserve">
    <value>Blockvolumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameBollinger" xml:space="preserve">
    <value>Bollinger</value>
  </data>
  <data name="NinjaScriptIndicatorNameBOP" xml:space="preserve">
    <value>BOP</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellPressure" xml:space="preserve">
    <value>Kauf/Verkauf Druck</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellVolume" xml:space="preserve">
    <value>Kauf/Verkauf Volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameCamarillaPivots" xml:space="preserve">
    <value>Camarilla Pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNameCandlestickPattern" xml:space="preserve">
    <value>Candlestick Pattern</value>
  </data>
  <data name="NinjaScriptIndicatorNameCCI" xml:space="preserve">
    <value>CCI</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinMoneyFlow" xml:space="preserve">
    <value>Chaikin Geldfluß</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinOscillator" xml:space="preserve">
    <value>Chaikin Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinVolatility" xml:space="preserve">
    <value>Chaikin Volatilität</value>
  </data>
  <data name="NinjaScriptIndicatorNameChoppinessIndex" xml:space="preserve">
    <value>Choppiness Index</value>
  </data>
  <data name="NinjaScriptIndicatorNameCMO" xml:space="preserve">
    <value>GMO</value>
  </data>
  <data name="NinjaScriptIndicatorNameConstantLines" xml:space="preserve">
    <value>Konstante Linien</value>
  </data>
  <data name="NinjaScriptIndicatorNameCorrelation" xml:space="preserve">
    <value>Korrelation</value>
  </data>
  <data name="NinjaScriptIndicatorNameCOT" xml:space="preserve">
    <value>Verpflichtungen der Händler</value>
  </data>
  <data name="NinjaScriptIndicatorNameCurrentDayOHL" xml:space="preserve">
    <value>Aktueller Tages OHL</value>
  </data>
  <data name="NinjaScriptIndicatorNameDarvas" xml:space="preserve">
    <value>Darvas</value>
  </data>
  <data name="NinjaScriptIndicatorNameDEMA" xml:space="preserve">
    <value>DEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameDisparityIndex" xml:space="preserve">
    <value>Disparity index</value>
  </data>
  <data name="NinjaScriptIndicatorNameDM" xml:space="preserve">
    <value>DM</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMI" xml:space="preserve">
    <value>DMI</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMIndex" xml:space="preserve">
    <value>DM Index</value>
  </data>
  <data name="NinjaScriptIndicatorNameDonchianChannel" xml:space="preserve">
    <value>Donchian Kanal</value>
  </data>
  <data name="NinjaScriptIndicatorNameDoubleStochastics" xml:space="preserve">
    <value>Doppel Stochastik</value>
  </data>
  <data name="NinjaScriptIndicatorNameEaseOfMovement" xml:space="preserve">
    <value>Ease of movement</value>
  </data>
  <data name="NinjaScriptIndicatorNameEMA" xml:space="preserve">
    <value>EMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameFibonacciPivots" xml:space="preserve">
    <value>Fibonacci Pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNameFisherTransform" xml:space="preserve">
    <value>Fisher Transformation</value>
  </data>
  <data name="NinjaScriptIndicatorNameFOSC" xml:space="preserve">
    <value>FOSC</value>
  </data>
  <data name="NinjaScriptIndicatorNameHMA" xml:space="preserve">
    <value>HMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKAMA" xml:space="preserve">
    <value>KAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKelterChannel" xml:space="preserve">
    <value>Keltner Bänder</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalDown" xml:space="preserve">
    <value>Key reversal abwärts</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalUp" xml:space="preserve">
    <value>Key reversal aufwärts</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinReg" xml:space="preserve">
    <value>Lin. reg.</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegIntercept" xml:space="preserve">
    <value>Lin. reg. Schnittpunkt</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegSlope" xml:space="preserve">
    <value>Lin. reg. Steigung</value>
  </data>
  <data name="NinjaScriptIndicatorNameMACD" xml:space="preserve">
    <value>MACD</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAEnvelopes" xml:space="preserve">
    <value>MA Bänder</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAMA" xml:space="preserve">
    <value>MAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAX" xml:space="preserve">
    <value>MAX</value>
  </data>
  <data name="NinjaScriptIndicatorNameMcClellanOscillator" xml:space="preserve">
    <value>McClellan-Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameMFI" xml:space="preserve">
    <value>MFI</value>
  </data>
  <data name="NinjaScriptIndicatorNameMIN" xml:space="preserve">
    <value>MIN</value>
  </data>
  <data name="NinjaScriptIndicatorNameMomentum" xml:space="preserve">
    <value>Momentum</value>
  </data>
  <data name="NinjaScriptIndicatorNameMoneyFlowOscillator" xml:space="preserve">
    <value>Money Flow Osillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameMovingAverageRibbon" xml:space="preserve">
    <value>Moving average ribbon</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsDown" xml:space="preserve">
    <value>N Kerzen abwärts</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsUp" xml:space="preserve">
    <value>N Kerzen aufwärts</value>
  </data>
  <data name="NinjaScriptIndicatorNameNetChangeDisplay" xml:space="preserve">
    <value>Net change Anzeige</value>
  </data>
  <data name="NinjaScriptIndicatorNameOBV" xml:space="preserve">
    <value>OBV</value>
  </data>
  <data name="NinjaScriptIndicatorNameParabolicSAR" xml:space="preserve">
    <value>Parabolic SAR</value>
  </data>
  <data name="NinjaScriptIndicatorNamePFE" xml:space="preserve">
    <value>PFE</value>
  </data>
  <data name="NinjaScriptIndicatorNamePivots" xml:space="preserve">
    <value>Pivots</value>
  </data>
  <data name="NinjaScriptIndicatorNamePPO" xml:space="preserve">
    <value>PPO</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceLine" xml:space="preserve">
    <value>Preislinie</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceOscillator" xml:space="preserve">
    <value>Preis Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriorDayOHLC" xml:space="preserve">
    <value>Vortages OHLC</value>
  </data>
  <data name="NinjaScriptIndicatorNamePsychologicalLine" xml:space="preserve">
    <value>Psychological Line</value>
  </data>
  <data name="NinjaScriptIndicatorNameRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="NinjaScriptIndicatorNameRangeCounter" xml:space="preserve">
    <value>Range counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameRegressionChannel" xml:space="preserve">
    <value>Regressionskanal</value>
  </data>
  <data name="NinjaScriptIndicatorNameRelativeVigorIndex" xml:space="preserve">
    <value>Relative vigor index</value>
  </data>
  <data name="NinjaScriptIndicatorNameRIND" xml:space="preserve">
    <value>RIND</value>
  </data>
  <data name="NinjaScriptIndicatorNameROC" xml:space="preserve">
    <value>ROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSI" xml:space="preserve">
    <value>RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSquared" xml:space="preserve">
    <value>R Quadrat</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSS" xml:space="preserve">
    <value>RSS</value>
  </data>
  <data name="NinjaScriptIndicatorNameRVI" xml:space="preserve">
    <value>RVI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSampleCustomRender" xml:space="preserve">
    <value>Sample custom render</value>
  </data>
  <data name="NinjaScriptIndicatorNameSMA" xml:space="preserve">
    <value>SMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdDev" xml:space="preserve">
    <value>Std. Dev.</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdError" xml:space="preserve">
    <value>Std. Fehler</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochastics" xml:space="preserve">
    <value>Stochastik</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochasticsFast" xml:space="preserve">
    <value>Schnelle Stochastik</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochRSI" xml:space="preserve">
    <value>Stoch RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSUM" xml:space="preserve">
    <value>SUMME</value>
  </data>
  <data name="NinjaScriptIndicatorNameSwing" xml:space="preserve">
    <value>Swing</value>
  </data>
  <data name="NinjaScriptIndicatorNameT3" xml:space="preserve">
    <value>T3</value>
  </data>
  <data name="NinjaScriptIndicatorNameTEMA" xml:space="preserve">
    <value>TEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTickCounter" xml:space="preserve">
    <value>Tick counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameTMA" xml:space="preserve">
    <value>TMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTrendLines" xml:space="preserve">
    <value>Trendlinien</value>
  </data>
  <data name="NinjaScriptIndicatorNameTRIX" xml:space="preserve">
    <value>TRIX</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSF" xml:space="preserve">
    <value>TSF</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSI" xml:space="preserve">
    <value>TSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameUltimateOscillator" xml:space="preserve">
    <value>Ultimate Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameVMA" xml:space="preserve">
    <value>VMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOL" xml:space="preserve">
    <value>VOL</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOLMA" xml:space="preserve">
    <value>VOLMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeCounter" xml:space="preserve">
    <value>Volume counter</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeOscillator" xml:space="preserve">
    <value>Volumen Oszillator</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeProfile" xml:space="preserve">
    <value>Volume Profile</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumesZones" xml:space="preserve">
    <value>Volumen Zonen</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeUpDown" xml:space="preserve">
    <value>Volumen Steigend Fallend</value>
  </data>
  <data name="NinjaScriptIndicatorNameVortex" xml:space="preserve">
    <value>Vortex</value>
  </data>
  <data name="NinjaScriptIndicatorNameVROC" xml:space="preserve">
    <value>VROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameVWMA" xml:space="preserve">
    <value>VWMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameWilliamsR" xml:space="preserve">
    <value>Williams R</value>
  </data>
  <data name="NinjaScriptIndicatorNameWMA" xml:space="preserve">
    <value>WMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameZigZag" xml:space="preserve">
    <value>Zig zag</value>
  </data>
  <data name="NinjaScriptIndicatorNameZLEMA" xml:space="preserve">
    <value>ZLEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNeutral" xml:space="preserve">
    <value>Neutral</value>
  </data>
  <data name="NinjaScriptIndicatorOverbought" xml:space="preserve">
    <value>Überkauft</value>
  </data>
  <data name="NinjaScriptIndicatorOverBoughtLine" xml:space="preserve">
    <value>Überkauftlinie</value>
  </data>
  <data name="NinjaScriptIndicatorOversold" xml:space="preserve">
    <value>Überverkauft</value>
  </data>
  <data name="NinjaScriptIndicatorOverSoldLine" xml:space="preserve">
    <value>Überverkauftlinie</value>
  </data>
  <data name="NinjaScriptIndicatorRelativeVigorIndex" xml:space="preserve">
    <value>Relative Vigor Index</value>
  </data>
  <data name="NinjaScriptIndicatorSignal" xml:space="preserve">
    <value>Signal</value>
  </data>
  <data name="NinjaScriptIndicatorUp" xml:space="preserve">
    <value>nach oben</value>
  </data>
  <data name="NinjaScriptIndicatorUpper" xml:space="preserve">
    <value>Upper</value>
  </data>
  <data name="NinjaScriptIndicatorVIMinus" xml:space="preserve">
    <value>VIMinus</value>
  </data>
  <data name="NinjaScriptIndicatorVIPlus" xml:space="preserve">
    <value>VIPlus</value>
  </data>
  <data name="NinjaScriptIndicatorVisualGroup" xml:space="preserve">
    <value>Visuell</value>
  </data>
  <data name="NinjaScriptIndicatorZeroLine" xml:space="preserve">
    <value>Nulllinie</value>
  </data>
  <data name="NinjaScriptIsVisibleOnlyFocused" xml:space="preserve">
    <value>Nur im Fokus sichtbar</value>
  </data>
  <data name="NinjaScriptLine" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="NinjaScriptLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="NinjaScriptLoadingData" xml:space="preserve">
    <value>  Laden der Daten   ´{0}´</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskPrice" xml:space="preserve">
    <value>Aktueller Ask-Kurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskSize" xml:space="preserve">
    <value>Aktueller Ask-Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAverageDailyVolume" xml:space="preserve">
    <value>∅ Tagesvolumen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBeta" xml:space="preserve">
    <value>Eine Maßzahl der Volatilität, oder das systematische Risiko, eines Wertpapiers oder eines Portfolios im Vergleich zu dem Markt als Ganzes.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidAskSpread" xml:space="preserve">
    <value>Die Differenz zwischen aktuellen Bid - und Ask-Kurses</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidPrice" xml:space="preserve">
    <value>Aktueller Bid-Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidSize" xml:space="preserve">
    <value>Aktuelle Bid Größe</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHigh" xml:space="preserve">
    <value>Hoch des aktuellen Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHighDate" xml:space="preserve">
    <value>Datum des Hoch des aktuellen Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLow" xml:space="preserve">
    <value>Tief des aktuellen Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLowDate" xml:space="preserve">
    <value>Datum des Tiefs des aktuellen Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartMini" xml:space="preserve">
    <value>Diese Marktscanner Spalte stellt ein Mini-Diagramm gemäß den Eingabeeigenschaften dar.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartNetChange" xml:space="preserve">
    <value>Diese Marktscanner Spalte stellt ein Mini-Diagramm gemäß den Eingabeeigenschaften dar.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCurrentRatio" xml:space="preserve">
    <value>Umlaufvermögen geteilt durch laufende Verbindlichkeiten</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyHigh" xml:space="preserve">
    <value>Tageshoch</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyLow" xml:space="preserve">
    <value>Tagestief</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyVolume" xml:space="preserve">
    <value>Tagesvolumen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDaysUntilRollover" xml:space="preserve">
    <value>Zeigt an, wie viele Tage von der Prolongation bis zum nächsten Kontrakt vergangen sind</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDescription" xml:space="preserve">
    <value>Beschreibung des Instruments</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendAmount" xml:space="preserve">
    <value>Dividendenbetrag</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendPayDate" xml:space="preserve">
    <value>Dividenden Auszahldatum</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendYield" xml:space="preserve">
    <value>Verhältnis, das zeigt, wie viel ein Unternehmen an Dividende pro Jahr zahlt im Vergleich zum Aktienkurs.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionEarningsPerShare" xml:space="preserve">
    <value>Teil eines Unternehmensergebnisses, der jeder ausstehenden Stammaktie zugewiesen ist.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Fünf Jahres Wachstum Prozentsatz</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52Weeks" xml:space="preserve">
    <value>Hoch der letzten 52 Wochen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52WeeksDate" xml:space="preserve">
    <value>Datum des Hoch der letzten 52 Wochen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHistoricalVolatility" xml:space="preserve">
    <value>Realisierte Volatilität eines Instruments im Laufe der Zeit</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionInstrument" xml:space="preserve">
    <value>Name des Instruments</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastClose" xml:space="preserve">
    <value>Schlusskurs des letzten Handelssitzung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastPrice" xml:space="preserve">
    <value>Letzter gehandelter Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastSize" xml:space="preserve">
    <value>Volumen letzter Trade</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52Weeks" xml:space="preserve">
    <value>Tief der letzten 52 Wochen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52WeeksDate" xml:space="preserve">
    <value>Datum des Tief der letzten 52 Wochen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketCap" xml:space="preserve">
    <value>Marktkapitalisierung. Der Gesamtwert der ausgegebenen Aktien.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketPrice" xml:space="preserve">
    <value>Aktuelle Preis und Nettoveränderung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChange" xml:space="preserve">
    <value>Aktueller Preis im Vergleich zum letzten Schlusskurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxDown" xml:space="preserve">
    <value>Aktuelles Tief im Vergleich zum letzten Schlusskurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxUp" xml:space="preserve">
    <value>Aktuelles Hoch im Vergleich zum letzten Schlusskurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNextYearsEarningsPerShare" xml:space="preserve">
    <value>Erwarteter Gewinn je Aktie</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNotes" xml:space="preserve">
    <value>Definierbares Feld. Doppelklick auf eine angewandte Notizen Spalte zum Erstellen oder Bearbeiten von Notizen.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpening" xml:space="preserve">
    <value>Eröffnungspreis der aktuellen Handelssitzung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpenInterest" xml:space="preserve">
    <value>Die Gesamtzahl der Optionen und / oder Terminkontrakte, die nicht geschlossen oder geliefert sind an einem bestimmten Tag.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPercentHeldByInstitutions" xml:space="preserve">
    <value>Anteil der Aktien in Institutionsbesitz</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionAvgPrice" xml:space="preserve">
    <value>Durchschnittlicher Einstiegspreis der aktuellen Position</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionSize" xml:space="preserve">
    <value>Aktuelle Positionsgröße</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPriceEarningsRatio" xml:space="preserve">
    <value>Aktueller Aktienkurs im Vergleich zum Ergebnis je Aktie.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionProfitLoss" xml:space="preserve">
    <value>Summe unrealisierter und realisierter Gewinne und Verluste </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRealizedProfitLoss" xml:space="preserve">
    <value>Realisierter Gewinn oder Verlust</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRevenuePerShare" xml:space="preserve">
    <value>Verhältnis von Einnahmen zu Aktienkurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSettlement" xml:space="preserve">
    <value>Heutiger Settlement Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSharesOutstanding" xml:space="preserve">
    <value>Anzahl ausstehender Aktien</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterest" xml:space="preserve">
    <value>Anzahl der Aktien, die Investoren aber nicht kurze verkauft haben noch abgedeckt oder glattgestellt.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterestRatio" xml:space="preserve">
    <value>Short Interesse geteilt durch durchschnittliches, tägliches Handelsvolumen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTimeLastTick" xml:space="preserve">
    <value>Zeitpunkt des letzten Trades</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTradedContracts" xml:space="preserve">
    <value>Heute ausgeführte Kontrakte</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTSTrend" xml:space="preserve">
    <value>Diese Spalte zeigt einen farbigen Balken an, der die eingehenden Ticks mit den gleichen Farben darstellt, die das T &amp; S-Fenster verwendet</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionUnrealizedProfitLoss" xml:space="preserve">
    <value>Gewinn und Verlust für die aktuelle Position </value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionVwap" xml:space="preserve">
    <value>Volumen gewichteter Durchschnittspreis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskPrice" xml:space="preserve">
    <value>Ask Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskSize" xml:space="preserve">
    <value>Ask Grösse</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAverageDailyVolume" xml:space="preserve">
    <value>∅ Tagesvolumen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBeta" xml:space="preserve">
    <value>Beta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidAskSpread" xml:space="preserve">
    <value>Bid Ask Spanne</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidPrice" xml:space="preserve">
    <value>Bid Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidSize" xml:space="preserve">
    <value>Bid Grösse</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHigh" xml:space="preserve">
    <value>Hoch des Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHighDate" xml:space="preserve">
    <value>Datum des Hoch des Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLow" xml:space="preserve">
    <value>Tief des Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLowDate" xml:space="preserve">
    <value>Datum des Tief des Kalenderjahres</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartMini" xml:space="preserve">
    <value>Chart - Mini</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartNetChange" xml:space="preserve">
    <value>Chart - Nettoveränderung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCurrentRatio" xml:space="preserve">
    <value>Current ratio</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyHigh" xml:space="preserve">
    <value>Tages Hoch</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyLow" xml:space="preserve">
    <value>Tages Tief</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyVolume" xml:space="preserve">
    <value>Tages Volumen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDaysUntilRollover" xml:space="preserve">
    <value>Tage bis zur Rollover</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDescription" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendAmount" xml:space="preserve">
    <value>Dividendenbetrag</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendPayDate" xml:space="preserve">
    <value>Dividenden Auszahldatum</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendYield" xml:space="preserve">
    <value>Dividendenrendite</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameEarningsPerShare" xml:space="preserve">
    <value>Ergebnis je Aktie</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Fünf Jahres Wachstum Prozentsatz</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52Weeks" xml:space="preserve">
    <value>52 Wochenhoch</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52WeeksDate" xml:space="preserve">
    <value>52 Wochenhoch Datum</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHistoricalVolatility" xml:space="preserve">
    <value>Historische Volatilität</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameInstrument" xml:space="preserve">
    <value>Instrument</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastClose" xml:space="preserve">
    <value>Letzter Schlußkurs</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastPrice" xml:space="preserve">
    <value>Letzter Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastSize" xml:space="preserve">
    <value>Last size</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52Weeks" xml:space="preserve">
    <value>52 Wochentief</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52WeeksDate" xml:space="preserve">
    <value>52 Wochentief Datum</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketCap" xml:space="preserve">
    <value>Marktkapitalisierung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketPrice" xml:space="preserve">
    <value>Marktpreis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChange" xml:space="preserve">
    <value>Netto Veränderung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxDown" xml:space="preserve">
    <value>Nettoveränderung max. abwärts</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxUp" xml:space="preserve">
    <value>Nettoveränderung max. aufwärts</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNextYearsEarningsPerShare" xml:space="preserve">
    <value>Nächstes Jahr Ergebnis je Aktie</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNotes" xml:space="preserve">
    <value>Notizen</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpening" xml:space="preserve">
    <value>Eröffnung</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpenInterest" xml:space="preserve">
    <value>Open Interest</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePercentHeldByInstitutions" xml:space="preserve">
    <value>% Anteil von institutionellen Anlegern</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionAvgPrice" xml:space="preserve">
    <value>Position ∅ Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionSize" xml:space="preserve">
    <value>Positionsgröße</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePriceEarningsRatio" xml:space="preserve">
    <value>Kurs Gewinn Verhältnis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameProfitLoss" xml:space="preserve">
    <value>Gewinn &amp; Verlust</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRealizedProfitLoss" xml:space="preserve">
    <value>Realisierter Gewinn &amp; Verlust</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRevenuePerShare" xml:space="preserve">
    <value>Umsatz je Aktie</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSettlement" xml:space="preserve">
    <value>Settlement Preis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSharesOutstanding" xml:space="preserve">
    <value>Ausstehende Aktien</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterest" xml:space="preserve">
    <value>Short Interesse</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterestRatio" xml:space="preserve">
    <value>Short Interesse Verhältnis</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTimeLastTick" xml:space="preserve">
    <value>Zeit des letzten Ticks</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTradedContracts" xml:space="preserve">
    <value>Gehandelte Kontrakte</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTSTrend" xml:space="preserve">
    <value>T &amp; S-Trend</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameUnrealizedProfitLoss" xml:space="preserve">
    <value>Unrealisierter Gewinn Verlust</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameVwap" xml:space="preserve">
    <value>VWAP</value>
  </data>
  <data name="NinjaScriptNumberOfRows" xml:space="preserve">
    <value>Zeilen</value>
  </data>
  <data name="NinjaScriptOnBarCloseError" xml:space="preserve">
    <value>{0} stützt sich auf Bid / Ask Tick Updates erwartet Berechnen 'Anhand Tick'</value>
  </data>
  <data name="NinjaScriptOnPriceChangeError" xml:space="preserve">
    <value>{0} beruht auf Volumenaktualisierungen und erwartet dieBerechnung 'Anhand Tick' oder Anhand Schlusskurs'</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfe" xml:space="preserve">
    <value>Max. Ø positive Bewegung</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeLong" xml:space="preserve">
    <value>Max. Ø positive Bewegung (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeShort" xml:space="preserve">
    <value>Max. Ø positive Bewegung (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfit" xml:space="preserve">
    <value>Max. Ø Gewinn</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitLong" xml:space="preserve">
    <value>Max. Ø Gewinn (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitShort" xml:space="preserve">
    <value>Max. Ø Gewinn (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfit" xml:space="preserve">
    <value>Max. Nettogewinn</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitLong" xml:space="preserve">
    <value>Max. Nettogewinn (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitShort" xml:space="preserve">
    <value>Max. Nettogewinn (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitable" xml:space="preserve">
    <value>Max. % profitabel</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableLong" xml:space="preserve">
    <value>Max. % profitabel (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableShort" xml:space="preserve">
    <value>Max. % profitabel (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablity" xml:space="preserve">
    <value>Max. Wahrscheinlichkeit</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityLong" xml:space="preserve">
    <value>Max. Wahrscheinlichkeit (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityShort" xml:space="preserve">
    <value>Max. Wahrscheinlichkeit (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactor" xml:space="preserve">
    <value>Max. profit faktor</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorLong" xml:space="preserve">
    <value>Max. profit faktor (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorShort" xml:space="preserve">
    <value>Max. profit faktor (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2" xml:space="preserve">
    <value>Max. R^2</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Long" xml:space="preserve">
    <value>Max. R^2 (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Short" xml:space="preserve">
    <value>Max. R^2 (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatio" xml:space="preserve">
    <value>Max. Sharpe-Ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioLong" xml:space="preserve">
    <value>Max. Sharpe-Ratio (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioShort" xml:space="preserve">
    <value>Max. Sharpe-Ratio (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatio" xml:space="preserve">
    <value>Max. Sortino Ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioLong" xml:space="preserve">
    <value>Max. Sortino Ratio (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioShort" xml:space="preserve">
    <value>Max. Sortino Ratio (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrength" xml:space="preserve">
    <value>Max. Stärke</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthLong" xml:space="preserve">
    <value>Max. Stärke (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthShort" xml:space="preserve">
    <value>Max. Stärke (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatio" xml:space="preserve">
    <value>Max. Ulcer Ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioLong" xml:space="preserve">
    <value>Max. Ulcer Ratio (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioShort" xml:space="preserve">
    <value>Max. Ulcer Ratio (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatio" xml:space="preserve">
    <value>Max. Gewinn/Verlust Ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioLong" xml:space="preserve">
    <value>Max. Gewinn/Verlust Ratio (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioShort" xml:space="preserve">
    <value>Max. Gewinn/Verlust Ratio (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMae" xml:space="preserve">
    <value>Min. Ø gegenläufige Bewegung</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeLong" xml:space="preserve">
    <value>Min. Ø gegenläufige Bewegung (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeShort" xml:space="preserve">
    <value>Min. Ø gegenläufige Bewegung (short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDown" xml:space="preserve">
    <value>Min. Draw Down</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownLong" xml:space="preserve">
    <value>Min. Draw Down (long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownShort" xml:space="preserve">
    <value>Min. Draw Down (short)</value>
  </data>
  <data name="NinjaScriptOptimizerDefault" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="NinjaScriptOptimizerGenetic" xml:space="preserve">
    <value>Genetisch</value>
  </data>
  <data name="NinjaScriptParameters" xml:space="preserve">
    <value>Parameter</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleATMStrategy" xml:space="preserve">
    <value>Beispiel für fortschrittliches Trademanagement</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleCustomPerformance" xml:space="preserve">
    <value>Beispiel einer benutzerdefinierten Performanzkennzahl</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleFramework" xml:space="preserve">
    <value>Diese Strategie zeigt einige der Fähigkeiten des NinjaTrader Development Frameworks</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMACrossOver" xml:space="preserve">
    <value>Beispiel einer einfachen MA Crossover Strategie.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiInstrument" xml:space="preserve">
    <value>Beispiel einer Multi-Instrument Strategie.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiTimeFrame" xml:space="preserve">
    <value>Beispiel einer Multi-Zeitrahmen Strategie.</value>
  </data>
  <data name="NinjaScriptStrategyGenerator" xml:space="preserve">
    <value>Strategiegenerator</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorCandleStickPatternPrompt" xml:space="preserve">
    <value>1 Kerzenmuster|{0} Kerzenmuster|Hinzufügen eines Kerzenmusters...|Kerzenmuster konfigurieren...|Kerzenmuster konfigurieren...|Kerzenmuster konfigurieren...</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntries" xml:space="preserve">
    <value>Einstiegsbedingungen</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntriesOrExits" xml:space="preserve">
    <value>Sie müssen mindestens eine Exit-Bedingung für den Einstiegsauftrag haben.</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorException" xml:space="preserve">
    <value>Ausnahme bei Ausdruck:{0}{1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorsPrompt" xml:space="preserve">
    <value>1 indicator|{0} indicators|Add indicator...|Configure indicator...|Configure indicators...</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorPeformance" xml:space="preserve">
    <value>Performance für {0} = {1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorProperties" xml:space="preserve">
    <value>AI Generierungseigenschaften</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorTerminated" xml:space="preserve">
    <value>Der Strategiegenerator wurde nach {1} Generationen auf '{0}' beendet, da es keine Performanceverbesserung für {2} Generationen gab</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseCandleStickPattern" xml:space="preserve">
    <value>Candlestick Pattern</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseIndicators" xml:space="preserve">
    <value>Indikatoren</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleATMStrategy" xml:space="preserve">
    <value>Beispiel einer ATM Strategie</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleCustomPerformance" xml:space="preserve">
    <value>Beispiel benutzerdefinierte Performanz</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleFramework" xml:space="preserve">
    <value>Beispiels-Framework</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMACrossOver" xml:space="preserve">
    <value>Beispiel MA Crossover</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiInstrument" xml:space="preserve">
    <value>Beispiel Multi Instrument</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiTimeFrame" xml:space="preserve">
    <value>Beispiel Multi Zeitrahmen</value>
  </data>
  <data name="NinjaScriptStrategyParameters" xml:space="preserve">
    <value>Strategieparameter </value>
  </data>
  <data name="NinjaScriptSuperDomColumnApq" xml:space="preserve">
    <value>APQ</value>
  </data>
  <data name="NinjaScriptSuperDomColumnBaseInitializeBarsPoolError" xml:space="preserve">
    <value>Fehler beim Laden der Kerzenreihe für '{0}/{1}': {2}</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionApq" xml:space="preserve">
    <value>In der Warteschlange (APQ) gibt es eine konservative Schätzung der aktuellen Position in der Warteschlange für Aufträge, die Sie platziert haben.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionNotes" xml:space="preserve">
    <value>Frei definierbares Feld. Doppelklicken Sie auf die Spalte Notizen um diese zu erstellen oder zu bearbeiten.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionPnl" xml:space="preserve">
    <value>Die Spalte "Gewinn und Verlust" (PnL) zeigt den potenziellen Gewinn und Verlust an jedem Kurs an, sobald Sie im eine geöffnete Position haben.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionVolume" xml:space="preserve">
    <value>Die Spalte Volume verwendet historische Tick-Daten, um die Anzahl der auf jeder Preisstufe gehandelten Kontrakte anzuzeigen. Sie können optional die Balken in Trades auf dem Ask oder Bid filtern.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnNotes" xml:space="preserve">
    <value>Notizen</value>
  </data>
  <data name="NinjaScriptSuperDomColumnProfitAndLoss" xml:space="preserve">
    <value>GuV</value>
  </data>
  <data name="NinjaScriptSuperDomColumnVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="NinjaScriptTileError" xml:space="preserve">
    <value>Fehler beim Laden des Zeichenwerkzeugs {0} : {1}</value>
  </data>
  <data name="NinjaScriptYOffset" xml:space="preserve">
    <value>Y-Pixel-Offset</value>
  </data>
  <data name="NumberOfCotPlots" xml:space="preserve">
    <value>Anzahl cot-Plots</value>
  </data>
  <data name="NumberOfTrendLines" xml:space="preserve">
    <value>Anzahl der Trendlinien</value>
  </data>
  <data name="NumStdDev" xml:space="preserve">
    <value>Anzahl der Standardabweichungen</value>
  </data>
  <data name="OffsetMultiplier" xml:space="preserve">
    <value>Offset Multiplikator</value>
  </data>
  <data name="OldTrendsOpacity" xml:space="preserve">
    <value>Opazität alter Trends</value>
  </data>
  <data name="Opacity" xml:space="preserve">
    <value>Trübung</value>
  </data>
  <data name="PathCapMode_Arrow" xml:space="preserve">
    <value>Pfeil</value>
  </data>
  <data name="PathCapMode_Line" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="PathToolCapMode_Arrow" xml:space="preserve">
    <value>Pfeil</value>
  </data>
  <data name="PathToolCapMode_Line" xml:space="preserve">
    <value>Linie</value>
  </data>
  <data name="PerformanceMetricSampleCumProfit" xml:space="preserve">
    <value>Beispiel kum. Gewinn Performanzmetrik</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Periode</value>
  </data>
  <data name="PeriodD" xml:space="preserve">
    <value>Periode D</value>
  </data>
  <data name="PeriodK" xml:space="preserve">
    <value>Periode K</value>
  </data>
  <data name="PeriodQ" xml:space="preserve">
    <value>Periode Q</value>
  </data>
  <data name="PFEZero" xml:space="preserve">
    <value>Null</value>
  </data>
  <data name="PiviotsDailyBarsError" xml:space="preserve">
    <value>Intraday- oder Tageskerzen müssen für Pivots verwendet werden</value>
  </data>
  <data name="PiviotsDailyDataError" xml:space="preserve">
    <value>Unzureichende Tagesdaten zur Berechnung von Pivots</value>
  </data>
  <data name="PiviotsInsufficentDataError" xml:space="preserve">
    <value>Zu wenig historische Daten um Pivots zu berechnen. Erhöhen Sie die Anzahl der dargestellten Daten.</value>
  </data>
  <data name="PiviotsPeriodTypeError" xml:space="preserve">
    <value>Der Datenreihentyp muss Tag sein mit einem Wert von 1</value>
  </data>
  <data name="PiviotsWeeklyBarsError" xml:space="preserve">
    <value>Tageskerzen benötigen die Verwendung von Wochen- oder Monats Pivotbereichen.</value>
  </data>
  <data name="PivotRange" xml:space="preserve">
    <value>Pivot Range</value>
  </data>
  <data name="PivotRange_Daily" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="PivotRange_Monthly" xml:space="preserve">
    <value>Monatlich</value>
  </data>
  <data name="PivotRange_Weekly" xml:space="preserve">
    <value>Woche</value>
  </data>
  <data name="PivotsPP" xml:space="preserve">
    <value>PP</value>
  </data>
  <data name="PivotsR1" xml:space="preserve">
    <value>R1</value>
  </data>
  <data name="PivotsR2" xml:space="preserve">
    <value>R2</value>
  </data>
  <data name="PivotsR3" xml:space="preserve">
    <value>R3</value>
  </data>
  <data name="PivotsR4" xml:space="preserve">
    <value>R4</value>
  </data>
  <data name="PivotsS1" xml:space="preserve">
    <value>S1</value>
  </data>
  <data name="PivotsS2" xml:space="preserve">
    <value>S2</value>
  </data>
  <data name="PivotsS3" xml:space="preserve">
    <value>S3</value>
  </data>
  <data name="PivotsS4" xml:space="preserve">
    <value>S4</value>
  </data>
  <data name="PlotCurrentValue" xml:space="preserve">
    <value>Nur den aktuellen Wert darstellen</value>
  </data>
  <data name="PositiveColor" xml:space="preserve">
    <value>Positive Farbe</value>
  </data>
  <data name="PPOSmoothed" xml:space="preserve">
    <value>Geglättet</value>
  </data>
  <data name="PriceLinePlotAsk" xml:space="preserve">
    <value>Ask Linie</value>
  </data>
  <data name="PriceLinePlotBid" xml:space="preserve">
    <value>Bid Linie</value>
  </data>
  <data name="PriceLinePlotLast" xml:space="preserve">
    <value>Linie Letzter Kurs</value>
  </data>
  <data name="PriorDayOHLCClose" xml:space="preserve">
    <value>Vortagsschlusskurs</value>
  </data>
  <data name="PriorDayOHLCHigh" xml:space="preserve">
    <value>Vortagshoch</value>
  </data>
  <data name="PriorDayOHLCIntradayError" xml:space="preserve">
    <value>Vortages OHLC funktioniert nur im Intradayintervall</value>
  </data>
  <data name="PriorDayOHLCLow" xml:space="preserve">
    <value>Vortagstief</value>
  </data>
  <data name="PriorDayOHLCOpen" xml:space="preserve">
    <value>Vortages Open</value>
  </data>
  <data name="RangeCounterBarError" xml:space="preserve">
    <value>Range Counter funktioniert nur bei Range Bars</value>
  </data>
  <data name="RangeCounterRemaing" xml:space="preserve">
    <value>Range verbleibend = {0}</value>
  </data>
  <data name="RangerCounterCount" xml:space="preserve">
    <value>Range count = {0}</value>
  </data>
  <data name="RangeValue" xml:space="preserve">
    <value>Range Wert</value>
  </data>
  <data name="RegionHighlightDirection_Horizontal" xml:space="preserve">
    <value>Horizonal</value>
  </data>
  <data name="RegionHighlightDirection_Vertical" xml:space="preserve">
    <value>Vertikal</value>
  </data>
  <data name="RegressionChannelType_Segment" xml:space="preserve">
    <value>Segment</value>
  </data>
  <data name="RegressionChannelType_StandardDeviation" xml:space="preserve">
    <value>Distanz der Standardabweichung</value>
  </data>
  <data name="ROCPeriod" xml:space="preserve">
    <value>Rate of Change Periode</value>
  </data>
  <data name="RVISignalLine" xml:space="preserve">
    <value>Signallinie</value>
  </data>
  <data name="SampleAddOnDescription" xml:space="preserve">
    <value>Beschreibung Beispielsname</value>
  </data>
  <data name="SampleAddOnHiThere" xml:space="preserve">
    <value>Hi there!</value>
  </data>
  <data name="SampleAddOnName" xml:space="preserve">
    <value>Name des Beispiels - AddOn´s </value>
  </data>
  <data name="SampleCumProfit" xml:space="preserve">
    <value>Beispiel kumulierter Gewinn</value>
  </data>
  <data name="SampleCumProfitDescription" xml:space="preserve">
    <value>Kumulierter Gewinn als Beispiel für eine benutzerdefinierte Performanzkennzahl</value>
  </data>
  <data name="SampleCustomPlotLowerRightCorner" xml:space="preserve">
    <value>Untere rechte Ecke</value>
  </data>
  <data name="SampleCustomPlotUpperLeftCorner" xml:space="preserve">
    <value>Obere linke Ecke</value>
  </data>
  <data name="SelectPattern" xml:space="preserve">
    <value>Pattern wählen</value>
  </data>
  <data name="SelectPatternDescription" xml:space="preserve">
    <value>Einen Pattern zum Suchen wählen</value>
  </data>
  <data name="SendAlerts" xml:space="preserve">
    <value>Alarme senden</value>
  </data>
  <data name="SendAlertsDescription" xml:space="preserve">
    <value>Auf True setzen um einen Alarm zum Alarm Fenster zu senden</value>
  </data>
  <data name="ShareArgsException" xml:space="preserve">
    <value>Es gab ein Problem, das OnShare mit Argumenten aufzurufen: {0}</value>
  </data>
  <data name="ShareBadGatewayError" xml:space="preserve">
    <value>Der Aktienprovider liefert einen Bad Gateway Fehler: ´{0}´</value>
  </data>
  <data name="ShareBadRequestError" xml:space="preserve">
    <value>Der Aktienprovider liefert einen Bad Request Fehler: ´{0}´</value>
  </data>
  <data name="ShareException" xml:space="preserve">
    <value>A WebException was thrown. Status: '{0}' Message: '{1}'</value>
  </data>
  <data name="ShareFacebookCouldNotRetrieveUser" xml:space="preserve">
    <value>Der User konnte nicht gefunden werden</value>
  </data>
  <data name="ShareFacebookCouldNotVerifyToken" xml:space="preserve">
    <value>Facebook konnte den Token des Users nicht überprüfen</value>
  </data>
  <data name="ShareFacebookNoResult" xml:space="preserve">
    <value>Fehler - keine Antwort von Facebook</value>
  </data>
  <data name="ShareFacebookPermissionDenied" xml:space="preserve">
    <value>Facebook-Berechtigung wurde durch den Benutzer verweigert</value>
  </data>
  <data name="ShareFacebookScopesNotFound" xml:space="preserve">
    <value>Facebook - Berechtigung konnte nicht verifiziert werden</value>
  </data>
  <data name="ShareFacebookSentSuccessfully" xml:space="preserve">
    <value>{0} Post erfolgreich</value>
  </data>
  <data name="ShareForbidden" xml:space="preserve">
    <value>Der Aktienprovider meldet einen Forbidden Message Fehler:´{0}´</value>
  </data>
  <data name="ShareGatewayTimeoutError" xml:space="preserve">
    <value>Der Aktienprovider meldet einen Gateway Timeout Fehler: ´{0}´</value>
  </data>
  <data name="ShareImageNoLongerExists" xml:space="preserve">
    <value>Das Bild an der Position '{0}' kann nicht gefunden werden.</value>
  </data>
  <data name="ShareInternalServerError" xml:space="preserve">
    <value>Der Aktienprovider meldet einen Internal Server Fehler:´{0}´</value>
  </data>
  <data name="ShareMailException" xml:space="preserve">
    <value>Fehler beim Senden der Email: {0} </value>
  </data>
  <data name="ShareMailPreconfiguredAol" xml:space="preserve">
    <value>AOL</value>
  </data>
  <data name="ShareMailPreconfiguredComcast" xml:space="preserve">
    <value>Comcast</value>
  </data>
  <data name="ShareMailPreconfiguredGmail" xml:space="preserve">
    <value>Gmail</value>
  </data>
  <data name="ShareMailPreconfiguredICloud" xml:space="preserve">
    <value>iCloud</value>
  </data>
  <data name="ShareMailPreconfiguredManual" xml:space="preserve">
    <value>Manuell</value>
  </data>
  <data name="ShareMailPreconfiguredOutlook" xml:space="preserve">
    <value>Outlook</value>
  </data>
  <data name="ShareMailPreconfiguredYahoo" xml:space="preserve">
    <value>Yahoo</value>
  </data>
  <data name="ShareMailSendError" xml:space="preserve">
    <value>Beim Senden der Nachricht entstand ein Fehler: {0}</value>
  </data>
  <data name="ShareMailSentSuccessfully" xml:space="preserve">
    <value>{0} - Nachricht erfolgreich gesendet</value>
  </data>
  <data name="ShareNonSuccessCode" xml:space="preserve">
    <value>Der Aktienprovider meldet einen {0} Fehler:´{1}´</value>
  </data>
  <data name="ShareNotAuthorized" xml:space="preserve">
    <value>Der Aktienprovider meldet einen Not-Authorized Fehler:´{0}´</value>
  </data>
  <data name="ShareServiceParameters" xml:space="preserve">
    <value>Anmeldedaten</value>
  </data>
  <data name="ShareServicePassword" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="ShareServiceSignature" xml:space="preserve">
    <value>Es gibt einen Ausnahmefehler bei dem Share-Dienst:´{0}´</value>
  </data>
  <data name="ShareServiceUserName" xml:space="preserve">
    <value>Benutzername</value>
  </data>
  <data name="ShareStockTwitsNoAccount" xml:space="preserve">
    <value>StockTwits Konto konnte nicht bestätigt werden</value>
  </data>
  <data name="ShareStockTwitsSentSuccessfully" xml:space="preserve">
    <value>{0} - Nachricht erfolgreich gesendet</value>
  </data>
  <data name="ShareTextMessageEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ShareTextMessageEmailRequired" xml:space="preserve">
    <value>Um die Textnachricht über den E-Mail-Share-Service zu konfigurieren, müssen Sie zunächst einen E-Mail-Share-Service einrichten.</value>
  </data>
  <data name="ShareTextMessageErrorOnShare" xml:space="preserve">
    <value>Es gab einen Fehler beim Senden einer Nachricht über den {0} E-Mail-Dienst: '{1}'</value>
  </data>
  <data name="ShareTextMessageMmsAddress" xml:space="preserve">
    <value>MMS-Adresse</value>
  </data>
  <data name="ShareTextMessageName" xml:space="preserve">
    <value>SMS-Nachricht per e-Mail</value>
  </data>
  <data name="ShareTextMessagePhoneNumber" xml:space="preserve">
    <value>Telefonnummer</value>
  </data>
  <data name="ShareTextMessagePreconfiguredAtt" xml:space="preserve">
    <value>AT&amp;T</value>
  </data>
  <data name="ShareTextMessagePreconfiguredManual" xml:space="preserve">
    <value>Manuell</value>
  </data>
  <data name="ShareTextMessagePreconfiguredSprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="ShareTextMessagePreconfiguredTMobile" xml:space="preserve">
    <value>T-Mobile</value>
  </data>
  <data name="ShareTextMessagePreconfiguredVerizon" xml:space="preserve">
    <value>Verizon</value>
  </data>
  <data name="ShareTextMessageSentSuccessfully" xml:space="preserve">
    <value>{0} - Textnachricht gesendet</value>
  </data>
  <data name="ShareTextMessageSmsAddress" xml:space="preserve">
    <value>SMS-Adresse</value>
  </data>
  <data name="ShareTooManyRequests" xml:space="preserve">
    <value>Der Aktienprovider meldet einen TooManyRequests Fehler: ´{0}´</value>
  </data>
  <data name="ShareTwitterSentSuccessfully" xml:space="preserve">
    <value>{0} - Tweet wurde erfolgreich gesendet</value>
  </data>
  <data name="ShowAskLine" xml:space="preserve">
    <value>Zeige Ask Linie</value>
  </data>
  <data name="ShowBidLine" xml:space="preserve">
    <value>Zeige Bid Linie</value>
  </data>
  <data name="ShowClose" xml:space="preserve">
    <value>Zeige Schlusskurs</value>
  </data>
  <data name="ShowHigh" xml:space="preserve">
    <value>Zeige Hoch</value>
  </data>
  <data name="ShowLastLine" xml:space="preserve">
    <value>Zeige Last Linie</value>
  </data>
  <data name="ShowLow" xml:space="preserve">
    <value>Zeige Tief</value>
  </data>
  <data name="ShowOpen" xml:space="preserve">
    <value>Zeige Eröffnung</value>
  </data>
  <data name="ShowPatternCount" xml:space="preserve">
    <value>Zeige Anzahlt der Pattern</value>
  </data>
  <data name="ShowPatternCountDescription" xml:space="preserve">
    <value>Auf True setzen um die Anzahlt der gefundenen Pattern anzuzeigen</value>
  </data>
  <data name="ShowPercent" xml:space="preserve">
    <value>Anzeige Prozent</value>
  </data>
  <data name="SignalPeriod" xml:space="preserve">
    <value>Signal Periode</value>
  </data>
  <data name="Slow" xml:space="preserve">
    <value>Langsam</value>
  </data>
  <data name="SlowLimit" xml:space="preserve">
    <value>Grenze langsam</value>
  </data>
  <data name="SlowPeriod" xml:space="preserve">
    <value>Langsame Periode</value>
  </data>
  <data name="SmallAreaColor" xml:space="preserve">
    <value>Farbe kleinere Fläche</value>
  </data>
  <data name="Smooth" xml:space="preserve">
    <value>Glättung</value>
  </data>
  <data name="Smoothing" xml:space="preserve">
    <value>Glätten</value>
  </data>
  <data name="StochasticsD" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="StochasticsK" xml:space="preserve">
    <value>K</value>
  </data>
  <data name="StockTwitsSentiment" xml:space="preserve">
    <value>Sentiment:</value>
  </data>
  <data name="StockTwitsSentimentDescription" xml:space="preserve">
    <value>Wähle Bärisch, Neutral. oder Bullisch für diese Nachricht</value>
  </data>
  <data name="StockTwitsServiceName" xml:space="preserve">
    <value>StockTwits</value>
  </data>
  <data name="StockTwitsSignature" xml:space="preserve">
    <value>Gesendet von NinjaTrader</value>
  </data>
  <data name="Strength" xml:space="preserve">
    <value>Stärke</value>
  </data>
  <data name="SuperDomColumnException" xml:space="preserve">
    <value>SuperDOM-Spalte'{0}': Fehler beim Aufruf der Methode'{1}': {2}</value>
  </data>
  <data name="SwingHigh" xml:space="preserve">
    <value>Swing Hoch</value>
  </data>
  <data name="SwingLow" xml:space="preserve">
    <value>Swing Tief</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0} .SwingHighBar: barsAgo muss größer / gleich 0 sein, war aber {1}</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0} .SwingHighBar: barsAgo aus gültigem Bereich 0 bis {1}, war {2}.</value>
  </data>
  <data name="SwingSwingHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0} .SowHighBar: Instanz muss größer / gleich 1 sein, war aber {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0} .SwingLowBar: barsAgo muss größer / gleich 0 sein, war aber {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0} .SwingLowBar: barsAgo aus gültigem Bereich 0 bis {1}, war {2}.</value>
  </data>
  <data name="SwingSwingLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0} .SwingLowBar: Instanz muss größer / gleich 1 sein, war aber {1}</value>
  </data>
  <data name="TCount" xml:space="preserve">
    <value>T Count</value>
  </data>
  <data name="TextColor" xml:space="preserve">
    <value>Farbe Text</value>
  </data>
  <data name="TextFont" xml:space="preserve">
    <value>Schrift Text</value>
  </data>
  <data name="TextFontDescription" xml:space="preserve">
    <value>Wählen Sie Schriftart, Stil und Größe aus, die im Diagramm angezeigt werden sollen</value>
  </data>
  <data name="TextPosition_BottomLeft" xml:space="preserve">
    <value>Unten Links</value>
  </data>
  <data name="TextPosition_BottomRight" xml:space="preserve">
    <value>Unten Rechts</value>
  </data>
  <data name="TextPosition_Center" xml:space="preserve">
    <value>Zentrieren</value>
  </data>
  <data name="TextPosition_TopLeft" xml:space="preserve">
    <value>Oben Links</value>
  </data>
  <data name="TextPosition_TopRight" xml:space="preserve">
    <value>Oben Rechts</value>
  </data>
  <data name="TickCounterBarError" xml:space="preserve">
    <value>Tick Counter funktioniert nur auf tickbasierten Charts</value>
  </data>
  <data name="TickCounterTickCount" xml:space="preserve">
    <value>Tick Count = </value>
  </data>
  <data name="TickCounterTicksRemaining" xml:space="preserve">
    <value>Ticks verbleibend =</value>
  </data>
  <data name="TrendLinesCurrentTrendLine" xml:space="preserve">
    <value>Aktuelle Trendlinie</value>
  </data>
  <data name="TrendLinesNotVisible" xml:space="preserve">
    <value>TrendLines-Indikator ist mit Strategy Analyzer nicht sichtbar</value>
  </data>
  <data name="TrendLinesTrendLineBroken" xml:space="preserve">
    <value>{0} gebrochen</value>
  </data>
  <data name="TrendLinesTrendLineHigh" xml:space="preserve">
    <value>Trendlinienhoch</value>
  </data>
  <data name="TrendLinesTrendLineLow" xml:space="preserve">
    <value>Trendlinientief</value>
  </data>
  <data name="TrendStrength" xml:space="preserve">
    <value>Dicke Text</value>
  </data>
  <data name="TrendStrengthDescription" xml:space="preserve">
    <value>Anzahl der Bars die erforderlich sind, um einen Trend zu definieren... falls ein Pattern einen Trend benötigt \ nA Wert von Null bedeutet dass kein Trend nötig ist.</value>
  </data>
  <data name="TRIXSignal" xml:space="preserve">
    <value>Signal</value>
  </data>
  <data name="TwitterAuthHeader" xml:space="preserve">
    <value>Konto erfolgreich autorisiert</value>
  </data>
  <data name="TwitterAuthText1" xml:space="preserve">
    <value>Du hast {0} erfolgreich autorisiert, auf deinen Twitter Account zuzugreifen.</value>
  </data>
  <data name="TwitterAuthText2" xml:space="preserve">
    <value>Sie können dieses Fenster schließen und zu {0} zurückkehren.</value>
  </data>
  <data name="TwitterServiceName" xml:space="preserve">
    <value>Twitter</value>
  </data>
  <data name="TwitterSignature" xml:space="preserve">
    <value> #NinjaTrader</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Anzahl</value>
  </data>
  <data name="UpBarColor" xml:space="preserve">
    <value>Farbe Aufwärtskerze</value>
  </data>
  <data name="UseHighLow" xml:space="preserve">
    <value>Verwenden der Hochs/Tiefs</value>
  </data>
  <data name="UserDefinedClose" xml:space="preserve">
    <value>Benutzerdefinierter Close</value>
  </data>
  <data name="UserDefinedHigh" xml:space="preserve">
    <value>Benutzerdefiniertes Hoch</value>
  </data>
  <data name="UserDefinedLow" xml:space="preserve">
    <value>Benutzerdefiniertes Tief</value>
  </data>
  <data name="VFactor" xml:space="preserve">
    <value>V factor</value>
  </data>
  <data name="VolatilityPeriod" xml:space="preserve">
    <value>Periode Volatilität</value>
  </data>
  <data name="VolumeCounterBarError" xml:space="preserve">
    <value>Volume Counter arbeitet nur auf volumenbasierten Intervallen</value>
  </data>
  <data name="VolumeCounterVolumeCount" xml:space="preserve">
    <value>Volumen = </value>
  </data>
  <data name="VolumeCounterVolumeRemaining" xml:space="preserve">
    <value>Restvolumen = </value>
  </data>
  <data name="VolumeDivisor" xml:space="preserve">
    <value>Volumen Teiler</value>
  </data>
  <data name="VolumeDown" xml:space="preserve">
    <value>Down-Volumen</value>
  </data>
  <data name="VolumeDownColor" xml:space="preserve">
    <value>Farbe Volumen fallend</value>
  </data>
  <data name="VolumeNeutralColor" xml:space="preserve">
    <value>Farbe Volumen neutral</value>
  </data>
  <data name="VolumeUp" xml:space="preserve">
    <value>Up Volumen</value>
  </data>
  <data name="VolumeUpColor" xml:space="preserve">
    <value>Farbe Volumen steigend</value>
  </data>
  <data name="VOLVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Breite</value>
  </data>
  <data name="WilliamsPercentR" xml:space="preserve">
    <value>Williams %R</value>
  </data>
  <data name="ZigZagDeviationValueError" xml:space="preserve">
    <value>"ZigZag kann keine Werte zeichnen, da der Abweichungswert zu groß ist. Bitte reduzieren Sie ihn."</value>
  </data>
  <data name="ZigZagHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0} .HighBar: barsAgo aus gültigem Bereich 0 bis {1}, war {2}</value>
  </data>
  <data name="ZigZagHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0} .HighBar: Instanz muss größer / gleich 1 sein, aber war {1}</value>
  </data>
  <data name="ZigZagLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0} .LowBar: barsAgo aus gültigem Bereich 0 bis {1}, war {2}</value>
  </data>
  <data name="ZigZagLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0} .LowBar: Instanz muss größer / gleich 1 sein, war aber {1}</value>
  </data>
  <data name="ZigZigHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0} .HighBar: barsAgo muss größer / gleich 0 sein, war aber {1}</value>
  </data>
  <data name="ZigZigLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0} .LowBar: barsAgo muss größer / gleich 0 sein, war aber {1}</value>
  </data>
  <data name="NinjaScriptRecentColumnResetWhen" xml:space="preserve">
    <value>Zurücksetzen, wenn</value>
  </data>
  <data name="RecentResetWhen_PriceReturns" xml:space="preserve">
    <value>Preisrückgaben</value>
  </data>
  <data name="RecentResetWhen_BidAskChange" xml:space="preserve">
    <value>Geld-/Briefwechsel</value>
  </data>
  <data name="RecentDisplayType_Bid" xml:space="preserve">
    <value>Gebot</value>
  </data>
  <data name="RecentDisplayType_BidAsk" xml:space="preserve">
    <value>Gebots- und Nachfragekurse</value>
  </data>
  <data name="RecentDisplayType_Ask" xml:space="preserve">
    <value>Fragen</value>
  </data>
  <data name="PropertyCategoryVisual" xml:space="preserve">
    <value>Visuell</value>
  </data>
  <data name="NinjaScriptSetup" xml:space="preserve">
    <value>Aufstellen</value>
  </data>
  <data name="NinjaScriptRecentColumnResetTolerance" xml:space="preserve">
    <value>Toleranz zurücksetzen</value>
  </data>
  <data name="NinjaScriptRecentColumnDiplay" xml:space="preserve">
    <value>Anzeige</value>
  </data>
  <data name="NinjaScriptRecentColumnBidForeground" xml:space="preserve">
    <value>Gebotsvordergrund</value>
  </data>
  <data name="NinjaScriptRecentColumnBidBackground" xml:space="preserve">
    <value>Angebotshintergrund</value>
  </data>
  <data name="NinjaScriptRecentColumnAskForeground" xml:space="preserve">
    <value>Fragen im Vordergrund</value>
  </data>
  <data name="NinjaScriptRecentColumnAskBackground" xml:space="preserve">
    <value>Fragen Sie nach dem Hintergrund</value>
  </data>
  <data name="PullingStackingDisplayType_Ask" xml:space="preserve">
    <value>Fragen</value>
  </data>
  <data name="PullingStackingDisplayType_BidAsk" xml:space="preserve">
    <value>Gebots- und Nachfragekurse</value>
  </data>
  <data name="PullingStackingDisplayType_Bid" xml:space="preserve">
    <value>Gebot</value>
  </data>
  <data name="PullingStackingResetWhen_BidAskChange" xml:space="preserve">
    <value>Geld-/Briefwechsel</value>
  </data>
  <data name="PullingStackingResetWhen_NoMoreData" xml:space="preserve">
    <value>Es werden keine Tiefendaten mehr empfangen</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceMarker" xml:space="preserve">
    <value>Preismarkierung</value>
  </data>
</root>