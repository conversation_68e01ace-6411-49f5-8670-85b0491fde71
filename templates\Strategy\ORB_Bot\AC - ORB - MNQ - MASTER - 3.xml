﻿<?xml version="1.0" encoding="utf-8"?>
<StrategyTemplate>
  <StrategyType>NinjaTrader.NinjaScript.Strategies.AlexORB</StrategyType>
  <Strategy>
    <AlexORB xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>5</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2023-01-01T00:00:00</From>
      <Panel>-1</Panel>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>true</ShowTransparentPlotsInDataBox>
      <To>2025-04-12T00:00:00</To>
      <Calculate>OnBarClose</Calculate>
      <Displacement>0</Displacement>
      <IsAutoScale>true</IsAutoScale>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>ORB BOT V1.4</Name>
      <Plots>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDA70D6&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Dash</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>3</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Range High</Name>
          <PlotStyle>Hash</PlotStyle>
        </Plot>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDA70D6&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Dash</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>3</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Range Low</Name>
          <PlotStyle>Hash</PlotStyle>
        </Plot>
      </Plots>
      <SelectedValueSeries>0</SelectedValueSeries>
      <BarsPeriodParameter>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name />
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </BarsPeriodParameter>
      <BarsRequiredToTrade>0</BarsRequiredToTrade>
      <Category>Backtest</Category>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <DaysToLoad>5</DaysToLoad>
      <DefaultQuantity>1</DefaultQuantity>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <EntriesPerDirection>1</EntriesPerDirection>
      <EntryHandling>AllEntries</EntryHandling>
      <ExitOnSessionCloseSeconds>30</ExitOnSessionCloseSeconds>
      <IncludeCommission>false</IncludeCommission>
      <InstrumentOrInstrumentList>MNQ JUN25</InstrumentOrInstrumentList>
      <IsAggregated>false</IsAggregated>
      <IsExitOnSessionCloseStrategy>false</IsExitOnSessionCloseStrategy>
      <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
      <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <IsWaitUntilFlat>false</IsWaitUntilFlat>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <OptimizationPeriod>10</OptimizationPeriod>
      <OrderFillResolution>High</OrderFillResolution>
      <OrderFillResolutionType>Minute</OrderFillResolutionType>
      <OrderFillResolutionValue>1</OrderFillResolutionValue>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
      <SetOrderQuantity>Strategy</SetOrderQuantity>
      <Slippage>0</Slippage>
      <StartBehavior>WaitUntilFlat</StartBehavior>
      <StopTargetHandling>PerEntryExecution</StopTargetHandling>
      <SupportsOptimizationGraph>true</SupportsOptimizationGraph>
      <TestPeriod>28</TestPeriod>
      <TradingHoursSerializable />
      <Gtd>1800-01-01T00:00:00</Gtd>
      <Template />
      <TimeInForce>Gtc</TimeInForce>
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ZOrder>-2147483648</ZOrder>
      <MaxLossIsOn>false</MaxLossIsOn>
      <MaxSessionLoss>500</MaxSessionLoss>
      <MaxWinIsOn>false</MaxWinIsOn>
      <MaxSessionProfit>500</MaxSessionProfit>
      <Backtest>true</Backtest>
      <PosSize1>1</PosSize1>
      <PosSize2>1</PosSize2>
      <PosSize3>1</PosSize3>
      <TradeWindow1IsOn>true</TradeWindow1IsOn>
      <TradeStart1>2020-01-01T06:30:00</TradeStart1>
      <TradeEnd1>2020-01-01T10:40:00</TradeEnd1>
      <RangeStart>2020-01-01T06:00:00</RangeStart>
      <RangeEnd>2020-01-01T06:55:00</RangeEnd>
      <RangeIsOvernight>false</RangeIsOvernight>
      <MyTradeDirection>Both</MyTradeDirection>
      <EntryOrderTickOffset>1</EntryOrderTickOffset>
      <StopLossTicks>200</StopLossTicks>
      <ProfitTargetTicks1>200</ProfitTargetTicks1>
      <ProfitTargetTicks2>400</ProfitTargetTicks2>
      <ProfitTargetTicks3>500</ProfitTargetTicks3>
      <BreakEvenIsOn>true</BreakEvenIsOn>
      <BreakEvenOffset>5</BreakEvenOffset>
      <BreakEvenAfterTicks>200</BreakEvenAfterTicks>
      <TrailIsOn>true</TrailIsOn>
      <TrailByTicks>200</TrailByTicks>
      <StartTrailAfterTicks>300</StartTrailAfterTicks>
      <TrailFrequency>25</TrailFrequency>
      <Martingale>false</Martingale>
      <MartingaleMultiplier>2</MartingaleMultiplier>
      <MaxMartingales>5</MaxMartingales>
    </AlexORB>
  </Strategy>
</StrategyTemplate>