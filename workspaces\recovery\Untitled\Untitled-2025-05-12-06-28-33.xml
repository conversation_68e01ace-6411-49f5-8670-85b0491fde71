﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-6cd2cc206e5c47f6a1950add790da77d>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Maximized</WindowState>
      <Location>52;52</Location>
      <Size>948;807</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>166</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-37f350168ee6402da5db5a0c320f236c>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>2015</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>50</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>25</Value>
                  <Value2>100</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>2</DaysBack>
                <From>2025-05-07T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2025-03-04T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>MNQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>640611ccd32c4553a4a9865ae87461f9</BarsSeriesId>
                <Id>640611ccd32c4553a4a9865ae87461f9</Id>
                <Instrument>MNQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>3</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies>
            <Strategy0 BarsIndex="0">357444288</Strategy0>
          </Strategies>
          <Indicators />
          <CrosshairType>Off</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>10</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Off</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>707</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21233.641361256545</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>21035.5</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
            <ChartPanel>
              <Height>212</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>88.25</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>-88.25</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-37f350168ee6402da5db5a0c320f236c>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>Playback101</Account>
        <ATM></ATM>
        <Instrument>MNQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Day</TIF>
      </ChartTrader>
    </Chart-6cd2cc206e5c47f6a1950add790da77d>
  </NTWindows>
</NinjaTrader>