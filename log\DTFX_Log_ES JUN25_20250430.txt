[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5669, TP=5659, R:R=-0.05
[2025-03-12 21:31:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5658.5, SL=5669, TP=5659
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5657.75, TP=5672.25, R:R=-0.03
[2025-03-14 11:20:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5672.75, SL=5657.75, TP=5672.25
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5659, R:R=-0.02
[2025-03-12 21:31:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5658.5, SL=5680.25, TP=5659
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5672.25, R:R=-0.02
[2025-03-14 11:20:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5672.75, SL=5646.5, TP=5672.25
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5659, R:R=-0.02
[2025-03-12 21:31:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5658.5, SL=5680.25, TP=5659
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5672.25, R:R=-0.02
[2025-03-14 11:20:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5672.75, SL=5646.5, TP=5672.25
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5659, R:R=-0.02
[2025-03-12 21:31:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5658.5, SL=5680.25, TP=5659
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5672.25, R:R=-0.02
[2025-03-14 11:20:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5672.75, SL=5646.5, TP=5672.25
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 02:29:00] [TRADE_SCAN]: Checking zone [Zone_6] for entry conditions
[2025-03-17 02:29:00] [TRADE_SCAN]: Price is in zone: Low=5653.75, High=5656, Zone=5654.5-5672.25
[2025-03-17 02:29:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5654.25 < Top=5672.25
[2025-03-17 02:29:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-17 02:29:00] [TRADE_CALC]: SHORT setup: Entry=5654.25, SL=5684.75, TP=5654.5, R:R=-0.01
[2025-03-17 02:29:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5654.25, SL=5684.75, TP=5654.5
[2025-03-17 02:29:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Price is in zone: Low=5685.25, High=5686, Zone=5654.5-5685.5
[2025-03-17 07:17:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5686 > Bottom=5654.5
[2025-03-17 07:17:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-17 07:17:00] [TRADE_CALC]: LONG setup: Entry=5686, SL=5642, TP=5685.5, R:R=-0.01
[2025-03-17 07:17:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5686, SL=5642, TP=5685.5
[2025-03-17 07:17:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5659, R:R=-0.02
[2025-03-12 21:31:00] [TRADE_REJECT]: SHORT rejected: Invalid R:R - Entry=5658.5, SL=5680.25, TP=5659
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5672.25, R:R=-0.02
[2025-03-14 11:20:00] [TRADE_REJECT]: LONG rejected: Invalid R:R - Entry=5672.75, SL=5646.5, TP=5672.25
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5636.75, R:R=1.00
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5680.25 TP=5636.75 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5699, R:R=1.00
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5646.5 TP=5699 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 02:29:00] [TRADE_SCAN]: Checking zone [Zone_6] for entry conditions
[2025-03-17 02:29:00] [TRADE_SCAN]: Price is in zone: Low=5653.75, High=5656, Zone=5654.5-5672.25
[2025-03-17 02:29:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5654.25 < Top=5672.25
[2025-03-17 02:29:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-17 02:29:00] [TRADE_CALC]: SHORT setup: Entry=5654.25, SL=5684.75, TP=5623.75, R:R=1.00
[2025-03-17 02:29:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_6 Entry=5654.25 SL=5684.75 TP=5623.75 HTF=0
[2025-03-17 02:29:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5680.25, TP=5636.75, R:R=1.00
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5680.25 TP=5636.75 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5646.5, TP=5699, R:R=1.00
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5646.5 TP=5699 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-16 21:01:05] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-16 21:01:10] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:15] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:25] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:30] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:35] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:40] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:45] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:50] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:02:05] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:02:05] [TRADE_SCAN]: Price is in zone: Low=5666.75, High=5666.75, Zone=5665-5667
[2025-03-16 21:02:05] [TRADE_TRIGGER]: Bullish entry trigger: Close=5666.75 > Bottom=5665
[2025-03-16 21:02:05] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-16 21:02:05] [TRADE_CALC]: LONG setup: Entry=5666.75, SL=5652.5, TP=5681, R:R=1.00
[2025-03-16 21:02:05] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_2 Entry=5666.75 SL=5652.5 TP=5681 HTF=0
[2025-03-16 21:02:05] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 03:18:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:05] [TRADE_SCAN]: Price is in zone: Low=5659, High=5662.75, Zone=5662.25-5670
[2025-03-17 04:00:05] [TRADE_TRIGGER]: Bearish entry trigger: Close=5659 < Top=5670
[2025-03-17 04:00:05] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-17 04:00:05] [TRADE_CALC]: SHORT setup: Entry=5659, SL=5682.5, TP=5635.5, R:R=1.00
[2025-03-17 04:00:05] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_7 Entry=5659 SL=5682.5 TP=5635.5 HTF=0
[2025-03-17 04:00:05] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:01:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-16 21:01:05] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-16 21:01:10] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:15] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:25] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:30] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:35] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:40] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:45] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:01:50] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:02:05] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-16 21:02:05] [TRADE_SCAN]: Price is in zone: Low=5666.75, High=5666.75, Zone=5665-5667
[2025-03-16 21:02:05] [TRADE_TRIGGER]: Bullish entry trigger: Close=5666.75 > Bottom=5665
[2025-03-16 21:02:05] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-16 21:02:05] [TRADE_CALC]: LONG setup: Entry=5666.75, SL=5652.5, TP=5681, R:R=1.00
[2025-03-16 21:02:05] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_2 Entry=5666.75 SL=5652.5 TP=5681 HTF=0
[2025-03-16 21:02:05] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 03:18:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:18:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:19:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:20:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:21:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:22:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:23:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:24:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:25:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:26:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:27:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:28:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:29:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:30:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:31:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:32:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:33:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:34:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:35:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:36:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:37:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:38:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:39:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:40:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:41:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:42:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:43:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:44:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:45:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:46:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:47:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:48:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:49:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:50:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:51:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:52:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:53:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:54:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:55:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:56:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:57:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:58:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:10] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:15] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:20] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:25] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:30] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:35] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:40] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:45] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:50] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 03:59:55] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:05] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 04:00:05] [TRADE_SCAN]: Price is in zone: Low=5659, High=5662.75, Zone=5662.25-5670
[2025-03-17 04:00:05] [TRADE_TRIGGER]: Bearish entry trigger: Close=5659 < Top=5670
[2025-03-17 04:00:05] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-17 04:00:05] [TRADE_CALC]: SHORT setup: Entry=5659, SL=5682.5, TP=5635.5, R:R=1.00
[2025-03-17 04:00:05] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_7 Entry=5659 SL=5682.5 TP=5635.5 HTF=0
[2025-03-17 04:00:05] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:01:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:01:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:02:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:03:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:04:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:05:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:06:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:07:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:08:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:09:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:10:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:11:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:12:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:13:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:14:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:15:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:16:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:17:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:18:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:19:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:20:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:21:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:22:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:23:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:24:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:25:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:26:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:27:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:28:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:29:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:40] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:45] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:50] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:30:55] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:05] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:10] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:15] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:20] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:25] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:30] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-17 07:31:35] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-13 11:30:00] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-13 11:30:00] [TRADE_SCAN]: Price is in zone: Low=5606.25, High=5619, Zone=5611.5-5675.25
[2025-03-13 11:30:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5607.75 < Top=5675.25
[2025-03-13 11:30:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-13 11:30:00] [TRADE_CALC]: SHORT setup: Entry=5607.75, SL=5687.75, TP=5527.75, R:R=1.00
[2025-03-13 11:30:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_1 Entry=5607.75 SL=5687.75 TP=5527.75 HTF=0
[2025-03-13 11:30:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-13 11:30:00] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-13 11:30:00] [TRADE_SCAN]: Price is in zone: Low=5606.25, High=5619, Zone=5611.5-5675.25
[2025-03-13 11:30:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5607.75 < Top=5675.25
[2025-03-13 11:30:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-13 11:30:00] [TRADE_CALC]: SHORT setup: Entry=5607.75, SL=5687.75, TP=5527.75, R:R=1.00
[2025-03-13 11:30:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_1 Entry=5607.75 SL=5687.75 TP=5527.75 HTF=0
[2025-03-13 11:30:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-13 11:30:00] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-13 11:30:00] [TRADE_SCAN]: Price is in zone: Low=5606.25, High=5619, Zone=5611.5-5675.25
[2025-03-13 11:30:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5607.75 < Top=5675.25
[2025-03-13 11:30:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-13 11:30:00] [TRADE_CALC]: SHORT setup: Entry=5607.75, SL=5687.75, TP=5527.75, R:R=1.00
[2025-03-13 11:30:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_1 Entry=5607.75 SL=5687.75 TP=5527.75 HTF=0
[2025-03-13 11:30:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 08:45:00] [TRADE_SCAN]: Checking zone [Zone_5] for entry conditions
[2025-03-17 08:45:00] [TRADE_SCAN]: Price is in zone: Low=5668.25, High=5701.5, Zone=5611.5-5691.25
[2025-03-17 08:45:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5697.75 > Bottom=5611.5
[2025-03-17 08:45:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-17 08:45:00] [TRADE_CALC]: LONG setup: Entry=5697.75, SL=5599, TP=5796.5, R:R=1.00
[2025-03-17 08:45:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_5 Entry=5697.75 SL=5599 TP=5796.5 HTF=0
[2025-03-17 08:45:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-31 03:15:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-31 03:15:00] [TRADE_SCAN]: Price is in zone: Low=5572.5, High=5584.5, Zone=5577.75-5691.25
[2025-03-31 03:15:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5575 < Top=5691.25
[2025-03-31 03:15:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-31 03:15:00] [TRADE_CALC]: SHORT setup: Entry=5575, SL=5703.75, TP=5446.25, R:R=1.00
[2025-03-31 03:15:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_7 Entry=5575 SL=5703.75 TP=5446.25 HTF=0
[2025-03-31 03:15:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-04-02 11:30:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-04-02 11:30:00] [TRADE_SCAN]: Price is in zone: Low=5673, High=5702.25, Zone=5577.75-5694.75
[2025-04-02 11:30:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5698 > Bottom=5577.75
[2025-04-02 11:30:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-04-02 11:30:00] [TRADE_CALC]: LONG setup: Entry=5698, SL=5565.25, TP=5830.75, R:R=1.00
[2025-04-02 11:30:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_8 Entry=5698 SL=5565.25 TP=5830.75 HTF=0
[2025-04-02 11:30:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-04-03 11:00:00] [TRADE_SCAN]: Checking zone [Zone_10] for entry conditions
[2025-04-03 11:00:00] [TRADE_SCAN]: Price is in zone: Low=5460.25, High=5482.5, Zone=5481-5694.75
[2025-04-03 11:00:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5461.25 < Top=5694.75
[2025-04-03 11:00:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-04-03 11:00:00] [TRADE_CALC]: SHORT setup: Entry=5461.25, SL=5707.25, TP=5215.25, R:R=1.00
[2025-04-03 11:00:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_10 Entry=5461.25 SL=5707.25 TP=5215.25 HTF=0
[2025-04-03 11:00:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5669, TP=5648, R:R=1.00
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5669 TP=5648 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5657.75, TP=5687.75, R:R=1.00
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5657.75 TP=5687.75 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 02:29:00] [TRADE_SCAN]: Checking zone [Zone_6] for entry conditions
[2025-03-17 02:29:00] [TRADE_SCAN]: Price is in zone: Low=5653.75, High=5656, Zone=5654.5-5672.25
[2025-03-17 02:29:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5654.25 < Top=5672.25
[2025-03-17 02:29:00] [TRADE_FILTER]: HTF Filter: Zone=Bear, HTF Trend=0, Alignment=True
[2025-03-17 02:29:00] [TRADE_CALC]: SHORT setup: Entry=5654.25, SL=5673.5, TP=5635, R:R=1.00
[2025-03-17 02:29:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_6 Entry=5654.25 SL=5673.5 TP=5635 HTF=0
[2025-03-17 02:29:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Price is in zone: Low=5685.25, High=5686, Zone=5654.5-5685.5
[2025-03-17 07:17:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5686 > Bottom=5654.5
[2025-03-17 07:17:00] [TRADE_FILTER]: HTF Filter: Zone=Bull, HTF Trend=0, Alignment=True
[2025-03-17 07:17:00] [TRADE_CALC]: LONG setup: Entry=5686, SL=5653.25, TP=5718.75, R:R=1.00
[2025-03-17 07:17:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_8 Entry=5686 SL=5653.25 TP=5718.75 HTF=0
[2025-03-17 07:17:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5669, TP=5648, R:R=1.00
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5669 TP=5648 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5657.75, TP=5687.75, R:R=1.00
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5657.75 TP=5687.75 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 02:29:00] [TRADE_SCAN]: Checking zone [Zone_6] for entry conditions
[2025-03-17 02:29:00] [TRADE_SCAN]: Price is in zone: Low=5653.75, High=5656, Zone=5654.5-5672.25
[2025-03-17 02:29:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5654.25 < Top=5672.25
[2025-03-17 02:29:00] [TRADE_CALC]: SHORT setup: Entry=5654.25, SL=5673.5, TP=5635, R:R=1.00
[2025-03-17 02:29:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_6 Entry=5654.25 SL=5673.5 TP=5635 HTF=0
[2025-03-17 02:29:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Price is in zone: Low=5685.25, High=5686, Zone=5654.5-5685.5
[2025-03-17 07:17:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5686 > Bottom=5654.5
[2025-03-17 07:17:00] [TRADE_CALC]: LONG setup: Entry=5686, SL=5653.25, TP=5718.75, R:R=1.00
[2025-03-17 07:17:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_8 Entry=5686 SL=5653.25 TP=5718.75 HTF=0
[2025-03-17 07:17:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5669, TP=5648, R:R=1.00
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5669 TP=5648 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5657.75, TP=5687.75, R:R=1.00
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5657.75 TP=5687.75 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 23:00:00] [TRADE_SCAN]: Checking zone [Zone_1] for entry conditions
[2025-03-12 23:00:00] [TRADE_SCAN]: Price is in zone: Low=5654.75, High=5657, Zone=5656-5675.25
[2025-03-12 23:00:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5655.5 < Top=5675.25
[2025-03-12 23:00:00] [TRADE_CALC]: SHORT setup: Entry=5655.5, SL=5676.5, TP=5634.5, R:R=1.00
[2025-03-12 23:00:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_1 Entry=5655.5 SL=5676.5 TP=5634.5 HTF=0
[2025-03-12 23:00:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 15:15:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 15:15:00] [TRADE_SCAN]: Price is in zone: Low=5684.25, High=5690.75, Zone=5656-5687.75
[2025-03-14 15:15:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5690.25 > Bottom=5656
[2025-03-14 15:15:00] [TRADE_CALC]: LONG setup: Entry=5690.25, SL=5654.75, TP=5725.75, R:R=1.00
[2025-03-14 15:15:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5690.25 SL=5654.75 TP=5725.75 HTF=0
[2025-03-14 15:15:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 03:20:00] [TRADE_SCAN]: Checking zone [Zone_5] for entry conditions
[2025-03-17 03:20:00] [TRADE_SCAN]: Price is in zone: Low=5651.5, High=5656.25, Zone=5654.5-5687.75
[2025-03-17 03:20:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5652.75 < Top=5687.75
[2025-03-17 03:20:00] [TRADE_CALC]: SHORT setup: Entry=5652.75, SL=5689, TP=5616.5, R:R=1.00
[2025-03-17 03:20:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_5 Entry=5652.75 SL=5689 TP=5616.5 HTF=0
[2025-03-17 03:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 09:35:00] [TRADE_SCAN]: Checking zone [Zone_7] for entry conditions
[2025-03-17 09:35:00] [TRADE_SCAN]: Price is in zone: Low=5685.25, High=5706.5, Zone=5654.5-5701.5
[2025-03-17 09:35:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5706.25 > Bottom=5654.5
[2025-03-17 09:35:00] [TRADE_CALC]: LONG setup: Entry=5706.25, SL=5653.25, TP=5759.25, R:R=1.00
[2025-03-17 09:35:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_7 Entry=5706.25 SL=5653.25 TP=5759.25 HTF=0
[2025-03-17 09:35:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-19 11:50:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-03-19 11:50:00] [TRADE_SCAN]: Price is in zone: Low=5709.75, High=5714.5, Zone=5653.25-5713.75
[2025-03-19 11:50:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5714.5 > Bottom=5653.25
[2025-03-19 11:50:00] [TRADE_CALC]: LONG setup: Entry=5714.5, SL=5652, TP=5777, R:R=1.00
[2025-03-19 11:50:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_8 Entry=5714.5 SL=5652 TP=5777 HTF=0
[2025-03-19 11:50:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-24 00:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 00:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 01:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 02:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 03:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 04:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 05:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 06:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 07:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 08:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 09:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 10:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 11:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 12:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 13:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 14:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 15:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 16:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 17:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 18:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 19:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 20:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 21:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 22:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-24 23:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 00:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 01:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 02:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 03:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 04:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 05:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 06:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 07:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 08:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 09:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 10:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 11:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 12:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 13:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 14:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 15:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 16:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 17:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 18:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 19:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 20:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 21:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 22:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-25 23:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 00:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 01:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 02:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 03:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 04:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 05:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 06:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 07:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 08:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 09:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 10:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 11:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 12:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 13:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 14:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 15:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 16:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 17:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 18:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 19:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 20:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 21:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 22:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-26 23:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 00:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 01:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 02:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 03:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 04:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 05:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 06:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 07:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:50:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 08:55:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:00:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:05:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:10:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:15:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:20:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:25:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:30:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:35:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:40:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:45:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-27 09:45:00] [TRADE_SCAN]: Price is in zone: Low=5720, High=5727.75, Zone=5651.25-5722.25
[2025-03-27 09:45:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5726.25 > Bottom=5651.25
[2025-03-27 09:45:00] [TRADE_CALC]: LONG setup: Entry=5726.25, SL=5650, TP=5802.5, R:R=1.00
[2025-03-27 09:45:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_9 Entry=5726.25 SL=5650 TP=5802.5 HTF=0
[2025-03-27 09:45:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-28 12:20:00] [TRADE_SCAN]: Checking zone [Zone_12] for entry conditions
[2025-03-28 12:20:00] [TRADE_SCAN]: Price is in zone: Low=5636.5, High=5649, Zone=5644.5-5722.25
[2025-03-28 12:20:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5636.5 < Top=5722.25
[2025-03-28 12:20:00] [TRADE_CALC]: SHORT setup: Entry=5636.5, SL=5723.5, TP=5549.5, R:R=1.00
[2025-03-28 12:20:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_12 Entry=5636.5 SL=5723.5 TP=5549.5 HTF=0
[2025-03-28 12:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-04-02 16:15:00] [TRADE_SCAN]: Checking zone [Zone_14] for entry conditions
[2025-04-02 16:15:00] [TRADE_SCAN]: Price is in zone: Low=5692.5, High=5761.5, Zone=5644.5-5739.25
[2025-04-02 16:15:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5753 > Bottom=5644.5
[2025-04-02 16:15:00] [TRADE_CALC]: LONG setup: Entry=5753, SL=5643.25, TP=5862.75, R:R=1.00
[2025-04-02 16:15:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_14 Entry=5753 SL=5643.25 TP=5862.75 HTF=0
[2025-04-02 16:15:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-04-03 10:25:00] [TRADE_SCAN]: Checking zone [Zone_16] for entry conditions
[2025-04-03 10:25:00] [TRADE_SCAN]: Price is in zone: Low=5475.5, High=5485, Zone=5481-5739.25
[2025-04-03 10:25:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5478.75 < Top=5739.25
[2025-04-03 10:25:00] [TRADE_CALC]: SHORT setup: Entry=5478.75, SL=5740.5, TP=5217, R:R=1.00
[2025-04-03 10:25:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_16 Entry=5478.75 SL=5740.5 TP=5217 HTF=0
[2025-04-03 10:25:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-12 21:31:00] [TRADE_SCAN]: Checking zone [Zone_2] for entry conditions
[2025-03-12 21:31:00] [TRADE_SCAN]: Price is in zone: Low=5658, High=5663, Zone=5659-5667.75
[2025-03-12 21:31:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5658.5 < Top=5667.75
[2025-03-12 21:31:00] [TRADE_CALC]: SHORT setup: Entry=5658.5, SL=5669, TP=5653.25, R:R=0.50
[2025-03-12 21:31:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_2 Entry=5658.5 SL=5669 TP=5653.25 HTF=0
[2025-03-12 21:31:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-14 11:20:00] [TRADE_SCAN]: Checking zone [Zone_4] for entry conditions
[2025-03-14 11:20:00] [TRADE_SCAN]: Price is in zone: Low=5670.75, High=5673.5, Zone=5659-5672.25
[2025-03-14 11:20:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5672.75 > Bottom=5659
[2025-03-14 11:20:00] [TRADE_CALC]: LONG setup: Entry=5672.75, SL=5657.75, TP=5680.25, R:R=0.50
[2025-03-14 11:20:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_4 Entry=5672.75 SL=5657.75 TP=5680.25 HTF=0
[2025-03-14 11:20:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 02:29:00] [TRADE_SCAN]: Checking zone [Zone_6] for entry conditions
[2025-03-17 02:29:00] [TRADE_SCAN]: Price is in zone: Low=5653.75, High=5656, Zone=5654.5-5672.25
[2025-03-17 02:29:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5654.25 < Top=5672.25
[2025-03-17 02:29:00] [TRADE_CALC]: SHORT setup: Entry=5654.25, SL=5673.5, TP=5644.75, R:R=0.49
[2025-03-17 02:29:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_6 Entry=5654.25 SL=5673.5 TP=5644.75 HTF=0
[2025-03-17 02:29:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-17 07:17:00] [TRADE_SCAN]: Checking zone [Zone_8] for entry conditions
[2025-03-17 07:17:00] [TRADE_SCAN]: Price is in zone: Low=5685.25, High=5686, Zone=5654.5-5685.5
[2025-03-17 07:17:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5686 > Bottom=5654.5
[2025-03-17 07:17:00] [TRADE_CALC]: LONG setup: Entry=5686, SL=5653.25, TP=5702.5, R:R=0.50
[2025-03-17 07:17:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_8 Entry=5686 SL=5653.25 TP=5702.5 HTF=0
[2025-03-17 07:17:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-18 11:58:00] [TRADE_SCAN]: Checking zone [Zone_9] for entry conditions
[2025-03-18 11:58:00] [TRADE_SCAN]: Price is in zone: Low=5650.75, High=5654.25, Zone=5653.25-5685.5
[2025-03-18 11:58:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5651 < Top=5685.5
[2025-03-18 11:58:00] [TRADE_CALC]: SHORT setup: Entry=5651, SL=5686.75, TP=5633.25, R:R=0.50
[2025-03-18 11:58:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_9 Entry=5651 SL=5686.75 TP=5633.25 HTF=0
[2025-03-18 11:58:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-19 09:34:00] [TRADE_SCAN]: Checking zone [Zone_11] for entry conditions
[2025-03-19 09:34:00] [TRADE_SCAN]: Price is in zone: Low=5686, High=5692.5, Zone=5653.25-5689.5
[2025-03-19 09:34:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5690.5 > Bottom=5653.25
[2025-03-19 09:34:00] [TRADE_CALC]: LONG setup: Entry=5690.5, SL=5652, TP=5709.75, R:R=0.50
[2025-03-19 09:34:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_11 Entry=5690.5 SL=5652 TP=5709.75 HTF=0
[2025-03-19 09:34:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-21 12:19:00] [TRADE_SCAN]: Checking zone [Zone_13] for entry conditions
[2025-03-21 12:19:00] [TRADE_SCAN]: Price is in zone: Low=5688.75, High=5695.5, Zone=5651.25-5691
[2025-03-21 12:19:00] [TRADE_TRIGGER]: Bullish entry trigger: Close=5693 > Bottom=5651.25
[2025-03-21 12:19:00] [TRADE_CALC]: LONG setup: Entry=5693, SL=5650, TP=5714.5, R:R=0.50
[2025-03-21 12:19:00] [TRADE_ENTRY]: ENTER LONG: Zone=Zone_13 Entry=5693 SL=5650 TP=5714.5 HTF=0
[2025-03-21 12:19:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
[2025-03-28 11:02:00] [TRADE_SCAN]: Checking zone [Zone_14] for entry conditions
[2025-03-28 11:02:00] [TRADE_SCAN]: Price is in zone: Low=5646, High=5651.75, Zone=5650.5-5691
[2025-03-28 11:02:00] [TRADE_TRIGGER]: Bearish entry trigger: Close=5647.5 < Top=5691
[2025-03-28 11:02:00] [TRADE_CALC]: SHORT setup: Entry=5647.5, SL=5692.25, TP=5625.25, R:R=0.50
[2025-03-28 11:02:00] [TRADE_ENTRY]: ENTER SHORT: Zone=Zone_14 Entry=5647.5 SL=5692.25 TP=5625.25 HTF=0
[2025-03-28 11:02:00] [TRADE_ZONE]: Zone marked as used for trading, will not generate more entries
