#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.NinjaScript;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Strategies;
#endregion

namespace NinjaTrader.NinjaScript.Strategies.KCStrategies
{
    public class BalaBot : KCAlgoBase
    {
       
        private Bala2Channels bala2Channels1;
        private bool useBalaTrendFilter = true; // Parameter to enable/disable trend filter


		public override string DisplayName { get { return Name; } }
		
        protected override void OnStateChange()
        {
            base.OnStateChange();

            if (State == State.SetDefaults)
            {
                Description = "Trades based on Bala2Channels indicator.";
                Name = "BalaBot v1.0.0";
                StrategyName = "BalaBot";
                Version = "v.1.0.0 June 2025";
                Credits = "Strategy by Bala";
                ChartType =  "1 Minute Chart";
				
				// Bala2Channels Default Parameters (can be adjusted here or made strategy 08b. Default Settings)
                Dev1_Param = 2.0;
                XS1_Param = 50.0;
                XL1_Param = 50.0;
                RSIPeriod1_Param = 21;
                EMAPeriod1_Param = 21;

                Dev2_Param = 1.0;
                XS2_Param = 49.0;
                XL2_Param = 50.0;
                RSIPeriod2_Param = 21;
                EMAPeriod2_Param = 21;

                BalaBackgroundOpacity_Param = 0.2;
                UseBalaTrendFilter = true; // Default to using the trend filter
				
//                InitialStop		= 97;
//				ProfitTarget	= 40;
            }
            else if (State == State.DataLoaded)
            {
                InitializeIndicators();
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBars[0] < BarsRequiredToTrade)
                return;

            base.OnBarUpdate();
        }

        protected override bool ValidateEntryLong()
        {
            if (bala2Channels1 == null || CurrentBar < 1 || bala2Channels1.LCrosses2 == null || bala2Channels1.C3TrendSeries == null)
                return false;

            // Long Signal: LCrosses2 appears on current bar (was not there on previous bar)
            bool balaLongSignal = bala2Channels1.LCrosses2[0].ApproxCompare(0) != 0 && bala2Channels1.LCrosses2[1].ApproxCompare(0) == 0;

            if (!balaLongSignal)
                return false;

            // Optional Trend Filter from Bala2Channels Channel 1
            if (UseBalaTrendFilter)
            {
                if (bala2Channels1.C3TrendSeries[0].ApproxCompare(1) != 0) // Trend must be up (+1)
                {
                    PrintOnce($"Bala_Long_TrendFail_{CurrentBar}", $"{Time[0]}: BalaChannels Long signal ignored, C3Trend not +1 (is {bala2Channels1.C3TrendSeries[0]})");
                    return false;
                }
            }
            PrintOnce($"Bala_Long_Valid_{CurrentBar}", $"{Time[0]}: BalaChannels Valid Long Signal. LCrosses2[0]={bala2Channels1.LCrosses2[0]}, C3Trend={bala2Channels1.C3TrendSeries[0]}");
            return true;
        }

        protected override bool ValidateEntryShort()
        {
            if (bala2Channels1 == null || CurrentBar < 1 || bala2Channels1.UCrosses2 == null || bala2Channels1.C3TrendSeries == null)
                return false;

            // Short Signal: UCrosses2 appears on current bar (was not there on previous bar)
            bool balaShortSignal = bala2Channels1.UCrosses2[0].ApproxCompare(0) != 0 && bala2Channels1.UCrosses2[1].ApproxCompare(0) == 0;

            if (!balaShortSignal)
                return false;

            // Optional Trend Filter from Bala2Channels Channel 1
            if (UseBalaTrendFilter)
            {
                if (bala2Channels1.C3TrendSeries[0].ApproxCompare(-1) != 0) // Trend must be down (-1)
                {
                     PrintOnce($"Bala_Short_TrendFail_{CurrentBar}", $"{Time[0]}: BalaChannels Short signal ignored, C3Trend not -1 (is {bala2Channels1.C3TrendSeries[0]})");
                    return false;
                }
            }
            PrintOnce($"Bala_Short_Valid_{CurrentBar}", $"{Time[0]}: BalaChannels Valid Short Signal. UCrosses2[0]={bala2Channels1.UCrosses2[0]}, C3Trend={bala2Channels1.C3TrendSeries[0]}");
            return true;
        }

//       	protected override bool ValidateExitLong()
//        {
//            // Logic for validating long exits
//            return enableExit? true: false;
//        }

//        protected override bool ValidateExitShort()
//        {
//			// Logic for validating short exits
//			return enableExit? true: false;
//        }

        #region Indicators
        protected override void InitializeIndicators()
        {
            // Initialize Bala2Channels
            bala2Channels1 = Bala2Channels(
                Dev1_Param, XS1_Param, XL1_Param, RSIPeriod1_Param, EMAPeriod1_Param,
                Dev2_Param, XS2_Param, XL2_Param, RSIPeriod2_Param, EMAPeriod2_Param,
                BalaBackgroundOpacity_Param
            );
            // Optionally add to chart if desired for visualization, KCAlgoBase doesn't usually do this for signal indies
             AddChartIndicator(bala2Channels1);
        }
        #endregion

        #region Properties
		[NinjaScriptProperty]
        [Display(Name="Bala Ch1 Dev", Order=100, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double Dev1_Param { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Bala Ch1 XS (OverBought)", Order=101, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double XS1_Param { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Bala Ch1 XL (OverSold)", Order=102, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double XL1_Param { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Bala Ch1 RSI Period", Order=103, GroupName="08a. Indicator Settings - Bala2Channels")]
        public int RSIPeriod1_Param { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Bala Ch1 EMA Period", Order=104, GroupName="08a. Indicator Settings - Bala2Channels")]
        public int EMAPeriod1_Param { get; set; }

        // --- Channel 2 Parameters ---
        [NinjaScriptProperty]
        [Display(Name="Bala Ch2 Dev", Order=105, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double Dev2_Param { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Bala Ch2 XS (OverBought)", Order=106, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double XS2_Param { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Bala Ch2 XL (OverSold)", Order=107, GroupName="08a. Indicator Settings - Bala2Channels")]
        public double XL2_Param { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Bala Ch2 RSI Period", Order=108, GroupName="08a. Indicator Settings - Bala2Channels")]
        public int RSIPeriod2_Param { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Bala Ch2 EMA Period", Order=109, GroupName="08a. Indicator Settings - Bala2Channels")]
        public int EMAPeriod2_Param { get; set; }

        [NinjaScriptProperty]
        [Range(0.0, 1.0)]
        [Display(Name = "Bala Background Opacity", Order = 110, GroupName = "08a. Indicator Settings - Bala2Channels")]
        public double BalaBackgroundOpacity_Param { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Use Bala C3Trend Filter", Order=111, GroupName="Strategy Logic")]
        public bool UseBalaTrendFilter
        {
            get { return useBalaTrendFilter; }
            set { useBalaTrendFilter = value; }
        }
        #endregion
    }
}
