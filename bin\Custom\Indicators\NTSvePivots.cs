//
// Copyright (C) 2018, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Core;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using SharpDX.DirectWrite;

#endregion

//This namespace holds Indicators in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Indicators
{
	[TypeConverter("NinjaTrader.NinjaScript.Indicators.NTSvePivotsTypeConverter")]
	public class NTSvePivots : Indicator
	{
		private DateTime				cacheMonthlyEndDate		= Globals.MinDate;
		private DateTime				cacheSessionDate		= Globals.MinDate;
		private DateTime				cacheSessionEnd			= Globals.MinDate;
		private DateTime				cacheTime;
		private DateTime				cacheWeeklyEndDate		= Globals.MinDate;
		private DateTime				currentDate				= Globals.MinDate;
		private DateTime				currentMonth			= Globals.MinDate;
		private DateTime				currentWeek				= Globals.MinDate;
		private DateTime				sessionDateTmp			= Globals.MinDate;
		private NTSveHLCCalculationMode		priorDayHlc;
		private NTSvePivotRange				NTSvePivotRangeType;
		private SessionIterator			storedSession;
		private double					currentClose;
		private double					currentHigh				= double.MinValue;
		private double					currentLow				= double.MaxValue;
		private double					dailyBarClose			= double.MinValue;
		private double					dailyBarHigh			= double.MinValue;
		private double					dailyBarLow				= double.MinValue;
		private double					pp;
		private double					r1;
		private double					r2;
		private double					r3;
		private double					s1;
		private double					s2;
		private double					s3;
		private double					r1m;
		private double					r2m;
		private double					r3m;
		private double					s1m;
		private double					s2m;
		private double					s3m;
		private double					ph;
		private double					pl;
		private double					userDefinedClose;
		private double					userDefinedHigh;
		private double					userDefinedLow;
		private int						cacheBar;
		private int						width					= 250;
		private readonly List<int>		newSessionBarIdxArr		= new List<int>();

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description				= "This is an adaption of the SvePivots indicator to the NinjaTrader 8 Pivots indicator. SvePivots is described in the December 2018 Stocks and Commodities article titled 'The V-Trade Part 3: Technical Analysis - Fibonacci Projections and Daily Pivots' by Syslvain Vervoot";
				Name					= "NTSvePivots";
				Calculate				= Calculate.OnBarClose;
				DisplayInDataBox		= true;
				DrawOnPricePanel		= false;
				IsAutoScale				= false;
				IsOverlay				= true;
				PaintPriceMarkers		= true;
				ScaleJustification		= ScaleJustification.Right;
				
				ShowHistorical 			= false;
				
				AddPlot(new Stroke(Brushes.Yellow, 		DashStyleHelper.Dot, 1), 	PlotStyle.Line, "PP");
				AddPlot(new Stroke(Brushes.SkyBlue, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "R1");
				AddPlot(new Stroke(Brushes.Aqua, 		DashStyleHelper.Dot, 1),	PlotStyle.Line, "R2");
				AddPlot(new Stroke(Brushes.LightBlue, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "R3");
				AddPlot(new Stroke(Brushes.Red, 		DashStyleHelper.Dot, 1),	PlotStyle.Line, "S1");
				AddPlot(new Stroke(Brushes.Coral, 		DashStyleHelper.Dot, 1),	PlotStyle.Line, "S2");
				AddPlot(new Stroke(Brushes.Pink, 		DashStyleHelper.Dot, 1),	PlotStyle.Line, "S3");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "R1M");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "R2M");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "R3M");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "S1M");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "S2M");
				AddPlot(new Stroke(Brushes.LightGray, 	DashStyleHelper.Dot, 1),	PlotStyle.Line, "S3M");
				AddPlot(new Stroke(Brushes.Lime, 	DashStyleHelper.Solid, 1),	PlotStyle.Line, "PH");
				AddPlot(new Stroke(Brushes.Magenta, 	DashStyleHelper.Solid, 1),	PlotStyle.Line, "PL");
			}
			else if (State == State.Configure)
			{
				if (priorDayHlc == NTSveHLCCalculationMode.DailyBars)
					AddDataSeries(BarsPeriodType.Day, 1);
			}
			else if (State == State.DataLoaded)
			{
				storedSession = new SessionIterator(Bars);
			}
			else if (State == State.Historical)
			{
				if (priorDayHlc == NTSveHLCCalculationMode.DailyBars && BarsArray[1].DayCount <= 0)
				{
					Draw.TextFixed(this, "NinjaScriptInfo", NinjaTrader.Custom.Resource.PiviotsDailyDataError, TextPosition.BottomRight);
					Log(NinjaTrader.Custom.Resource.PiviotsDailyDataError, LogLevel.Error);
					return;
				}

				if (!Bars.BarsType.IsIntraday && BarsPeriod.BarsPeriodType != BarsPeriodType.Day && (BarsPeriod.BarsPeriodType != BarsPeriodType.HeikenAshi && BarsPeriod.BarsPeriodType != BarsPeriodType.Volumetric || BarsPeriod.BaseBarsPeriodType != BarsPeriodType.Day))
				{
					Draw.TextFixed(this, "NinjaScriptInfo", NinjaTrader.Custom.Resource.PiviotsDailyBarsError, TextPosition.BottomRight);
					Log(NinjaTrader.Custom.Resource.PiviotsDailyBarsError, LogLevel.Error);
				}
				if ((BarsPeriod.BarsPeriodType == BarsPeriodType.Day || ((BarsPeriod.BarsPeriodType == BarsPeriodType.HeikenAshi || BarsPeriod.BarsPeriodType == BarsPeriodType.Volumetric) && BarsPeriod.BaseBarsPeriodType == BarsPeriodType.Day)) && NTSvePivotRangeType == NTSvePivotRange.Daily)
				{
					Draw.TextFixed(this, "NinjaScriptInfo", NinjaTrader.Custom.Resource.PiviotsWeeklyBarsError, TextPosition.BottomRight);
					Log(NinjaTrader.Custom.Resource.PiviotsWeeklyBarsError, LogLevel.Error);
				}
				if ((BarsPeriod.BarsPeriodType == BarsPeriodType.Day || ((BarsPeriod.BarsPeriodType == BarsPeriodType.HeikenAshi || BarsPeriod.BarsPeriodType == BarsPeriodType.Volumetric) && BarsPeriod.BaseBarsPeriodType == BarsPeriodType.Day)) && BarsPeriod.Value > 1)
				{
					Draw.TextFixed(this, "NinjaScriptInfo", NinjaTrader.Custom.Resource.PiviotsPeriodTypeError, TextPosition.BottomRight);
					Log(NinjaTrader.Custom.Resource.PiviotsPeriodTypeError, LogLevel.Error);
				}
				if ((priorDayHlc == NTSveHLCCalculationMode.DailyBars &&
					(NTSvePivotRangeType == NTSvePivotRange.Monthly && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddMonths(-1)
					|| NTSvePivotRangeType == NTSvePivotRange.Weekly && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddDays(-7)
					|| NTSvePivotRangeType == NTSvePivotRange.Daily && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddDays(-1)))
					|| NTSvePivotRangeType == NTSvePivotRange.Monthly && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddMonths(-1)
					|| NTSvePivotRangeType == NTSvePivotRange.Weekly && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddDays(-7)
					|| NTSvePivotRangeType == NTSvePivotRange.Daily && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddDays(-1)
					)
				{
					Draw.TextFixed(this, "NinjaScriptInfo", NinjaTrader.Custom.Resource.PiviotsInsufficentDataError, TextPosition.BottomRight);
					Log(NinjaTrader.Custom.Resource.PiviotsInsufficentDataError, LogLevel.Error);
				}
			}
		}

		protected override void OnBarUpdate()
		{
			if (BarsInProgress != 0)
				return;

			if ((priorDayHlc == NTSveHLCCalculationMode.DailyBars && BarsArray[1].DayCount <= 0)
				|| (!Bars.BarsType.IsIntraday && BarsPeriod.BarsPeriodType != BarsPeriodType.Day && (BarsPeriod.BarsPeriodType != BarsPeriodType.HeikenAshi && BarsPeriod.BarsPeriodType != BarsPeriodType.Volumetric || BarsPeriod.BaseBarsPeriodType != BarsPeriodType.Day))
				|| ((BarsPeriod.BarsPeriodType == BarsPeriodType.Day || ((BarsPeriod.BarsPeriodType == BarsPeriodType.HeikenAshi || BarsPeriod.BarsPeriodType == BarsPeriodType.Volumetric) && BarsPeriod.BaseBarsPeriodType == BarsPeriodType.Day)) && NTSvePivotRangeType == NTSvePivotRange.Daily)
				|| ((BarsPeriod.BarsPeriodType == BarsPeriodType.Day || ((BarsPeriod.BarsPeriodType == BarsPeriodType.HeikenAshi || BarsPeriod.BarsPeriodType == BarsPeriodType.Volumetric) && BarsPeriod.BaseBarsPeriodType == BarsPeriodType.Day)) && BarsPeriod.Value > 1)
				|| ((priorDayHlc == NTSveHLCCalculationMode.DailyBars && (NTSvePivotRangeType == NTSvePivotRange.Monthly && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddMonths(-1)
				|| NTSvePivotRangeType == NTSvePivotRange.Weekly && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddDays(-7)
				|| NTSvePivotRangeType == NTSvePivotRange.Daily && BarsArray[1].GetTime(0).Date >= BarsArray[1].GetTime(BarsArray[1].Count - 1).Date.AddDays(-1)))
				|| NTSvePivotRangeType == NTSvePivotRange.Monthly && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddMonths(-1)
				|| NTSvePivotRangeType == NTSvePivotRange.Weekly && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddDays(-7)
				|| NTSvePivotRangeType == NTSvePivotRange.Daily && BarsArray[0].GetTime(0).Date >= BarsArray[0].GetTime(BarsArray[0].Count - 1).Date.AddDays(-1)))
				return;

			RemoveDrawObject("NinjaScriptInfo");

			if (PriorDayHlc == NTSveHLCCalculationMode.DailyBars && CurrentBars[1] >= 0)
			{
				// Get daily bars like this to avoid situation where primary series moves to next session before previous day OHLC are added
				if (cacheTime != Times[0][0])
				{
					cacheTime	= Times[0][0];
					cacheBar	= BarsArray[1].GetBar(Times[0][0]);
				}
				dailyBarHigh	= BarsArray[1].GetHigh(cacheBar);
				dailyBarLow		= BarsArray[1].GetLow(cacheBar);
				dailyBarClose	= BarsArray[1].GetClose(cacheBar);
			}
			else
			{
				dailyBarHigh	= double.MinValue;
				dailyBarLow		= double.MinValue;
				dailyBarClose	= double.MinValue;
			}

			double high		= (dailyBarHigh == double.MinValue)		? Highs[0][0]	: dailyBarHigh;
			double low		= (dailyBarLow == double.MinValue)		? Lows[0][0]	: dailyBarLow;
			double close	= (dailyBarClose == double.MinValue)	? Closes[0][0]	: dailyBarClose;

			DateTime lastBarTimeStamp = GetLastBarSessionDate(Times[0][0], NTSvePivotRangeType);

			if ((currentDate != Globals.MinDate && NTSvePivotRangeType == NTSvePivotRange.Daily && lastBarTimeStamp != currentDate)
				|| (currentWeek != Globals.MinDate && NTSvePivotRangeType == NTSvePivotRange.Weekly && lastBarTimeStamp != currentWeek)
				|| (currentMonth != Globals.MinDate && NTSvePivotRangeType == NTSvePivotRange.Monthly && lastBarTimeStamp != currentMonth))
			{
				pp				= (currentHigh + currentLow + currentClose) / 3;
				s1				= 2 * pp - currentHigh;
				r1				= 2 * pp - currentLow;
				s2				= pp - (currentHigh - currentLow);
				r2				= pp + (currentHigh - currentLow);
				s3				= pp - 2 * (currentHigh - currentLow);
				r3				= pp + 2 * (currentHigh - currentLow);
				pl				= currentLow;
				s1m				= (pp - s1) / 2 + s1;
				r1m				= (r1 - pp) / 2 + pp;
				s2m				= (s1 - s2) / 2 + s2;
				r2m				= (r2 - r1) / 2 + r1;
				s3m				= (s2 - s3) / 2 + s3;
				r3m				= (r3 - r2) / 2 + r2;
				ph				= currentHigh;
				currentClose	= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedClose	: close;
				currentHigh		= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedHigh	: high;
				currentLow		= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedLow	: low;
			}
			else
			{
				currentClose	= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedClose	: close;
				currentHigh		= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedHigh	: Math.Max(currentHigh, high);
				currentLow		= (priorDayHlc == NTSveHLCCalculationMode.UserDefinedValues) ? UserDefinedLow	: Math.Min(currentLow, low);
			}


			if (NTSvePivotRangeType == NTSvePivotRange.Daily)
				currentDate = lastBarTimeStamp;
			if (NTSvePivotRangeType == NTSvePivotRange.Weekly)
				currentWeek = lastBarTimeStamp;
			if (NTSvePivotRangeType == NTSvePivotRange.Monthly)
				currentMonth = lastBarTimeStamp;

			if ((NTSvePivotRangeType == NTSvePivotRange.Daily && currentDate != Globals.MinDate)
				|| (NTSvePivotRangeType == NTSvePivotRange.Weekly && currentWeek != Globals.MinDate)
				|| (NTSvePivotRangeType == NTSvePivotRange.Monthly && currentMonth != Globals.MinDate))
			{
				Pp[0] = pp;
				R1[0] = r1;
				R2[0] = r2;
				R3[0] = r3;
				S1[0] = s1;
				S2[0] = s2;
				S3[0] = s3;
				R1M[0] = r1m;
				R2M[0] = r2m;
				R3M[0] = r3m;
				S1M[0] = s1m;
				S2M[0] = s2m;
				S3M[0] = s3m;
				PH[0] = ph;
				PL[0] = pl;
			}
		}

		#region Misc
		private DateTime GetLastBarSessionDate(DateTime time, NTSvePivotRange NTSvePivotRange)
		{
			// Check the time[0] against the previous session end
			if (time > cacheSessionEnd)
			{
				if (Bars.BarsType.IsIntraday)
				{
					// Make use of the stored session iterator to find the next session...
					storedSession.GetNextSession(time, true);
					// Store the actual session's end datetime as the session
					cacheSessionEnd = storedSession.ActualSessionEnd;
					// We need to convert that time from the session to the users time zone settings
					sessionDateTmp = TimeZoneInfo.ConvertTime(cacheSessionEnd.AddSeconds(-1), Globals.GeneralOptions.TimeZoneInfo, Bars.TradingHours.TimeZoneInfo).Date;
				}
				else
					sessionDateTmp = time.Date;
			}

			if (NTSvePivotRange == NTSvePivotRange.Daily)
			{
				if (sessionDateTmp != cacheSessionDate)
				{
					if (newSessionBarIdxArr.Count == 0 || newSessionBarIdxArr.Count > 0 && CurrentBar > newSessionBarIdxArr[newSessionBarIdxArr.Count - 1])
						newSessionBarIdxArr.Add(CurrentBar);
					cacheSessionDate = sessionDateTmp;
				}
				return sessionDateTmp;
			}

			DateTime tmpWeeklyEndDate = RoundUpTimeToPeriodTime(sessionDateTmp, NTSvePivotRange.Weekly);
			if (NTSvePivotRange == NTSvePivotRange.Weekly)
			{
				if (tmpWeeklyEndDate != cacheWeeklyEndDate)
				{
					if (newSessionBarIdxArr.Count == 0 || newSessionBarIdxArr.Count > 0 && CurrentBar > newSessionBarIdxArr[newSessionBarIdxArr.Count - 1])
						newSessionBarIdxArr.Add(CurrentBar);
					cacheWeeklyEndDate = tmpWeeklyEndDate;
				}
				return tmpWeeklyEndDate;
			}

			DateTime tmpMonthlyEndDate = RoundUpTimeToPeriodTime(sessionDateTmp, NTSvePivotRange.Monthly);
			if (tmpMonthlyEndDate != cacheMonthlyEndDate)
			{
				if (newSessionBarIdxArr.Count == 0 || newSessionBarIdxArr.Count > 0 && CurrentBar > newSessionBarIdxArr[newSessionBarIdxArr.Count - 1])
					newSessionBarIdxArr.Add(CurrentBar);
				cacheMonthlyEndDate = tmpMonthlyEndDate;
			}
			return tmpMonthlyEndDate;
		}

		private DateTime RoundUpTimeToPeriodTime(DateTime time, NTSvePivotRange NTSvePivotRange)
		{
			if (NTSvePivotRange == NTSvePivotRange.Weekly)
			{
				DateTime periodStart = time.AddDays((6 - (((int) time.DayOfWeek) + 1) % 7));
				return periodStart.Date.AddDays(Math.Ceiling(Math.Ceiling(time.Date.Subtract(periodStart.Date).TotalDays)/7)*7).Date;
			}
			if (NTSvePivotRange == NTSvePivotRange.Monthly)
			{
				var result = new DateTime(time.Year, time.Month, 1);
				return result.AddMonths(1).AddDays(-1);
			}
			return time;
		}

		protected override void OnRender(ChartControl chartControl, ChartScale chartScale)
		{
			if (ShowHistorical)
				base.OnRender(chartControl, chartScale);
			// Set text to chart label color and font
			TextFormat	textFormat			= chartControl.Properties.LabelFont.ToDirectWriteTextFormat();

			// Loop through each Plot Values on the chart
			for (int seriesCount = 0; seriesCount < Values.Length; seriesCount++)
			{
				double	y					= -1;
				double	startX				= -1;
				double	endX				= -1;
				int		firstBarIdxToPaint	= -1;
				int		firstBarPainted		= ChartBars.FromIndex;
				int		lastBarPainted		= ChartBars.ToIndex;
				Plot	plot				= Plots[seriesCount];

				for (int i = newSessionBarIdxArr.Count - 1; i >= 0; i--)
				{
					int prevSessionBreakIdx = newSessionBarIdxArr[i];
					if (prevSessionBreakIdx <= lastBarPainted)
					{
						firstBarIdxToPaint = prevSessionBreakIdx;
						break;
					}
				}

				// Loop through visble bars to render plot values
				for (int idx = lastBarPainted; idx >= Math.Max(firstBarPainted, lastBarPainted - width); idx--)
				{
					if (idx < firstBarIdxToPaint)
						break;

					startX		= chartControl.GetXByBarIndex(ChartBars, idx);
					endX		= chartControl.GetXByBarIndex(ChartBars, lastBarPainted);
					double val	= Values[seriesCount].GetValueAt(idx);
					y			= chartScale.GetYByValue(val);
				}

				// Draw pivot lines
				Point startPoint	= new Point(startX, y);
				Point endPoint		= new Point(endX, y);
				RenderTarget.DrawLine(startPoint.ToVector2(), endPoint.ToVector2(), plot.BrushDX, plot.Width, plot.StrokeStyle);

				// Draw pivot text
				TextLayout textLayout = new TextLayout(Globals.DirectWriteFactory, plot.Name, textFormat, ChartPanel.W, textFormat.FontSize);
				RenderTarget.DrawTextLayout(startPoint.ToVector2(), textLayout, plot.BrushDX);
				textLayout.Dispose();
			}
			textFormat.Dispose();
		}
		#endregion

		#region Properties
		
		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Show Historical Pivots", GroupName = "NinjaScriptParameters", Order = 0)]
		public bool ShowHistorical
		{ get; set; }

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "NTSvePivotRange", GroupName = "NinjaScriptParameters", Order = 1)]
		public NTSvePivotRange PivotRangeType
		{
			get { return NTSvePivotRangeType; }
			set { NTSvePivotRangeType = value; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Pp
		{
			get { return Values[0]; }
		}

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "NTSveHLCCalculationMode", GroupName = "NinjaScriptParameters", Order = 2)]
		[RefreshProperties(RefreshProperties.All)] // Update UI when value is changed
		public NTSveHLCCalculationMode PriorDayHlc
		{
			get { return priorDayHlc; }
			set { priorDayHlc = value; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R1
		{
			get { return Values[1]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R2
		{
			get { return Values[2]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R3
		{
			get { return Values[3]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S1
		{
			get { return Values[4]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S2
		{
			get { return Values[5]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S3
		{
			get { return Values[6]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R1M
		{
			get { return Values[7]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R2M
		{
			get { return Values[8]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> R3M
		{
			get { return Values[9]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S1M
		{
			get { return Values[10]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S2M
		{
			get { return Values[11]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> S3M
		{
			get { return Values[12]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> PH
		{
			get { return Values[13]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> PL
		{
			get { return Values[14]; }
		}

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "UserDefinedClose", GroupName = "NinjaScriptParameters", Order = 3)]
		public double UserDefinedClose
		{
			get { return userDefinedClose; }
			set { userDefinedClose = value; }
		}

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "UserDefinedHigh", GroupName = "NinjaScriptParameters", Order = 4)]
		public double UserDefinedHigh
		{
			get { return userDefinedHigh; }
			set { userDefinedHigh = value; }
		}

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "UserDefinedLow", GroupName = "NinjaScriptParameters", Order = 5)]
		public double UserDefinedLow
		{
			get { return userDefinedLow; }
			set { userDefinedLow = value; }
		}

		[NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Width", GroupName = "NinjaScriptParameters", Order = 6)]
		public int Width
		{
			get { return width; }
			set { width = value; }
		}

		#endregion
	}

	// Hide UserDefinedValues properties when not in use by the NTSveHLCCalculationMode.UserDefinedValues
	// When creating a custom type converter for indicators it must inherit from NinjaTrader.NinjaScript.IndicatorBaseConverter to work correctly with indicators
	public class NTSvePivotsTypeConverter : NinjaTrader.NinjaScript.IndicatorBaseConverter
	{
		public override bool GetPropertiesSupported(ITypeDescriptorContext context) { return true; }

		public override PropertyDescriptorCollection GetProperties(ITypeDescriptorContext context, object value, Attribute[] attributes)
		{
			PropertyDescriptorCollection propertyDescriptorCollection = base.GetPropertiesSupported(context) ? base.GetProperties(context, value, attributes) : TypeDescriptor.GetProperties(value, attributes);

			NTSvePivots						thisPivotsInstance			= (NTSvePivots) value;
			NTSveHLCCalculationMode	selectedNTSveHLCCalculationMode	= thisPivotsInstance.PriorDayHlc;
			if (selectedNTSveHLCCalculationMode == NTSveHLCCalculationMode.UserDefinedValues)
				return propertyDescriptorCollection;

			PropertyDescriptorCollection adjusted = new PropertyDescriptorCollection(null);
			foreach (PropertyDescriptor thisDescriptor in propertyDescriptorCollection)
			{
				if (thisDescriptor.Name == "UserDefinedClose" || thisDescriptor.Name == "UserDefinedHigh" || thisDescriptor.Name == "UserDefinedLow")
					adjusted.Add(new PropertyDescriptorExtended(thisDescriptor, o => value, null, new Attribute[] {new BrowsableAttribute(false), }));
				else
					adjusted.Add(thisDescriptor);
			}
			return adjusted;
		}
	}
}

[TypeConverter("NinjaTrader.Custom.ResourceEnumConverter")]
public enum NTSveHLCCalculationMode
{
	CalcFromIntradayData,
	DailyBars,
	UserDefinedValues
}

[TypeConverter("NinjaTrader.Custom.ResourceEnumConverter")]
public enum NTSvePivotRange
{
	Daily,
	Weekly,
	Monthly,
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private NTSvePivots[] cacheNTSvePivots;
		public NTSvePivots NTSvePivots(bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			return NTSvePivots(Input, showHistorical, pivotRangeType, priorDayHlc, userDefinedClose, userDefinedHigh, userDefinedLow, width);
		}

		public NTSvePivots NTSvePivots(ISeries<double> input, bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			if (cacheNTSvePivots != null)
				for (int idx = 0; idx < cacheNTSvePivots.Length; idx++)
					if (cacheNTSvePivots[idx] != null && cacheNTSvePivots[idx].ShowHistorical == showHistorical && cacheNTSvePivots[idx].PivotRangeType == pivotRangeType && cacheNTSvePivots[idx].PriorDayHlc == priorDayHlc && cacheNTSvePivots[idx].UserDefinedClose == userDefinedClose && cacheNTSvePivots[idx].UserDefinedHigh == userDefinedHigh && cacheNTSvePivots[idx].UserDefinedLow == userDefinedLow && cacheNTSvePivots[idx].Width == width && cacheNTSvePivots[idx].EqualsInput(input))
						return cacheNTSvePivots[idx];
			return CacheIndicator<NTSvePivots>(new NTSvePivots(){ ShowHistorical = showHistorical, PivotRangeType = pivotRangeType, PriorDayHlc = priorDayHlc, UserDefinedClose = userDefinedClose, UserDefinedHigh = userDefinedHigh, UserDefinedLow = userDefinedLow, Width = width }, input, ref cacheNTSvePivots);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.NTSvePivots NTSvePivots(bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			return indicator.NTSvePivots(Input, showHistorical, pivotRangeType, priorDayHlc, userDefinedClose, userDefinedHigh, userDefinedLow, width);
		}

		public Indicators.NTSvePivots NTSvePivots(ISeries<double> input , bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			return indicator.NTSvePivots(input, showHistorical, pivotRangeType, priorDayHlc, userDefinedClose, userDefinedHigh, userDefinedLow, width);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.NTSvePivots NTSvePivots(bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			return indicator.NTSvePivots(Input, showHistorical, pivotRangeType, priorDayHlc, userDefinedClose, userDefinedHigh, userDefinedLow, width);
		}

		public Indicators.NTSvePivots NTSvePivots(ISeries<double> input , bool showHistorical, NTSvePivotRange pivotRangeType, NTSveHLCCalculationMode priorDayHlc, double userDefinedClose, double userDefinedHigh, double userDefinedLow, int width)
		{
			return indicator.NTSvePivots(input, showHistorical, pivotRangeType, priorDayHlc, userDefinedClose, userDefinedHigh, userDefinedLow, width);
		}
	}
}

#endregion
