﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-9086a7bf96e34174929a21dfee4f59b0>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>-9;40</Location>
      <Size>826;994</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>148</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-479f29684c144c618aacccfc57dbdfe8>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-14T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2099-12-01T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>9182c97c4519443e8b6fd1ffb799c3ba</BarsSeriesId>
                <Id>fe1d72393d3244e9b4e52ff75ebdf0b7</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>3.9273309899788647</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1">
              <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-05-14T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-19T00:00:00</To>
                <Calculate>OnPriceChange</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Price line</Name>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Ask line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Bid line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Last line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>24</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10005</ZOrder>
                <ShowAskLine>false</ShowAskLine>
                <ShowBidLine>false</ShowBidLine>
                <ShowLastLine>true</ShowLastLine>
                <AskLineLength>100</AskLineLength>
                <BidLineLength>100</BidLineLength>
                <LastLineLength>100</LastLineLength>
                <AskStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </AskStroke>
                <BidStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </BidStroke>
                <LastStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </LastStroke>
              </PriceLine>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1">
              <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>5</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>false</DisplayInDataBox>
                <From>2025-05-14T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-19T00:00:00</To>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Bar timer</Name>
                <Plots />
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>27</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10006</ZOrder>
              </BarTimer>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
          </Indicators>
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>13.0911026</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>882</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Fixed</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21547.459364970731</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>21087.635368995347</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-479f29684c144c618aacccfc57dbdfe8>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>TDYA150355451300000019</Account>
        <ATM>NQ</ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-9086a7bf96e34174929a21dfee4f59b0>
    <Chart-530159dfe034489b905a4c0599b99e6b>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Normal</WindowState>
      <Location>821;43</Location>
      <Size>1100;988</Size>
      <ZOrder>1</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>148</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-c0633b80f5a7416a83337e0f16e79842>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-14T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2099-12-01T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ JUN25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>d2fde978b1d34060bbf7732caaea433f</BarsSeriesId>
                <Id>d2fde978b1d34060bbf7732caaea433f</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>2.4939348695153094</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                      <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                      <StrokeSerialize>DEFAULT</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies />
          <Indicators>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1">
              <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-05-14T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-19T00:00:00</To>
                <Calculate>OnPriceChange</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Price line</Name>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Ask line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Bid line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Last line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>32</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10005</ZOrder>
                <ShowAskLine>false</ShowAskLine>
                <ShowBidLine>false</ShowBidLine>
                <ShowLastLine>true</ShowLastLine>
                <AskLineLength>100</AskLineLength>
                <BidLineLength>100</BidLineLength>
                <LastLineLength>100</LastLineLength>
                <AskStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </AskStroke>
                <BidStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </BidStroke>
                <LastStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </LastStroke>
              </PriceLine>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1">
              <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <DisplayInDataBox>false</DisplayInDataBox>
                <From>2025-05-14T00:00:00</From>
                <Panel>-1</Panel>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-05-19T00:00:00</To>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <IsAutoScale>true</IsAutoScale>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Bar timer</Name>
                <Plots />
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>35</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10006</ZOrder>
              </BarTimer>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
          </Indicators>
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>8.313118</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>876</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Fixed</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>21469.098314331215</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>21314.187999475649</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <DrawingTools>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30004</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>025187650f5149df9296180e1d32f158</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>5075</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-19T09:36:00</Time>
                <Price>21194.522733333331</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>5075</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T09:36:00</Time>
                <Price>21194.522733333331</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30005</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>7d1ea2222c8240a78162e79ef445c715</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 2</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>4888</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-19T06:29:00</Time>
                <Price>21111.968125498774</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>4888</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T06:29:00</Time>
                <Price>21111.968125498774</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <FibonacciRetracements xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30006</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>7252348cc2ef4585bd57308e3b66ec9a</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Fibonacci retracements</Tag>
              <ZOrderType>Normal</ZOrderType>
              <PriceLevels>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>0</Value>
                  <Name>0.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>30</Value>
                  <Name>30.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>50</Value>
                  <Name>50.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>70</Value>
                  <Name>70.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>100</Value>
                  <Name>100.00%</Name>
                </PriceLevel>
              </PriceLevels>
              <AnchorLineStroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush Color="#FFA9A9A9" Opacity="0.5" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" /&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>50</Opacity>
                <Width>1</Width>
              </AnchorLineStroke>
              <StartAnchor>
                <SlotIndex>5044</SlotIndex>
                <DisplayName>Start</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T09:05:00</Time>
                <Price>21194.43243226672</Price>
              </StartAnchor>
              <EndAnchor>
                <SlotIndex>5108</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T10:09:00</Time>
                <Price>21112.03910451581</Price>
              </EndAnchor>
              <PriceLevelOpacity>5</PriceLevelOpacity>
              <IsExtendedLinesRight>false</IsExtendedLinesRight>
              <IsExtendedLinesLeft>false</IsExtendedLinesLeft>
              <TextLocation>InsideRight</TextLocation>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.FibonacciRetracements</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </FibonacciRetracements>
            <HorizontalLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30007</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>f3904269e4a540b4aa1f6967467d8dcb</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Horizontal Line 3</Tag>
              <ZOrderType>Normal</ZOrderType>
              <EndAnchor>
                <SlotIndex>5067</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>false</IsBrowsable>
                <Time>2025-05-19T09:28:00</Time>
                <Price>21333.123265329967</Price>
              </EndAnchor>
              <StartAnchor>
                <SlotIndex>5067</SlotIndex>
                <DisplayName>Anchor</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T09:28:00</Time>
                <Price>21333.123265329967</Price>
              </StartAnchor>
              <Stroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF6495ED&lt;/SolidColorBrush&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>100</Opacity>
                <Width>2</Width>
              </Stroke>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.HorizontalLine</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </HorizontalLine>
            <FibonacciRetracements xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <IsVisible>true</IsVisible>
              <IsAutoScale>false</IsAutoScale>
              <MaxValue>-1.7976931348623157E+308</MaxValue>
              <MinValue>1.7976931348623157E+308</MinValue>
              <ZOrder>30008</ZOrder>
              <AttachedTo>
                <ChartObjectSerialize>ChartBars</ChartObjectSerialize>
                <InstrumentSerialize>NQ JUN25</InstrumentSerialize>
              </AttachedTo>
              <ScaleJustification>Right</ScaleJustification>
              <DrawingState>Normal</DrawingState>
              <DisplayOnChartsMenus>true</DisplayOnChartsMenus>
              <GlobalLastSeenSerialize>0</GlobalLastSeenSerialize>
              <GlobalWorkspace>Untitled</GlobalWorkspace>
              <Id>234fbce6aa4f4198b11b9ad775f93b1a</Id>
              <IsLocked>false</IsLocked>
              <PanelIndex>0</PanelIndex>
              <Tag>Fibonacci retracements 2</Tag>
              <ZOrderType>Normal</ZOrderType>
              <PriceLevels>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>0</Value>
                  <Name>0.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>30</Value>
                  <Name>30.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>50</Value>
                  <Name>50.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>70</Value>
                  <Name>70.00%</Name>
                </PriceLevel>
                <PriceLevel>
                  <IsVisible>true</IsVisible>
                  <Stroke>
                    <IsOpacityVisible>true</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFA9A9A9&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                  </Stroke>
                  <Value>100</Value>
                  <Name>100.00%</Name>
                </PriceLevel>
              </PriceLevels>
              <AnchorLineStroke>
                <IsOpacityVisible>true</IsOpacityVisible>
                <BrushSerialize>&lt;SolidColorBrush Color="#FFA9A9A9" Opacity="0.5" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" /&gt;</BrushSerialize>
                <DashStyleHelper>Solid</DashStyleHelper>
                <Opacity>50</Opacity>
                <Width>1</Width>
              </AnchorLineStroke>
              <StartAnchor>
                <SlotIndex>5101</SlotIndex>
                <DisplayName>Start</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T10:02:00</Time>
                <Price>21462.389574723726</Price>
              </StartAnchor>
              <EndAnchor>
                <SlotIndex>5202</SlotIndex>
                <DisplayName>End</DisplayName>
                <IsBrowsable>true</IsBrowsable>
                <Time>2025-05-19T11:43:00</Time>
                <Price>21410.905795418857</Price>
              </EndAnchor>
              <PriceLevelOpacity>5</PriceLevelOpacity>
              <IsExtendedLinesRight>false</IsExtendedLinesRight>
              <IsExtendedLinesLeft>false</IsExtendedLinesLeft>
              <TextLocation>InsideRight</TextLocation>
              <FullTypeName>NinjaTrader.NinjaScript.DrawingTools.FibonacciRetracements</FullTypeName>
              <IsGlobalDrawingObjectDefault>false</IsGlobalDrawingObjectDefault>
              <IsOwnerVisible>true</IsOwnerVisible>
            </FibonacciRetracements>
          </DrawingTools>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-c0633b80f5a7416a83337e0f16e79842>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>TDYA150355451300000019</Account>
        <ATM>NQ</ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-530159dfe034489b905a4c0599b99e6b>
  </NTWindows>
</NinjaTrader>