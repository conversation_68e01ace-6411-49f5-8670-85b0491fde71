#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;

#endregion



#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		
		private aiDuplicateAccountActions[] cacheaiDuplicateAccountActions;

		
		public aiDuplicateAccountActions aiDuplicateAccountActions(double protectSec)
		{
			return aiDuplicateAccountActions(Input, protectSec);
		}


		
		public aiDuplicateAccountActions aiDuplicateAccountActions(ISeries<double> input, double protectSec)
		{
			if (cacheaiDuplicateAccountActions != null)
				for (int idx = 0; idx < cacheaiDuplicateAccountActions.Length; idx++)
					if (cacheaiDuplicateAccountActions[idx].ProtectSec == protectSec && cacheaiDuplicateAccountActions[idx].EqualsInput(input))
						return cacheaiDuplicateAccountActions[idx];
			return CacheIndicator<aiDuplicateAccountActions>(new aiDuplicateAccountActions(){ ProtectSec = protectSec }, input, ref cacheaiDuplicateAccountActions);
		}

	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		
		public Indicators.aiDuplicateAccountActions aiDuplicateAccountActions(double protectSec)
		{
			return indicator.aiDuplicateAccountActions(Input, protectSec);
		}


		
		public Indicators.aiDuplicateAccountActions aiDuplicateAccountActions(ISeries<double> input , double protectSec)
		{
			return indicator.aiDuplicateAccountActions(input, protectSec);
		}
	
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		
		public Indicators.aiDuplicateAccountActions aiDuplicateAccountActions(double protectSec)
		{
			return indicator.aiDuplicateAccountActions(Input, protectSec);
		}


		
		public Indicators.aiDuplicateAccountActions aiDuplicateAccountActions(ISeries<double> input , double protectSec)
		{
			return indicator.aiDuplicateAccountActions(input, protectSec);
		}

	}
}

#endregion
