﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <BarsPeriodType_HeikenAshi>
    <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <BarsPeriod>
        <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriod>
      <RangeType>Days</RangeType>
      <BarsBack>50</BarsBack>
      <DaysBack>5</DaysBack>
      <From>2025-05-25T00:00:00</From>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <To>2025-05-30T00:00:00</To>
      <TradingHoursSerializable></TradingHoursSerializable>
      <AutoScale>true</AutoScale>
      <CenterPriceOnScale>false</CenterPriceOnScale>
      <DisplayInDataBox>true</DisplayInDataBox>
      <Label></Label>
      <MaxSerialized>0</MaxSerialized>
      <MinSerialized>0</MinSerialized>
      <PriceMarker>
        <BackgroundSerialize>DEFAULT</BackgroundSerialize>
        <IsVisible>true</IsVisible>
      </PriceMarker>
      <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
      <ScaleJustification>Right</ScaleJustification>
      <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
      <TradingHoursBreakPenSerialize>&lt;Pen Thickness="1" Brush="#FF2D2D2F" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
      <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
      <PlotExecutions>TextAndMarker</PlotExecutions>
      <MarkerSize>5</MarkerSize>
      <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
      <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
      <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
      <BarsSeriesId>c6482ccddd2043c18fa4bcf3488637af</BarsSeriesId>
      <Id>7dcd1b1f1a474f4192d86bce4412f4fc</Id>
      <IsLinked>true</IsLinked>
      <IsPrimarySeries>true</IsPrimarySeries>
      <ZOrder>-2147483648</ZOrder>
    </BarsProperties>
    <ChartStyles>
      <CandleStyle>
        <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
          <DownBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFF8F8FF&lt;/SolidColorBrush&gt;</DownBrushSerialize>
          <UpBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF00FF00&lt;/SolidColorBrush&gt;</UpBrushSerialize>
          <StrokeSerialize>&lt;Pen Thickness="1" Brush="#FF000000" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StrokeSerialize>
          <Stroke2Serialize>DEFAULT</Stroke2Serialize>
        </CandleStyle>
      </CandleStyle>
    </ChartStyles>
  </BarsPeriodType_HeikenAshi>
</NinjaTrader>