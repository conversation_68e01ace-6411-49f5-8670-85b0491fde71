﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <TradingHours xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <HolidaysSerializable>
      <Holiday>
        <Date>2015-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2015-04-03T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2015-04-06T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2015-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2015-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2015-12-31T00:00:00</Date>
        <Description>New Year's Eve</Description>
      </Holiday>
      <Holiday>
        <Date>2016-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2016-03-25T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2016-03-28T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2016-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2017-04-14T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2017-04-17T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2017-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2017-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2017-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2018-03-30T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2018-04-02T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2018-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2018-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2018-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2019-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2019-04-19T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2019-04-22T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2019-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2019-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2019-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2020-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2020-04-10T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2020-04-13T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2020-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2020-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2020-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2021-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2021-04-02T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2021-04-05T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2021-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2021-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2021-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2022-01-01T00:00:00</Date>
        <Description>New Year's Day</Description>
      </Holiday>
      <Holiday>
        <Date>2022-04-15T00:00:00</Date>
        <Description>Good Friday</Description>
      </Holiday>
      <Holiday>
        <Date>2022-04-18T00:00:00</Date>
        <Description>Easter Monday</Description>
      </Holiday>
      <Holiday>
        <Date>2022-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
      <Holiday>
        <Date>2022-12-25T00:00:00</Date>
        <Description>Christmas Day</Description>
      </Holiday>
      <Holiday>
        <Date>2022-12-26T00:00:00</Date>
        <Description>Boxing Day</Description>
      </Holiday>
      <Holiday>
        <Date>2023-05-01T00:00:00</Date>
        <Description>Labour Day</Description>
      </Holiday>
    </HolidaysSerializable>
    <PartialHolidaysSerializable />
    <Version>4491</Version>
    <Name>Euronext GMT Interest Rate Futures1</Name>
    <Sessions>
      <Session>
        <BeginDay>Monday</BeginDay>
        <BeginTime>100</BeginTime>
        <EndDay>Monday</EndDay>
        <EndTime>2100</EndTime>
        <TradingDay>Monday</TradingDay>
      </Session>
      <Session>
        <BeginDay>Tuesday</BeginDay>
        <BeginTime>100</BeginTime>
        <EndDay>Tuesday</EndDay>
        <EndTime>2100</EndTime>
        <TradingDay>Tuesday</TradingDay>
      </Session>
      <Session>
        <BeginDay>Wednesday</BeginDay>
        <BeginTime>100</BeginTime>
        <EndDay>Wednesday</EndDay>
        <EndTime>2100</EndTime>
        <TradingDay>Wednesday</TradingDay>
      </Session>
      <Session>
        <BeginDay>Thursday</BeginDay>
        <BeginTime>100</BeginTime>
        <EndDay>Thursday</EndDay>
        <EndTime>2100</EndTime>
        <TradingDay>Thursday</TradingDay>
      </Session>
      <Session>
        <BeginDay>Friday</BeginDay>
        <BeginTime>100</BeginTime>
        <EndDay>Friday</EndDay>
        <EndTime>2100</EndTime>
        <TradingDay>Friday</TradingDay>
      </Session>
    </Sessions>
    <TimeZone>GMT Standard Time</TimeZone>
  </TradingHours>
</NinjaTrader>