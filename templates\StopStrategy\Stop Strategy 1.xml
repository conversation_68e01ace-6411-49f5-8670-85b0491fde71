﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <StopStrategy xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <AutoBreakEvenPlus>2</AutoBreakEvenPlus>
    <AutoBreakEvenProfitTrigger>20</AutoBreakEvenProfitTrigger>
    <AutoTrailSteps>
      <AutoTrailStep>
        <Frequency>1</Frequency>
        <ProfitTrigger>25</ProfitTrigger>
        <StopLoss>15</StopLoss>
      </AutoTrailStep>
      <AutoTrailStep>
        <Frequency>1</Frequency>
        <ProfitTrigger>40</ProfitTrigger>
        <StopLoss>25</StopLoss>
      </AutoTrailStep>
      <AutoTrailStep>
        <Frequency>1</Frequency>
        <ProfitTrigger>60</ProfitTrigger>
        <StopLoss>40</StopLoss>
      </AutoTrailStep>
    </AutoTrailSteps>
    <IsSimStopEnabled>false</IsSimStopEnabled>
    <VolumeTrigger>0</VolumeTrigger>
    <Template>Stop Strategy 1</Template>
  </StopStrategy>
</NinjaTrader>