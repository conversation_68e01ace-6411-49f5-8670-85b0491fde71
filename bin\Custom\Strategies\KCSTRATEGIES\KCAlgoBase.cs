#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.WebSockets;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.Core;
using BlueZ = NinjaTrader.NinjaScript.Indicators.BlueZ; // Alias for better readability
using RegressionChannel = NinjaTrader.NinjaScript.Indicators.RegressionChannel;
#endregion

namespace NinjaTrader.NinjaScript.Strategies.KCStrategies
{
    abstract public class KCAlgoBase : Strategy, ICustomTypeDescriptor
    {
        #region Variables
		
        private DateTime lastEntryTime;
        private readonly TimeSpan tradeDelay = TimeSpan.FromSeconds(5);

		private bool marketIsChoppy;
		private bool autoDisabledByChop; // Tracks if Auto was turned off by the system due to chop

        // Indicator Variables
        private BlueZ.BlueZHMAHooks hullMAHooks;
        private bool hmaUp;
        private bool hmaDown;

        private BuySellPressure BuySellPressure1;
        private bool buyPressureUp;
        private bool sellPressureUp;
		private Series<double> buyPressure;
		private Series<double> sellPressure;		

        private RegressionChannel RegressionChannel1, RegressionChannel2;
        private RegressionChannelHighLow RegressionChannelHighLow1;
        private bool regChanUp;
        private bool regChanDown;

        private VMA VMA1;
        private bool volMaUp;
        private bool volMaDown;

        private NTSvePivots pivots;
        private double pivotPoint, s1, s2, s3, r1, r2, r3, s1m, s2m, s3m, r1m, r2m, r3m;

		private Momentum Momentum1;
		private double momentum;		
        private bool momoUp;
        private bool momoDown;
		
        private ADX ADX1;
		private double currentAdx;
        private bool adxUp;

        private ATR ATR1;
        private bool atrUp;

        private bool aboveEMAHigh;
        private bool belowEMALow;

        protected bool uptrend;
        protected bool downtrend;

        private bool priceUp;
        private bool priceDown;

        public bool isLong;
        public bool isShort;
        public bool isFlat;
        public bool exitLong;
        public bool exitShort;
        public bool longSignal;
        public bool shortSignal;

        private double lastStopLevel = 0;  // Tracks the last stop level
        private bool stopUpdated = false;  // To ensure stop is moved only when favorable

        // Progress tracking
        private double actualPnL;
        private int trailStop;
        private bool _beRealized;
        private bool enableFixedStopLoss = false;
        private bool threeStepTrail;
        private bool trailingDrawdownReached = false;
        private int ProgressState;

        private double entryPrice;
        private double currentPrice;
        private bool additionalContractExists;

        private bool isBuySellMarketOrder;
        private bool tradesPerDirection;
        private int counterLong;
        private int counterShort;
        private bool QuickLong;
        private bool QuickShort;
        private bool quickLongBtnActive;
        private bool quickShortBtnActive;

        //		private bool isEnableTime1;
        private bool isEnableTime2;
        private bool isEnableTime3;
        private bool isEnableTime4;
        private bool isEnableTime5;
        private bool isEnableTime6;

        private bool isManualEnabled;
        private bool isAutoEnabled;
        private bool isLongEnabled;
        private bool isShortEnabled;

        //		Chart Trader Buttons
        private System.Windows.Controls.RowDefinition addedRow;
        private Gui.Chart.ChartTab chartTab;
        private Gui.Chart.Chart chartWindow;
        private System.Windows.Controls.Grid chartTraderGrid, chartTraderButtonsGrid, lowerButtonsGrid;
		
        //		New Toggle Buttons
        private System.Windows.Controls.Button manualBtn, autoBtn, longBtn, shortBtn, quickLongBtn, quickShortBtn;
        private System.Windows.Controls.Button add1Btn, close1Btn, BEBtn, TSBtn, moveTSBtn, moveToBEBtn;
        private System.Windows.Controls.Button moveTS50PctBtn, closeBtn, panicBtn, donatePayPalBtn;
        private bool panelActive;
        private System.Windows.Controls.TabItem tabItem;
        private System.Windows.Controls.Grid myGrid;

        // KillAll
        private Account chartTraderAccount;
        private AccountSelector accountSelector;
        private Order myEntryOrder = null;
        private Order myStopOrder = null;
        private Order myTargetOrder = null;
        private double myStopPrice = 0;
        private double myLimitPrice = 0;
		private bool activeOrder;

        //		Status Panel
        private string textLine0;
        private string textLine1;
        private string textLine2;
        private string textLine3;
        private string textLine4;
        private string textLine5;
        private string textLine6;
        private string textLine7;

        //		PnL
        private double totalPnL;
        private double cumPnL;
        private double dailyPnL;
        private bool canTradeOK = true;

        private bool syncPnl;
        private double historicalTimeTrades;//Sync  PnL
        private double dif;//To Calculate PNL sync
        private double cumProfit;//For real time pnl and pnl synchronization

        private bool restartPnL;

        private bool beSetAuto;
        private bool showctrlBESetAuto;
        private bool atrTrailSetAuto;
        private bool showAtrTrailSetAuto;
        private bool enableTrail;
        private bool showTrailOptions;
        public bool tickTrail;

        private TrailStopTypeKC trailStopType;
        private bool showTickTrailOption;
        private bool showAtrTrailOptions;
        private bool showThreeStepTrailOptions;

        private bool enableDynamicProfit = false;
        private bool enableFixedProfit = true;
        private bool showctrlEnableDynamicProfit = false;
        private bool showctrlEnableFixedProfit = true;

        // Error Handling
        private readonly object orderLock = new object(); // Critical for thread safety
        private Dictionary<string, Order> activeOrders = new Dictionary<string, Order>(); // Track active orders with labels.
        private DateTime lastOrderActionTime = DateTime.MinValue;
        private readonly TimeSpan minOrderActionInterval = TimeSpan.FromSeconds(1); // Prevent rapid order submissions.
        private bool orderErrorOccurred = false; // Flag to halt trading after an order error.

        // Rogue Order Detection
        private DateTime lastAccountReconciliationTime = DateTime.MinValue;
        private readonly TimeSpan accountReconciliationInterval = TimeSpan.FromMinutes(5); // Check for rogue orders every 5 minutes

        // Trailing Drawdown variables
        private double maxProfit;  // Stores the highest profit achieved

        #endregion

        #region Order Label Constants (Highly Recommended)

        // Define your order labels as constants.  This prevents typos and ensures consistency.
        private const string LE1 = "LE1";
		private const string LE2 = "LE2";
		private const string LE3 = "LE3";
		private const string LE4 = "LE4";
        private const string SE1 = "SE1";
        private const string SE2 = "SE2";
        private const string SE3 = "SE3";
        private const string SE4 = "SE4";
        private const string QLE = "QLE";
        private const string QSE = "QSE";
		private const string Add1LE = "Add1LE";
		private const string Add1SE = "Add1SE";
        private const string ManualClose1 = "Manual Close 1"; // Label for the manual close action
        // Add constants for other order labels as needed (e.g., LE2, SE2, "TrailingStop")

        #endregion

		#region Constants

		private const string ManualButton = "ManualBtn";
		private const string AutoButton = "AutoBtn";
		private const string LongButton = "LongBtn";
		private const string ShortButton = "ShortBtn";
		private const string QuickLongButton = "QuickLongBtn";
		private const string QuickShortButton = "QuickShortBtn";
		private const string Add1Button = "Add1Btn";
		private const string Close1Button = "Close1Btn";
		private const string BEButton = "BEBtn";
		private const string TSButton = "TSBtn";
		private const string MoveTSButton = "MoveTSBtn";
		private const string MoveTS50PctButton = "MoveTS50PctBtn";
		private const string MoveToBeButton = "MoveToBeBtn";
		private const string CloseButton = "CloseBtn";
		private const string PanicButton = "PanicBtn";		
		private const string DonatePayPalButton = "BuyCoffeeBtn";

		#endregion
		
        #region TradeToDiscord

        private ClientWebSocket clientWebSocket;
        private List<dynamic> signalHistory = new List<dynamic>();
        private DateTime lastDiscordMessageTime = DateTime.MinValue;
        private readonly TimeSpan discordRateLimitInterval = TimeSpan.FromSeconds(30); // Adjust the interval as needed

        private string lastSignalType = "N/A";
        private double lastEntryPrice = 0.0;
        private double lastStopLoss = 0.0;
        private double lastProfitTarget = 0.0;
        private DateTime lastSignalTime = DateTime.MinValue;

        #endregion

        public override string DisplayName { get { return Name; } }

        #region OnStateChange
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
				Description									= @"Base Strategy with OEB v.5.0.2 TradeSaber(Dre). and ArchReactor for KC (Khanh Nguyen)";
				Name										= "KCAlgoBase";
				BaseAlgoVersion								= "KCAlgoBase v5.2";
				Author										= "indiVGA, Khanh Nguyen, Oshi, based on ArchReactor";
				Version										= "Version 5.2 Apr. 2025";
				Credits										= "";
				StrategyName 								= "";
				ChartType									= "Orenko 34-40-40";	
				paypal 										= "https://www.paypal.com/signin"; 
		

                EntriesPerDirection = 10;					// This value should limit the number of contracts that the strategy can open per direction.
															// It has nothing to do with the parameter defining the entries per direction that we define in the strategy and are controlled by code.
                Calculate									= Calculate.OnEachTick;
				EntryHandling 								= EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy 				= true;
                ExitOnSessionCloseSeconds 					= 30;
                IsFillLimitOnTouch 							= false;
                MaximumBarsLookBack 						= MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution 						= OrderFillResolution.High;
                Slippage 									= 0;
                StartBehavior 								= StartBehavior.WaitUntilFlat;
                TimeInForce 								= TimeInForce.Gtc;
                TraceOrders 								= false;
                RealtimeErrorHandling 						= RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling 							= StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade 						= 20;
				RealtimeErrorHandling 						= RealtimeErrorHandling.StopCancelClose; // important to manage errors on rogue orders
                IsInstantiatedOnEachOptimizationIteration 	= false;
				
                // Default Parameters
				isAutoEnabled 					= true;
				isManualEnabled					= false;
				isLongEnabled					= true;
				isShortEnabled					= true;
				canTradeOK 						= true;
				
				OrderType						= OrderType.Limit;
				
		        // Choppiness Defaults
		        SlopeLookBack            		= 4;
		        FlatSlopeFactor       			= 0.125; 
		        ChopAdxThreshold        		= 20;
				EnableChoppinessDetection 		= true;
		        marketIsChoppy          		= false;
		        autoDisabledByChop      		= false;
				enableBackgroundSignal			= true;
				
				enableBuySellPressure 			= true;
				showBuySellPressure 			= false;
				
				HmaPeriod 						= 16;
				enableHmaHooks 					= true;
				showHmaHooks 					= true;
	
				RegChanPeriod 					= 40;
				RegChanWidth 					= 4;
				RegChanWidth2 					= 3;
				enableRegChan1 					= true;
				enableRegChan2 					= true;
				showRegChan1 					= true;
				showRegChan2 					= true;
				showRegChanHiLo 				= true;

				enableVMA						= true;
				showVMA							= true;
				
				MomoUp							= 1;
				MomoDown						= -1;
				enableMomo						= true;
				showMomo						= false;
				
				adxPeriod						= 7;
				adxThreshold					= 25;
				adxThreshold2					= 50;
				adxExitThreshold				= 45;
				enableADX						= true;
				showAdx							= false;
				
				emaLength						= 110;
				enableEMAFilter 				= false;
				showEMA							= false;
				
				AtrPeriod						= 14;
				atrThreshold					= 1.5;
				enableVolatility				= true;
				
				showPivots						= false;
				
				enableExit						= false;
				
				LimitOffset						= 8;
				TickMove						= 4;								
								
				EnableFixedProfit				= true;
				EnableDynamicProfit				= false;
				
				Contracts						= 1;
				Contracts2 						= 1;
				Contracts3 					    = 1;
				Contracts4						= 1;
				
				InitialStop						= 97;
				trailStop						= InitialStop;
				
				ProfitTarget					= 40;
				ProfitTarget2					= 44;
				ProfitTarget3					= 48;
				ProfitTarget4					= 52;
				
				EnableProfitTarget2				= true;
				EnableProfitTarget3				= true;
				EnableProfitTarget4				= true;
				
			//	Set BE Stop
				BESetAuto						= true;
				beSetAuto						= true;
				showctrlBESetAuto				= true;
				BE_Trigger						= 32;
				BE_Offset						= 0;
				_beRealized						= false;

			//	Trailing Stops
				enableTrail 					= true;
				tickTrail						= false;
				showTrailOptions 				= true;	
				trailStopType 					= TrailStopTypeKC.TickTrail;
				
			//	ATR Trail
				atrTrailSetAuto					= false;
				showAtrTrailSetAuto				= false;
				showAtrTrailOptions 			= false;
				enableAtrProfitTarget			= false;
				atrMultiplier					= 1.5;
				RiskRewardRatio					= 0.75;
//				Trail_frequency					= 4;
				
			//	3 Step Trail	
				showThreeStepTrailOptions 		= false;
				threeStepTrail					= false;
				step1ProfitTrigger 				= 1;	// Set your step 1 profit trigger
                step1StopLoss 					= 97;	// Set your step 1 stop loss
                step2ProfitTrigger 				= 44;	// Set your step 2 profit trigger
                step2StopLoss 					= 40;	// Set your step 2 stop loss
				step3ProfitTrigger 				= 52;	// Set your step 3 profit trigger
				step3StopLoss 					= 16;	// Set your step 3 stop loss
//				step1Frequency					= 4;
//				step2Frequency					= 4;
//				step3Frequency					= 2;
				ProgressState 					= 0;
				
				tradesPerDirection				= false;
				longPerDirection				= 5;
				shortPerDirection				= 5;	
				iBarsSinceExit					= 0;				
				SecsSinceEntry					= 0;
				
				QuickLong						= false;
				QuickShort						= false;
				
				counterLong						= 0;
				counterShort					= 0;
				
				Start							= DateTime.Parse("06:30", System.Globalization.CultureInfo.InvariantCulture);
				End								= DateTime.Parse("09:30", System.Globalization.CultureInfo.InvariantCulture);
				Start2							= DateTime.Parse("11:30", System.Globalization.CultureInfo.InvariantCulture);
				End2							= DateTime.Parse("13:00", System.Globalization.CultureInfo.InvariantCulture);
				Start3							= DateTime.Parse("15:00", System.Globalization.CultureInfo.InvariantCulture);
				End3							= DateTime.Parse("18:00", System.Globalization.CultureInfo.InvariantCulture);
				Start4							= DateTime.Parse("00:00", System.Globalization.CultureInfo.InvariantCulture);
				End4							= DateTime.Parse("03:30", System.Globalization.CultureInfo.InvariantCulture);
				Start5							= DateTime.Parse("06:30", System.Globalization.CultureInfo.InvariantCulture);
				End5							= DateTime.Parse("13:00", System.Globalization.CultureInfo.InvariantCulture);
				Start6							= DateTime.Parse("00:00", System.Globalization.CultureInfo.InvariantCulture);
				End6							= DateTime.Parse("23:59", System.Globalization.CultureInfo.InvariantCulture);
				
				// Panel Status
				showDailyPnl					= true;
				PositionDailyPNL				= TextPosition.BottomLeft;	
				colorDailyProfitLoss			= Brushes.Cyan; // Default value
				
				showPnl							= false;
				PositionPnl						= TextPosition.TopLeft;
				colorPnl 						= Brushes.Yellow; // Default value
			
				// PnL Daily Limits
				dailyLossProfit					= true;
				DailyProfitLimit				= 100000;
				DailyLossLimit					= 1000;				
				TrailingDrawdown				= 1000;
				StartTrailingDD					= 3000;
				maxProfit 						= double.MinValue;	// double.MinValue guarantees that any totalPnL will trigger it to set the variable
				enableTrailingDD 				= true;
				
				ShowHistorical					= true;
				
				useWebHook						= false;
				DiscordWebhooks					= "https://discord.com/channels/963493404988289124/1343311936736989194";
				
            }
            else if (State == State.Configure)
            {
				// Ensure RealtimeErrorHandling is set
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
				
				clientWebSocket = new ClientWebSocket();
				
				buyPressure = new Series<double>(this);
				sellPressure = new Series<double>(this);
            }
            else if (State == State.DataLoaded)
            {	
				hullMAHooks = BlueZHMAHooks(Close, HmaPeriod, 0, false, false, true, Brushes.Lime, Brushes.Red);
				hullMAHooks.Plots[0].Brush = Brushes.White;
				hullMAHooks.Plots[0].Width = 2;
				if (showHmaHooks) AddChartIndicator(hullMAHooks);
	
				RegressionChannel1 = RegressionChannel(Close, RegChanPeriod, RegChanWidth);
				if (showRegChan1) AddChartIndicator(RegressionChannel1);
	
				RegressionChannel2 = RegressionChannel(Close, RegChanPeriod, RegChanWidth2);
				if (showRegChan2) AddChartIndicator(RegressionChannel2);
	
				RegressionChannelHighLow1 = RegressionChannelHighLow(Close, RegChanPeriod, RegChanWidth);
				if (showRegChanHiLo) AddChartIndicator(RegressionChannelHighLow1);
	
				BuySellPressure1				= BuySellPressure(Close);
				BuySellPressure1.Plots[0].Width = 2;
				BuySellPressure1.Plots[0].Brush = Brushes.Lime;
				BuySellPressure1.Plots[1].Width = 2;
				BuySellPressure1.Plots[1].Brush = Brushes.Red;
				if (showBuySellPressure) AddChartIndicator(BuySellPressure1);
			
				VMA1				= VMA(Close, 9, 9);
				VMA1.Plots[0].Brush = Brushes.SkyBlue;
				VMA1.Plots[0].Width = 3;
				if (showVMA) AddChartIndicator(VMA1);			
				
				ATR1 	= ATR(AtrPeriod);
				        
				Momentum1			= Momentum(Close, 14);	
				Momentum1.Plots[0].Brush = Brushes.Yellow;
				Momentum1.Plots[0].Width = 2;
				if (showMomo) AddChartIndicator(Momentum1);
				
				ADX1				= ADX(Close, adxPeriod);
				ADX1.Plots[0].Brush = Brushes.Yellow;
				ADX1.Plots[0].Width = 2;
				if (showAdx) AddChartIndicator(ADX1);
				
				pivots = NTSvePivots(Close, false, NTSvePivotRange.Daily, NTSveHLCCalculationMode.CalcFromIntradayData, 0, 0, 0, 250);
				pivots.Plots[0].Width = 4;
				if (showPivots) AddChartIndicator(pivots);
				
				if(showEMA) 
				{
					AddChartIndicator(EMA(High, emaLength));
					AddChartIndicator(EMA(Low, emaLength));
				}
					
				if (additionalContractExists)
			    {
			        string quickProfitTargetLabel = isLong ? QLE : QSE;  // QLE = Quick Long Entry, QSE = Quick Short Entry
			        SetProfitTarget(quickProfitTargetLabel, CalculationMode.Ticks, ProfitTarget);
			    } 				
				
				// Initialize maxProfit with the totalPnL (if it's the first time, it's 0)
				maxProfit = totalPnL;  // Ensure maxProfit starts at the current PnL
            }
			else if (State == State.Historical)
			{
				// Chart Trader Buttons Load	
				Dispatcher.InvokeAsync((() => {	CreateWPFControls();	}));				
			}
			else if (State == State.Terminated)
			{
				// Chart Trader Buttons dispose
				ChartControl?.Dispatcher.InvokeAsync(() =>	{	DisposeWPFControls();	});
				
				clientWebSocket?.Dispose();	
				
				// Log any remaining active orders
				lock (orderLock)
				{
					if (activeOrders.Count > 0)
					{
						Print (string.Format("{0}: Strategy terminated with active orders. Investigate:", Time[0]));
						foreach (var kvp in activeOrders)
						{
							Print (string.Format("{0}: Order Label: {1}, Order ID: {2}", Time[0], kvp.Key, kvp.Value.OrderId));
							// Consider attempting to cancel the order.  Do this ONLY if you have
							// carefully considered the implications (e.g., potential for slippage)
							CancelOrder(kvp.Value); // IMPORTANT: Cancel rogue orders before terminating.
						}
					}
				}				
			}
        }
		#endregion	
		
        #region OnBarUpdate
		protected override void OnBarUpdate()
        {
			if(Time[0] - lastEntryTime < tradeDelay) return;

			canTradeOK = true; // Reset canTradeOK at the beginning of each bar.
			// Ensure enough bars have passed overall for the *longest period* required by indicators in this block.
			
            if (BarsInProgress != 0 || CurrentBars[0] < 5 || orderErrorOccurred)
				return;
			
			//Track the Highest Profit Achieved
			if (totalPnL > maxProfit)
			{
				maxProfit = totalPnL;
			}
			
			// *** Account Reconciliation ***
			if (DateTime.Now - lastAccountReconciliationTime > accountReconciliationInterval)
			{
				ReconcileAccountOrders();
				lastAccountReconciliationTime = DateTime.Now;
			}

			if (!ShowHistorical && State != State.Realtime) return;	
			
			// Get pivot points and support/resistance levels
            pivotPoint = pivots.Pp[0];
            s1 = pivots.S1[0];
            s2 = pivots.S2[0];
            s3 = pivots.S3[0];
            r1 = pivots.R1[0];
            r2 = pivots.R2[0];
            r3 = pivots.R3[0];
            s1m = pivots.S1M[0];
            s2m = pivots.S2M[0];
			s3m = pivots.S3M[0];
            r1m = pivots.R1M[0];
            r2m = pivots.R2M[0];
			r3m = pivots.R3M[0];
			
			atrUp = enableVolatility ? ATR1[0] > atrThreshold : true;
			
			adxUp = !enableADX || ADX1[0] > adxThreshold && ADX1[0] < adxThreshold2;
			
			regChanUp = RegressionChannel1.Middle[0] > RegressionChannel1.Middle[1];
			regChanDown = RegressionChannel1.Middle[0] < RegressionChannel1.Middle[1];

			buyPressureUp = !enableBuySellPressure || (BuySellPressure1.BuyPressure[0] > BuySellPressure1.SellPressure[0]);
			sellPressureUp = !enableBuySellPressure || (BuySellPressure1.SellPressure[0] > BuySellPressure1.BuyPressure[0]);
			
			buyPressure[0] = BuySellPressure1.BuyPressure[0];
			sellPressure[0] = BuySellPressure1.SellPressure[0];
			
			hmaUp = (hullMAHooks[0] > hullMAHooks[1]);
			hmaDown = (hullMAHooks[0] < hullMAHooks[1]);
			
			volMaUp = !enableVMA || VMA1[0] > VMA1[1];
			volMaDown = !enableVMA || VMA1[0] < VMA1[1];
			
			momentum = Momentum1[0];
			momoUp = !enableMomo || (Momentum1[0] > MomoUp && Momentum1[0] > Momentum1[1]);
			momoDown = !enableMomo || (Momentum1[0] < MomoDown && Momentum1[0] < Momentum1[1]);
			
			aboveEMAHigh = !enableEMAFilter || Open[1] > EMA(High, emaLength)[1];
			belowEMALow = !enableEMAFilter || Open[1] < EMA(Low, emaLength)[1];
			
			currentAdx = ADX1[0];

			if (EnableChoppinessDetection)
			{
				// --- Regression Channel Slope Choppiness ---
				marketIsChoppy = false; // Default
				
				// Check if enough bars exist for slope calculation AND for ADX
				if (CurrentBar >= Math.Max(RegChanPeriod, Math.Max(adxPeriod, SlopeLookBack)) -1 )
				{
				    double middleNow = RegressionChannel1.Middle[0];
				    double middleBefore = RegressionChannel1.Middle[SlopeLookBack];
				
				    // Calculate slope (change in price per bar)
				    double regChanSlope = (middleNow - middleBefore) / SlopeLookBack;
				
				    // Define a threshold for "flat" slope (needs tuning - VERY instrument dependent)
				    // Might be a fraction of TickSize, e.g., 0.1 * TickSize
				    double flatSlopeThreshold = FlatSlopeFactor * TickSize; // EXAMPLE - TUNE THIS CAREFULLY!
				
				    bool isRegChanFlat = Math.Abs(regChanSlope) < flatSlopeThreshold;
				    bool adxIsLow = currentAdx < ChopAdxThreshold; // Use existing ADX check
				
				    marketIsChoppy = isRegChanFlat && adxIsLow;
				}
				// --- End Regression Channel Slope Choppiness ---

			    // --- Manage Auto Trading Based on Choppiness ---
			    bool autoStatusChanged = false; // Flag to see if we changed status this bar				
				
				// Inside OnBarUpdate, after marketIsChoppy is calculated:
				if (marketIsChoppy) // Or your specific condition
				{
				    // Define the Alpha for 50% transparency
				    byte alpha = 32;
				    // Get the base color components
				    Color baseColor = Colors.LightGray;
				    // Create the new semi-transparent color
				    Color semiTransparentColor = Color.FromArgb(alpha, baseColor.R, baseColor.G, baseColor.B);
				    // Create the new brush
				    SolidColorBrush semiTransparentBrush = new SolidColorBrush(semiTransparentColor);
				    // Freeze the brush for performance (important!)
				    semiTransparentBrush.Freeze();
				    // Assign the semi-transparent brush to BackBrush
				    BackBrush = semiTransparentBrush;
				}
				else
				{
				    // Reset the background when the condition is false
				    BackBrush = null; // Or Brushes.Transparent, or your default chart background if known
				}
					
			    if (marketIsChoppy)
			    {
			        if (isAutoEnabled) // Only act if it was enabled
			        {
			            isAutoEnabled = false;
			            autoDisabledByChop = true; // Mark that the *system* disabled it
			            autoStatusChanged = true;
			            Print($"{Time[0]}: Market choppy (ADX={currentAdx:F1} < {ChopAdxThreshold}, BBWidth Factor < {FlatSlopeFactor:P0}). Auto trading DISABLED.");
			        }
			    }
			    else // Market is NOT choppy
			    {
			        if (autoDisabledByChop) // Only re-enable if *system* disabled it
			        {
			            isAutoEnabled = true;
			            autoDisabledByChop = false; // Clear the flag
			            autoStatusChanged = true;
			            Print($"{Time[0]}: Market no longer choppy. Auto trading RE-ENABLED.");
			        }
			        // If autoDisabledByChop is false, it means the user turned it off manually, so we leave it off.
			    }

			    // --- Update Auto Button Visual ---
			    // Use Dispatcher for safety, although often works without in strategies
			    if (autoStatusChanged && autoBtn != null && ChartControl != null) // Check if button and chart control exist
			    {
			         ChartControl.Dispatcher.InvokeAsync(() => { // Ensures UI update happens on UI thread
			            DecorateButton(autoBtn, isAutoEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Auto On", "\uD83D\uDD13 Auto Off");
			            // Also update manual button state inversely
			            DecorateButton(manualBtn, !isAutoEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Manual On", "\uD83D\uDD13 Manual Off");
			         });
			    }
			}
			
			uptrend = momoUp && buyPressureUp && hmaUp && volMaUp && regChanUp && adxUp && atrUp && aboveEMAHigh;
            downtrend = momoDown && sellPressureUp && hmaDown && volMaDown && regChanDown && adxUp && atrUp && belowEMALow;
			
			priceUp =  Close[0] > Close[1] && Close[0] > Open[0];		
			priceDown =  Close[0] < Close[1] && Close[0] < Open[0];
			
			UpdatePositionState();
			
			if (isAutoEnabled)
			{
				ProcessLongEntry();
				ProcessShortEntry();
			}
			
			if (enableBackgroundSignal)
			{
				if (uptrend)
				{				    
				    // Define the Alpha for 50% transparency
				    byte alpha = 32;
				    // Get the base color components
				    Color baseColor = Colors.Lime;
				    // Create the new semi-transparent color
				    Color semiTransparentColor = Color.FromArgb(alpha, baseColor.R, baseColor.G, baseColor.B);
				    // Create the new brush
				    SolidColorBrush semiTransparentBrush = new SolidColorBrush(semiTransparentColor);
				    // Freeze the brush for performance (important!)
				    semiTransparentBrush.Freeze();
				    // Assign the semi-transparent brush to BackBrush
				    BackBrush = semiTransparentBrush;
				}
				else if (downtrend)
				{				    
				    // Define the Alpha for 50% transparency
				    byte alpha = 32;
				    // Get the base color components
				    Color baseColor = Colors.Crimson;
				    // Create the new semi-transparent color
				    Color semiTransparentColor = Color.FromArgb(alpha, baseColor.R, baseColor.G, baseColor.B);
				    // Create the new brush
				    SolidColorBrush semiTransparentBrush = new SolidColorBrush(semiTransparentColor);
				    // Freeze the brush for performance (important!)
				    semiTransparentBrush.Freeze();
				    // Assign the semi-transparent brush to BackBrush
				    BackBrush = semiTransparentBrush;
				}
				else
				{
				    // Reset the background when the condition is false
				    BackBrush = null; // Or Brushes.Transparent, or your default chart background if known
				}
			}
				
		    // --- Stop/Target Management ---
		    ManageAutoBreakeven();
		    ManageStopLoss();
		    SetProfitTargets(); // Keep setting targets even if auto entry is off

			// Calculate Profit Target based on ATR
		    if (enableAtrProfitTarget) ProfitTarget = ATR1[0] * RiskRewardRatio / TickSize;
			
			if (EnableDynamicProfit)
			{
				// Set profit target at each pivot level
				if (isLong && Close[0] > r3 && Low[0] <= r3)
					SetProfitTarget(@LE1, CalculationMode.Ticks, ProfitTarget);
				else
					SetProfitTargetBasedOnLongConditions();
				
				if (isShort && Close[0] < s3 && High[0] >= s3)
					SetProfitTarget(@SE1, CalculationMode.Ticks, ProfitTarget);
				else 
					SetProfitTargetBasedOnShortConditions();
			}
			
		    // --- PnL / Status Display ---
		    if (Bars.IsFirstBarOfSession)
			{
				cumPnL 			= totalPnL; ///Double that copies the full session PnL (If trading multiple days). Is only calculated once per day.
				dailyPnL		= totalPnL - cumPnL; ///Subtract the copy of the full session by the full session PnL. This resets your daily PnL back to 0.
			}
			
		    if (showPnl) ShowPNLStatus();

//			if (IsFirstTickOfBar) checkPositions();    // Detect unwanted Positions opened (possible rogue Order?
			
			#region Reset Trades Per Direction
            if (TradesPerDirection){
                if (counterLong != 0 && Close[1] < Open[1])
                    counterLong = 0;
                if (counterShort != 0 && Close[1] > Open[1])
                    counterShort = 0;
            }
            #endregion	
			
			#region Reset Stop Loss
			
			// Reset when Flat
			if (isFlat)
			{
				// Reset quick order buttons
			    quickLongBtnActive = false;
			    quickShortBtnActive = false;
			
			    // Reset counters and progress state
			    ProgressState = 0;
				trailStop = InitialStop;
		        lastStopLevel = InitialStop;
			    _beRealized = false;
				
				// Clear active orders on flatten. CRITICAL for ghost order prevention.
				lock (orderLock)
				{
					activeOrders.Clear();
				}
			}
			
			if (!canTradeOK)
			{
                if (Time[0] >= lastEntryTime.AddSeconds(SecsSinceEntry))
                {
					Print(Time[0] + " Timer de-activated");
                    canTradeOK = true;
                }				
			}	
			
			#endregion
			
			if (ValidateExitLong()) 
			{
				// Create the order labels array based on whether additional contracts exist
				string[] orderLabels = additionalContractExists ? new[] { LE2, LE3, LE4, QLE, "QLE2", "QLE3", "QLE4" } : new[] { LE1 };
				
				// Apply the initial stop for all relevant orders
				foreach (string label in orderLabels)
				{		              
					ExitLong(label);
				}
			}
			
			if (ValidateExitShort())
			{
				// Create the order labels array based on whether additional contracts exist
				string[] orderLabels = additionalContractExists ? new[] { SE2, SE3, SE4, QSE, "QSE2", "QSE3", "QSE4" } : new[] { SE1 };
				
				// Apply the initial stop for all relevant orders
				foreach (string label in orderLabels)
				{		              
					ExitShort(label);
				}	
			}
			
			KillSwitch();
        }
		#endregion
		
		#region Breakeven Management

		// Helper method to determine the active order labels based on position
		private List<string> GetRelevantOrderLabels()
		{
		    List<string> labels = new List<string>();
		    bool isLongPosition = Position.MarketPosition == MarketPosition.Long;
		
		    // Add base labels depending on position type (Auto or Quick)
		    if (isLongPosition)
		    {
		        labels.Add(quickLongBtnActive ? QLE : LE1); // QLE or LE
		        if (additionalContractExists) // Add scaled-in entries only if they exist conceptually
		        {
		             if (EnableProfitTarget2) labels.Add(quickLongBtnActive ? "QLE2" : LE2);
		             if (EnableProfitTarget3) labels.Add(quickLongBtnActive ? "QLE3" : LE3);
		             if (EnableProfitTarget4) labels.Add(quickLongBtnActive ? "QLE4" : LE4);
		        }
		    }
		    else // Short Position
		    {
		        labels.Add(quickShortBtnActive ? QSE : SE1); // QSE or SE
		         if (additionalContractExists) // Add scaled entries only if they exist conceptually
		        {
		            if (EnableProfitTarget2) labels.Add(quickShortBtnActive ? "QSE2" : SE2);
		            if (EnableProfitTarget3) labels.Add(quickShortBtnActive ? "QSE3" : SE3);
		            if (EnableProfitTarget4) labels.Add(quickShortBtnActive ? "QSE4" : SE4);
		        }
		    }
		    // Add manual add-in labels if relevant (adjust based on your actual label usage)
		    // if (/* condition indicating Add1LE might be active */) labels.Add(Add1LE);
		    // if (/* condition indicating Add1SE might be active */) labels.Add(Add1SE);
		    return labels;
		}
		
		// Helper to safely set TRAILING stop loss (incorporates error handling)
		private void SetTrailingStop(string fromEntrySignal, CalculationMode mode, double value, bool isSimulatedStop = true)
		{
		     lock(orderLock) // Ensure thread safety
		     {
		         // Optional: Check if order already exists and is in a terminal state before modifying
		         // Relying on SetTrailStop's internal handling but wrap in try-catch.
		
		         try
		         {
		             // Use isSimulatedStop = true to keep strategy in control of trailing logic
		             SetTrailStop(fromEntrySignal, mode, value, isSimulatedStop);
		             Print($"{Time[0]}: SetTrailStop called for label '{fromEntrySignal}'. Mode: {mode}, Value: {value}, IsSimulated: {isSimulatedStop}");
		         }
		         catch (Exception ex)
		         {
		             Print($"{Time[0]}: Error calling SetTrailStop for label '{fromEntrySignal}': {ex.Message}");
		             orderErrorOccurred = true; // Flag the error
		         }
		     }
		}

		// Main method to manage the automatic breakeven logic for EITHER Fixed or Trailing Stops
		private void ManageAutoBreakeven()
		{
		    // --- Pre-checks ---
		    if (isFlat || !beSetAuto || _beRealized) return;
		
		    // --- Calculation & Logging ---
		    double currentUnrealizedPnlTicks = Position.GetUnrealizedProfitLoss(PerformanceUnit.Ticks, Close[0]);
		    Print($"{Time[0]}: Checking Auto BE. PnL Ticks: {currentUnrealizedPnlTicks:F2}, Trigger: {BE_Trigger}, BE Realized: {_beRealized}");
		
		    // --- Trigger Condition ---
		    if (currentUnrealizedPnlTicks >= BE_Trigger)
		    {
		        Print($"{Time[0]}: Auto-Breakeven triggered. PnL (Ticks): {currentUnrealizedPnlTicks:F2} >= Trigger: {BE_Trigger}");
		
		        // --- Calculate Target Breakeven Stop Price ---
		        double entryPrice = Position.AveragePrice;
		        if (entryPrice == 0) { Print($"{Time[0]}: ManageAutoBreakeven - Cannot calculate, entry price is 0."); return; } // Safety check
		
		        double offsetPriceAdjustment = BE_Offset * TickSize;
		        double breakevenStopPrice = entryPrice + (Position.MarketPosition == MarketPosition.Long ? offsetPriceAdjustment : -offsetPriceAdjustment);
		
		        Print($"{Time[0]}: Calculated Breakeven Stop Price: {breakevenStopPrice:F5} (Entry: {entryPrice:F5}, Offset Ticks: {BE_Offset})");

		        // --- Apply Stop Based on Strategy Setting ---
		        List<string> relevantLabels = GetRelevantOrderLabels();
		        if (relevantLabels.Count == 0) { Print($"{Time[0]}: Warning: Breakeven triggered but no relevant order labels found."); return; }
		
		        bool stopAppliedSuccessfully = false; // Flag to track if modification was attempted
		
		        // ***** MODIFICATION START *****
		        if (enableFixedStopLoss)
		        {
		            Print($"{Time[0]}: Applying FIXED Breakeven Stop to labels: {string.Join(", ", relevantLabels)}");
		            foreach (string tag in relevantLabels)
		            {
		                // Use SetFixedStopLoss helper to move to the specific breakeven PRICE
		                SetFixedStopLoss(tag, CalculationMode.Price, breakevenStopPrice, false);
		            }
		            stopAppliedSuccessfully = true; // Assume success for now (helper handles errors)
		        }
		        else if (enableTrail)
		        {
		            // --- Calculate Trailing Value (Ticks) for SetTrailStop ---
		            double currentMarketPrice = Close[0];
		            double valueInTicks;
		            if (Position.MarketPosition == MarketPosition.Long)
		                valueInTicks = (currentMarketPrice - breakevenStopPrice) / TickSize;
		            else
		                valueInTicks = (breakevenStopPrice - currentMarketPrice) / TickSize;
		
		            Print($"{Time[0]}: Calculated Trailing Value (Ticks) for SetTrailStop: {valueInTicks:F2}");
		
		            if (valueInTicks <= 0)
		            {
		                Print($"{Time[0]}: Warning: Market price {currentMarketPrice:F5} is at or beyond calculated breakeven stop {breakevenStopPrice:F5}. Skipping TRAILING SetTrailStop for breakeven this bar.");
		                // Do NOT set _beRealized = true yet if we skip the trailing application
		            }
		            else
		            {
		                Print($"{Time[0]}: Applying TRAILING Breakeven Stop to labels: {string.Join(", ", relevantLabels)}");
		                foreach (string tag in relevantLabels)
		                {
		                    SetTrailingStop(tag, CalculationMode.Ticks, valueInTicks, true);
		                }
		                stopAppliedSuccessfully = true; // Trail stop application was attempted
		            }
		        }
		        else
		        {
		            Print($"{Time[0]}: Warning: Breakeven triggered but neither Fixed Stop nor Trailing Stop is enabled.");
		        }
		        // ***** MODIFICATION END *****
		
		        // --- Mark as Realized (Only if a stop was actually applied) ---
		        if (stopAppliedSuccessfully)
		        {
		            _beRealized = true;
		            Print($"{Time[0]}: Auto-Breakeven stop adjustment applied. _beRealized set to true.");
		        }
		    }
		}
		#endregion
		
		#region Stop Loss Management

		// Helper to safely set stop loss (incorporates error handling)
		private void SetFixedStopLoss(string fromEntrySignal, CalculationMode mode, double priceValue, bool isSimulatedStop = false)
		{
		     lock(orderLock) // Ensure thread safety
		     {
		         // Optional: Check if order already exists and is in a terminal state before modifying
		         // This can prevent unnecessary calls but adds complexity to find the exact order.
		         // For simplicity, we rely on SetStopLoss's internal handling but wrap in try-catch.
		
		         try
		         {
		             SetStopLoss(fromEntrySignal, mode, priceValue, isSimulatedStop);
		             Print($"{Time[0]}: SetStopLoss called for label '{fromEntrySignal}'. Mode: {mode}, Value: {priceValue}");
		         }
		         catch (Exception ex)
		         {
		             Print($"{Time[0]}: Error calling SetStopLoss for label '{fromEntrySignal}': {ex.Message}");
		             orderErrorOccurred = true; // Flag the error
		         }
		     }
		}

		// Helper method to calculate the trailing stop value in ticks based on the active mode
		private double CalculateTrailingStopTicks()
		{
		    double calculatedTrailStopTicks = InitialStop; // Default to InitialStop (acts like Tick Trail default)
		
		    if (threeStepTrail)
		    {
		        // Cache the unrealized PnL value for the current close price
		        double currentUnrealizedPnlTicks = Position.GetUnrealizedProfitLoss(PerformanceUnit.Ticks, Close[0]);
		        int currentProgressState = ProgressState; // Capture current state before potential change
		
		        // Determine the stop value based on the current progress state
		        // Note: We calculate the stop value based on the *current* state,
		        // then check if the state *should transition* for the *next* bar.
		        switch (currentProgressState)
		        {
		            case 0:
		                calculatedTrailStopTicks = step1StopLoss; // Or InitialStop if step1StopLoss isn't meant for state 0? Review logic. Assuming step1 is target stop *after* hitting trigger. Use InitialStop if pre-trigger.
		                // Check for transition AFTER determining current stop value
		                if (currentUnrealizedPnlTicks >= step1ProfitTrigger)
		                {
		                    ProgressState = 1;
		                    // Use the stop value intended for the *new* state (State 1)
		                    calculatedTrailStopTicks = step1StopLoss;
		                    Print($"{Time[0]}: [3-Step Trail] Transitioning to State 1. PnL: {currentUnrealizedPnlTicks:F2} >= Trigger: {step1ProfitTrigger}. Stop Ticks: {calculatedTrailStopTicks}");
		                }
		                break;
		            case 1:
		                calculatedTrailStopTicks = step1StopLoss; // Stop value for state 1
		                 // Check for transition AFTER determining current stop value
		                if (currentUnrealizedPnlTicks >= step2ProfitTrigger)
		                {
		                    ProgressState = 2;
		                     // Use the stop value intended for the *new* state (State 2)
		                    calculatedTrailStopTicks = step2StopLoss;
		                    Print($"{Time[0]}: [3-Step Trail] Transitioning to State 2. PnL: {currentUnrealizedPnlTicks:F2} >= Trigger: {step2ProfitTrigger}. Stop Ticks: {calculatedTrailStopTicks}");
		                }
		                break;
		            case 2:
		                calculatedTrailStopTicks = step2StopLoss; // Stop value for state 2
		                 // Check for transition AFTER determining current stop value
		                if (currentUnrealizedPnlTicks >= step3ProfitTrigger)
		                {
		                    // Note: There's no state 3 in the switch, so we just update the stop value
		                    // If ProgressState should go to 3, add 'ProgressState = 3;'
		                    calculatedTrailStopTicks = step3StopLoss;
		                    Print($"{Time[0]}: [3-Step Trail] Reached State 3 Trigger. PnL: {currentUnrealizedPnlTicks:F2} >= Trigger: {step3ProfitTrigger}. Stop Ticks: {calculatedTrailStopTicks}");
		                }
		                break;
		             // Add case 3 if needed
		        }
		         Print($"[DEBUG 3-Step] Current State: {currentProgressState}, Calculated Stop Ticks: {calculatedTrailStopTicks}");
		    }
		    else if (atrTrailSetAuto)
		    {
		        // ATR trailing stop logic
		        // Ensure ATR1 is calculated and valid
		        if (ATR1 != null && ATR1.IsValidDataPoint(0) && TickSize > 0)
		        {
		            calculatedTrailStopTicks = Math.Max(1, ATR1[0] * atrMultiplier / TickSize); // Ensure at least 1 tick
		            Print($"[DEBUG ATR Trail] ATR: {ATR1[0]:F5}, Multiplier: {atrMultiplier}, TickSize: {TickSize}, Calculated Stop Ticks: {calculatedTrailStopTicks:F2}");
		        }
		        else
		        {
		             Print($"[WARN ATR Trail] ATR not ready or TickSize invalid. Using default: {calculatedTrailStopTicks}");
		        }
		    }
		    else if (tickTrail)
		    {
		        calculatedTrailStopTicks = trailStop;
		        Print($"[DEBUG Tick Trail] Using InitialStop: {calculatedTrailStopTicks}");
		    }
		    // If none of the specific trail types are active, it defaults to InitialStop defined at the start.
		
		    return calculatedTrailStopTicks;
		}		
		// Main method to manage stop loss based on active settings
		private void ManageStopLoss()
		{
		    if (isFlat) return;
			
		    List<string> relevantLabels = GetRelevantOrderLabels();
		    
			if (relevantLabels.Count == 0) { /* ... Print warning ... */ return; }

		    if (enableFixedStopLoss)
		    {
		        if (_beRealized) { /* ... Print skip message ... */ return; }

		        double entryPrice = Position.AveragePrice;
		        if(entryPrice == 0) { /* ... Print warning ... */ return; }

		        double stopPrice = 0; // Target stop price
                bool isLong = Position.MarketPosition == MarketPosition.Long;
				const int StopModificationPriceBufferTicks = 4;
				
		        if (isLong)
		        {
		            stopPrice = entryPrice - (InitialStop * TickSize);
                    // *** ADD VALIDATION for Sell Stop ***
                    double currentAsk = GetCurrentAsk();
                    double minStopPrice = currentAsk - StopModificationPriceBufferTicks * TickSize; // Add 4 tick buffer
                    if (stopPrice >= minStopPrice)
                    {
                        Print($"{Time[0]}: [Fixed Stop] Calculated stop price {stopPrice:F5} is too close to or above current Ask {currentAsk:F5}. Skipping modification.");
                        return; // Don't modify
                    }
                    // *** END VALIDATION ***
		        }
		        else if (Position.MarketPosition == MarketPosition.Short)
		        {
		            stopPrice = entryPrice + (InitialStop * TickSize);
                    // *** ADD VALIDATION for Buy Stop ***
                    double currentBid = GetCurrentBid();
                    double maxStopPrice = currentBid + StopModificationPriceBufferTicks * TickSize; // Add 4 tick buffer
                    if (stopPrice <= maxStopPrice)
                    {
                        Print($"{Time[0]}: [Fixed Stop] Calculated stop price {stopPrice:F5} is too close to or below current Bid {currentBid:F5}. Skipping modification.");
                        return; // Don't modify
                    }
                    // *** END VALIDATION ***
		        }
                else { /* ... Print warning ... */ return; }

		        Print($"{Time[0]}: [Fixed Stop] Applying initial fixed stop loss. Target Price: {stopPrice:F5} (InitialStop Ticks: {InitialStop})");
		        foreach (string label in relevantLabels) {
		            SetFixedStopLoss(label, CalculationMode.Price, stopPrice, false); // Uses helper
		        }
		    }
            // ... (Trailing logic - ensure CalculateTrailingStopTicks returns > 0) ...
		    else if (enableTrail)
		    {
		         if (_beRealized) { /* ... Print skip message ... */ return; }

                 double trailingStopTicks = CalculateTrailingStopTicks(); // Assumes this returns > 0 now
                 // Optional: Add conceptual price validation here too if needed
                 // double conceptualStopPrice = Position.MarketPosition == MarketPosition.Long ?
                 //      Position.AveragePrice - trailingStopTicks * TickSize : // Or maybe CurrentPrice - trail? Depends on exact trail type!
                 //      Position.AveragePrice + trailingStopTicks * TickSize;
                 // if (validation fails for conceptualStopPrice) return;

                 Print($"{Time[0]}: [Trailing Stop] Applying trail stop. Calculated Ticks: {trailingStopTicks:F2}");
                 // The check for trailingStopTicks <= 0 was moved inside CalculateTrailingStopTicks potentially

		         foreach (string label in relevantLabels) {
		            SetTrailingStop(label, CalculationMode.Ticks, trailingStopTicks, true);
		         }
		    }
		}
		
		#endregion

		#region Update Position State
		private void UpdatePositionState()
		{
			isLong = Position.MarketPosition == MarketPosition.Long;
			isShort = Position.MarketPosition == MarketPosition.Short;
			isFlat = Position.MarketPosition == MarketPosition.Flat;
			
			entryPrice = Position.AveragePrice;
			currentPrice = Close[0];
				
			// Logic to check if additional contracts exist (i.e., more than one contract is held)
		    additionalContractExists = Position.Quantity > 1;			
		}
		#endregion

		#region Long Entry			
		private void ProcessLongEntry()
		{
			if (IsLongEntryConditionMet())
		    {
				EnterLongPosition();
		    }
		}			
		#endregion
		
		#region Short Entry
		private void ProcessShortEntry()
		{
            if (IsShortEntryConditionMet())
		    {
				EnterShortPosition();
		    }	
		}			
		#endregion
			
		#region Entry Condition Checkers

        private bool IsLongEntryConditionMet()
        {
			// Combine all entry conditions into a single, readable expression
            return ValidateEntryLong()
                   && isLongEnabled
                   && checkTimers()
                   && (dailyLossProfit ? dailyPnL > -DailyLossLimit && dailyPnL < DailyProfitLimit : true)
                   && isFlat
                   && uptrend
                   && !trailingDrawdownReached
                   && (iBarsSinceExit > 0 ? BarsSinceExitExecution(0, "", 0) > iBarsSinceExit : BarsSinceExitExecution(0, "", 0) > 1 || BarsSinceExitExecution(0, "", 0) == -1)
                   && canTradeOK
                   && (!TradesPerDirection || (TradesPerDirection && counterLong < longPerDirection));
        }

        private bool IsShortEntryConditionMet()
        {
            return ValidateEntryShort()
				&& isShortEnabled
				&& checkTimers()
				&& (dailyLossProfit ? dailyPnL > -DailyLossLimit && dailyPnL < DailyProfitLimit : true)
				&& isFlat
				&& downtrend
				&& !trailingDrawdownReached
				&& (iBarsSinceExit > 0 ? BarsSinceExitExecution(0, "", 0) > iBarsSinceExit : BarsSinceExitExecution(0, "", 0) > 1 || BarsSinceExitExecution(0, "", 0) == -1)
				&& canTradeOK
				&& (!TradesPerDirection || (TradesPerDirection && counterShort < shortPerDirection));
        }

        #endregion

        #region Entry Execution

        private void EnterLongPosition()
        {
            counterLong += 1;
            counterShort = 0;

            if (State == State.Realtime)
            {
                double _entryPrice = Close[0];
                double _stopLoss = _entryPrice - InitialStop * TickSize;
                double _profitTarget = _entryPrice + ProfitTarget * TickSize;

                //	Update last signal details
                lastSignalType = "LONG";
                lastEntryPrice = _entryPrice;
                lastStopLoss = _stopLoss;
                lastProfitTarget = _profitTarget;
                lastSignalTime = Time[0];

                //	Send Entry Signal to Discord
                _ = SendSignalToDiscordAsync(lastSignalType, lastEntryPrice, lastStopLoss, lastProfitTarget, lastSignalTime);
            }

            SubmitEntryOrder(LE1, OrderType, Contracts);
            Draw.Dot(this, LE1 + Convert.ToString(CurrentBars[0]), false, 0, (Close[0]), Brushes.Cyan);
            lastEntryTime = Time[0];

            // Ensure stop and target are set right after the entry
            EnterMultipleLongContracts(false);
            SetStopLosses(LE1);
            SetProfitTargets();
        }

        private void EnterShortPosition()
        {
            counterLong = 0;
            counterShort += 1;

            if (State == State.Realtime)
            {
                double _entryPrice = Close[0];
                double _stopLoss = _entryPrice + InitialStop * TickSize;
                double _profitTarget = _entryPrice - ProfitTarget * TickSize;

                //	Update last signal details
                lastSignalType = "SHORT";
                lastEntryPrice = _entryPrice;
                lastStopLoss = _stopLoss;
                lastProfitTarget = _profitTarget;
                lastSignalTime = Time[0];

                //	Send Entry Signal to Discord
                _ = SendSignalToDiscordAsync(lastSignalType, lastEntryPrice, lastStopLoss, lastProfitTarget, lastSignalTime);
            }

            SubmitEntryOrder(SE1, OrderType, Contracts);
            Draw.Dot(this, SE1 + Convert.ToString(CurrentBars[0]), false, 0, (Close[0]), Brushes.Yellow);
            lastEntryTime = Time[0];

            // Ensure stop and target are set right after the entry
            EnterMultipleShortContracts(false);
            SetStopLosses(SE1);
            SetProfitTargets();
        }

        #endregion
		
		#region Order Submission Helpers

		// This method encapsulates all order submissions and error handling.
		private Order SubmitEntryOrder(string orderLabel, OrderType orderType, int contracts)
		{
			Order submittedOrder = null;

			lock (orderLock)
			{
				if (!CanSubmitOrder())
				{
					Print (string.Format("{0}: Cannot submit {1} order: Minimum order interval not met.", Time[0], orderLabel));
					return null; // Or throw an exception if order submission is absolutely critical
				}

				try
				{
					switch (orderType)
					{
						case OrderType.Market:
							if (orderLabel == LE1 || orderLabel == QLE)
								submittedOrder = EnterLong(contracts, orderLabel);
							else if (orderLabel == SE1 || orderLabel == QSE)
								submittedOrder = EnterShort(contracts, orderLabel);
							else
								throw new ArgumentException("Invalid order label for Market order.");
							break;
						case OrderType.Limit:
							if (orderLabel == LE1 || orderLabel == QLE)
								submittedOrder = EnterLongLimit(contracts, GetCurrentBid() - LimitOffset * TickSize, orderLabel);
							else if (orderLabel == SE1 || orderLabel == QSE)
								submittedOrder = EnterShortLimit(contracts, GetCurrentAsk() + LimitOffset * TickSize, orderLabel);
							else
								throw new ArgumentException("Invalid order label for Limit order.");
							break;
						case OrderType.MIT:
							if (orderLabel == LE1 || orderLabel == QLE)
								submittedOrder = EnterLongMIT(contracts, GetCurrentBid() - LimitOffset * TickSize, orderLabel);
							else if (orderLabel == SE1 || orderLabel == QSE)
								submittedOrder = EnterShortMIT(contracts, GetCurrentAsk() + LimitOffset * TickSize, orderLabel);
							else
								throw new ArgumentException("Invalid order label for MIT order.");
							break;
						case OrderType.StopLimit:
							if (orderLabel == LE1 || orderLabel == QLE)
								submittedOrder = EnterLongLimit(contracts, GetCurrentBid() - LimitOffset * TickSize, orderLabel);
							else if (orderLabel == SE1 || orderLabel == QSE)
								submittedOrder = EnterShortLimit(contracts, GetCurrentAsk() + LimitOffset * TickSize, orderLabel);
							else
								throw new ArgumentException("Invalid order label for StopLimit order.");
							break;
						case OrderType.StopMarket:
							if (orderLabel == LE1 || orderLabel == QLE)
								submittedOrder = EnterLong(contracts, orderLabel);
							else if (orderLabel == SE1 || orderLabel == QSE)
								submittedOrder = EnterShort(contracts, orderLabel);
							else
								throw new ArgumentException("Invalid order label for StopMarket order.");
							break;
						default:
							throw new ArgumentOutOfRangeException(nameof(orderType), orderType, "Unsupported order type");
					}

					if (submittedOrder != null)
					{
						activeOrders[orderLabel] = submittedOrder;  // TRACK THE ORDER!
						lastOrderActionTime = DateTime.Now;
						Print (string.Format("{0}: Submitted {1} order with OrderId: {2}", Time[0], orderLabel, submittedOrder.OrderId));
					}
					else
					{
						Print (string.Format("{0}: Error: {1} Entry order was null after submission.", Time[0], orderLabel));
						orderErrorOccurred = true;
					}
				}
				catch (Exception ex)
				{
					Print (string.Format("{0}: Error submitting {1} entry order: {2}", Time[0], orderLabel, ex.Message));
					orderErrorOccurred = true;
				}
			}

			return submittedOrder;
		}

		private void SubmitExitOrder(string orderLabel)
		{
			lock(orderLock)
			{
				try
				{
					if (orderLabel == LE1 || orderLabel == QLE || orderLabel == Add1LE) {
						ExitLong(orderLabel);
					} else if(orderLabel == SE1 || orderLabel == QSE || orderLabel == Add1SE){
						ExitShort(orderLabel);
					} else {
						Print ($"Error: invalid order label {orderLabel}");
					}
					
					if(!activeOrders.ContainsKey(orderLabel))
						Print ($"Cannot cancel order that does not exist");
					
					if(activeOrders.TryGetValue(orderLabel, out Order orderToCancel)) {
						CancelOrder(orderToCancel);
						activeOrders.Remove(orderLabel);
					}
				} catch(Exception ex) {
					Print ($"Error submitting Exit order: {ex.Message}");
					orderErrorOccurred = true;
				}
			}
		}

		#endregion
		
		#region Rogue Order Detection

		private void ReconcileAccountOrders()
		{
		    lock (orderLock)
		    {
		        try
		        {
		            // Get all accounts
		            var accounts = Account.All;

		            if (accounts == null || accounts.Count == 0)
		            {
		                Print(string.Format("{0}: No accounts found.", Time[0]));
		                return;
		            }

		            // Iterate through the accounts and reconcile orders
		            foreach (Account account in accounts)
		            {
		                // Get the list of all orders associated with each instrument in that account
		                List<Order> accountOrders = new List<Order>();

		                try
		                {
		                    foreach (Position position in account.Positions)
		                    {
		                        Instrument instrument = position.Instrument;
		                        foreach (Order order in Orders)
		                        {
		                            if (order.Instrument == instrument && order.Account == account)
		                            {
		                                accountOrders.Add(order);
		                            }
		                        }
		                    }
		                }
		                catch (Exception ex)
		                {
		                    Print(string.Format("{0}: Error getting orders for account {1}: {2}", Time[0], account.Name, ex.Message));
		                    continue; // Move to the next account. Don't halt the entire strategy if one account fails.
		                }

		                // Check for nulls and validity of account orders
		                if (accountOrders == null || accountOrders.Count == 0)
		                {
		                    Print(string.Format("{0}: No orders found in account {1}.", Time[0], account.Name));
		                    continue; //Move to the next account
		                }

						// Create a list of order IDs from activeOrders
						HashSet<string> strategyOrderIds = new HashSet<string>(activeOrders.Values.Select(o => o.OrderId));

						// Iterate through the account orders and check if they are tracked by the strategy
						foreach (Order accountOrder in accountOrders)
						{
							// Use null conditional operator for more succinct code
							if (!strategyOrderIds.Contains(accountOrder?.OrderId))
							{
								// This is a rogue order!
								Print(string.Format("{0}: Rogue order detected! Account: {6} OrderId: {1}, OrderType: {2}, OrderStatus: {3}, Quantity: {4}, AveragePrice: {5}",
									Time[0], accountOrder.OrderId, accountOrder.OrderType, accountOrder.OrderState, accountOrder.Quantity, accountOrder.AverageFillPrice, account.Name));

								// You can either attempt to manage it:

								// Attempt to cancel the rogue order.  If it's a manual order, you might want to skip this step and just log it.
								try
								{
									CancelOrder(accountOrder);
									Print(string.Format("{0}: Attempted to cancel rogue order: {1}", Time[0], accountOrder.OrderId));
								}
								catch (Exception ex)
								{
									Print(string.Format("{0}: Failed to Cancel rogue order. Account: {6} OrderId: {1}, OrderType: {2}, OrderStatus: {3}, Quantity: {4}, AveragePrice: {5}, Reason: {7}",
										Time[0], accountOrder.OrderId, accountOrder.OrderType, accountOrder.OrderState, accountOrder.Quantity, accountOrder.AverageFillPrice, account.Name, ex.Message));
								}
							}
						}
		            } // End of account iteration
		        }
		        catch (Exception ex)
		        {
		            Print(string.Format("{0}: Error during account reconciliation: {1}", Time[0], ex.Message));
		            orderErrorOccurred = true;  // Consider whether to halt trading
		        }
		    }
		}

		#endregion
		
		#region Can Submit Order

		// Method to check the minimum interval between order submissions
		private bool CanSubmitOrder()
		{
			return (DateTime.Now - lastOrderActionTime) >= minOrderActionInterval;
		}
	
		#endregion
	
		#region OnExecutionUpdate
		
		protected virtual void OnExecutionUpdate(Execution execution, string executionId, double price,
                                           int quantity, MarketPosition marketPosition, string orderId,
                                           DateTime time)
		{
		    if (execution.Order.Name == "FixedStop")
		    {
		        if (isLong)
		        {
		            // Update FixedStopLossTicks for long positions
		            InitialStop = (int)((Position.AveragePrice - price) / TickSize);
		            Print($"Long Stop Loss adjusted. New FixedStopLossTicks: {InitialStop}");
		        }
		        else if (isShort)
		        {
		            // Update FixedStopLossTicks for short positions
		            InitialStop = (int)((price - Position.AveragePrice) / TickSize);
		            Print($"Short Stop Loss adjusted. New FixedStopLossTicks: {InitialStop}");
		        }
		    }
		
		    // *** CRITICAL: Track order fills, modifications, and cancellations ***
		    lock (orderLock)
		    {
		        // Find the order in our activeOrders dictionary
		        string orderLabel = activeOrders.FirstOrDefault(x => x.Value.OrderId == orderId).Key;
		
		        if (!string.IsNullOrEmpty(orderLabel))
		        {
		            switch (execution.Order.OrderState)
		            {
		                case OrderState.Filled:
		                    Print(string.Format("{0}: Order {1} with label {2} filled.", Time[0], orderId, orderLabel));
		                    activeOrders.Remove(orderLabel); // Remove the order when it's filled.
		
		                    if (execution.Order.OrderState == OrderState.Filled && isFlat)
		                    {
		                        if (execution.Order.Name.StartsWith(LE1) || execution.Order.Name.StartsWith(QLE) || execution.Order.Name.StartsWith("Add1LE"))
		                        {
		                            counterLong = 0;
		                        }
		                        else if (execution.Order.Name.StartsWith(SE1) || execution.Order.Name.StartsWith(QSE) || execution.Order.Name.StartsWith(Add1SE))
		                        {
		                            counterShort = 0;
		                        }
		                    }
		
		                    break;
		
		                case OrderState.Cancelled:
		                    Print(string.Format("{0}: Order {1} with label {2} cancelled.", Time[0], orderId, orderLabel));
		                    activeOrders.Remove(orderLabel); // Remove cancelled orders
		                    break;
		
		                case OrderState.Rejected:
		                    Print(string.Format("{0}: Order {1} with label {2} rejected.", Time[0], orderId, orderLabel));
		                    activeOrders.Remove(orderLabel); // Remove rejected orders
		                    break;
		
		                default:
		                    Print(string.Format("{0}: Order {1} with label {2} updated to state: {3}", Time[0], orderId, orderLabel, execution.Order.OrderState));
		                    break;
		            }
		        }
		        else
		        {
		            // This could indicate a rogue order or an order not tracked by the strategy.
		            Print(string.Format("{0}: Execution update for order {1}, but order is not tracked by the strategy.", Time[0], orderId));
		
		            // Attempt to Cancel the Rogue Order
		            try
		            {
		                CancelOrder(execution.Order);
		                Print(string.Format("{0}: Successfully Canceled the Rogue Order: {1}.", Time[0], orderId));
		            }
		            catch (Exception ex)
		            {
		                Print(string.Format("{0}: Could not Cancel the Rogue Order: {1}. {2}", Time[0], orderId, ex.Message));
		                orderErrorOccurred = true;  // Consider whether to halt trading
		            }
		        }
		    }
		}
		
		#endregion
		
		#region Pivot Profit Targets

        private void SetProfitTargetBasedOnLongConditions()
        {
            if (Close[0] > s3 && Low[0] <= s3)
				SetProfitTarget(LE1, CalculationMode.Price, s3m);
			else if (Close[0] > s3m && Low[0] <= s3m)
				SetProfitTarget(LE1, CalculationMode.Price, s2);
			else if (Close[0] > s2 && Low[0] <= s2)
				SetProfitTarget(LE1, CalculationMode.Price, s2m);
			else if (Close[0] > s2m && Low[0] <= s2m)	
				SetProfitTarget(LE1, CalculationMode.Price, s1);
			else if (Close[0] > s1 && Low[0] <= s1)
				SetProfitTarget(LE1, CalculationMode.Price, s1m);
			else if (Close[0] > s1m && Low[0] <= s1m)
				SetProfitTarget(LE1, CalculationMode.Price, pivotPoint);
			else if (Close[0] > pivotPoint && Low[0] <= pivotPoint)
				SetProfitTarget(LE1, CalculationMode.Price, r1m);
			else if (Close[0] > r1m && Low[0] <= r1m)
				SetProfitTarget(LE1, CalculationMode.Price, r1);
			else if (Close[0] > r1 && Low[0] <= r1)
				SetProfitTarget(LE1, CalculationMode.Price, r2m);
			else if (Close[0] > r2m && Low[0] <= r2m)
				SetProfitTarget(LE1, CalculationMode.Price, r2);
			else if (Close[0] > r2 && Low[0] <= r2)
				SetProfitTarget(LE1, CalculationMode.Price, r3m);
			else if (Close[0] > r3m && Low[0] <= r3m)
				SetProfitTarget(LE1, CalculationMode.Price, r3);
			else if (Close[0] > r3 && Low[0] <= r3)
				SetProfitTarget(@LE1, CalculationMode.Ticks, ProfitTarget);
        }

        private void SetProfitTargetBasedOnShortConditions()
        {
            if (Close[0] < r3 && High[0] >= r3)
				SetProfitTarget(SE1, CalculationMode.Price, r3m);
			else if (Close[0] < r3m && High[0] >= r3m)
				SetProfitTarget(SE1, CalculationMode.Price, r2);
			else if (Close[0] < r2 && High[0] >= r2)
				SetProfitTarget(SE1, CalculationMode.Price, r2m);
			else if (Close[0] < r2m && High[0] >= r2m)
				SetProfitTarget(SE1, CalculationMode.Price, r1);
			else if (Close[0] < r1 && High[0] >= r1)
				SetProfitTarget(SE1, CalculationMode.Price, r1m);
			else if (Close[0] < r1m && High[0] >= r1m)
				SetProfitTarget(SE1, CalculationMode.Price, pivotPoint);
			else if (Close[0] < pivotPoint && High[0] >= pivotPoint)
				SetProfitTarget(SE1, CalculationMode.Price, s1m);
			else if (Close[0] < s1m && High[0] >= s1m)
				SetProfitTarget(SE1, CalculationMode.Price, s1);
			else if (Close[0] < s1 && High[0] >= s1)
				SetProfitTarget(SE1, CalculationMode.Price, s2m);
			else if (Close[0] < s2m && High[0] >= s2m)
				SetProfitTarget(SE1, CalculationMode.Price, s2);
			else if (Close[0] < s2 && High[0] >= s2)
				SetProfitTarget(SE1, CalculationMode.Price, s3m);
			else if (Close[0] < s3m && High[0] >= s3m)
				SetProfitTarget(SE1, CalculationMode.Price, s3);
			else if (Close[0] < s3 && High[0] >= s3)
				SetProfitTarget(@SE1, CalculationMode.Ticks, ProfitTarget);
        	}
		
		private void EnterMultipleLongContracts(bool isManual) {
			if (enableFixedProfit) {
				if(isManual) {
					EnterMultipleOrders(true, EnableProfitTarget2, @"QLE2", Contracts2);
					EnterMultipleOrders(true, EnableProfitTarget3,  @"QLE3", Contracts3);
					EnterMultipleOrders(true, EnableProfitTarget4,  @"QLE4", Contracts4);
				} else {
					EnterMultipleOrders(true, EnableProfitTarget2, @LE2, Contracts2);
					EnterMultipleOrders(true, EnableProfitTarget3,  @LE3, Contracts3);
					EnterMultipleOrders(true, EnableProfitTarget4,  @LE4, Contracts4);
				}
			}
		}
		
		private void EnterMultipleShortContracts(bool isManual) {
			if (enableFixedProfit) {
				if(isManual) {
					EnterMultipleOrders(false, EnableProfitTarget2, @"QSE2", Contracts2);
					EnterMultipleOrders(false, EnableProfitTarget3,  @"QSE3", Contracts3);
					EnterMultipleOrders(false, EnableProfitTarget4,  @"QSE4", Contracts4);
				} else {
					EnterMultipleOrders(false, EnableProfitTarget2, @SE2, Contracts2);
					EnterMultipleOrders(false, EnableProfitTarget3,  @SE3, Contracts3);
					EnterMultipleOrders(false, EnableProfitTarget4,  @SE4, Contracts4);
				}
			}
		}
		
		private void EnterMultipleOrders(bool isLong, bool isEnableTarget, string signalName, int contracts)
		{
		    if (isEnableTarget)
		    {
		        if (isLong)
		        {
		            if (OrderType == OrderType.Market)
		                EnterLong(Convert.ToInt32(contracts), signalName);
		            else if (OrderType == OrderType.Limit)
		                EnterLongLimit(Convert.ToInt32(contracts), GetCurrentBid() - LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.MIT)
		                EnterLongMIT(Convert.ToInt32(contracts), GetCurrentBid() - LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.StopLimit)
		                EnterLongLimit(Convert.ToInt32(contracts), GetCurrentBid() - LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.StopMarket)
		                EnterLong(Convert.ToInt32(contracts), signalName);
		        }
		        else
		        {
		            if (OrderType == OrderType.Market)
		                EnterShort(Convert.ToInt32(contracts), signalName);
		            else if (OrderType == OrderType.Limit)
		                EnterShortLimit(Convert.ToInt32(contracts), GetCurrentAsk() + LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.MIT)
		                EnterShortMIT(Convert.ToInt32(contracts), GetCurrentAsk() + LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.StopLimit)
		                EnterShortLimit(Convert.ToInt32(contracts), GetCurrentAsk() + LimitOffset * TickSize, signalName);
		            else if (OrderType == OrderType.StopMarket)
		                EnterShort(Convert.ToInt32(contracts), signalName);
		        }
		    }
		}
		
		#endregion		
		
        #region Set Stop Losses
        private void SetStopLosses(string entryOrderLabel) // Renamed parameter for clarity, assuming it's the primary entry label
        {
            if (enableTrail)
            {
				trailStop = InitialStop;
				
                Print($"{Time[0]}: [Initial Stop] Applying Initial Trail Stop ({InitialStop} ticks) for label {entryOrderLabel}.");
                // Apply initial trail stop using the safe helper
                SetTrailingStop(entryOrderLabel, CalculationMode.Ticks, trailStop, true);
                // Apply to potentially scaled-in orders as well
                SetMultipleStopLosses(trailStop, true); // Pass true for isTrailing
            }
            else if (enableFixedStopLoss)
            {
                 // --- Directly implement fixed stop logic here ---
                 double entryPrice = Position.AveragePrice; // Get entry price
                 if(entryPrice == 0) // Safety check if position isn't fully registered yet
                 {
                      Print($"{Time[0]}: [Initial Stop] Warning: Cannot set initial fixed stop, entry price is 0.");
                      return;
                 }

                 double stopPrice;
                 if (Position.MarketPosition == MarketPosition.Long)
                 {
                     stopPrice = entryPrice - (InitialStop * TickSize);
                 }
                 else // Short Position
                 {
                     stopPrice = entryPrice + (InitialStop * TickSize);
                 }

                 Print($"{Time[0]}: [Initial Stop] Applying Initial Fixed Stop Price: {stopPrice:F5} (InitialStop Ticks: {InitialStop}) for label {entryOrderLabel}.");

                 // Apply initial fixed stop loss using the safe helper
                 SetFixedStopLoss(entryOrderLabel, CalculationMode.Price, stopPrice, false);
                 // Apply to potentially scaled-in orders as well
                 SetMultipleStopLosses(stopPrice, false); // Pass false for isTrailing, pass price
                 // --- End direct fixed stop logic ---
            }
        }

        // Modify SetMultipleStopLosses to accept price or ticks based on mode
        private void SetMultipleStopLosses(double stopValue, bool isTrailing) // Added isTrailing flag
		{
            Print($"{Time[0]}: SetMultipleStopLosses called. Mode: {(isTrailing ? "Trailing (Ticks)" : "Fixed (Price)")}, Value: {stopValue}"); // Existing log

			if (enableFixedProfit) // Only applies if scale-ins are possible
            {
                // Determine label prefix based on current position and if it was a quick entry
                string labelPrefix = "";
                 MarketPosition currentPositionState = Position.MarketPosition; // Get current state

                if (currentPositionState == MarketPosition.Long)
                    labelPrefix = quickLongBtnActive ? QLE : LE1;
                else if (currentPositionState == MarketPosition.Short)
                    labelPrefix = quickShortBtnActive ? QSE : SE1;
                else
                {
                    Print($"{Time[0]}: SetMultipleStopLosses - Cannot determine label prefix, position is flat or unknown ({currentPositionState}).");
                    return; // Cannot proceed without a valid prefix
                }

                // --- ADDED LOGGING ---
                Print($"{Time[0]}: SetMultipleStopLosses - Determined Label Prefix: {labelPrefix} (QuickLongActive: {quickLongBtnActive}, QuickShortActive: {quickShortBtnActive})");
                // ---------------------

				if (EnableProfitTarget2) {
                    string lbl = labelPrefix + "2";
                    Print($"{Time[0]}: SetMultipleStopLosses - Attempting to set stop for label {lbl}."); // Log before call
                    if(isTrailing) SetTrailingStop(lbl, CalculationMode.Ticks, stopValue, true); // Check logs from this helper
                    else SetFixedStopLoss(lbl, CalculationMode.Price, stopValue, false); // Check logs from this helper
				}

				if (EnableProfitTarget3) {
                    string lbl = labelPrefix + "3";
                    Print($"{Time[0]}: SetMultipleStopLosses - Attempting to set stop for label {lbl}."); // Log before call
					if(isTrailing) SetTrailingStop(lbl, CalculationMode.Ticks, stopValue, true); // Check logs from this helper
                    else SetFixedStopLoss(lbl, CalculationMode.Price, stopValue, false); // Check logs from this helper
				}

				if (EnableProfitTarget4) {
                    string lbl = labelPrefix + "4";
                    Print($"{Time[0]}: SetMultipleStopLosses - Attempting to set stop for label {lbl}."); // Log before call
                    if(isTrailing) SetTrailingStop(lbl, CalculationMode.Ticks, stopValue, true); // Check logs from this helper
                    else SetFixedStopLoss(lbl, CalculationMode.Price, stopValue, false); // Check logs from this helper
				}
			}
            else
            {
                 Print($"{Time[0]}: SetMultipleStopLosses - enableFixedProfit is false, skipping scale-in stops.");
            }
		}
		#endregion
		
		#region Set Profit Targets
		
		private void SetProfitTargets()
		{
		    try
		    {
		        if (EnableFixedProfit)
		        {
		            SetProfitTarget(@LE1, CalculationMode.Ticks, ProfitTarget);
		            SetProfitTarget(@SE1, CalculationMode.Ticks, ProfitTarget);
		            SetProfitTarget(@QLE, CalculationMode.Ticks, ProfitTarget);
		            SetProfitTarget(@QSE, CalculationMode.Ticks, ProfitTarget);
		
		            // Set the additional profit targets dynamically
		            SetProfitTargetForLabel(@LE2, ProfitTarget2, EnableProfitTarget2);
		            SetProfitTargetForLabel(@SE2, ProfitTarget2, EnableProfitTarget2);
		            SetProfitTargetForLabel(@"QLE2", ProfitTarget2, EnableProfitTarget2);
		            SetProfitTargetForLabel(@"QSE2", ProfitTarget2, EnableProfitTarget2);
		
		            SetProfitTargetForLabel(@LE3, ProfitTarget3, EnableProfitTarget3);
		            SetProfitTargetForLabel(@SE3, ProfitTarget3, EnableProfitTarget3);
		            SetProfitTargetForLabel(@"QLE3", ProfitTarget3, EnableProfitTarget3);
		            SetProfitTargetForLabel(@"QSE3", ProfitTarget3, EnableProfitTarget3);
		
		            SetProfitTargetForLabel(@LE4, ProfitTarget4, EnableProfitTarget4);
		            SetProfitTargetForLabel(@SE4, ProfitTarget4, EnableProfitTarget4);
		            SetProfitTargetForLabel(@"QLE4", ProfitTarget4, EnableProfitTarget4);
		            SetProfitTargetForLabel(@"QSE4", ProfitTarget4, EnableProfitTarget4);
		        }
		        else if (EnableDynamicProfit)
		        {
		            // Set profit target at each pivot level
		            if (isLong)
		            {
		                if (Close[0] > r3 && Low[0] <= r3)
		                    SetProfitTarget(@LE1, CalculationMode.Price, r3m);
		                else
		                    SetProfitTargetBasedOnLongConditions();
		            }
		
		            if (isShort)
		            {
		                if (Close[0] < s3 && High[0] >= s3)
		                    SetProfitTarget(@SE1, CalculationMode.Price, s3m);
		                else
		                    SetProfitTargetBasedOnShortConditions();
		            }
		        }
		    }
		    catch (Exception ex)
		    {
		        Print($"Error in SetProfitTargets: {ex.Message}");
		    }
		}
		
		private void SetProfitTargetForLabel(string label, double profitTarget, bool isEnabled)
		{
		    if (isEnabled)
		    {
		        SetProfitTarget(label, CalculationMode.Ticks, profitTarget);
		    }
		}
		
		#endregion
		
		#region Stop Adjustment (Manual Buttons)

        // Adjusts the active trailing stop by a specified number of ticks
		protected void AdjustStopLoss(int tickAdjustment)
		{
            // --- Pre-checks ---
		    if (isFlat)
		    {
		        Print($"{Time[0]}: AdjustStopLoss: No active position.");
		        return;
		    }
            if (tickAdjustment == 0)
            {
                Print($"{Time[0]}: AdjustStopLoss: Tick adjustment is zero, no change needed.");
                return;
            }
            // Ensure trailing is conceptually enabled for adjustment
            if (!enableTrail && !enableFixedStopLoss) // Only adjust if some stop mechanism is active
            {
                 Print($"{Time[0]}: AdjustStopLoss: Neither Trailing nor Fixed Stop enabled.");
                 return;
            }

            // --- Get Current State ---
		    double entryPrice = Position.AveragePrice;
            if (entryPrice == 0) { Print($"{Time[0]}: AdjustStopLoss: Cannot adjust, entry price is 0."); return; } // Safety check
		    bool isLong = Position.MarketPosition == MarketPosition.Long;
		    double currentMarketPrice = Close[0];

            // --- Determine Current Stop Price ---
            // Find the current stop price level to adjust FROM.
            // We need to check active orders OR infer from current settings if no order found.
            double currentStopPrice = 0;
            Order workingStop = Orders.FirstOrDefault(o => o.OrderState == OrderState.Working && o.IsStopMarket); // Find *any* working stop

            if (workingStop != null)
            {
                currentStopPrice = workingStop.StopPrice;
                Print($"{Time[0]}: AdjustStopLoss: Found working stop order {workingStop.OrderId} at price {currentStopPrice:F5}.");
            }
            else
            {
                // No working stop order found. Infer based on current mode.
                Print($"{Time[0]}: AdjustStopLoss: No working stop order found. Inferring current stop level...");
                if (enableFixedStopLoss)
                {
                    currentStopPrice = isLong ? entryPrice - (InitialStop * TickSize) : entryPrice + (InitialStop * TickSize);
                    Print($"{Time[0]}: AdjustStopLoss: Inferred from Fixed Stop setting. Current Stop Price: {currentStopPrice:F5}");
                }
                else if (enableTrail)
                {
                    // Need to calculate the effective stop price based on the *current* trail mode/value
                    double currentTrailTicks = CalculateTrailingStopTicks(); // Use the helper!
                    currentStopPrice = isLong ? currentMarketPrice - (currentTrailTicks * TickSize) : currentMarketPrice + (currentTrailTicks * TickSize);
                    // Note: This inferred price for trailing might slightly differ from an actual working order due to timing.
                     Print($"{Time[0]}: AdjustStopLoss: Inferred from Trailing Stop setting ({currentTrailTicks} ticks). Current Stop Price: {currentStopPrice:F5}");
                }
                else
                {
                     Print($"{Time[0]}: AdjustStopLoss: Cannot determine current stop price level. Aborting.");
                     return;
                }
            }

             if (currentStopPrice == 0) { Print($"{Time[0]}: AdjustStopLoss: Could not determine a valid current stop price. Aborting."); return; } // Final check

            // --- Calculate New Target Stop Price ---
		    double newTargetStopPrice = isLong
		        ? currentStopPrice + tickAdjustment * TickSize  // Move towards market for longs
		        : currentStopPrice - tickAdjustment * TickSize;  // Move towards market for shorts

            Print($"{Time[0]}: AdjustStopLoss: Current Stop: {currentStopPrice:F5}, Tick Adj: {tickAdjustment}, New Target Stop: {newTargetStopPrice:F5}");

            // --- Validate New Stop Price ---
            // Prevent moving stop TO or BEYOND the current market price
		    if ((isLong && newTargetStopPrice >= currentMarketPrice) || (!isLong && newTargetStopPrice <= currentMarketPrice))
		    {
		        Print($"{Time[0]}: AdjustStopLoss: Cannot move stop. New target price {newTargetStopPrice:F5} invalid relative to current market price {currentMarketPrice:F5}.");
		        return; // Do not proceed
		    }
            // Optional: Prevent moving stop TO or BEYOND the entry price if it's not desired (e.g., preventing moving BE back into loss)
            // if ((isLong && newTargetStopPrice < entryPrice) || (!isLong && newTargetStopPrice > entryPrice))
            // {
            //      Print($"{Time[0]}: AdjustStopLoss: Cannot move stop beyond entry price {entryPrice:F5}.");
            //      return;
            // }


            // --- Calculate Tick Offset for SetTrailStop (from Entry Price) ---
            // This determines how many ticks behind the *entry* price the stop needs to be placed
            // to achieve the newTargetStopPrice. SetTrailStop(Mode=Ticks) always works relative to entry.
            double breakevenTicks = isLong
		        ? (entryPrice - newTargetStopPrice) / TickSize
		        : (newTargetStopPrice - entryPrice) / TickSize;

            Print($"{Time[0]}: AdjustStopLoss: Calculated Tick Offset From Entry: {breakevenTicks:F2}");

            // --- Sanity Check Offset ---
            // The offset must be positive for SetTrailStop(Mode=Ticks)
            if (breakevenTicks <= 0)
            {
                 Print($"{Time[0]}: AdjustStopLoss: Calculated non-positive breakevenTicks ({breakevenTicks:F2}). Aborting adjustment. Check logic or price validation.");
                 return; // Stop adjustment if offset is invalid
            }

            // --- Apply to Relevant Labels using Safe Helper ---
		    List<string> orderLabels = GetRelevantOrderLabels();
            if (orderLabels.Count == 0)
            {
                 Print($"{Time[0]}: AdjustStopLoss: No relevant order labels found to apply adjustment to.");
                 return;
            }

		    Print($"{Time[0]}: AdjustStopLoss: Applying adjustment (Offset: {breakevenTicks:F2} ticks from entry) to labels: {string.Join(", ", orderLabels)}");
		    foreach (string label in orderLabels)
		    {
                // Use the SAFE helper function
                // Mode is Ticks, value is offset from ENTRY, isSimulated=true to keep strategy managing trail
		        SetTrailingStop(label, CalculationMode.Ticks, breakevenTicks, true);
		    }
            ForceRefresh(); // Refresh chart UI if needed after manual adjustment
		}

        #endregion 	
 
		#region Move To Breakeven (Manual Buttons) // Keep methods in this region or similar

        // Manually moves the active trailing stop to the Breakeven level (+/- offset)
		protected void MoveToBreakeven()
		{
            // --- Pre-checks ---
		    if (isFlat)
            {
                 Print($"{Time[0]}: MoveToBreakeven: No active position.");
                 return;
            }
            // Ensure trailing is conceptually enabled, otherwise BE doesn't make sense
            if (!enableTrail && !enableFixedStopLoss)
            {
                 Print($"{Time[0]}: MoveToBreakeven: Neither Trailing nor Fixed Stop enabled.");
                 return;
            }

            // Optional: A stricter check might compare PnL directly if needed:
			double currentUnrealizedPnlTicks = Position.GetUnrealizedProfitLoss(PerformanceUnit.Ticks, Close[0]);
			
            // --- Get Current State ---
            double entryPrice = Position.AveragePrice;
            if (entryPrice == 0) { Print($"{Time[0]}: MoveToBreakeven: Cannot adjust, entry price is 0."); return; } // Safety check
            bool isLong = Position.MarketPosition == MarketPosition.Long;
		    double currentMarketPrice = Close[0];

            // --- Calculate Target Breakeven Stop Price ---
            // Move stop to Entry Price +/- Offset ticks
		    double offsetPriceAdjustment = BE_Offset * TickSize;
			double targetBreakevenStopPrice = entryPrice + (isLong ? offsetPriceAdjustment : -offsetPriceAdjustment);
				
            Print($"{Time[0]}: MoveToBreakeven: Target BE Stop Price: {targetBreakevenStopPrice:F5} (Entry: {entryPrice:F5}, Offset Ticks: {BE_Offset})");

            // --- Validate New Stop Price ---
            // Prevent moving stop TO or BEYOND the current market price
		    if ((isLong && targetBreakevenStopPrice >= currentMarketPrice) || (!isLong && targetBreakevenStopPrice <= currentMarketPrice))
		    {
		        Print($"{Time[0]}: MoveToBreakeven: Cannot move stop. Target BE price {targetBreakevenStopPrice:F5} invalid relative to current market price {currentMarketPrice:F5}. Position might not be profitable enough.");
		        return; // Do not proceed
		    }
			
			if (currentUnrealizedPnlTicks < BE_Offset) { // Or maybe just < 0 ?
				Print($"{Time[0]}: MoveToBreakeven: Position not sufficiently profitable (PnL Ticks: {currentUnrealizedPnlTicks:F2} < Offset: {BE_Offset}).");
				return;
			}

			// Determine if breakeven conditions are met
			if (currentUnrealizedPnlTicks >= BE_Offset) 
			{	
	            // --- Calculate Tick Offset for SetTrailStop (from Entry Price) ---
	            double breakevenTicks = targetBreakevenStopPrice / TickSize;
	
	            Print($"{Time[0]}: MoveToBreakeven: Calculated Tick Offset From Entry: {breakevenTicks:F2}");
	
	            // --- Sanity Check Offset ---
	            if (breakevenTicks <= 0)
	            {
	                 Print($"{Time[0]}: MoveToBreakeven: Calculated non-positive breakevenTicks ({breakevenTicks:F2}). Aborting adjustment. Check logic/offset.");
	                 // This case implies the BE point is beyond the entry price in the direction of loss,
	                 // which should only happen with a negative BE_Offset.
	                 return;
	            }
	
	            // --- Apply to Relevant Labels using Safe Helper ---
			    List<string> orderLabels = GetRelevantOrderLabels();
	            if (orderLabels.Count == 0)
	            {
	                 Print($"{Time[0]}: MoveToBreakeven: No relevant order labels found to apply adjustment to.");
	                 return;
	            }

	            Print($"{Time[0]}: MoveToBreakeven: Applying adjustment (Offset: {breakevenTicks:F2} ticks from entry) to labels: {string.Join(", ", orderLabels)}");
			    foreach (string label in orderLabels)
			    {
			        SetTrailingStop(label, CalculationMode.Ticks, breakevenTicks, true);
			    }
	
	            // Mark breakeven as realized if using the flag for logic elsewhere
	            _beRealized = true; // Set flag after successful manual application
	            Print($"{Time[0]}: MoveToBreakeven: Manual Breakeven applied. _beRealized set to true.");
			}

			ForceRefresh(); // Refresh chart UI
		}
		
        #endregion
		
		#region Move Trail Stop 50%
		// Manually moves the active trailing stop closer to the current price by a percentage
		protected void MoveTrailingStopByPercentage(double percentage)
		{
		    Print($"{Time[0]}: MoveTrailingStopByPercentage button clicked. Percentage: {percentage:P1}"); // Log percentage

            // --- Pre-checks ---
		    if (isFlat)
		    {
		        Print($"{Time[0]}: MoveTrailingStopByPercentage: No active position.");
		        return;
		    }
            if (percentage <= 0 || percentage >= 1) // Percentage must be > 0 and < 1
            {
                 Print($"{Time[0]}: MoveTrailingStopByPercentage: Invalid percentage ({percentage:P1}). Must be between 0% and 100%.");
                 return;
            }
             // Ensure trailing is conceptually enabled
            if (!enableTrail && !enableFixedStopLoss)
            {
                 Print($"{Time[0]}: MoveTrailingStopByPercentage: Neither Trailing nor Fixed Stop enabled.");
                 return;
            }

            // --- Get Current State ---
		    double entryPrice = Position.AveragePrice;
            if (entryPrice == 0) { Print($"{Time[0]}: MoveTrailingStopByPercentage: Cannot adjust, entry price is 0."); return; } // Safety check
		    bool isLong = Position.MarketPosition == MarketPosition.Long;
		    double currentMarketPrice = Close[0];

            // --- Determine Current Stop Price ---
            // (Using the same robust logic as AdjustStopLoss)
            double currentStopPrice = 0;
            Order workingStop = Orders.FirstOrDefault(o => o.OrderState == OrderState.Working && o.IsStopMarket);

            if (workingStop != null)
            {
                currentStopPrice = workingStop.StopPrice;
                Print($"{Time[0]}: MoveTrailingStopByPercentage: Found working stop order {workingStop.OrderId} at price {currentStopPrice:F5}.");
            }
            else
            {
                Print($"{Time[0]}: MoveTrailingStopByPercentage: No working stop order found. Inferring current stop level...");
                if (enableFixedStopLoss) { // If fixed stop is primary, adjust that fixed level conceptually
                    currentStopPrice = isLong ? entryPrice - (InitialStop * TickSize) : entryPrice + (InitialStop * TickSize);
                    Print($"{Time[0]}: MoveTrailingStopByPercentage: Inferred from Fixed Stop setting. Current Stop Price: {currentStopPrice:F5}");
                } else if (enableTrail) { // If trailing is primary, infer from current trail calculation
                    double currentTrailTicks = CalculateTrailingStopTicks();
                    currentStopPrice = isLong ? currentMarketPrice - (currentTrailTicks * TickSize) : currentMarketPrice + (currentTrailTicks * TickSize);
                    Print($"{Time[0]}: MoveTrailingStopByPercentage: Inferred from Trailing Stop setting ({currentTrailTicks} ticks). Current Stop Price: {currentStopPrice:F5}");
                } else {
                     Print($"{Time[0]}: MoveTrailingStopByPercentage: Cannot determine current stop price level. Aborting.");
                     return;
                }
            }
			
            if (currentStopPrice == 0) { Print($"{Time[0]}: MoveTrailingStopByPercentage: Could not determine a valid current stop price. Aborting."); return; }

            // --- Calculate New Target Stop Price ---
            double distance = Math.Abs(currentMarketPrice - currentStopPrice);
            double moveAmount = distance * percentage;
            double newTargetStopPrice = isLong
                ? currentStopPrice + moveAmount // Move towards market
                : currentStopPrice - moveAmount; // Move towards market

            Print($"{Time[0]}: MoveTrailingStopByPercentage: Current Stop: {currentStopPrice:F5}, Market: {currentMarketPrice:F5}, Distance: {distance:F5}");
            Print($"{Time[0]}: MoveTrailingStopByPercentage: Move Amount: {moveAmount:F5} ({percentage:P1}), New Target Stop: {newTargetStopPrice:F5}");

            // --- Validate New Stop Price ---
            // Prevent moving stop TO or BEYOND the current market price
            if ((isLong && newTargetStopPrice >= currentMarketPrice) || (!isLong && newTargetStopPrice <= currentMarketPrice))
            {
                Print($"{Time[0]}: MoveTrailingStopByPercentage: Cannot move stop. New target price {newTargetStopPrice:F5} invalid relative to current market price {currentMarketPrice:F5}.");
                return; // Do not proceed
            }

            // --- Calculate Tick Offset for SetTrailStop (from Entry Price) ---
            double breakevenTicks = isLong
		        ? (entryPrice - newTargetStopPrice) / TickSize
		        : (newTargetStopPrice - entryPrice) / TickSize;

            Print($"{Time[0]}: MoveTrailingStopByPercentage: Calculated Tick Offset From Entry: {breakevenTicks:F2}");

            // --- Sanity Check Offset ---
            if (breakevenTicks <= 0)
            {
                 Print($"{Time[0]}: MoveTrailingStopByPercentage: Calculated non-positive breakevenTicks ({breakevenTicks:F2}). Aborting adjustment. Check logic or price validation.");
                 return;
            }

            // --- Apply to Relevant Labels using Safe Helper ---
		    List<string> orderLabels = GetRelevantOrderLabels();
            if (orderLabels.Count == 0)
            {
                 Print($"{Time[0]}: MoveTrailingStopByPercentage: No relevant order labels found to apply adjustment to.");
                 return;
            }

            Print($"{Time[0]}: MoveTrailingStopByPercentage: Applying adjustment (Offset: {breakevenTicks:F2} ticks from entry) to labels: {string.Join(", ", orderLabels)}");
		    foreach (string label in orderLabels)
		    {
		        SetTrailingStop(label, CalculationMode.Ticks, breakevenTicks, true);
		    }

			ForceRefresh(); // Refresh chart UI
		}

        #endregion // End Stop Adjustment region
		
		#region Button Definitions

		private List<ButtonDefinition> buttonDefinitions;

		private class ButtonDefinition
		{
			public string Name { get; set; }
			public string Content { get; set; }
			public string ToolTip { get; set; }
			public Action<KCAlgoBase, System.Windows.Controls.Button> InitialDecoration { get; set; }
			public Action<KCAlgoBase> ClickAction { get; set; } // Action to perform when clicked
		}

		private void InitializeButtonDefinitions()
		{
			buttonDefinitions = new List<ButtonDefinition>
			{
				new ButtonDefinition
				{
				    Name = AutoButton,
				    Content = "\uD83D\uDD12 Auto On",
				    ToolTip = "Enable (Green) / Disbled (Red) Auto Button",
				    InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.isAutoEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Auto On", "\uD83D\uDD13 Auto Off"),
				    ClickAction = (strategy) =>
				    {
				        strategy.isAutoEnabled = !strategy.isAutoEnabled;
				        strategy.isManualEnabled = !strategy.isManualEnabled;
				        strategy.autoDisabledByChop = false; // User took control, clear the system flag
				        strategy.DecorateButton(strategy.autoBtn, strategy.isAutoEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Auto On", "\uD83D\uDD13 Auto Off");
				        strategy.DecorateButton(strategy.manualBtn, strategy.isManualEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Manual On", "\uD83D\uDD13 Manual Off");
				        strategy.Print("Auto Button Clicked. Auto: " + strategy.isAutoEnabled);
				    }
				},
				new ButtonDefinition
				{
				    Name = ManualButton,
				    Content = "\uD83D\uDD12 Manual On",
				    ToolTip = "Enable (Green) / Disbled (Red) Manual Button",
				    InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.isManualEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Manual On", "\uD83D\uDD13 Manual Off"),
				    ClickAction = (strategy) =>
				    {
				        strategy.isManualEnabled = !strategy.isManualEnabled;
				        strategy.isAutoEnabled = !strategy.isAutoEnabled;
				        strategy.autoDisabledByChop = false; // User took control, clear the system flag
				        strategy.DecorateButton(strategy.manualBtn, strategy.isManualEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Manual On", "\uD83D\uDD13 Manual Off");
				        strategy.DecorateButton(strategy.autoBtn, strategy.isAutoEnabled ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 Auto On", "\uD83D\uDD13 Auto Off");
				        strategy.Print("Manual Button Clicked. Manual: " + strategy.isManualEnabled);
				    }
				},				
				new ButtonDefinition
				{
					Name = LongButton,
					Content = "LONG",
					ToolTip = "Enable (Green) / Disbled (Red) Auto Long Entry",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.isLongEnabled ? ButtonState.Enabled : ButtonState.Disabled, "LONG", "LONG Off"),
					ClickAction = (strategy) =>
					{
						strategy.isLongEnabled = !strategy.isLongEnabled;
						strategy.DecorateButton(strategy.longBtn, strategy.isLongEnabled ? ButtonState.Enabled : ButtonState.Disabled, "LONG", "LONG Off");
						strategy.Print("Long Enabled " + strategy.isLongEnabled);
					}
				},
				new ButtonDefinition
				{
					Name = ShortButton,
					Content = "SHORT",
					ToolTip = "Enable (Green) / Disbled (Red) Auto Short Entry",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.isShortEnabled ? ButtonState.Enabled : ButtonState.Disabled, "SHORT", "SHORT Off"),
					ClickAction = (strategy) =>
					{
						strategy.isShortEnabled = !strategy.isShortEnabled;
						strategy.DecorateButton(strategy.shortBtn, strategy.isShortEnabled ? ButtonState.Enabled : ButtonState.Disabled, "SHORT", "SHORT Off");
						strategy.Print("Short Activated " + strategy.isShortEnabled);
					}
				},
				new ButtonDefinition
				{
					Name = QuickLongButton,
					Content = "Buy",
					ToolTip = "Quick Long Entry",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Buy", foreground: Brushes.White, background: Brushes.DarkGreen),
					ClickAction = (strategy) =>
					{
						if (isManualEnabled && strategy.uptrend) // Ensure manual mode is on and trend is favorable
						{
							// Removed strategy.QuickLong = true; - This variable didn't seem to be used elsewhere reliably
							strategy.Print("Quick Long Button Clicked");
							strategy.quickLongBtnActive = true; // Flag that the last entry was manual/quick

                            // Submit the entry order using the helper which calls EnterLong/EnterLongLimit etc.
							strategy.SubmitEntryOrder(QLE, strategy.OrderType, Convert.ToInt32(strategy.Contracts)); // Use QLE label

                            // Submit scale-in orders IF enabled (EnterMultipleLongContracts handles this)
							strategy.EnterMultipleLongContracts(true); // Pass true for isManual/Quick

                            // *** REMOVED STOP LOSS LOGIC FROM HERE ***
                            // The initial stop is now handled ONLY by SetStopLosses called within EnterLongPosition/SubmitEntryOrder

							// strategy.QuickLong = false; // Reset flag if needed, or reset when flat
						}
                        else
                        {
                             strategy.Print($"Quick Long Button: Cannot execute. Manual Enabled: {isManualEnabled}, Uptrend: {strategy.uptrend}");
                        }
					}
				},
				new ButtonDefinition
				{
					Name = QuickShortButton,
					Content = "Sell",
					ToolTip = "Quick Short Entry",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Sell", foreground: Brushes.White, background: Brushes.DarkRed),
					ClickAction = (strategy) =>
					{
						if (isManualEnabled && strategy.downtrend) // Ensure manual mode is on and trend is favorable
						{
                            // Removed strategy.QuickShort = true;
							strategy.Print("Quick Short Button Clicked");
							strategy.quickShortBtnActive = true; // Flag that the last entry was manual/quick

                            // Submit the entry order using the helper
							strategy.SubmitEntryOrder(QSE, strategy.OrderType, Convert.ToInt32(strategy.Contracts)); // Use QSE label

                            // Submit scale-in orders IF enabled
							strategy.EnterMultipleShortContracts(true); // Pass true for isManual/Quick

                            // *** REMOVED STOP LOSS LOGIC FROM HERE ***
                            // The initial stop is now handled ONLY by SetStopLosses called within EnterShortPosition/SubmitEntryOrder

							// strategy.QuickShort = false; // Reset flag if needed, or reset when flat
						}
                        else
                        {
                             strategy.Print($"Quick Short Button: Cannot execute. Manual Enabled: {isManualEnabled}, Downtrend: {strategy.downtrend}");
                        }
					}
				},
				new ButtonDefinition
				{
					Name = Add1Button,
					Content = "Add 1",
					ToolTip = "Add 1 contract to open position",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Add 1", foreground: Brushes.White, background: Brushes.DarkGreen),
					ClickAction = (strategy) => strategy.add1Entry()
				},
				new ButtonDefinition
				{
					Name = Close1Button,
					Content = "Close 1",
					ToolTip = "Close 1 contract from open position",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Close 1", foreground: Brushes.White, background: Brushes.DarkRed),
					ClickAction = (strategy) => strategy.close1Exit()
				},
				new ButtonDefinition
				{
					Name = BEButton,
					Content = "\uD83D\uDD12 BE On",
					ToolTip = "Enable (Green) / Disbled (Red) Auto BE",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.beSetAuto ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 BE On", "\uD83D\uDD13 BE Off"),
					ClickAction = (strategy) =>
					{
						strategy.beSetAuto = !strategy.beSetAuto;
						strategy.DecorateButton(strategy.BEBtn, strategy.beSetAuto ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 BE On", "\uD83D\uDD13 BE Off");
					}
				},
				new ButtonDefinition
				{
					Name = TSButton,
					Content = "\uD83D\uDD12 TS On",
					ToolTip = "Enable (Green) / Disbled (Red) Auto TS",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, strategy.enableTrail ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 TS On", "\uD83D\uDD13 TS Off"),
					ClickAction = (strategy) =>
					{
						strategy.enableTrail = !strategy.enableTrail;
						strategy.DecorateButton(strategy.TSBtn, strategy.enableTrail ? ButtonState.Enabled : ButtonState.Disabled, "\uD83D\uDD12 TS On", "\uD83D\uDD13 TS Off");
					}
				},
				new ButtonDefinition
				{
					Name = MoveTSButton,
					Content = "Move TS",
					ToolTip = "Increase trailing stop",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Move TS", background: Brushes.DarkBlue, foreground: Brushes.Yellow),
					ClickAction = (strategy) =>
					{
						strategy.AdjustStopLoss(strategy.TickMove);
						strategy.ForceRefresh();
					}
				},
				new ButtonDefinition
				{
					Name = MoveTS50PctButton,
					Content = "Move TS 50%",
					ToolTip = "Move trailing stop 50% closer to the current price",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Move TS 50%", background: Brushes.DarkBlue, foreground: Brushes.Yellow),
					ClickAction = (strategy) =>
					{
						strategy.MoveTrailingStopByPercentage(0.5);
						strategy.ForceRefresh();
					}
				},
				new ButtonDefinition
				{
					Name = MoveToBeButton,
					Content = "Breakeven",
					ToolTip = "Move stop to breakeven if in profit",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Breakeven", background: Brushes.DarkBlue, foreground: Brushes.White),
					ClickAction = (strategy) =>
					{
						strategy.MoveToBreakeven();
						strategy.ForceRefresh();
					}
				},
				new ButtonDefinition
				{
					Name = CloseButton,
					Content = "Close All Positions",
					ToolTip = "Manual Close: CloseAllPosiions manually. Alert!!! Only works with the entries made by the strategy. Manual entries will not be closed from this option.",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Close All Positions", background: Brushes.DarkRed, foreground: Brushes.White),
					ClickAction = (strategy) =>
					{
						strategy.CloseAllPositions();
						strategy.ForceRefresh();
					}
				},
				new ButtonDefinition
				{
					Name = PanicButton,
					Content = "\u2620 Panic Shutdown",
					ToolTip = "PanicBtn: CloseAllPosiions",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "\u2620 Panic Shutdown", background: Brushes.DarkRed, foreground: Brushes.Yellow),
					ClickAction = (strategy) =>
					{
						strategy.FlattenAllPositions();
						strategy.ForceRefresh();
					}
				},
				new ButtonDefinition
				{
					Name = DonatePayPalButton,
					Content = "Donate (PayPal)",
					ToolTip = "Support the developer via PayPal",
					InitialDecoration = (strategy, button) => strategy.DecorateButton(button, ButtonState.Neutral, "Donate (PayPal)", background: Brushes.Blue, foreground: Brushes.Yellow),
					ClickAction = (strategy) =>
					{
						strategy.HandlePayPalDonationClick();
					}
				}
			};
		}

		#endregion

		#region Button Decorations

		private enum ButtonState
		{
			Enabled,
			Disabled,
			Neutral
		}

		private void DecorateButton(System.Windows.Controls.Button button, ButtonState state, string contentOn, string contentOff = null, Brush foreground = null, Brush background = null)
		{
			switch (state)
			{
				case ButtonState.Enabled:
					button.Content = contentOn;
					button.Background = background ?? Brushes.DarkGreen;
					button.Foreground = foreground ?? Brushes.White;
					break;
				case ButtonState.Disabled:
					button.Content = contentOff ?? contentOn;
					button.Background = background ?? Brushes.DarkRed;
					button.Foreground = foreground ?? Brushes.White;
					break;
				case ButtonState.Neutral:
					button.Content = contentOn;
					button.Background = background ?? Brushes.LightGray;
					button.Foreground = foreground ?? Brushes.Black;
					break;
			}

			button.BorderBrush = Brushes.Black;
		}

		#endregion		
		
		#region Create WPF Controls
		protected void CreateWPFControls()
		{
			//	ChartWindow
			chartWindow	= System.Windows.Window.GetWindow(ChartControl.Parent) as Gui.Chart.Chart;
			
			// if not added to a chart, do nothing
			if (chartWindow == null)
				return;

			// this is the entire chart trader area grid
			chartTraderGrid			= (chartWindow.FindFirst("ChartWindowChartTraderControl") as Gui.Chart.ChartTrader).Content as System.Windows.Controls.Grid;
			
			// this grid contains the existing chart trader buttons
			chartTraderButtonsGrid	= chartTraderGrid.Children[0] as System.Windows.Controls.Grid;
			
			InitializeButtonDefinitions(); // Initialize the button definitions

			CreateButtons();

			// this grid is to organize stuff below
			lowerButtonsGrid = new System.Windows.Controls.Grid();
			
			// Initialize
    		InitializeButtonGrid();

			addedRow	= new System.Windows.Controls.RowDefinition() { Height = new GridLength(250) };
			
    		// SetButtons
    		SetButtonLocations();

    		// AddButtons
    		AddButtonsToPanel();			
				
			if (TabSelected())
				InsertWPFControls();

			chartWindow.MainTabControl.SelectionChanged += TabChangedHandler;

		}
		#endregion
		
		#region Create Buttons
		protected void CreateButtons()
		{						
			// this style (provided by NinjaTrader_MichaelM) gives the correct default minwidth (and colors) to make buttons appear like chart trader buttons
			Style basicButtonStyle	= System.Windows.Application.Current.FindResource("BasicEntryButton") as Style;			
	
			manualBtn = CreateButton(ManualButton, basicButtonStyle);
			autoBtn = CreateButton(AutoButton, basicButtonStyle);
			longBtn = CreateButton(LongButton, basicButtonStyle);
			shortBtn = CreateButton(ShortButton, basicButtonStyle);
			quickLongBtn = CreateButton(QuickLongButton, basicButtonStyle);
			quickShortBtn = CreateButton(QuickShortButton, basicButtonStyle);
			BEBtn = CreateButton(BEButton, basicButtonStyle);
			TSBtn = CreateButton(TSButton, basicButtonStyle);
			moveTSBtn = CreateButton(MoveTSButton, basicButtonStyle);
			moveTS50PctBtn = CreateButton(MoveTS50PctButton, basicButtonStyle);
			moveToBEBtn = CreateButton(MoveToBeButton, basicButtonStyle);
			add1Btn = CreateButton(Add1Button, basicButtonStyle);
			close1Btn = CreateButton(Close1Button, basicButtonStyle);
			closeBtn = CreateButton(CloseButton, basicButtonStyle);
			panicBtn = CreateButton(PanicButton, basicButtonStyle);
			donatePayPalBtn = CreateButton(DonatePayPalButton, basicButtonStyle);
		}

		private System.Windows.Controls.Button CreateButton(string buttonName, Style basicButtonStyle)
		{
			var definition = buttonDefinitions.FirstOrDefault(b => b.Name == buttonName);
			if (definition == null)
			{
				Print($"Error: Button definition not found for {buttonName}");
				return null; // Or throw an exception
			}

			var button = new System.Windows.Controls.Button
			{
				Name = buttonName,
				Height = 25,
				Margin = new Thickness(1, 0, 1, 0),
				Padding = new Thickness(0, 0, 0, 0),
				Style = basicButtonStyle,
				BorderThickness = new Thickness(1.5),
				IsEnabled = true,
				ToolTip = definition.ToolTip,
			};

			definition.InitialDecoration?.Invoke(this, button); // Apply initial decoration
			button.Click += OnButtonClick; // All buttons use the same click handler

			return button;
		}
		
		protected void InitializeButtonGrid()
		{
    		// Create new grid
    		lowerButtonsGrid = new System.Windows.Controls.Grid();

    		// Columns number
    		for (int i = 0; i < 2; i++)
    		{
        		lowerButtonsGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
    		}

    		// Row number
    		for (int i = 0; i <= 10; i++)
    		{
        		lowerButtonsGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
    		}
		}				

		protected void SetButtonLocations()
		{
			// Btn, Column, Row, Column span
			
    		SetButtonLocation(manualBtn, 0, 1);    // Column 0 2 pos
    		SetButtonLocation(autoBtn, 1, 1);    			
    		SetButtonLocation(longBtn, 0, 2);
    		SetButtonLocation(shortBtn, 1, 2);
   			SetButtonLocation(quickLongBtn, 0, 3);
    		SetButtonLocation(quickShortBtn, 1, 3);    	
   			SetButtonLocation(add1Btn, 0, 4);
    		SetButtonLocation(close1Btn, 1, 4);    		
   			SetButtonLocation(BEBtn, 0, 5);
    		SetButtonLocation(TSBtn, 1, 5); 
		    SetButtonLocation(moveTSBtn, 0, 6);
    		SetButtonLocation(moveTS50PctBtn, 1, 6);  
   			SetButtonLocation(moveToBEBtn, 0, 7, 2);
			SetButtonLocation(closeBtn, 0, 8, 2);
			SetButtonLocation(panicBtn, 0, 9, 2);			
			SetButtonLocation(donatePayPalBtn, 0, 10, 2);
		}		
		
		protected void SetButtonLocation(System.Windows.Controls.Button button, int column, int row, int columnSpan = 1)
		{
    		System.Windows.Controls.Grid.SetColumn(button, column);
    		System.Windows.Controls.Grid.SetRow(button, row);
    
   			if (columnSpan > 1)
        		System.Windows.Controls.Grid.SetColumnSpan(button, columnSpan);
		}		
		
		protected void AddButtonsToPanel()
		{
    		// Add Buttons to grid
    		lowerButtonsGrid.Children.Add(manualBtn);
    		lowerButtonsGrid.Children.Add(autoBtn);
    		lowerButtonsGrid.Children.Add(longBtn);
    		lowerButtonsGrid.Children.Add(shortBtn);
    		lowerButtonsGrid.Children.Add(quickLongBtn);
    		lowerButtonsGrid.Children.Add(quickShortBtn);
    		lowerButtonsGrid.Children.Add(add1Btn);
    		lowerButtonsGrid.Children.Add(close1Btn);
    		lowerButtonsGrid.Children.Add(BEBtn);
    		lowerButtonsGrid.Children.Add(TSBtn);  
    		lowerButtonsGrid.Children.Add(moveTSBtn);  
    		lowerButtonsGrid.Children.Add(moveTS50PctBtn);  
    		lowerButtonsGrid.Children.Add(moveToBEBtn);    
			lowerButtonsGrid.Children.Add(closeBtn);
			lowerButtonsGrid.Children.Add(panicBtn);			
			lowerButtonsGrid.Children.Add(donatePayPalBtn);
		}			
		#endregion
		
		#region Buttons Click Events
		
		protected void OnButtonClick(object sender, RoutedEventArgs rea)
		{
			Button button = sender as Button;

			var definition = buttonDefinitions.FirstOrDefault(b => b.Name == button.Name);
			if (definition != null)
			{
				definition.ClickAction?.Invoke(this);
			}
			else
			{
				Print($"Error: No click action defined for button {button.Name}");
			}
		}
		
		#endregion
		       
		#region Dispose
		protected void DisposeWPFControls() 
		{
			if (chartWindow != null)
			chartWindow.MainTabControl.SelectionChanged -= TabChangedHandler;

			//Unsubscribe from all button click events
			UnsubscribeButtonClick(manualBtn);
			UnsubscribeButtonClick(autoBtn);
			UnsubscribeButtonClick(longBtn);
			UnsubscribeButtonClick(shortBtn);
			UnsubscribeButtonClick(quickLongBtn);
			UnsubscribeButtonClick(quickShortBtn);
			UnsubscribeButtonClick(add1Btn);
			UnsubscribeButtonClick(close1Btn);
			UnsubscribeButtonClick(BEBtn);
			UnsubscribeButtonClick(TSBtn);
			UnsubscribeButtonClick(moveTSBtn);
			UnsubscribeButtonClick(moveTS50PctBtn);
			UnsubscribeButtonClick(moveToBEBtn);
			UnsubscribeButtonClick(closeBtn);
			UnsubscribeButtonClick(panicBtn);
			UnsubscribeButtonClick(donatePayPalBtn);
	
			RemoveWPFControls();
		}

		private void UnsubscribeButtonClick(Button button)
		{
			if (button != null)
			{
				button.Click -= OnButtonClick;
			}
		}
		#endregion
		
		#region Insert WPF
		public void InsertWPFControls()
		{
			if (panelActive)
				return;
			
			// add a new row (addedRow) for our lowerButtonsGrid below the ask and bid prices and pnl display			
			chartTraderGrid.RowDefinitions.Add(addedRow);
			System.Windows.Controls.Grid.SetRow(lowerButtonsGrid, (chartTraderGrid.RowDefinitions.Count - 1));
			chartTraderGrid.Children.Add(lowerButtonsGrid);

			panelActive = true;
		}
		#endregion
		
		#region Remove WPF
		protected void RemoveWPFControls()
		{
			if (!panelActive)
				return;
			
			if (chartTraderButtonsGrid != null || lowerButtonsGrid != null)
			{
				chartTraderGrid.Children.Remove(lowerButtonsGrid);
				chartTraderGrid.RowDefinitions.Remove(addedRow);
			}

			panelActive = false;
		}
		#endregion
		
		#region TabSelcected 
		protected bool TabSelected()
		{
			bool tabSelected = false;

			// loop through each tab and see if the tab this indicator is added to is the selected item
			foreach (System.Windows.Controls.TabItem tab in chartWindow.MainTabControl.Items)
				if ((tab.Content as Gui.Chart.ChartTab).ChartControl == ChartControl && tab == chartWindow.MainTabControl.SelectedItem)
					tabSelected = true;

			return tabSelected;
		}
		#endregion
		
		#region TabHandler
		protected void TabChangedHandler(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
		{
			if (e.AddedItems.Count <= 0)
				return;

			tabItem = e.AddedItems[0] as System.Windows.Controls.TabItem;
			if (tabItem == null)
				return;

			chartTab = tabItem.Content as Gui.Chart.ChartTab;
			if (chartTab == null)
				return;

			if (TabSelected())
				InsertWPFControls();
			else
				RemoveWPFControls();
		}		
		#endregion	

		#region Close All Positions
		protected void CloseAllPositions()
		{
		//	Close actual position manually
        //	Check if there is an open position
			Print("Position Closing");
			
			if(isLong) 
			{
				// Create the order labels array based on whether additional contracts exist
		        string[] orderLabels = additionalContractExists ? new[] { LE1, LE2, LE3, LE4, QLE, "QLE2", "QLE3", "QLE4" } : new[] { LE1 };
		
		        // Apply the initial trailing stop for all relevant orders
		        foreach (string label in orderLabels)
		        {
		            ExitLong("Manual Exit", label);
		        }
			}
			else if(isShort) 
			{
				// Create the order labels array based on whether additional contracts exist
		        string[] orderLabels = additionalContractExists ? new[] { SE1, SE2, SE3, SE4, QSE, "QSE2", "QSE3", "QSE4" } : new[] { SE1 };
		
		        // Apply the initial trailing stop for all relevant orders
		        foreach (string label in orderLabels)
		        {
		            ExitShort("Manual Exit", label);
		        }
			}		
		}	
		
        protected void FlattenAllPositions()
        {
			//  Access the open position
        	Position openPosition = Position;
			Account myAccount;
			AccountSelector accountSelector = Extensions.FindFirst(Window.GetWindow(ChartControl.Parent), "ChartTraderControlAccountSelector") as AccountSelector;
			this.chartTraderAccount = ((accountSelector != null) ? accountSelector.SelectedAccount : null);
			this.accountSelector = ((accountSelector != null) ? accountSelector : null);
			
			// Get the account (replace "Sim101" with your actual account name)
            myAccount = Account.All.FirstOrDefault((Account a) => a.Name == this.chartTraderAccount.DisplayName);
			Print("Account selected: " + this.chartTraderAccount.DisplayName);
            if (myAccount == null) Print("Account selected: " + this.chartTraderAccount.DisplayName + " Account not found !!!");
			if (myAccount == null)
			     throw new Exception("Account not found.");
			
        	if (openPosition != null && openPosition.MarketPosition != MarketPosition.Flat)
        	{
			// Less drastic method, we make a Flatten All to the account used in the strategy and to the instrument that we have loaded on the chart
				List<Instrument> instrumentNames = new List<Instrument>();
				foreach (Position position in this.chartTraderAccount.Positions)
	            {
	              Instrument instrument = position.Instrument;
	              if (!instrumentNames.Contains(instrument))
	                instrumentNames.Add(instrument);
	            }
	            this.chartTraderAccount.Flatten((ICollection<Instrument>) instrumentNames);
        	}		
		}
		
		protected void HandlePayPalDonationClick()
		{
			Print("Donate (PayPal) button clicked."); // Log the click

		    // Check if the URL parameter has been set
		    if (string.IsNullOrWhiteSpace(paypal))
		    {
		        Print("PayPal Donation URL is not configured in strategy parameters.");
		        // Optionally show a message box to the user (requires adding `using System.Windows;`)
		        // MessageBox.Show("PayPal Donation URL is not configured in the strategy parameters.", "Missing URL", MessageBoxButton.OK, MessageBoxImage.Warning);
		        return; // Exit if no URL is set
		    }
		
		    try
		    {
		        // Use Process.Start to open the URL in the default browser
		        System.Diagnostics.Process.Start(paypal);
		        Print($"Attempting to open PayPal URL: {paypal}");
		    }
		    catch (Exception ex)
		    {
		        // Handle potential errors (e.g., invalid URL format, OS permissions)
		        Print($"Error opening PayPal URL '{paypal}': {ex.Message}");
		        // Optionally show a message box to the user
		        // MessageBox.Show($"Could not open the PayPal donation link.\nURL: {paypal}\nError: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
		    }
		}

		protected void add1Entry()
		{
		    int oneContract = 1;
		    Position openPosition = Position;
		    if (openPosition == null || openPosition.MarketPosition == MarketPosition.Flat)
		    {
		        Print($"{Time[0]}: Add1: No open position.");
		        return;
		    }
		
		    double currentPositionQty = openPosition.Quantity;
		    if (currentPositionQty + oneContract > EntriesPerDirection)
		    {
		         Print($"{Time[0]}: Add1: Cannot add contract, would exceed EntriesPerDirection limit ({EntriesPerDirection}).");
		         return;
		    }
		
		    try
		    {
		        if (isLong && uptrend) // Consider adding more checks if needed
		        {
		            string addLabel = quickLongBtnActive ? Add1LE : Add1LE; // Or maybe keep LE/QLE if truly intended? Decide based on desired stop/target behavior.
		            Print($"{Time[0]}: Adding 1 Long contract with label {addLabel}.");
		            // SubmitEntryOrder(addLabel, OrderType, oneContract); // Use the helper
		             EnterLong(oneContract, addLabel); // Or directly if SubmitEntryOrder is only for initial entries
		            // Q: Does this added contract need its own stop/target or share the main one?
		            // If sharing, no SetStop/SetTarget needed here. If separate, add calls here.
		        }
		        else if (isShort && downtrend) // Consider adding more checks
		        {
		             string addLabel = quickShortBtnActive ? Add1SE : Add1SE; // Or maybe SE/QSE?
		             Print($"{Time[0]}: Adding 1 Short contract with label {addLabel}.");
		             // SubmitEntryOrder(addLabel, OrderType, oneContract);
		              EnterShort(oneContract, addLabel);
		             // Add stop/target if needed for this specific contract
		        }
		        else
		        {
		             Print($"{Time[0]}: Add1: Cannot add contract. Position direction/trend mismatch (IsLong: {isLong}, Uptrend: {uptrend}, IsShort: {isShort}, Downtrend: {downtrend}).");
		        }
		    }
		    catch (Exception ex)
		    {
		        Print($"{Time[0]}: Failed to add contract due to: {ex.Message}");
		        orderErrorOccurred = true;
		    }
		}

		protected void close1Exit()
		{
			// Print("Close 1 button clicked."); // Logging handled in OnButtonClick
			int oneContract = 1;

        	if (Position.MarketPosition != MarketPosition.Flat) // Check if position exists
        	{
                if (Position.Quantity >= oneContract) // Check if there's at least one contract to close
                {
				    CloseOneContractFromPosition(); // Call the corrected method
                }
                else
                {
                    Print($"Cannot close {oneContract} contract. Position quantity ({Position.Quantity}) is less than requested.");
                }
			}
		}	
		
		protected void CloseOneContractFromPosition()
		{
		 	int contractsToClose = 1;
		    try
		    {
                // Check position state again for safety just before submitting
				if (Position.MarketPosition == MarketPosition.Long && Position.Quantity >= contractsToClose)
                {
                    Print($"{Time[0]}: Submitting request to close {contractsToClose} long contract(s) (FIFO).");
                    // Exit 1 long contract using FIFO logic by providing quantity and NO specific fromEntrySignal
                    ExitLong(contractsToClose, ManualClose1, ""); // "" or null for fromEntrySignal uses FIFO
                }
                else if (Position.MarketPosition == MarketPosition.Short && Position.Quantity >= contractsToClose)
                {
                     Print($"{Time[0]}: Submitting request to close {contractsToClose} short contract(s) (FIFO).");
                    // Exit 1 short contract using FIFO logic
                    ExitShort(contractsToClose, ManualClose1, ""); // "" or null for fromEntrySignal uses FIFO
                }
                else if (Position.Quantity < contractsToClose)
                {
                     Print($"{Time[0]}: Cannot close {contractsToClose} contract(s). Position quantity ({Position.Quantity}) is less than requested.");
                }
                else // Position is Flat or Direction unknown
                {
                    Print($"{Time[0]}: No position exists or direction unknown. Cannot close {contractsToClose} contract(s).");
                }
		    }
		    catch (Exception ex)
		    {
		        Print($"{Time[0]}: Failed to submit request to close {contractsToClose} contract(s) due to: {ex.Message}");
                orderErrorOccurred = true; // Flag error
		    }
		}
		
		protected void AddContractToOpenPosition()
		{   // Add 1
			int oneContract = 1;
		    try
		    {
				if(isLong && uptrend) {
					if (!quickLongBtnActive)
					{	
						EnterLong(oneContract, @LE1);	
//						EnterMultipleLongContracts(false);
					}
					if (quickLongBtnActive)
					{	
						EnterLong(oneContract, @QLE);
//						EnterMultipleLongContracts(true);
						
					}					
				
				}else if(isShort && downtrend) {
					if (!quickShortBtnActive)
					{	
						EnterShort(oneContract, @SE1);
//						EnterMultipleShortContracts(false);
						
					//	if(OrderType == OrderType.Market) EnterShort(oneContract, @SE1);
					//	if(!OrderType == OrderType.Market) EnterShortLimit(oneContract, GetCurrentAsk(0), @SE1);	
					}	
					if (quickShortBtnActive)
					{	
						EnterShort(oneContract, @QSE);
//						EnterMultipleShortContracts(true);
						
					//	if(OrderType == OrderType.Market) EnterShort(oneContract, @QSE);
					//	if(!OrderType == OrderType.Market) EnterShortLimit(oneContract, GetCurrentAsk(0), @QSE);
					}						
				}	
		        else {
		            Print("No open position to close contracts from.");
		        }
		    }
		    catch (Exception ex)
		    {
		        Print($"Failed to add contracts due to: {ex.Message}");
		    }
		}
		
		protected void checkPositions()
		{
		//	Detect unwanted Positions opened (possible rogue Order?)
	        double currentPosition = Position.Quantity; // Get current position quantity
		
			if (isFlat)
			{
		        foreach (var order in Orders)
		        {
		            if (order != null) CancelOrder(order);
		        }				
			}
		}	
		
		protected void checkOrder()
		{
		// Verify one active order and set myStopPrice and mylimitPrice to be used in changing orders when add or close 1 contracts to open positions
			activeOrder = false;
			
			if (Orders.Count != 0)
			{
				Print($"{Times[0][0].TimeOfDay} ACTIVE Orders Count:  {Orders.Count}");
				foreach (var order in Orders)
		        {
					string entrySignal = order.FromEntrySignal;
					Print($"{Times[0][0].TimeOfDay} myOrder NOT null {order.OrderId}  StopPrice:  {order.StopPrice}   LimitPrice  {order.LimitPrice}    orderQuantity {order.Quantity}   tiene el estado: {order.OrderState}  y es del tipo {order.OrderTypeString}    FROM EntrySignal {entrySignal}");
		            // Verificar el estado de cada orden
					if (order.OrderState == OrderState.Filled)
		            {
		                myEntryOrder = order;
						if (order.IsStopMarket && entrySignal != "Add 1")
						{
							myStopOrder = order;
							myStopPrice = myStopOrder.StopPrice;
						}	
						if (order.IsLimit &&  entrySignal != "Add 1") 
						{
							myLimitPrice = myEntryOrder.LimitPrice;
							
						}	
		            }					
					else if (order.OrderState == OrderState.TriggerPending && entrySignal != "Add 1")
		            {
		                if (order.IsStopMarket)
						{
							myStopOrder = order;
							myStopPrice = myStopOrder.StopPrice;
						}
		            }
					else if (order.OrderState == OrderState.Working && entrySignal != "Add 1")
		            {						
						if (order.IsLimit)
						{ 
							myTargetOrder = order;
							myLimitPrice = myTargetOrder.LimitPrice;	
						}	
		            }					
		            else
		            {
		                Print("La orden " + order.OrderId + " tiene el estado: " + order.OrderState);
		            }							
		        }
				Print($"{Times[0][0].TimeOfDay} myEntryOrder NOT null {myEntryOrder.OrderId}  StopPrice:  {myEntryOrder.StopPrice}   LimitPrice  {myEntryOrder.LimitPrice}    orderQuantity {myEntryOrder.Quantity}   tiene el estado: {myEntryOrder.OrderState}  y es del tipo {myEntryOrder.OrderTypeString}");
				activeOrder = true;
			}
		}
		
		protected bool checkTimers()
		{
		//	check we are in timer	
			if((Times[0][0].TimeOfDay >= Start.TimeOfDay) && (Times[0][0].TimeOfDay < End.TimeOfDay) 
					|| (Time2 && Times[0][0].TimeOfDay >= Start2.TimeOfDay && Times[0][0].TimeOfDay <= End2.TimeOfDay)
					|| (Time3 && Times[0][0].TimeOfDay >= Start3.TimeOfDay && Times[0][0].TimeOfDay <= End3.TimeOfDay)
					|| (Time4 && Times[0][0].TimeOfDay >= Start4.TimeOfDay && Times[0][0].TimeOfDay <= End4.TimeOfDay)
					|| (Time5 && Times[0][0].TimeOfDay >= Start5.TimeOfDay && Times[0][0].TimeOfDay <= End5.TimeOfDay)
					|| (Time6 && Times[0][0].TimeOfDay >= Start6.TimeOfDay && Times[0][0].TimeOfDay <= End6.TimeOfDay)
			)
			{
				return true;
			}
			else
			{
				return false;
			}			
		}
		
		protected string GetActiveTimer()
		{
		//	check active timer	
		    TimeSpan currentTime = Times[0][0].TimeOfDay;
		
		    if ((Times[0][0].TimeOfDay >= Start.TimeOfDay) && (Times[0][0].TimeOfDay < End.TimeOfDay))
		    {
		        return $"{Start:HH\\:mm} - {End:HH\\:mm}";
		    }
		    else if (Time2 && Times[0][0].TimeOfDay >= Start2.TimeOfDay && Times[0][0].TimeOfDay <= End2.TimeOfDay)
		    {
		        return $"{Start2:HH\\:mm} - {End2:HH\\:mm}";
		    }
		    else if (Time3 && Times[0][0].TimeOfDay >= Start3.TimeOfDay && Times[0][0].TimeOfDay <= End3.TimeOfDay)
		    {
		        return $"{Start3:HH\\:mm} - {End3:HH\\:mm}";
		    }
		    else if (Time4 && Times[0][0].TimeOfDay >= Start4.TimeOfDay && Times[0][0].TimeOfDay <= End4.TimeOfDay)
		    {
		        return $"{Start4:HH\\:mm} - {End4:HH\\:mm}";
		    }
		    else if (Time5 && Times[0][0].TimeOfDay >= Start5.TimeOfDay && Times[0][0].TimeOfDay <= End5.TimeOfDay)
		    {
		        return $"{Start5:HH\\:mm} - {End5:HH\\:mm}";
		    }
		    else if (Time6 && Times[0][0].TimeOfDay >= Start6.TimeOfDay && Times[0][0].TimeOfDay <= End6.TimeOfDay)
		    {
		        return $"{Start6:HH\\:mm} - {End6:HH\\:mm}";
		    }
		
		    return "No active timer";
		}
		
		#endregion				
		
		#region DrawPnl
		protected void ShowPNLStatus() {
			textLine0 = "Active Timer";
			textLine1 = GetActiveTimer();
			textLine2 = "Long Per Direction";
			textLine3 = $"{counterLong} / {longPerDirection} | " + (TradesPerDirection ? "On" : "Off");
			textLine4 = "Short Per Direction";
			textLine5 = $"{counterShort} / {shortPerDirection} | " + (TradesPerDirection ? "On" : "Off");
			textLine6 = "Bars Since Exit ";
			textLine7 = $"{iBarsSinceExit}    |    " + (iBarsSinceExit > 1 ?  "On" : "Off");
			string statusPnlText = textLine0 + "\t" + textLine1 + "\n" + textLine2 + "  " + textLine3 + "\n" + textLine4 + "  " + textLine5+ "\n" + textLine6 + "\t";
			SimpleFont font = new SimpleFont("Arial", 16);
			
			Draw.TextFixed(this, "statusPnl", statusPnlText, PositionPnl, colorPnl, font, Brushes.Transparent, Brushes.Transparent, 0);
								
		}
		#endregion			
		
		#region Discord Signal
		private async Task SendSignalToDiscordAsync(string direction, double entryPrice, double stopLoss, double profitTarget, DateTime entryTime)
		{
		    try
		    {
		        // Check rate limit
		        if (DateTime.Now - lastDiscordMessageTime < discordRateLimitInterval)
		        {
		            Print("Skipping Discord signal due to rate limit.");
		            return;
		        }
		
		        // Update the last sent time
		        lastDiscordMessageTime = DateTime.Now;
		
		        // Create the embed message for Discord
		        var fields = new List<object>
		        {
		            new { name = "Direction", value = direction, inline = true },
		            new { name = "Entry Price", value = entryPrice.ToString("F2"), inline = true },
		            new { name = "Stop Loss", value = stopLoss.ToString("F2"), inline = true },
		            new { name = "Profit Target", value = profitTarget.ToString("F2"), inline = true },
		            new { name = "Time", value = entryTime.ToString("HH:mm:ss"), inline = false }
		        };
		
		        var embed = new
		        {
		            title = $"Trade Signal: {direction}",
		            color = direction.Contains("LONG") ? 3066993 : 15158332, // Green for long, Red for short
		            fields = fields
		        };
		
		        using (var client = new HttpClient())
		        {
		            var payload = new { username = "Trading Bot", embeds = new[] { embed } };
		            var json = new JavaScriptSerializer().Serialize(payload);
		            var content = new StringContent(json, Encoding.UTF8, "application/json");
		
		            var webhookUrl = DiscordWebhooks;
		
		            var response = await client.PostAsync(webhookUrl, content);
		
		            if (response.IsSuccessStatusCode)
		            {
		                Print($"Discord Signal sent: {direction} - Time: {entryTime:HH:mm:ss}");
		            }
		            else
		            {
		                Print($"Discord Signal failed: {response.StatusCode} {response.ReasonPhrase}");
		            }
		        }
		    }
		    catch (Exception ex)
		    {
		        Print($"Error sending Discord Signal: {ex.Message}");
		    }
		}		
		#endregion		
		
		#region Entry Signals & Inits
		
		protected abstract bool ValidateEntryLong(); 
        	
		// protected abstract bool CheckLongEntryConditions();	
		
        protected abstract bool ValidateEntryShort();

		// protected abstract bool CheckShortEntryConditions();	
		
        protected virtual bool ValidateExitLong() {
			return false;
		}

        protected virtual bool ValidateExitShort() {
			return false;
		}
		
		protected abstract void InitializeIndicators();		
		
		protected virtual void addDataSeries() {}
		
		#endregion
		
		#region Daily PNL
		
		protected override void OnPositionUpdate(Cbi.Position position, double averagePrice, 
			int quantity, Cbi.MarketPosition marketPosition)
		{			
			if (isFlat && SystemPerformance.AllTrades.Count > 0)
			{
//				PositionPnl = TextPosition.BottomLeft;
//				totalPnL = 0; //backtest
			
				totalPnL = SystemPerformance.RealTimeTrades.TradesPerformance.Currency.CumProfit; ///Double that sets the total PnL 
				dailyPnL = (totalPnL) - (cumPnL); ///Your daily limit is the difference between these
				
				// Re-enable the strategy if it was disabled by the DD and totalPnL increases
				if (enableTrailingDD && trailingDrawdownReached && totalPnL > maxProfit - TrailingDrawdown)
	            {
	                trailingDrawdownReached = false;
					isAutoEnabled = true;
					Print("Trailing Drawdown Lifted. Strategy Re-Enabled!");
				}
	
				
				if (dailyPnL <= -DailyLossLimit) //Print this when daily Pnl is under Loss Limit
				{
					Print("Daily Loss of " + DailyLossLimit +  " has been hit. No More Entries! Daily PnL >> " + dailyPnL + " <<" +  Time[0]);
					
					Text myTextLoss = Draw.TextFixed(this, "loss_text", "Daily Loss of " + DailyLossLimit +  " has been hit. No More Entries! Daily PnL >> " + "$" + totalPnL + " <<", PositionDailyPNL, colorDailyProfitLoss, ChartControl.Properties.LabelFont, Brushes.Transparent, Brushes.Transparent, 100);
					myTextLoss.Font = new SimpleFont("Arial", 18) {Bold = true };
				}				
				
				if (dailyPnL >= DailyProfitLimit) //Print this when daily Pnl is above Profit limit
				{
					
					Print("Daily Profit of " + DailyProfitLimit +  " has been hit. No more Entries! Daily PnL >>" +  dailyPnL + " <<" + Time[0]);
					
					Text myTextProfit = Draw.TextFixed(this, "profit_text", "Daily Profit of " + DailyProfitLimit +  " has been hit. No more Entries! Daily PnL >>" + "$" +  totalPnL + " <<", PositionDailyPNL, colorDailyProfitLoss, ChartControl.Properties.LabelFont, Brushes.Transparent, Brushes.Transparent, 100);
					myTextProfit.Font = new SimpleFont("Arial", 18) {Bold = true };	
				}
			}	
			
			if (isFlat)	checkPositions(); // Detect unwanted Positions opened (possible rogue Order?)						
		}
		
		#endregion	
		
		protected override void OnRender(ChartControl chartControl, ChartScale chartScale)
		{
			base.OnRender(chartControl, chartScale);
			if (showDailyPnl) DrawStrategyPnl(chartControl);
		}

		protected void DrawStrategyPnl(ChartControl chartControl)
		{
		    if (Account == null) return; // Added safety check for connection
		
		    // ... (Get account PnL logic remains the same) ...
		    double accountRealized = (State == State.Realtime) ? Account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar) : SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
		    double accountUnrealized = (State == State.Realtime) ? Account.Get(AccountItem.UnrealizedProfitLoss, Currency.UsDollar) : (Position != null && Position.MarketPosition != MarketPosition.Flat ? Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]) : 0); // Safety for Position
		    double accountTotal = accountRealized + accountUnrealized;
		    dailyPnL = accountTotal - cumPnL; // Assuming cumPnL is correctly managed elsewhere
		    if (accountTotal > maxProfit) maxProfit = accountTotal;
		
		    // --- Determine Trend and Signal Strings ---
		    // Need to ensure these conditions don't themselves cause issues if called too early
		    // But typically OnBarUpdate handles the main indicator calculations first.
		    string trendStatus = uptrend ? "Up" : downtrend ? "Down" : "Neutral";
		    string signalStatus = "No Signal"; // Default

		    // These checks rely on flags set in OnBarUpdate, which should be safe if OnBarUpdate handled warm-up
		    if (IsLongEntryConditionMet()) signalStatus = "Long Ready";
		    else if (IsShortEntryConditionMet()) signalStatus = "Short Ready";
		
		    // --- Apply Overrides based on State ---
		    if (!isFlat) signalStatus = "In Position";
			
			if (EnableChoppinessDetection)
			{
			    if (!isAutoEnabled && !autoDisabledByChop) signalStatus = "Auto OFF (Manual)"; // User turned it off
			
				if (marketIsChoppy) // Choppiness override (applies even if Auto is OFF manually)
			    {
			        trendStatus = "Choppy";
			        signalStatus = "No Trade (Chop)";
			    }
			     if (!isAutoEnabled && autoDisabledByChop) signalStatus = "Auto OFF (Chop)"; // System turned it off due to chop
			}
			
		    // Other overrides (higher priority?)
		    if (!checkTimers()) signalStatus = "Outside Hours";
		    if (orderErrorOccurred) signalStatus = "Order Error!";
		    if (enableTrailingDD && trailingDrawdownReached) signalStatus = "DD Limit Hit";
		    if (dailyLossProfit && dailyPnL <= -DailyLossLimit) signalStatus = "Loss Limit Hit";
		    if (dailyLossProfit && dailyPnL >= DailyProfitLimit) signalStatus = "Profit Limit Hit";
		
		    string pnlSource = (State == State.Realtime) ? "Account" : "System";
		    // Added null check for Account.Connection
		    string connectionStatus = (Account.Connection != null) ? Account.Connection.Status.ToString() : "N/A";
		
		    // --- FIXED INDICATOR VALUE DISPLAY ---
		    // Instead of IsValidDataPoint, check if CurrentBar is sufficient for the indicator's period.
		    // This prevents displaying default values (like 0) during the initial strategy warm-up.
		
		    // Get the period for Momentum1 (it was hardcoded 14 during initialization)
		    // If Momentum1 instance exists and has a Period property, use that, otherwise default to known value
		    int momentumPeriod = (Momentum1 != null ? Momentum1.Period : 14);
		    // BuySellPressure readiness check - using BarsRequiredToTrade as proxy
		    // Assuming BuySellPressure1 needs at least BarsRequiredToTrade bars.
		    int buySellPressureRequiredBars = BarsRequiredToTrade; // Or specific period if known for BuySellPressure
		
		    // Check CurrentBar against the required period (0-based index means CurrentBar >= Period - 1)
		    string adxText = CurrentBar >= adxPeriod - 1 ? currentAdx.ToString("F1") : "N/A";
		    string momoText = CurrentBar >= momentumPeriod - 1 ? momentum.ToString("F1") : "N/A";
		    // Assuming buyPressure/sellPressure series are populated when BuySellPressure1 is calculated in OnBarUpdate
		    string buyPressText = CurrentBar >= buySellPressureRequiredBars - 1 ? buyPressure[0].ToString("F1") : "N/A";
		    string sellPressText = CurrentBar >= buySellPressureRequiredBars - 1 ? sellPressure[0].ToString("F1") : "N/A";
		    // --- END FIXED INDICATOR VALUE DISPLAY ---

		
		    string realTimeTradeText =
		        $"{Account.Name} | {(Account.Connection != null ? Account.Connection.Options.Name : "N/A")} ({connectionStatus})\n" +
		        $"PnL Src: {pnlSource}\n" +
		        $"Real PnL:\t{accountRealized:C}\n" +
		        $"Unreal PnL:\t{accountUnrealized:C}\n" +
		        $"Total PnL:\t{accountTotal:C}\n" +
		        $"Daily PnL:\t{dailyPnL:C}\n" +
		        $"Max Profit:\t{(maxProfit == double.MinValue ? "N/A" : maxProfit.ToString("C"))}\n" +
		        $"-------------\n" +
		        $"ADX:\t\t{adxText}\n" +             // Use safe text
		        $"Momentum:\t{momoText}\n" +           // Use safe text
		        $"Buy Pressure:\t{buyPressText}\n" +   // Use safe text
		        $"Sell Pressure:\t{sellPressText}\n" +  // Use safe text
		        $"-------------\n" +
		        $"Trend:\t{trendStatus}\n" +      // Use overridden status
		        $"Signal:\t{signalStatus}";       // Use overridden status

		    SimpleFont font = new SimpleFont("Arial", 16);
		    Brush pnlColor = accountTotal == 0 ? Brushes.Cyan : accountTotal > 0 ? Brushes.Lime : Brushes.Pink;
		
		    try
		    {
		        // Ensure ChartControl and other UI elements are available before drawing
		        if (chartControl != null)
		        {
		             Draw.TextFixed(this, "realTimeTradeText", realTimeTradeText, PositionDailyPNL, pnlColor, font, Brushes.Transparent, Brushes.Transparent, 0);
		        }
		    }
		    catch (Exception ex) { Print($"Error drawing PNL display: {ex.Message}"); }
		}

		#region KillSwitch
		protected void KillSwitch()
		{
		    totalPnL = SystemPerformance.RealTimeTrades.TradesPerformance.Currency.CumProfit;
		    dailyPnL = totalPnL + Account.Get(AccountItem.UnrealizedProfitLoss, Currency.UsDollar);
		
		    // Track the Highest Profit Achieved
		    if (totalPnL > maxProfit)
		    {
		        maxProfit = totalPnL;
		    }
		
		    // Determine all relevant order labels
		    List<string> longOrderLabels = new List<string> { LE1, QLE }; // Base Labels for Longs
		    List<string> shortOrderLabels = new List<string> { SE1, QSE }; // Base Labels for Shorts
		
		    if (EnableProfitTarget2)
		    {
		        longOrderLabels.AddRange(new[] { LE2, "QLE2" });
		        shortOrderLabels.AddRange(new[] { SE2, "QSE2" });
		    }

		    if (EnableProfitTarget3)
		    {
		        longOrderLabels.AddRange(new[] { LE3, "QLE3" });
		        shortOrderLabels.AddRange(new[] { SE3, "QSE3" });
		    }
		
		    if (EnableProfitTarget4)
		    {
		        longOrderLabels.AddRange(new[] { LE4, "QLE4" });
		        shortOrderLabels.AddRange(new[] { SE4, "QSE4" });
		    }
		
		    // Common Action: Close all Positions and Disable the Strategy
		    Action closeAllPositionsAndDisableStrategy = () =>
		    {
		        foreach (string label in longOrderLabels)
		        {
		            ExitLong(Convert.ToInt32(Position.Quantity), @"LongExitKillSwitch", label);
		        }
		
		        foreach (string label in shortOrderLabels)
		        {
		            ExitShort(Convert.ToInt32(Position.Quantity), @"ShortExitKillSwitch", label);
		        }
		
		        isAutoEnabled = false;
		        Print("Kill Switch Activated: Strategy Disabled!");
		    };

		    if (dailyLossProfit && enableTrailingDD) //Check both the enableDailyLossLimit and enableTrailingDD
		    {
		        if (totalPnL >= StartTrailingDD && (maxProfit - totalPnL) >= TrailingDrawdown)
		        {
		            closeAllPositionsAndDisableStrategy();
		            trailingDrawdownReached = true;
		        }
		    }
		
		    if (dailyPnL <= -DailyLossLimit)
		    {
		        closeAllPositionsAndDisableStrategy();
		    }
		
		    if (dailyPnL >= DailyProfitLimit)
		    {
		        closeAllPositionsAndDisableStrategy();
		    }
		
			if (!isAutoEnabled)
				Print("Kill Switch Activated!!!");
		}
		#endregion
		
		#region Custom Property Manipulation	
		
		public void ModifyProperties(PropertyDescriptorCollection col)
        {
			if (TradesPerDirection == false)
            {
				col.Remove(col.Find("longPerDirection", true));
				col.Remove(col.Find("shortPerDirection", true));
            }
			if (Time2 == false)
            {
				col.Remove(col.Find("Start2", true));
				col.Remove(col.Find("End2", true));
            }
			if (Time3 == false)
            {
				col.Remove(col.Find("Start3", true));
				col.Remove(col.Find("End3", true));
            }
			if (Time4 == false)
            {
				col.Remove(col.Find("Start4", true));
				col.Remove(col.Find("End4", true));
            }
			if (Time5 == false)
            {
				col.Remove(col.Find("Start5", true));
				col.Remove(col.Find("End5", true));
            }
			if (Time6 == false)
            {
				col.Remove(col.Find("Start6", true));
				col.Remove(col.Find("End6", true));
            }
		}
		
		public void ModifyBESetAutoProperties(PropertyDescriptorCollection col) {
			if (showctrlBESetAuto == false) {
				col.Remove(col.Find("BE_Trigger", true));
				col.Remove(col.Find("BE_Offset", true));
			}
		}		

		public void ModifyEnableTypeProfitProperties(PropertyDescriptorCollection col) {
			if (showctrlEnableDynamicProfit) {	
				col.Remove(col.Find("EnableFixedProfit", true));
				col.Remove(col.Find("EnableProfitTarget2", true));
				col.Remove(col.Find("ProfitTarget2", true));
				col.Remove(col.Find("EnableProfitTarget3", true));
				col.Remove(col.Find("ProfitTarget3", true));
				col.Remove(col.Find("EnableProfitTarget3", true));
				col.Remove(col.Find("ProfitTarget4", true));
				col.Remove(col.Find("EnableProfitTarget4", true));
				col.Remove(col.Find("Contracts2", true));
				col.Remove(col.Find("Contracts3", true));
				col.Remove(col.Find("Contracts4", true));
			
			}
			if (showctrlEnableFixedProfit) { col.Remove(col.Find("EnableDynamicProfit", true));}
		
		}	
		
		public void ModifyTrailProperties(PropertyDescriptorCollection col) {
			if (showTrailOptions == false) {
				col.Remove(col.Find("TrailSetAuto", true));
				col.Remove(col.Find("AtrPeriod", true));
				col.Remove(col.Find("atrMultiplier", true));
				col.Remove(col.Find("RiskRewardRatio", true));
				col.Remove(col.Find("Trail_Frequency", true));
				col.Remove(col.Find("TrailByThreeStep", true));
				col.Remove(col.Find("threeStepTrail", true));
				col.Remove(col.Find("step1ProfitTrigger", true));
				col.Remove(col.Find("step2ProfitTrigger", true));
				col.Remove(col.Find("step3ProfitTrigger", true));
				col.Remove(col.Find("step1StopLoss", true));
				col.Remove(col.Find("step2StopLoss", true));
				col.Remove(col.Find("step3StopLoss", true));
				col.Remove(col.Find("step1Frequency", true));
				col.Remove(col.Find("step2Frequency", true));
				col.Remove(col.Find("step3Frequency", true));				
			}
		}	
		
		public void ModifyTrailStopTypeProperties(PropertyDescriptorCollection col) {
		//	if (SystemPrint) Print("showAtrTrailOptions "+showAtrTrailOptions);
		//	if (SystemPrint) Print("showATRTrailOptions" "+ showATRTrailOptions);
		//	if (SystemPrint) Print("showThreeStepTrailOptions "+showThreeStepTrailOptions);
			if (showAtrTrailOptions == false) {
				col.Remove(col.Find("TrailSetAuto", true));
				col.Remove(col.Find("AtrPeriod", true));
				col.Remove(col.Find("atrMultiplier", true));
				col.Remove(col.Find("RiskRewardRatio", true));
				col.Remove(col.Find("Trail_Frequency", true));
			} 
			if (showThreeStepTrailOptions == false) {
			//	if (SystemPrint) Print("Remove Trail By ThreeStep");
				col.Remove(col.Find("threeStepTrail", true));
				col.Remove(col.Find("step1ProfitTrigger", true));
				col.Remove(col.Find("step2ProfitTrigger", true));
				col.Remove(col.Find("step3ProfitTrigger", true));
				col.Remove(col.Find("step1StopLoss", true));
				col.Remove(col.Find("step2StopLoss", true));
				col.Remove(col.Find("step3StopLoss", true));				
				col.Remove(col.Find("step1Frequency", true));
				col.Remove(col.Find("step2Frequency", true));
				col.Remove(col.Find("step3Frequency", true));				
			}
		}
		
		public void ModifyTrailSetAutoProperties(PropertyDescriptorCollection col) {
			if (showAtrTrailSetAuto == false) {
				col.Remove(col.Find("AtrPeriod", true));
				col.Remove(col.Find("atrMultiplier", true));
				col.Remove(col.Find("RiskRewardRatio", true));
				col.Remove(col.Find("Trail_frequency", true));
			}
		}			

		public void ModifyThreeStepTrailSetAutoProperties(PropertyDescriptorCollection col) {
			if (threeStepTrail == false) {
				col.Remove(col.Find("step1ProfitTrigger", true));
				col.Remove(col.Find("step2ProfitTrigger", true));
				col.Remove(col.Find("step3ProfitTrigger", true));
				col.Remove(col.Find("step1StopLoss", true));
				col.Remove(col.Find("step2StopLoss", true));
				col.Remove(col.Find("step3StopLoss", true));				
				col.Remove(col.Find("step1Frequency", true));
				col.Remove(col.Find("step2Frequency", true));
				col.Remove(col.Find("step3Frequency", true));				
			}
		}

		#endregion

		#region Utility Methods

		// Dictionary to track messages printed by PrintOnce (Key = message key, Value = bar number printed)
		private Dictionary<string, int> printedMessages = new Dictionary<string, int>();

		/// <summary>
		/// Prints a message to the NinjaScript output window only once per bar for a given key.
		/// </summary>
		/// <param name="key">A unique identifier for the specific message type.</param>
		/// <param name="message">The message string to print.</param>
		protected void PrintOnce(string key, string message)
		{
			// Check if Bars is initialized and we have a valid CurrentBar
			if (Bars == null || Bars.Count == 0 || CurrentBar < 0)
			{
				Print($"PrintOnce WARNING: Cannot track message key '{key}' - Bars not ready. Message: {message}");
				return; // Cannot track without bar context
			}

			// Check if this message key has been printed on this bar
			if (!printedMessages.ContainsKey(key) || printedMessages[key] != CurrentBar)
			{
				Print(message);
				printedMessages[key] = CurrentBar; // Record that we printed this key on this bar
			}
		}

		/// <summary>
		/// Logs a message with timestamp and strategy name.
		/// </summary>
		/// <param name="key">A unique identifier for the message type.</param>
		/// <param name="message">The message to log.</param>
		protected void LogMessage(string key, string message)
		{
			Print($"[{DateTime.Now:HH:mm:ss}] {Name} [{key}]: {message}");
		}

		/// <summary>
		/// Logs a message with timestamp and strategy name (single parameter version).
		/// </summary>
		/// <param name="message">The message to log.</param>
		protected void LogMessage(string message)
		{
			Print($"[{DateTime.Now:HH:mm:ss}] {Name}: {message}");
		}

		/// <summary>
		/// Logs an error message with timestamp and strategy name.
		/// </summary>
		/// <param name="key">A unique identifier for the error type.</param>
		/// <param name="message">The error message to log.</param>
		protected void LogError(string key, string message)
		{
			Print($"[{DateTime.Now:HH:mm:ss}] ERROR {Name} [{key}]: {message}");
		}

		/// <summary>
		/// Logs an error message with timestamp and strategy name (single parameter version).
		/// </summary>
		/// <param name="message">The error message to log.</param>
		protected void LogError(string message)
		{
			Print($"[{DateTime.Now:HH:mm:ss}] ERROR {Name}: {message}");
		}

		#endregion

		#region ICustomTypeDescriptor Members

        public AttributeCollection GetAttributes()
        {
            return TypeDescriptor.GetAttributes(GetType());
        }

        public string GetClassName()
        {
            return TypeDescriptor.GetClassName(GetType());
        }

        public string GetComponentName()
        {
            return TypeDescriptor.GetComponentName(GetType());
        }

        public TypeConverter GetConverter()
        {
            return TypeDescriptor.GetConverter(GetType());
        }

        public EventDescriptor GetDefaultEvent()
        {
            return TypeDescriptor.GetDefaultEvent(GetType());
        }

        public PropertyDescriptor GetDefaultProperty()
        {
            return TypeDescriptor.GetDefaultProperty(GetType());
        }

        public object GetEditor(Type editorBaseType)
        {
            return TypeDescriptor.GetEditor(GetType(), editorBaseType);
        }

        public EventDescriptorCollection GetEvents(Attribute[] attributes)
        {
            return TypeDescriptor.GetEvents(GetType(), attributes);
        }

        public EventDescriptorCollection GetEvents()
        {
            return TypeDescriptor.GetEvents(GetType());
        }

        public PropertyDescriptorCollection GetProperties(Attribute[] attributes)
        {
            PropertyDescriptorCollection orig = TypeDescriptor.GetProperties(GetType(), attributes);
            PropertyDescriptor[] arr = new PropertyDescriptor[orig.Count];
            orig.CopyTo(arr, 0);
            PropertyDescriptorCollection col = new PropertyDescriptorCollection(arr);

            ModifyProperties(col);
			ModifyBESetAutoProperties(col);

			ModifyTrailProperties(col);
			ModifyTrailStopTypeProperties(col);
			ModifyTrailSetAutoProperties(col);
			ModifyEnableTypeProfitProperties(col);	
			ModifyThreeStepTrailSetAutoProperties(col);			
			
            return col;

        }

        public PropertyDescriptorCollection GetProperties()
        {
            return TypeDescriptor.GetProperties(GetType());
        }

        public object GetPropertyOwner(PropertyDescriptor pd)
        {
            return this;
        }
		#endregion		
	
		#region Properties

		#region 01a. Release Notes
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
		[Display(Name="BaseAlgoVersion", Order=1, GroupName="01a. Release Notes")]
		public string BaseAlgoVersion
		{ get; set; }
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
		[Display(Name="Author", Order=2, GroupName="01a. Release Notes")]
		public string Author
		{ get; set; }		
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
//		[ReadOnly(true)]
		[Display(Name="StrategyName", Order=3, GroupName="01a. Release Notes")]
		public string StrategyName
		{ get; set; }
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
//		[ReadOnly(true)]
		[Display(Name="Version", Order =4, GroupName="01a. Release Notes")]
		public string Version
		{ get; set; }
		
		[ReadOnly(true)]
		[NinjaScriptProperty]
//		[ReadOnly(true)]
		[Display(Name="Credits", Order=5, GroupName="01a. Release Notes")]
		public string Credits
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Chart Type", Order=6, GroupName="01a. Release Notes")]
		public string ChartType
		{ get; set; }
		
		#endregion
		
		#region 01b. Support Developer
		
		[NinjaScriptProperty]
		[Display(Name = "PayPal Donation URL", Order = 1, GroupName = "01b. Support Developer", Description = "https://www.paypal.com/signin")]
		public string paypal { get; set; }
		
		#endregion

		#region 02. Order Settings	
		
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Fixed Profit", Order=1, GroupName="02. Order Settings")]
		public bool EnableFixedProfit
		{ 	get{
				return enableFixedProfit;
			} 
			set {
				enableFixedProfit = value;
				
				if (enableFixedProfit == true) {
					showctrlEnableDynamicProfit = false;
					showctrlEnableFixedProfit = true;
					enableDynamicProfit = false;
				} else {
					showctrlEnableDynamicProfit = true;
					showctrlEnableFixedProfit = false;
					enableDynamicProfit = true;
				}
			}
		}
		
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Dynamic Profit", Order= 1, GroupName="02. Order Settings")]
		public bool EnableDynamicProfit
		{ 	get{
				return enableDynamicProfit;
			} 
			set {
				enableDynamicProfit = value;
				
				if (enableDynamicProfit == true) {
					showctrlEnableDynamicProfit = true;
					showctrlEnableFixedProfit = false;
					enableFixedProfit = false;
				} else {
					showctrlEnableDynamicProfit = false;
					showctrlEnableFixedProfit = true;
					enableFixedProfit = true;
				}
			}
		}
		
		[NinjaScriptProperty]
        [Display(Name = "Order Type (Market/Limit)", Order = 2, GroupName = "02. Order Settings")]
        public OrderType OrderType { get; set; } 
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Limit Order Offset", Order= 3, GroupName="02. Order Settings")]
		public double LimitOffset
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Contracts", Order= 4, GroupName="02. Order Settings")]
		public int Contracts
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Tick Move (Button Click)", Order= 5, GroupName="02. Order Settings")]
		public int TickMove
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Initial Stop (Ticks)", Order= 6, GroupName="02. Order Settings")]
		public int InitialStop
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Profit Target", Order=7, GroupName="02. Order Settings")]
		public double ProfitTarget
		{ get; set; }
		
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Profit Target 2", Order= 8, GroupName="02. Order Settings")]
		public bool EnableProfitTarget2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Contract 2", Order= 9, GroupName="02. Order Settings")]
		public int Contracts2
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name="Profit Target 2", Order=10, GroupName="02. Order Settings")]
		public double ProfitTarget2
		{ get; set; }
		
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Profit Target 3", Order= 11, GroupName="02. Order Settings")]
		public bool EnableProfitTarget3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Contract 3", Order= 12, GroupName="02. Order Settings")]
		public int Contracts3
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name="Profit Target3", Order=13, GroupName="02. Order Settings")]
		public double ProfitTarget3
		{ get; set; }
		
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Profit Target 4", Order= 14, GroupName="02. Order Settings")]
		public bool EnableProfitTarget4
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Contract 4", Order= 15, GroupName="02. Order Settings")]
		public int Contracts4
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name="Profit Target4", Order=16, GroupName="02. Order Settings")]
		public double ProfitTarget4
		{ get; set; }	
		
		#endregion	
		
		#region 03. Order Management
				
		[NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Stop Loss Type", Description= "Type of Trail Stop", GroupName = "03. Order Management", Order = 1)]
        [RefreshProperties(RefreshProperties.All)]
		public TrailStopTypeKC TrailStopType
        { 
			get { return trailStopType; } 
			set { 
				trailStopType = value; 
				if (trailStopType == TrailStopTypeKC.TickTrail) {
					tickTrail = true;
					enableFixedStopLoss = false;
					atrTrailSetAuto = false;
					showAtrTrailSetAuto = false;					
					showAtrTrailOptions = false;
					threeStepTrail = false;
					showThreeStepTrailOptions = false;
				}
				else if (trailStopType == TrailStopTypeKC.FixedStop) {
					enableFixedStopLoss = true;
					atrTrailSetAuto = false;
					showAtrTrailSetAuto = false;					
					showAtrTrailOptions = false;
					tickTrail = false;
					threeStepTrail = false;
					showThreeStepTrailOptions = false;
				}
				else if (trailStopType == TrailStopTypeKC.ATR_Trail) {
					enableFixedStopLoss = false;
					atrTrailSetAuto = true;
					showAtrTrailSetAuto = true;					
					showAtrTrailOptions = true;
					tickTrail = false;
					threeStepTrail = false;
					showThreeStepTrailOptions = false;
				} else if (trailStopType == TrailStopTypeKC.ThreeStepTrail) {
//					TrailSetAuto = false;
					enableFixedStopLoss = false;
					threeStepTrail = true;
					showThreeStepTrailOptions = true;	
					showAtrTrailOptions = false;				
					atrTrailSetAuto = false;
					showAtrTrailSetAuto = false;	
					tickTrail = false;
				}
			}
		}

		[NinjaScriptProperty]
		[Display(Name="ATR Period", Order= 2, GroupName="03. Order Management")]
		public int AtrPeriod	
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="ATR Trailing Multiplier", Order= 3, GroupName="03. Order Management")]
		public double atrMultiplier
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Risk To Reward Ratio", Order= 4, GroupName="03. Order Management")]
		public double RiskRewardRatio
		{ get; set; }

//		[NinjaScriptProperty]
//		[Display(Name="Trail Frecuency (Ticks)", Order=6, GroupName="03. Order Management - 1. Tick")]
//		public int Trail_frequency
//		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name = "Enable ATR Profit Target", Description = "Enable  Profit Target based on TrendMagic", Order = 5, GroupName = "03. Order Management")]
		[RefreshProperties(RefreshProperties.All)]
		public bool enableAtrProfitTarget			
		{ get; set; }
		
		//Breakeven Actual				
		[NinjaScriptProperty]
		[RefreshProperties(RefreshProperties.All)]	
		[Display(Name="Enable Breakeven", Order= 6, GroupName="03. Order Management")]	
		public bool BESetAuto
		{ 	get{
				return beSetAuto;
			} 
			set {
				beSetAuto = value;
				
				if (beSetAuto == true) {
					showctrlBESetAuto = true;
				} else {
					showctrlBESetAuto = false;
				}
			}
		}
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Breakeven Trigger", Order = 7, Description="In Ticks", GroupName="03. Order Management")]
		public int BE_Trigger
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Breakeven Offset", Order = 8, Description="In Ticks", GroupName="03. Order Management")]
		public int BE_Offset
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Background Color Signal", Description = "Enable Exit", Order = 9, GroupName = "03. Order Management")]
		[RefreshProperties(RefreshProperties.All)]
		public bool enableBackgroundSignal
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Exit", Description = "Enable Exit", Order = 10, GroupName = "03. Order Management")]
		[RefreshProperties(RefreshProperties.All)]
		public bool enableExit
		{ get; set; }
		
		
		#endregion			

		#region 04. Three-step Trailing Stop
		
		[NinjaScriptProperty]
		[Display(Name="Profit Trigger Step 1", Order = 1, GroupName="04. Three-step Trailing Stop")]
		public int step1ProfitTrigger
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Display(Name="Stop Loss Step 1", Order = 2, GroupName="04. Three-step Trailing Stop")]
		public int step1StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Profit Trigger Step 2", Order = 3, GroupName="04. Three-step Trailing Stop")]
		public int step2ProfitTrigger
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Stop Loss Step 2", Order = 4, GroupName="04. Three-step Trailing Stop")]
		public int step2StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Profit Trigger Step 3", Order = 5, GroupName="04. Three-step Trailing Stop")]
		public int step3ProfitTrigger
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Display(Name="Stop Loss Step 3", Order = 6, GroupName="04. Three-step Trailing Stop")]
		public int step3StopLoss
		{ get; set; }		
		
//		[NinjaScriptProperty]
//		[Display(Name="Step1Frequency", Order=7, GroupName="04. Three-step Trailing Stop")]
//		public int step1Frequency
//		{ get; set; }
		
//		[NinjaScriptProperty]
//		[Display(Name="Step2Frequency", Order=8, GroupName="04. Three-step Trailing Stop")]
//		public int step2Frequency
//		{ get; set; }			
		
//		[NinjaScriptProperty]
//		[Display(Name="Step 3 Frequency", Order=9, GroupName="04. Three-step Trailing Stop")]
//		public int step3Frequency
//		{ get; set; }	
		
		#endregion	
		
		#region 05. Profit/Loss Limit	
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Daily Loss / Profit ", Description = "Enable / Disable Daily Loss & Profit control", Order =1, GroupName = "05. Profit/Loss Limit	")]
		[RefreshProperties(RefreshProperties.All)]
		public bool dailyLossProfit
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, double.MaxValue)]
		[Display(ResourceType = typeof(Custom.Resource), Name="Daily Profit Limit ($)", Description="No positive or negative sign, just integer", Order=2, GroupName="05. Profit/Loss Limit	")]
		public double DailyProfitLimit
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, double.MaxValue)]
		[Display(ResourceType = typeof(Custom.Resource), Name="Daily Loss Limit ($)", Description="No positive or negative sign, just integer", Order=3, GroupName="05. Profit/Loss Limit	")]
		public double DailyLossLimit
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Trailing Drawdown", Description = "Enable / Disable trailing drawdown", Order =4, GroupName = "05. Profit/Loss Limit	")]
		[RefreshProperties(RefreshProperties.All)]
		public bool enableTrailingDD
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, double.MaxValue)]
		[Display(ResourceType = typeof(Custom.Resource), Name="Trailing Drawdown ($)", Description="No positive or negative sign, just integer", Order=5, GroupName="05. Profit/Loss Limit	")]
		public double TrailingDrawdown
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Range(0, double.MaxValue)]
		[Display(ResourceType = typeof(Custom.Resource), Name="Start Trailing Drawdown ($)", Description="No positive or negative sign, just integer", Order=6, GroupName="05. Profit/Loss Limit	")]
		public double StartTrailingDD
		{ get; set; }	
		
		#endregion

		#region	06. Trades Per Direction	
		[NinjaScriptProperty]
		[Display(Name = "Enable Trades Per Direction", Description = "Switch off Historical Trades to use this option.", Order = 0, GroupName = "06. Trades Per Direction")]
		[RefreshProperties(RefreshProperties.All)]
		public bool TradesPerDirection 
		{
		 	get{return tradesPerDirection;} 
			set{tradesPerDirection = (value);} 
		}
		
		[NinjaScriptProperty]
		[Display(Name="Long Per Direction", Description = "Number of long in a row", Order = 1, GroupName = "06. Trades Per Direction")]
		public int longPerDirection
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Short Per Direction", Description = "Number of short in a row", Order = 2, GroupName = "06. Trades Per Direction")]
		public int shortPerDirection
		{ get; set; }

		#endregion
		
		#region 07. Other Trade Controls
		
		[NinjaScriptProperty]
		[Display(Name="Seconds Since Entry", Description = "Time between orders i seconds", Order = 3, GroupName = "07. Other Trade Controls")]
		public int SecsSinceEntry
		{ get; set; }				
		
		[NinjaScriptProperty]
		[Display(Name="Bars Since Exit", Description = "Number of bars that have elapsed since the last specified exit. 0 == Not used. >1 == Use number of bars specified ", Order=4, GroupName="07. Other Trade Controls" )]
		public int iBarsSinceExit
		{ get; set; }
		
		#endregion
		
		#region 08b. Default Settings			
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Buy Sell Pressure", Order = 1, GroupName = "08b. Default Settings")]
		public bool enableBuySellPressure { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show Buy Sell Pressure", Order = 2, GroupName = "08b. Default Settings")]
		public bool showBuySellPressure { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Enable VMA", Order = 3, GroupName = "08b. Default Settings")]
		public bool enableVMA { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show VMA", Order = 4, GroupName = "08b. Default Settings")]
		public bool showVMA { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Enable Hooker", Order = 5, GroupName = "08b. Default Settings")]
		public bool enableHmaHooks { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show HMA Hooks", Order = 6, GroupName = "08b. Default Settings")]
		public bool showHmaHooks { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "HMA Period", Order = 7, GroupName = "08b. Default Settings")]
		public int HmaPeriod { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Enable KingKhanh", Order = 8, GroupName = "08b. Default Settings")]
		public bool enableRegChan1 { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Enable Inner Regression Channel", Order = 9, GroupName = "08b. Default Settings")]
		public bool enableRegChan2 { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show Outer Regression Channel", Order = 10, GroupName = "08b. Default Settings")]
		public bool showRegChan1 { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show Inner Regression Channel", Order = 11, GroupName = "08b. Default Settings")]
		public bool showRegChan2 { get; set; }
	
		[NinjaScriptProperty]
		[Display(Name = "Show High and Low Lines", Order = 12, GroupName = "08b. Default Settings")]
		public bool showRegChanHiLo { get; set; }

		[NinjaScriptProperty]
		[Display(Name="Regression Channel Period", Order = 13, GroupName="08b. Default Settings")]
		public int RegChanPeriod
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Outer Regression Channel Width", Order = 14, GroupName="08b. Default Settings")]
		public double RegChanWidth
		{ get; set; }
			
		[NinjaScriptProperty]
		[Display(Name = "Inner Regression Channel Width", Order = 15, GroupName = "08b. Default Settings")]
		public double RegChanWidth2 { get; set; }
	
		[NinjaScriptProperty]
        [Display(Name = "Enable Momo", Order = 16, GroupName = "08b. Default Settings")]
        public bool enableMomo { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Show Momentum", Order = 17, GroupName = "08b. Default Settings")]
        public bool showMomo { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Momo Up", Order = 18, GroupName="08b. Default Settings")]
		public int MomoUp
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Momo Down", Order = 19, GroupName="08b. Default Settings")]
		public int MomoDown
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Enable ADX", Order = 20, GroupName = "08b. Default Settings")]
        public bool enableADX { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Show ADX", Order = 21, GroupName = "08b. Default Settings")]
        public bool showAdx { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "ADX Period", Order = 22, GroupName = "08b. Default Settings")]
        public int adxPeriod { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "ADX Threshold 1", Order = 23, GroupName = "08b. Default Settings")]
        public int adxThreshold { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "ADX Threshold 2", Order = 24, GroupName = "08b. Default Settings")]
        public int adxThreshold2 { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "ADX Exit Threshold", Order = 25, GroupName = "08b. Default Settings")]
        public int adxExitThreshold { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Enable Volatility", Order = 26, GroupName = "08b. Default Settings")]
        public bool enableVolatility { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name="Volatility Threshold", Order = 27, GroupName="08b. Default Settings")]
        public double atrThreshold { get; set; }		
		
		[NinjaScriptProperty]
        [Display(Name = "Enable EMA Filter", Order = 28, GroupName = "08b. Default Settings")]
        public bool enableEMAFilter { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Show EMA", Order = 29, GroupName = "08b. Default Settings")]
        public bool showEMA { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="EMA Length", Order = 30, GroupName="08b. Default Settings")]
		public int emaLength
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Show Pivots", Order = 31, GroupName = "08b. Default Settings")]
        public bool showPivots { get; set; }
		
		#endregion	
		
		#region 09. Market Condition		
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Choppiness Detection", Order = 1, GroupName = "09. Market Condition")]
		public bool EnableChoppinessDetection { get; set; } 
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Regression Channel Look Back Period", Description="Period for Regression Channel used in chop detection.", Order=2, GroupName="09. Market Condition")]
		public int SlopeLookBack { get; set; }
		
		[NinjaScriptProperty]
		[Range(0.1, 1.0)] // Factor less than 1 to indicate narrower than average
		[Display(Name="Flat Slope Factor", Description="Factor of slope of Regression Channel indicates flatness.", Order=3, GroupName="09. Market Condition")]
		public double FlatSlopeFactor { get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Chop ADX Threshold", Description="ADX value below which the market is considered choppy (if RegChan is also flat).", Order=4, GroupName="09. Market Condition")]
		public int ChopAdxThreshold { get; set; }
		
		#endregion

		#region 10. Timeframes
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Trades", Order=1, GroupName="10. Timeframes")]
		public DateTime Start
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Trades", Order=2, GroupName="10. Timeframes")]
		public DateTime End
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Time 2", Description = "Enable 2 times.", Order=3, GroupName = "10. Timeframes")]
		[RefreshProperties(RefreshProperties.All)]
		public bool Time2
		{
		 	get{return isEnableTime2;} 
			set{isEnableTime2 = (value);} 
		}
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Time 2", Order=4, GroupName="10. Timeframes")]
		public DateTime Start2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Time 2", Order=5, GroupName="10. Timeframes")]
		public DateTime End2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Time 3", Description = "Enable 3 times.", Order=6, GroupName = "10. Timeframes")]
		[RefreshProperties(RefreshProperties.All)]
		public bool Time3
		{
		 	get{return isEnableTime3;} 
			set{isEnableTime3 = (value);} 
		}
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Time 3", Order=7, GroupName="10. Timeframes")]
		public DateTime Start3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Time 3", Order=8, GroupName="10. Timeframes")]
		public DateTime End3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Time 4", Description = "Enable 4 times.", Order=9, GroupName = "10. Timeframes")]
		[RefreshProperties(RefreshProperties.All)]
		public bool Time4
		{
		 	get{return isEnableTime4;} 
			set{isEnableTime4 = (value);} 
		}
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Time 4", Order=10, GroupName="10. Timeframes")]
		public DateTime Start4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Time 4", Order=11, GroupName="10. Timeframes")]
		public DateTime End4
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Time 5", Description = "Enable 5 times.", Order=12, GroupName = "10. Timeframes")]
		[RefreshProperties(RefreshProperties.All)]
		public bool Time5
		{
		 	get{return isEnableTime5;} 
			set{isEnableTime5 = (value);} 
		}
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Time 5", Order=13, GroupName="10. Timeframes")]
		public DateTime Start5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Time 5", Order=14, GroupName="10. Timeframes")]
		public DateTime End5
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Enable Time 6", Description = "Enable 6 times.", Order =15, GroupName = "10. Timeframes")]
		[RefreshProperties(RefreshProperties.All)]
		public bool Time6
		{
		 	get{return isEnableTime6;} 
			set{isEnableTime6 = (value);} 
		}
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="Start Time 6", Order=16, GroupName="10. Timeframes")]
		public DateTime Start6
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name="End Time 6", Order=17, GroupName="10. Timeframes")]
		public DateTime End6
		{ get; set; }
		
		#endregion
		
		#region 11. Status Panel	
		
		[NinjaScriptProperty]
        [Display(Name = "Show Daily PnL", Order = 1, GroupName = "11. Status Panel")]
        public bool showDailyPnl { get; set; }			
		
		[XmlIgnore()]
		[Display(Name = "Daily PnL Color", Order = 2, GroupName = "11. Status Panel")]
		public Brush colorDailyProfitLoss
		{ get; set; }	
		
		[NinjaScriptProperty]
		[Display(Name="Daily PnL Position", Description = "Daily PNL Alert Position", Order = 3, GroupName = "11. Status Panel")]
		public TextPosition PositionDailyPNL
		{ get; set; }
		
		// Serialize our Color object
		[Browsable(false)]
		public string colorDailyProfitLossSerialize
		{
			get { return Serialize.BrushToString(colorDailyProfitLoss); }
   			set { colorDailyProfitLoss = Serialize.StringToBrush(value); }
		}
		
        [NinjaScriptProperty]
        [Display(Name = "Show STATUS PANEL", Order = 4, GroupName = "11. Status Panel")]
        public bool showPnl { get; set; }		

		[XmlIgnore()]
		[Display(Name = "STATUS PANEL Color", Order = 5, GroupName = "11. Status Panel")]
		public Brush colorPnl
		{ get; set; }				
		
		[NinjaScriptProperty]
		[Display(Name="STATUS PANEL Position", Description = "Status PNL Position", Order = 6, GroupName = "11. Status Panel")]
		public TextPosition PositionPnl		
		{ get; set; }	
		
		// Serialize our Color object
		[Browsable(false)]
		public string colorPnlSerialize
		{
			get { return Serialize.BrushToString(colorPnl); }
   			set { colorPnl = Serialize.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name="Show Historical Trades", Description = "Show Historical Teorical Trades", Order= 7, GroupName="11. Status Panel")]
		public bool ShowHistorical
		{ get; set; }
		
        #endregion
		
		#region 12. WebHook

		[NinjaScriptProperty]
		[Display(Name="Activate Discord webhooks", Description="Activate One or more Discord webhooks", GroupName="11. Webhook", Order = 0)]
		public bool useWebHook { get; set; }		
		
//		[NinjaScriptProperty]
//		[Display(Name="Discord webhooks", Description="One or more Discord webhooks, separated by comma.", GroupName="11. Webhook", Order = 1)]
//		[TypeConverter(typeof(NinjaTrader.NinjaScript.AccountNameConverter))]
//		public string AccountName { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Discord webhooks", Description="One or more Discord webhooks, separated by comma.", GroupName="11. Webhook", Order = 2)]
		public string DiscordWebhooks
		{ get; set; }
		
		#endregion	
		
		#region Trailing Stop Type
		// Stop Loss Type
		public enum TrailStopTypeKC
		{
			TickTrail,
			FixedStop,
			ATR_Trail,
			ThreeStepTrail
		}
		#endregion
		
		#endregion
    }
}

/*
  // Only enter if at least 10 bars has passed since our last exit or if we have never traded yet
  if ((BarsSinceExitExecution() > iBarsSinceExit || BarsSinceExitExecution() == -1) && CrossAbove(SMA(10), SMA(20), 1))
      EnterLong();

*/