﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <MachineId2>d496db5147504a3aaa7d584a3b403e6c</MachineId2>
  <CachedConnectionTokens>
    <mike3066>
      <CachedUserName>BkBlCwtFcL5h2j5+Li6JsCaL2rgxohyB+s3HQACoECA=</CachedUserName>
      <CachedAccessToken>BkBlCwtFcL5h2j5+Li6JsD8h+Vb4z+B1PPMlxrby5+geVuKUEI5HBg5HvKikgn5ATUUKq/goSvocQhkIPtUTByGwSfkwho72HwVu4LD4LIRcTVzlf1PWXd4ahoyHBqXDziSW9Pj4C2P2WMfaSotQP1MwrSvXDMNJAZqZCUpM/gCUv5hP6RlfVS7b1mOAteubZc++iaASgoYVMQppUCP60uNlM+kGwiEcr7dzrQxvshvyNqR5VQM0c3hEUtktG/OlfdZEyssYadw1Po91L+ZcY2E6nUldHpEl9bTouVTzooo=</CachedAccessToken>
      <CachedMarketDataAccessToken>BkBlCwtFcL5h2j5+Li6JsCEmI2j2zltyFDecdnwAGVi98+p800AGZjvoPAwMOP7gdnJY5ptl79Pk7qhjcLWgg2e/AM9fJTu4FGaOI0DNBd3DUCKvz9nLcoMq608ABBCUUH+aeJDH8XEDrKjz0p8y2IN5vYXAsfSYqx4BCsGRGiV1mH904aSbGmAQQArECyDWt9yNFtDriPnvj6yU9q+xUERyZdLwncZQ1sj+ZOYAjdZN8Fz/3y0rDtnZXXletZwv6Ep67p9qJbPR/2Hgv4s6amlNFpfymi60PTkyWUlXr7g=</CachedMarketDataAccessToken>
      <CachedTokenExpiration>06/09/2025 08:05:58</CachedTokenExpiration>
      <CachedUserId>2809435</CachedUserId>
    </mike3066>
    <TDY001310>
      <CachedAccessToken>BkBlCwtFcL5h2j5+Li6JsOScIXEhLjJg9nTgWnRR2VTRGfFoqoft1hMH34gUYTzM02KqvZB9jY2uwLLh+ITRb9xJurdVfX8nG6rKtEhIMzwMliE8raCPK7mVFY1i53s7AqSRmRvP37kCrrOCTyqlI8n1bBZzsUqBjosqaT0X+Y55yGTRiaTZSTFbduWgFQbwjXUaJuJYu65Zv80mxIgHu4Kav3/fVnzaLxDse3fucaYLU78ZNDAKJvFlljj5KjvWUMurDNV84cVmx5bHudeKcKvkIaotwa0eD5siF6CykS0=</CachedAccessToken>
      <CachedMarketDataAccessToken>BkBlCwtFcL5h2j5+Li6JsCvHo6wW+QP5KoXQduiTOqIb1s+AlE9782TantzNhVMJx7IqcuJb+rm6O5HN/rjJT01U9OvvkbVrRM7UpD/6aL72Dho/MUIoe5zeannF23rFK4yNZ4DolkPcHUOFGf4OG1y/RmwNlkGPsSUHFof+7Y4jACTjsLpltOoEp3uA6cknfTiLhqFU+OFEfu0cGyDrzTeNs84G4yA3KtdlAo1lipKQuKY/S6kOqdD7b/RvJQ1pJXPrspCtNjVpaDDc3k6FKN5BxAxU0UHHZgPdqdaXy0o=</CachedMarketDataAccessToken>
      <CachedUserId>3554513</CachedUserId>
    </TDY001310>
  </CachedConnectionTokens>
  <Username>mike3066</Username>
  <LastMachineId>A704A6037C73334DE437BD564DB72FD7</LastMachineId>
  <Backup>
    <ScheduleInterval>Monday</ScheduleInterval>
    <LastTimeBackup>01/01/1800 00:00:00</LastTimeBackup>
    <IsBackupReminderActive>False</IsBackupReminderActive>
  </Backup>
  <AddOnRegistry>
    <ArrayOfAddOnAuthorization xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
  </AddOnRegistry>
  <LastEntitlements>
    <string />
  </LastEntitlements>
  <ConnectOptions>
    <KinetickOptions>
      <CanManageOrders>true</CanManageOrders>
      <ConnectOnStartup>false</ConnectOnStartup>
      <DisableL2Data>false</DisableL2Data>
      <IsHdsEnabled>false</IsHdsEnabled>
      <IsPasswordRequiredOnStartup>false</IsPasswordRequiredOnStartup>
      <RunAsProcess>false</RunAsProcess>
      <TypeName>NinjaTrader.Cbi.KinetickOptions</TypeName>
      <Mode>Live</Mode>
      <Name>Kinetick – End Of Day (Free)</Name>
      <Password />
      <Provider>Provider7</Provider>
      <User />
      <GlobexNonProFees>false</GlobexNonProFees>
      <BackadjustContinuousContracts>false</BackadjustContinuousContracts>
      <Free>true</Free>
      <NewsDaysBack>1</NewsDaysBack>
      <UseFundamentalData>true</UseFundamentalData>
    </KinetickOptions>
    <PlaybackOptions>
      <CanManageOrders>true</CanManageOrders>
      <ConnectOnStartup>false</ConnectOnStartup>
      <DisableL2Data>false</DisableL2Data>
      <IsHdsEnabled>false</IsHdsEnabled>
      <IsPasswordRequiredOnStartup>false</IsPasswordRequiredOnStartup>
      <RunAsProcess>false</RunAsProcess>
      <TypeName>NinjaTrader.Cbi.PlaybackOptions</TypeName>
      <Mode>Live</Mode>
      <Name>Playback Connection</Name>
      <Password />
      <Provider>Playback</Provider>
      <User />
    </PlaybackOptions>
    <SimulatorOptions>
      <CanManageOrders>true</CanManageOrders>
      <ConnectOnStartup>false</ConnectOnStartup>
      <DisableL2Data>false</DisableL2Data>
      <IsHdsEnabled>false</IsHdsEnabled>
      <IsPasswordRequiredOnStartup>false</IsPasswordRequiredOnStartup>
      <RunAsProcess>false</RunAsProcess>
      <TypeName>NinjaTrader.Cbi.SimulatorOptions</TypeName>
      <Mode>Live</Mode>
      <Name>Simulated Data Feed</Name>
      <Password />
      <Provider>Simulator</Provider>
      <User />
      <TicksPerSecond>2</TicksPerSecond>
      <TimerMilliseconds>500</TimerMilliseconds>
    </SimulatorOptions>
    <TradovateOptions>
      <CanManageOrders>true</CanManageOrders>
      <ConnectOnStartup>false</ConnectOnStartup>
      <DisableL2Data>false</DisableL2Data>
      <IsHdsEnabled>false</IsHdsEnabled>
      <IsPasswordRequiredOnStartup>false</IsPasswordRequiredOnStartup>
      <RunAsProcess>false</RunAsProcess>
      <TypeName>NinjaTrader.Cbi.TradovateOptions</TypeName>
      <Mode>Live</Mode>
      <Name>TRADIFY</Name>
      <Password>BkBlCwtFcL5h2j5+Li6JsAzXVP+yjo95BtgrHC9eIJC077iPn9W7LFG4K6vsUooL</Password>
      <Provider>Provider31</Provider>
      <User>BkBlCwtFcL5h2j5+Li6JsJkQRegfk/VXRMYeVbpVA3I=</User>
      <AccessToken>BkBlCwtFcL5h2j5+Li6JsOScIXEhLjJg9nTgWnRR2VTRGfFoqoft1hMH34gUYTzM02KqvZB9jY2uwLLh+ITRb9xJurdVfX8nG6rKtEhIMzwMliE8raCPK7mVFY1i53s7AqSRmRvP37kCrrOCTyqlI8n1bBZzsUqBjosqaT0X+Y55yGTRiaTZSTFbduWgFQbwjXUaJuJYu65Zv80mxIgHu4Kav3/fVnzaLxDse3fucaYLU78ZNDAKJvFlljj5KjvWUMurDNV84cVmx5bHudeKcKvkIaotwa0eD5siF6CykS0=</AccessToken>
      <AccountType>Simulation</AccountType>
      <MdAccessToken>BkBlCwtFcL5h2j5+Li6JsCvHo6wW+QP5KoXQduiTOqIb1s+AlE9782TantzNhVMJx7IqcuJb+rm6O5HN/rjJT01U9OvvkbVrRM7UpD/6aL72Dho/MUIoe5zeannF23rFK4yNZ4DolkPcHUOFGf4OG1y/RmwNlkGPsSUHFof+7Y4jACTjsLpltOoEp3uA6cknfTiLhqFU+OFEfu0cGyDrzTeNs84G4yA3KtdlAo1lipKQuKY/S6kOqdD7b/RvJQ1pJXPrspCtNjVpaDDc3k6FKN5BxAxU0UHHZgPdqdaXy0o=</MdAccessToken>
      <UseDevEnvironment>false</UseDevEnvironment>
      <UseLocalOcoSimulation>true</UseLocalOcoSimulation>
      <UseNativeHistoricalData>false</UseNativeHistoricalData>
    </TradovateOptions>
  </ConnectOptions>
  <AtiOptions>
    <AtiOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsAtiEnabled>false</IsAtiEnabled>
      <DefaultAccount />
      <IgnoreDuplicateOifFiles>false</IgnoreDuplicateOifFiles>
      <IsTradeStationEmailInterfaceEnabled>false</IsTradeStationEmailInterfaceEnabled>
      <Name>Automated trading interface</Name>
      <ServerPort>36973</ServerPort>
      <TradeStationLimitPriceOffset>10</TradeStationLimitPriceOffset>
      <TradeStationOrderHandlingSubmit>SubmitMarketOrder</TradeStationOrderHandlingSubmit>
      <TradeStationStopOrderHandlingSubmit>SubmitAsIs</TradeStationStopOrderHandlingSubmit>
      <TradeStationSubmitMarketOrderIfStopRejected>false</TradeStationSubmitMarketOrderIfStopRejected>
      <TradeStationSyncTimeout>20</TradeStationSyncTimeout>
      <TradeStationUnfilledToMarketDelay>10</TradeStationUnfilledToMarketDelay>
    </AtiOptions>
  </AtiOptions>
  <MarketDataOptions>
    <MarketDataOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <AdjustForSplitsOnDaily>true</AdjustForSplitsOnDaily>
      <AdjustForSplitsOnIntraday>true</AdjustForSplitsOnIntraday>
      <AdjustForDividends>false</AdjustForDividends>
      <AutoSubscribe>true</AutoSubscribe>
      <DownloadCotData>false</DownloadCotData>
      <FilterBadTicks>false</FilterBadTicks>
      <FilterBadTicksPercent>0.1</FilterBadTicksPercent>
      <GetDataFromServer>true</GetDataFromServer>
      <GlobalMergePolicy>MergeBackAdjusted</GlobalMergePolicy>
      <IsTickReplayEnabled>false</IsTickReplayEnabled>
      <Name>Market data</Name>
      <PreferredCfdConnection>Unknown</PreferredCfdConnection>
      <PreferredCryptocurrencyConnection>Unknown</PreferredCryptocurrencyConnection>
      <PreferredForexConnection>Unknown</PreferredForexConnection>
      <PreferredFutureConnection>Unknown</PreferredFutureConnection>
      <PreferredIndexConnection>Unknown</PreferredIndexConnection>
      <PreferredOptionConnection>Unknown</PreferredOptionConnection>
      <PreferredStockConnection>Unknown</PreferredStockConnection>
      <PreferredRealtimeCfdConnection>Unknown</PreferredRealtimeCfdConnection>
      <PreferredRealtimeCryptocurrencyConnection>Unknown</PreferredRealtimeCryptocurrencyConnection>
      <PreferredRealtimeForexConnection>Unknown</PreferredRealtimeForexConnection>
      <PreferredRealtimeFutureConnection>Unknown</PreferredRealtimeFutureConnection>
      <PreferredRealtimeIndexConnection>Unknown</PreferredRealtimeIndexConnection>
      <PreferredRealtimeOptionConnection>Unknown</PreferredRealtimeOptionConnection>
      <PreferredRealtimeStockConnection>Unknown</PreferredRealtimeStockConnection>
      <RecordForPlayback>false</RecordForPlayback>
      <SaveDataAsHistorical>false</SaveDataAsHistorical>
    </MarketDataOptions>
  </MarketDataOptions>
  <ServerOptions>
    <ServerOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <AdapterServerPort>4530</AdapterServerPort>
      <AdminPort>31660</AdminPort>
      <DbConnectionString />
      <DbType>SQLite</DbType>
      <DSDomainName />
      <DSPort>31659</DSPort>
      <DSPortWebSocket>31664</DSPortWebSocket>
      <DSPrimaryLicenseServer />
      <DSPrimaryLicenseServerPassword />
      <DSPrimaryLicenseServerUserName />
      <DSPrimaryLicenseServerUseSsl>false</DSPrimaryLicenseServerUseSsl>
      <DSRunWebServer>false</DSRunWebServer>
      <DSSecondaryLicenseServer />
      <DSSecondaryLicenseServerPassword />
      <DSSecondaryLicenseServerUserName />
      <DSSecondaryLicenseServerUseSsl>false</DSSecondaryLicenseServerUseSsl>
      <DSCustomerServicesUrl />
      <DSCustomerServicesApiKey />
      <DSTokenValidSeconds>3600</DSTokenValidSeconds>
      <HDSPort>31655</HDSPort>
      <HDSPortWebSocket>31653</HDSPortWebSocket>
      <HDSNoSSL>false</HDSNoSSL>
      <HDSTokenValidSeconds>60</HDSTokenValidSeconds>
      <ISPort>31658</ISPort>
      <Name>Server</Name>
      <ServerId>0</ServerId>
      <System>NT</System>
      <ApplicationType>Desktop</ApplicationType>
      <ServerName />
      <UseNagle>false</UseNagle>
    </ServerOptions>
  </ServerOptions>
  <StrategiesOptions>
    <StrategiesOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <UseServerAtmSerialize>false</UseServerAtmSerialize>
      <ModifyInnerBracket>true</ModifyInnerBracket>
      <ModifyNearestBracket>true</ModifyNearestBracket>
      <SimulatedOrderAsYellow>true</SimulatedOrderAsYellow>
      <UseLastPrice4AutoTrail>true</UseLastPrice4AutoTrail>
      <CancelEntriesOnStrategyDisable>false</CancelEntriesOnStrategyDisable>
      <CancelExitsOnStrategyDisable>false</CancelExitsOnStrategyDisable>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <NoAlertStrategySetting>Always</NoAlertStrategySetting>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <Name>Strategies</Name>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
    </StrategiesOptions>
  </StrategiesOptions>
  <TradingOptions>
    <TradingOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <AutoClosePosition>false</AutoClosePosition>
      <AutoClosePositionTime>2025-04-29T00:00:00</AutoClosePositionTime>
      <AutoClosePositionAllInstruments>false</AutoClosePositionAllInstruments>
      <AutoClosePositionInstrumentsSerializable />
      <IsGlobalSimulationMode>false</IsGlobalSimulationMode>
      <IsKeyboardFocusAllowed>false</IsKeyboardFocusAllowed>
      <IsOrderEntryHotKeysEnabled>false</IsOrderEntryHotKeysEnabled>
      <LocalOcoCancelBehavior>CancelOtherSide</LocalOcoCancelBehavior>
      <Name>Trading</Name>
      <SimulationColorSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</SimulationColorSerialize>
      <SimulatorEnforceImmediateFills>false</SimulatorEnforceImmediateFills>
      <SimulatorEnforcePartialFills>false</SimulatorEnforcePartialFills>
      <StartInGlobalSimulationMode>false</StartInGlobalSimulationMode>
      <RealizedPnlIncludesCommissions>false</RealizedPnlIncludesCommissions>
      <UseLastPrice4ProfitLossCalc>true</UseLastPrice4ProfitLossCalc>
      <ConfirmOrderPlacement>false</ConfirmOrderPlacement>
      <PositionCloseWaitMilliSeconds>5000</PositionCloseWaitMilliSeconds>
      <IsBuySellFlippedSerialize>true</IsBuySellFlippedSerialize>
    </TradingOptions>
  </TradingOptions>
  <GeneralOptions>
    <GeneralOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <LogsMaintainedDays>20</LogsMaintainedDays>
      <MailAlertMessagesTo />
      <TimeZoneInfoSerializable />
      <ConfirmWindowClose>false</ConfirmWindowClose>
      <IsGlobalDrawingObjectsAcrossWorkspaces>false</IsGlobalDrawingObjectsAcrossWorkspaces>
      <IsGlobalLinkButtonEnabled>true</IsGlobalLinkButtonEnabled>
      <Language>English</Language>
      <MultiProvider>true</MultiProvider>
      <PerformanceMetrics />
      <ReopenWorkspaces>false</ReopenWorkspaces>
      <SaveUsername>true</SaveUsername>
      <ShowAccountNumbersAndBalances>true</ShowAccountNumbersAndBalances>
      <ShowDefaultWorkspaces>true</ShowDefaultWorkspaces>
      <ShowToolTips>true</ShowToolTips>
      <Skin>NinjaTrader Dark</Skin>
      <RecoveryWorkspaces>10</RecoveryWorkspaces>
      <SoundPlayConsecutively>false</SoundPlayConsecutively>
      <SoundAnnouncement>C:\Program Files\NinjaTrader 8\sounds\Announcement.wav</SoundAnnouncement>
      <SoundAutoBreakeven>C:\Program Files\NinjaTrader 8\sounds\AutoBreakEven.wav</SoundAutoBreakeven>
      <SoundAutoChase>C:\Program Files\NinjaTrader 8\sounds\AutoChase.wav</SoundAutoChase>
      <SoundAutoTrail>C:\Program Files\NinjaTrader 8\sounds\AutoTrail.wav</SoundAutoTrail>
      <SoundCompiledSuccessfully>C:\Program Files\NinjaTrader 8\sounds\CompiledSuccessfully.wav</SoundCompiledSuccessfully>
      <SoundConnected>C:\Program Files\NinjaTrader 8\sounds\Connected.wav</SoundConnected>
      <SoundConnectionLost>C:\Program Files\NinjaTrader 8\sounds\ConnectionLost.wav</SoundConnectionLost>
      <SoundOrderCanceled>C:\Program Files\NinjaTrader 8\sounds\OrderCanceled.wav</SoundOrderCanceled>
      <SoundOrderFilled>C:\Program Files\NinjaTrader 8\sounds\OrderFilled.wav</SoundOrderFilled>
      <SoundOrderPending>C:\Program Files\NinjaTrader 8\sounds\OrderPending.wav</SoundOrderPending>
      <SoundReversing>C:\Program Files\NinjaTrader 8\sounds\Reversing.wav</SoundReversing>
      <SoundStopFilled>C:\Program Files\NinjaTrader 8\sounds\StopFilled.wav</SoundStopFilled>
      <SoundTargetFilled>C:\Program Files\NinjaTrader 8\sounds\TargetFilled.wav</SoundTargetFilled>
      <BrushUpPrimarySerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</BrushUpPrimarySerialize>
      <BrushUpSecondarySerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008B8B&lt;/SolidColorBrush&gt;</BrushUpSecondarySerialize>
      <BrushDownPrimarySerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</BrushDownPrimarySerialize>
      <BrushDownSecondarySerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushDownSecondarySerialize>
      <BrushUpPrimarySerializeKo>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</BrushUpPrimarySerializeKo>
      <BrushUpSecondarySerializeKo>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushUpSecondarySerializeKo>
      <BrushDownPrimarySerializeKo>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF115BCB&lt;/SolidColorBrush&gt;</BrushDownPrimarySerializeKo>
      <BrushDownSecondarySerializeKo>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF4682B4&lt;/SolidColorBrush&gt;</BrushDownSecondarySerializeKo>
      <CurrentUICultureSerializable>en-US</CurrentUICultureSerializable>
      <KinetickGlobexNonProFees>false</KinetickGlobexNonProFees>
      <LogsLoaded>1000</LogsLoaded>
      <Name>General</Name>
      <PromptLanguageWarningForBasicEntry>false</PromptLanguageWarningForBasicEntry>
    </GeneralOptions>
  </GeneralOptions>
  <References>
    <ArrayOfString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <string>C:\Windows\Microsoft.NET\Framework64\v4.0.30319\mscorlib.dll</string>
      <string>System.dll</string>
      <string>System.ComponentModel.DataAnnotations.dll</string>
      <string>System.Core.dll</string>
      <string>System.Net.Http.dll</string>
      <string>System.Web.Extensions.dll</string>
      <string>System.Windows.Forms.dll</string>
      <string>System.Xaml.dll</string>
      <string>System.Xml.dll</string>
      <string>System.Xml.Linq.dll</string>
      <string>Microsoft.CSharp.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\InfragisticsWPF4.DataPresenter.v15.1.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\Infralution.Localization.Wpf.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\NinjaTrader.Core.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\NinjaTrader.Gui.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\SharpDX.dll</string>
      <string>*ProgramFiles*\NinjaTrader 8\bin\SharpDX.Direct2D1.dll</string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll</string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\IFVGBOT.dll</string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\ORBBOT.dll</string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll</string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\<EMAIL></string>
      <string>*MyDocuments*\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll</string>
      <string>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\WindowsBase.dll</string>
      <string>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\PresentationCore.dll</string>
      <string>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\PresentationFramework.dll</string>
      <string>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\UIAutomationProvider.dll</string>
    </ArrayOfString>
  </References>
</NinjaTrader>