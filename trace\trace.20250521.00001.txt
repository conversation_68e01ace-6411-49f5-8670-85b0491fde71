******************* Session Start (Version *******) *******************
2025-05-21 08:59:00:639 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-05-21 08:59:00:642 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-05-21 08:59:00:897 Core.Instrumentation.ActivitySource: enabled=True randomPercent=70.22993 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-05-21 08:59:00:923 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-05-21 08:59:01:975 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-21 08:59:01:983 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.8817878' renewSecs='2400.4408939'
2025-05-21 08:59:03:039 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-05-21 08:59:03:885 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-05-21 08:59:04:723 PrimaryMonitorWPFDPIScale=1.00
2025-05-21 08:59:05:466 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab (Beta) Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-05-21 08:59:05:733 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-05-21 08:59:05:733 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-05-21 08:59:05:733 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-05-21 08:59:05:734 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-05-21 08:59:05:734 OSLanguage='en-US'
2025-05-21 08:59:05:734 OSEnvironment='64bit'
2025-05-21 08:59:05:734 Processors=8
2025-05-21 08:59:05:734 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-05-21 08:59:06:800 ProcessorSpeed=2.4 GHz
2025-05-21 08:59:06:800 PhysicalMemory=8192 MB
2025-05-21 08:59:07:038 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-05-21 08:59:07:038 Monitors=2/1280x720|1920x1080
2025-05-21 08:59:07:038 .NET/CLR Version='4.8'/64bit
2025-05-21 08:59:07:041 SQLiteVersion='1.0.116.0'
2025-05-21 08:59:07:042 ApplicationTimezone=EST +0 hour(s)
2025-05-21 08:59:07:042 ApplicationTimezone=UTC -4 hour(s)
2025-05-21 08:59:07:042 LocalTimezone=EST +0 hour(s)
2025-05-21 08:59:07:042 LocalTimezone=UTC -4 hour(s)
2025-05-21 08:59:07:149 DirectXRenderingHW
2025-05-21 08:59:07:150 Copying custom assemblies...
2025-05-21 08:59:07:204 Loading custom assemblies...
2025-05-21 08:59:07:204 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-05-21 08:59:07:537 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-05-21 08:59:07:564 Deleting temporary files...
2025-05-21 08:59:07:730 Copying db and restoring templates...
2025-05-21 08:59:07:808 Loading third party assemblies...
2025-05-21 08:59:07:935 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-05-21 08:59:07:935 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-05-21 08:59:07:935 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-05-21 08:59:07:936 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-05-21 08:59:07:936 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-05-21 08:59:07:936 Initializing database...
2025-05-21 08:59:07:936 Loading master instruments...
2025-05-21 08:59:08:364 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-05-21 08:59:08:369 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-05-21 08:59:09:068 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-05-21 08:59:09:290 Loading instruments...
2025-05-21 08:59:09:934 Loading accounts...
2025-05-21 08:59:10:133 Loading users...
2025-05-21 08:59:10:199 Downloading server info...
2025-05-21 08:59:10:200 Starting instrument management...
2025-05-21 08:59:10:217 Starting timer...
2025-05-21 08:59:10:217 Creating file type watcher...
2025-05-21 08:59:10:218 Setting ATI...
2025-05-21 08:59:10:258 Connecting ATI server...
2025-05-21 08:59:10:259 Server.AtiServer.Connect0
2025-05-21 08:59:10:260 Starting adapter server...
2025-05-21 08:59:10:275 Server.AtiServer.Connect1: Port='36973'
2025-05-21 08:59:10:281 Server.AtiServer.Connect2
2025-05-21 08:59:10:289 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-21 08:59:10:296 Starting bars dictionary...
2025-05-21 08:59:10:299 Starting recorder...
2025-05-21 08:59:10:305 Starting server(s)...
2025-05-21 08:59:10:393 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3127
2025-05-21 08:59:10:393 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-05-21 08:59:10:393 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9780
2025-05-21 08:59:10:393 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=12032
2025-05-21 08:59:10:393 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5059
2025-05-21 08:59:10:526 Required resource key 'brushOrderWorking' is missing.
2025-05-21 08:59:10:526 Required resource key 'brushOrderAccepted' is missing.
2025-05-21 08:59:10:527 Required resource key 'brushOrderPartFilled' is missing.
2025-05-21 08:59:10:527 Required resource key 'brushOrderInitialized' is missing.
2025-05-21 08:59:10:627 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarPrice='' SnapModeDisabled='' SnapModePrice='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-05-21 08:59:10:630 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-05-21 08:59:10:631 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-21 08:59:10:632 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-05-21 08:59:10:635 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='Ctrl+Z' SimulatedOrder=''
2025-05-21 08:59:10:656 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-21 08:59:10:656 OrderEntryHotKeys=disabled
2025-05-21 08:59:10:659 AutoClose=disabled
2025-05-21 08:59:11:994 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-05-21 08:59:15:415 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=1 RolloverCollection=260 TradingHours=0
2025-05-21 08:59:15:838 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 08:59:16:164 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 08:59:16:282 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 08:59:17:024 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.09ms InstrumentLists=0.47ms MasterInstruments=0.02ms Messages=0.38ms Risks=7.58ms RolloverCollection=1599.52ms TradingHours=0.04ms
2025-05-21 08:59:17:025 Starting server message polling timer with interval 3600 seconds...
2025-05-21 08:59:17:596 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 08:59:17:826 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-05-21 08:59:18:577 2 Chart Chart1Tab1 1 Ser 2 Ind 1 DrawObj Chart2Tab1 1 Ser 2 Ind 6 DrawObj 
2025-05-21 08:59:32:626 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-21 08:59:32:678 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-21 runAsProcess=False
2025-05-21 08:59:32:693 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 08:59:32:696 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 08:59:32:707 (TRADIFY) Cbi.Connection.Connect1
2025-05-21 08:59:32:721 (TRADIFY) Cbi.Connection.Connect2
2025-05-21 08:59:32:721 (TRADIFY) Cbi.Connection.Connect3
2025-05-21 08:59:32:728 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-21 08:59:32:735 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:32:737 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount3' displayName='SimAccount3' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount3'
2025-05-21 08:59:32:743 (TRADIFY) Cbi.Connection.Connect4
2025-05-21 08:59:32:756 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 08:59:32:760 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-21 08:59:32:773 (TRADIFY) Cbi.Connection.Connect5
2025-05-21 08:59:32:776 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-21 08:59:32:801 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-21 08:59:32:929 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-21 08:59:32:930 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-21 08:59:32:930 Core.Instrumentation.LogActivity: activityType=Adapter errorCode=NoError errorMessage=''
2025-05-21 08:59:33:477 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-21 08:59:33:477 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.9676156' renewSecs='2400.4838078'
2025-05-21 08:59:33:505 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-21 08:59:33:904 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-21 08:59:33:905 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-21 08:59:33:906 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-21 08:59:33:910 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-21 08:59:35:059 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-21 08:59:35:484 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-21 08:59:35:485 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000017' displayName='TDYA150355451300000017' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 08:59:35:485 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:35:485 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000017'
2025-05-21 08:59:35:485 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-21 08:59:35:486 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000019' displayName='TDYA150355451300000019' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 08:59:35:488 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000019'
2025-05-21 08:59:35:490 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-21 08:59:35:504 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:35:504 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:35:504 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:35:504 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:35:517 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-21 08:59:35:522 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 08:59:35:792 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 08:59:35:805 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:805 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:806 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:806 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:806 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:809 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:811 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 08:59:35:845 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-21 08:59:35:845 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000019' fcm=''
2025-05-21 08:59:35:845 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-21 08:59:35:845 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-21 08:59:35:846 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-21 08:59:35:846 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000017' fcm=''
2025-05-21 08:59:35:846 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount3' fcm=''
2025-05-21 08:59:35:907 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-21 08:59:35:913 (TRADIFY) Cbi.Account.Restore.AtmTerminate3: account='SimAccount3' fcm='' atmId=********* reason='!hasActiveOrder'
2025-05-21 08:59:35:915 (TRADIFY) Cbi.Account.Restore.AtmTerminate3: account='SimAccount3' fcm='' atmId=********* reason='!hasActiveOrder'
2025-05-21 08:59:35:915 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount3' fcm=''
2025-05-21 08:59:36:015 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000017' fcm=''
2025-05-21 08:59:36:031 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000019' fcm=''
2025-05-21 08:59:36:329 (TRADIFY) Cbi.Account.Restore.AtmTerminate3: account='SimAccount1' fcm='' atmId=********* reason='!hasActiveOrder'
2025-05-21 08:59:36:330 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-21 08:59:36:793 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-21 08:59:37:092 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-21 08:59:37:093 (TRADIFY) Core.Connection.Statistics: connectAttempts=1/4319.9ms
2025-05-21 08:59:37:093 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-21 08:59:37:094 Server.HdsClient.Connect: type=HDS server='hds-us-nt-017.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-21 08:59:37:253 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 08:59:37:254 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 08:59:37:254 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-21 08:59:37:289 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-21 08:59:37:295 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-21 08:59:37:295 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-21 08:59:37:352 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='20/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 08:59:37:787 (TRADIFY) Cbi.CustomOrder.OnMarketData.IsSimulatedStopEnabled.Submit: orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=TriggerPending instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=21414.25 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39058 time='2025-05-20 19:04:05' gtd='2099-12-01' statementDate='2025-05-20'
2025-05-21 08:59:37:796 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='78292235502e4b2295eb945b7ef32852' maxFillQuantity=1 price=5923.25 thread=44
2025-05-21 08:59:37:796 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='78292235502e4b2295eb945b7ef32852' fillQuantity=1 price=5923.25
2025-05-21 08:59:37:804 (TRADIFY) Cbi.Account.Submit0: realOrderState=TriggerPending isPendingSubmit=False orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=TriggerPending instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=21414.25 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39058 time='2025-05-20 19:04:05' gtd='2099-12-01' statementDate='2025-05-20'
2025-05-21 08:59:37:818 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='78292235502e4b2295eb945b7ef32852' account='Sim101' name='Profit target' orderState=PartFilled instrument='MES JUN25' orderAction=BuyToCover limitPrice=5923.75 stopPrice=0 quantity=2 orderType='Limit' filled=1 averageFillPrice=5923.25 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:819 (TRADIFY) Cbi.Account.Submit1: realOrderState=TriggerPending orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=TriggerPending instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=21414.25 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39058 time='2025-05-20 19:04:05' gtd='2099-12-01' statementDate='2025-05-20'
2025-05-21 08:59:37:820 (TRADIFY) Cbi.Simulator.Submit: realOrderState=TriggerPending orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=21414.25 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39058 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 08:59:37:820 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21414.25 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:830 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='78292235502e4b2295eb945b7ef32852' account='Sim101' name='Profit target' orderState=PartFilled instrument='MES JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=5923.75 stopPrice=0 quantity=2 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=1 averageFillPrice=5923.25 onBehalfOf='' id=39054 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' order.MaxQuantitySeen=2 order2.maxQuantitySeen=-1 e.Filled=1 order2.QuantityChanged=1
2025-05-21 08:59:37:848 (TRADIFY) Cbi.Account.Change0: realOrderState=TriggerPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=ChangePending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=2 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=0 averageFillPrice=0 onBehalfOf='' id=39053 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=6065.5 quantityChanged=1
2025-05-21 08:59:37:848 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=ChangePending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:850 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=0 averageFillPrice=0 onBehalfOf='' id=39053 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=6065.5
2025-05-21 08:59:37:850 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=ChangeSubmitted instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=6
2025-05-21 08:59:37:850 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=TriggerPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=7
2025-05-21 08:59:37:862 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='68030e6f835845868dff8e98e73bc94f' account='Sim101' instrument='MES JUN25' exchange=Globex price=5923.25 quantity=1 marketPosition=Long operation=Add orderID='78292235502e4b2295eb945b7ef32852' isSod=False time='2025-05-21 08:59:37' statementDate='2025-05-21'
2025-05-21 08:59:37:871 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='68030e6f835845868dff8e98e73bc94f' profitCurrencyBeforeCommissionAndFees=87.5
2025-05-21 08:59:37:872 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MES JUN25' account='Sim101' avgPrice=5940.75 quantity=3 marketPosition=Short operation=Update
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='f014b1080bf841c1a58e86238f4e7c24' maxFillQuantity=1 price=5923.25 thread=44
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='f014b1080bf841c1a58e86238f4e7c24' fillQuantity=1 price=5923.25
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='f014b1080bf841c1a58e86238f4e7c24' account='Sim101' name='Profit target' orderState=PartFilled instrument='MES JUN25' orderAction=BuyToCover limitPrice=5923.75 stopPrice=0 quantity=2 orderType='Limit' filled=1 averageFillPrice=5923.25 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='f014b1080bf841c1a58e86238f4e7c24' account='Sim101' name='Profit target' orderState=PartFilled instrument='MES JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=5923.75 stopPrice=0 quantity=2 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=1 averageFillPrice=5923.25 onBehalfOf='' id=39056 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' order.MaxQuantitySeen=2 order2.maxQuantitySeen=-1 e.Filled=1 order2.QuantityChanged=1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.Change0: realOrderState=TriggerPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39055 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=6065.5 quantityChanged=1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=ChangePending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39055 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=6065.5
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=ChangeSubmitted instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=TriggerPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='5f716b388917419f8c029f3ccfa8b370' account='Sim101' instrument='MES JUN25' exchange=Globex price=5923.25 quantity=1 marketPosition=Long operation=Add orderID='f014b1080bf841c1a58e86238f4e7c24' isSod=False time='2025-05-21 08:59:37' statementDate='2025-05-21'
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='5f716b388917419f8c029f3ccfa8b370' profitCurrencyBeforeCommissionAndFees=87.5
2025-05-21 08:59:37:883 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MES JUN25' account='Sim101' avgPrice=5940.75 quantity=2 marketPosition=Short operation=Update
2025-05-21 08:59:37:884 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='78292235502e4b2295eb945b7ef32852' maxFillQuantity=1 price=5923.25 thread=44
2025-05-21 08:59:37:884 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='78292235502e4b2295eb945b7ef32852' fillQuantity=1 price=5923.25
2025-05-21 08:59:37:884 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='78292235502e4b2295eb945b7ef32852' account='Sim101' name='Profit target' orderState=Filled instrument='MES JUN25' orderAction=BuyToCover limitPrice=5923.75 stopPrice=0 quantity=2 orderType='Limit' filled=2 averageFillPrice=5923.25 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:902 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=TriggerPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=TriggerPending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=0 averageFillPrice=0 onBehalfOf='' id=39053 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:911 (TRADIFY) Cbi.Account.Cancel0: realOrderState=TriggerPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=CancelSubmitted instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=0 averageFillPrice=0 onBehalfOf='' id=39053 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:911 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=CancelPending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=8
2025-05-21 08:59:37:911 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=CancelSubmitted instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='06e192be9ba44c80b69842de7a0b2b3a' filled=0 averageFillPrice=0 onBehalfOf='' id=39053 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:911 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=CancelSubmitted instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=9
2025-05-21 08:59:37:913 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='c362fefaa7ae4bc6a1c6a22122538241' account='Sim101' name='Trail stop' orderState=Cancelled instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='2b49a3b4e41947378f1be49c46961011' account='Sim101' instrument='MES JUN25' exchange=Globex price=5923.25 quantity=1 marketPosition=Long operation=Add orderID='78292235502e4b2295eb945b7ef32852' isSod=False time='2025-05-21 08:59:37' statementDate='2025-05-21'
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='2b49a3b4e41947378f1be49c46961011' profitCurrencyBeforeCommissionAndFees=87.5
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MES JUN25' account='Sim101' avgPrice=5940.75 quantity=1 marketPosition=Short operation=Update
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='f014b1080bf841c1a58e86238f4e7c24' maxFillQuantity=1 price=5923.25 thread=44
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='f014b1080bf841c1a58e86238f4e7c24' fillQuantity=1 price=5923.25
2025-05-21 08:59:37:914 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='f014b1080bf841c1a58e86238f4e7c24' account='Sim101' name='Profit target' orderState=Filled instrument='MES JUN25' orderAction=BuyToCover limitPrice=5923.75 stopPrice=0 quantity=2 orderType='Limit' filled=2 averageFillPrice=5923.25 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=TriggerPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=CancelPending instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39055 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.Cancel0: realOrderState=TriggerPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=Cancelled instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39055 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=CancelPending instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=Cancelled instrument='MES JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=6065.5 quantity=1 tif=Gtc oco='3ef70bb2ef5749478fff861ef8fd93c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39055 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=CancelSubmitted instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='d29e6e186d4844b6a090cb727157296b' account='Sim101' name='Trail stop' orderState=Cancelled instrument='MES JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=6065.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='d79dec25f5e142a8837f3bd54bbed3ad' account='Sim101' instrument='MES JUN25' exchange=Globex price=5923.25 quantity=1 marketPosition=Long operation=Add orderID='f014b1080bf841c1a58e86238f4e7c24' isSod=False time='2025-05-21 08:59:37' statementDate='2025-05-21'
2025-05-21 08:59:37:929 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='d79dec25f5e142a8837f3bd54bbed3ad' profitCurrencyBeforeCommissionAndFees=87.5
2025-05-21 08:59:37:930 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MES JUN25' account='Sim101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 08:59:37:962 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21414.25 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:962 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21414.25 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:962 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='12e3fd2693d44597bacb7060a90369ff' maxFillQuantity=2 price=21313.75 thread=42
2025-05-21 08:59:37:962 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='12e3fd2693d44597bacb7060a90369ff' fillQuantity=2 price=21313.75
2025-05-21 08:59:37:962 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='12e3fd2693d44597bacb7060a90369ff' account='SimAccount1' name='Trail stop' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21414.25 quantity=2 orderType='Market' filled=2 averageFillPrice=21313.75 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:979 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21459.5 stopPrice=0 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39059 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:979 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21459.5 stopPrice=0 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39059 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:979 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21459.5 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:980 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21459.5 stopPrice=0 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39059 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 08:59:37:981 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21459.5 stopPrice=0 quantity=2 tif=Gtc oco='1ee1bca1d0e0494a98d47d3e787c37ea' filled=0 averageFillPrice=0 onBehalfOf='' id=39059 time='2025-05-21 08:59:37' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 08:59:37:981 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21459.5 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 08:59:37' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:37:982 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='d78e38ab648c419986f7c3b6c2b52f44' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21313.75 quantity=2 marketPosition=Short operation=Add orderID='12e3fd2693d44597bacb7060a90369ff' isSod=False time='2025-05-21 08:59:37' statementDate='2025-05-21'
2025-05-21 08:59:37:982 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='d78e38ab648c419986f7c3b6c2b52f44' profitCurrencyBeforeCommissionAndFees=-4230
2025-05-21 08:59:37:982 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 08:59:38:083 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='82632f86fec54308b64f16481249e2af' account='SimAccount1' name='Profit target' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21459.5 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 08:59:38' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 08:59:38:138 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:38:139 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount1' fcm='' fcmDate='2025-05-20'
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 08:59:38:140 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='Sim101' fcm='' fcmDate='2025-05-20'
2025-05-21 08:59:38:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:38:141 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount2' fcm='' fcmDate='2025-05-20'
2025-05-21 08:59:38:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount3' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 08:59:38:141 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount3' fcm='' fcmDate='2025-05-20'
2025-05-21 08:59:38:643 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 08:59:38:643 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 08:59:39:034 SQLite error (17): statement aborts at 50: [SELECT Id,Account,BarIndex,Commission,Exchange,ExecutionId,Fee,Instrument,IsEntry,IsEntryStrategy,IsExit,IsExitStrategy,LotSize,MarketPosition,MaxPrice,MinPrice,Name,OrderId,Position,P
2025-05-21 08:59:58:916 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='20/05/2025 12:00:00 AM' to='20/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 08:59:59:520 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:00:02:121 (TRADIFY) Cbi.Position.Close0: instrument='MNQ JUN25' account='SimAccount2' currentQuantity=-8 signalName='Close'
2025-05-21 09:00:02:123 (TRADIFY) Cbi.Position.Close2: instrument='MNQ JUN25' account='SimAccount2' currentQuantity=-8
2025-05-21 09:00:02:131 (TRADIFY) Cbi.Account.CreateOrder: orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Initialized instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39060 time='2025-05-21 09:00:02' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:00:02:138 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Initialized instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39060 time='2025-05-21 09:00:02' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:00:02:138 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39060 time='2025-05-21 09:00:02' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:00:02:138 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39060 time='2025-05-21 09:00:02' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:00:02:138 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:00:02:256 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Accepted instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:00:02:256 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Working instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:00:02:257 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='e3df08e77c154a8ea190e61364e0f16e' maxFillQuantity=2 price=21313.75 thread=43
2025-05-21 09:00:02:257 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='e3df08e77c154a8ea190e61364e0f16e' fillQuantity=2 price=21313.75
2025-05-21 09:00:02:257 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=PartFilled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=2 averageFillPrice=21313.75 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:00:02:257 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='a13116148adc426489f7bc854a14c54f' account='SimAccount2' instrument='MNQ JUN25' exchange=Globex price=21313.75 quantity=2 marketPosition=Long operation=Add orderID='e3df08e77c154a8ea190e61364e0f16e' isSod=False time='2025-05-21 09:00:02' statementDate='2025-05-21'
2025-05-21 09:00:02:258 (TRADIFY) Cbi.Account.ExecutionUpdateCallback.PositionClose: currentQuantity=-6
2025-05-21 09:00:02:258 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='a13116148adc426489f7bc854a14c54f' profitCurrencyBeforeCommissionAndFees=409
2025-05-21 09:00:02:258 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount2' avgPrice=21416 quantity=6 marketPosition=Short operation=Update
2025-05-21 09:00:02:258 (TRADIFY) Cbi.Account.PositionUpdateCallback.PositionClose1: instrument='MNQ JUN25' account='SimAccount2' avgPrice=21416 quantity=6 marketPosition=Short operation=Update
2025-05-21 09:00:02:259 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='e3df08e77c154a8ea190e61364e0f16e' maxFillQuantity=6 price=21314 thread=43
2025-05-21 09:00:02:259 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='e3df08e77c154a8ea190e61364e0f16e' fillQuantity=6 price=21314
2025-05-21 09:00:02:260 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21313.9375 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:00:02:281 (TRADIFY) Cbi.Account.OrderUpdateCallback.PositionClose1: orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21313.9375 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=5
2025-05-21 09:00:02:281 (TRADIFY) Cbi.Account.OrderUpdateCallback.PositionClose2: orderId='e3df08e77c154a8ea190e61364e0f16e' account='SimAccount2' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21313.9375 time='2025-05-21 09:00:02' statementDate='2025-05-21' error=NoError comment='' nr=5
2025-05-21 09:00:02:281 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='bbb4cd6bff1d44478586d7bf6db94bde' account='SimAccount2' instrument='MNQ JUN25' exchange=Globex price=21314 quantity=6 marketPosition=Long operation=Add orderID='e3df08e77c154a8ea190e61364e0f16e' isSod=False time='2025-05-21 09:00:02' statementDate='2025-05-21'
2025-05-21 09:00:02:281 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='bbb4cd6bff1d44478586d7bf6db94bde' profitCurrencyBeforeCommissionAndFees=1224
2025-05-21 09:00:02:281 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount2' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:00:03:239 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:00:03:239 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:00:03:239 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:00:03:239 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:01:33:144 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:02:08:603 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 09:02:08:708 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-21 09:02:08:941 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:04:13:213 (TRADIFY) Gui.ControlCenter.OnDisconnect
2025-05-21 09:04:13:222 (TRADIFY) Cbi.Connection.Disconnect
2025-05-21 09:04:13:223 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:13:223 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:223 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:223 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:223 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:224 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:224 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:224 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:13:233 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:13:237 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close1
2025-05-21 09:04:13:241 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:13:241 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Disconnecting priceStatus=Disconnecting
2025-05-21 09:04:13:241 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=False
2025-05-21 09:04:13:241 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Disconnected
2025-05-21 09:04:13:246 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close3
2025-05-21 09:04:13:247 SQLite error (17): statement aborts at 44: [UPDATE MasterInstruments SET AutoLiquidation=?,Currency=?,Description=?,InstrumentType=?,IsServerSupported=?,MergePolicy=?,Name=?,PointValue=?,PriceLevel=?,TradingHours=?,TickSize=?,Ur
2025-05-21 09:04:13:256 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close4
2025-05-21 09:04:13:256 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close5
2025-05-21 09:04:13:256 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-05-21 09:04:13:256 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close7
2025-05-21 09:04:13:257 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close8
2025-05-21 09:04:13:287 (TRADIFY) Tradovate.Adapter.DisposeMarketData0
2025-05-21 09:04:13:804 (TRADIFY) Tradovate.Adapter.DisposeTrade0
2025-05-21 09:04:14:817 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:14:817 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:818 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:819 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:819 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:819 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:819 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:819 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:14:821 Flushing DB thread
2025-05-21 09:04:14:824 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:14:825 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:14:825 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Using args status=Disconnected
2025-05-21 09:04:15:038 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 09:04:21:665 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-21 09:04:21:669 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-21 runAsProcess=False
2025-05-21 09:04:21:669 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:21:669 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:21:670 (TRADIFY) Cbi.Connection.Connect1
2025-05-21 09:04:21:674 (TRADIFY) Cbi.Connection.Connect2
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.Connect3
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-21 09:04:21:675 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-21 09:04:21:675 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-21 09:04:21:675 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:21:675 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount3' displayName='SimAccount3' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount3'
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.Connect4
2025-05-21 09:04:21:675 (TRADIFY) Cbi.Connection.Connect5
2025-05-21 09:04:21:675 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-21 09:04:21:678 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-21 09:04:22:414 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-21 09:04:22:414 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.8855631' renewSecs='2400.********'
2025-05-21 09:04:22:418 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-21 09:04:22:825 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-21 09:04:22:825 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-21 09:04:22:826 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-21 09:04:22:826 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-21 09:04:23:556 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000017' displayName='TDYA150355451300000017' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000017'
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000019' displayName='TDYA150355451300000019' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000019'
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:04:23:557 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:04:23:562 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-21 09:04:23:562 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 09:04:23:849 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-21 09:04:24:258 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-21 09:04:24:258 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-21 09:04:24:259 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-21 09:04:24:259 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:496 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount3' fcm=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000017' fcm=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:24:498 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000019' fcm=''
2025-05-21 09:04:25:186 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-21 09:04:25:521 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-21 09:04:25:529 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000019' fcm=''
2025-05-21 09:04:25:640 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000017' fcm=''
2025-05-21 09:04:25:650 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount3' fcm=''
2025-05-21 09:04:25:837 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-21 09:04:25:841 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-21 09:04:25:842 (TRADIFY) Core.Connection.Statistics: connectAttempts=2/4243.4ms
2025-05-21 09:04:25:842 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-21 09:04:25:842 Server.HdsClient.Connect: type=HDS server='hds-us-nt-008.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-21 09:04:25:863 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:04:25:864 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:04:25:864 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-21 09:04:25:864 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-21 09:04:25:864 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-21 09:04:25:864 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-21 09:04:25:879 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:04:25:879 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:04:27:410 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:04:27:410 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:04:53:008 (null) Cbi.Account.ResetSimulationAccount.Start: account='SimAccount4'
2025-05-21 09:04:53:017 (null) Cbi.Account.ResetSimulationAccount.End: account='SimAccount4'
2025-05-21 09:04:53:072 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:53:072 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount4'
2025-05-21 09:04:53:072 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:04:53:072 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount4' fcm=''
2025-05-21 09:04:53:072 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount4' fcm=''
2025-05-21 09:04:54:057 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount4' fcm='' fcmDate='2025-05-20'
2025-05-21 09:04:55:691 (TRADIFY) Gui.ControlCenter.OnDisconnect
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Connection.Disconnect
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:691 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:04:55:692 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:55:692 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close1
2025-05-21 09:04:55:692 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close3
2025-05-21 09:04:55:696 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:04:55:696 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Disconnecting priceStatus=Disconnecting
2025-05-21 09:04:55:696 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=False
2025-05-21 09:04:55:696 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Disconnected
2025-05-21 09:04:55:700 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close4
2025-05-21 09:04:55:700 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close5
2025-05-21 09:04:55:700 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-05-21 09:04:55:700 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close7
2025-05-21 09:04:55:700 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close8
2025-05-21 09:04:55:700 (TRADIFY) Tradovate.Adapter.DisposeMarketData0
2025-05-21 09:04:56:207 (TRADIFY) Tradovate.Adapter.DisposeTrade0
2025-05-21 09:04:56:754 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:209 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:210 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:210 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:04:57:210 Flushing DB thread
2025-05-21 09:04:57:210 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:57:214 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:04:57:214 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Using args status=Disconnected
2025-05-21 09:04:59:511 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-21 09:04:59:515 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-21 runAsProcess=False
2025-05-21 09:04:59:515 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:59:515 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:59:515 (TRADIFY) Cbi.Connection.Connect1
2025-05-21 09:04:59:517 (TRADIFY) Cbi.Connection.Connect2
2025-05-21 09:04:59:518 (TRADIFY) Cbi.Connection.Connect3
2025-05-21 09:04:59:518 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-21 09:04:59:518 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:59:518 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-21 09:04:59:518 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:04:59:518 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:59:519 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-21 09:04:59:519 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-21 09:04:59:519 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:59:519 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-21 09:04:59:519 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount3' displayName='SimAccount3' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount3'
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount4' displayName='SimAccount4' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount4'
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Connection.Connect4
2025-05-21 09:04:59:521 (TRADIFY) Cbi.Connection.Connect5
2025-05-21 09:04:59:521 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-21 09:04:59:525 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-21 09:05:00:200 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-21 09:05:00:200 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.9262901' renewSecs='2400.********'
2025-05-21 09:05:00:205 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-21 09:05:00:608 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-21 09:05:00:608 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-21 09:05:00:608 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-21 09:05:00:610 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-21 09:05:01:346 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000017' displayName='TDYA150355451300000017' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:05:01:346 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:05:01:346 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000017'
2025-05-21 09:05:01:346 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000019' displayName='TDYA150355451300000019' fcm='' denomination=UsDollar forexLotSize=1
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000019'
2025-05-21 09:05:01:347 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:05:01:350 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:05:01:350 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:05:01:350 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000019' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:05:01:352 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-21 09:05:01:352 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 09:05:01:633 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-21 09:05:02:043 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-21 09:05:02:043 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-21 09:05:02:043 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-21 09:05:02:045 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-21 09:05:02:350 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:05:02:350 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:350 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount3' fcm=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:351 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount4' fcm=''
2025-05-21 09:05:02:352 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount4' fcm=''
2025-05-21 09:05:02:352 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:352 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000017' fcm=''
2025-05-21 09:05:02:353 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:353 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-21 09:05:02:354 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:05:02:354 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000019' fcm=''
2025-05-21 09:05:02:359 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount3' fcm=''
2025-05-21 09:05:02:625 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-21 09:05:02:995 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-21 09:05:03:099 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000017' fcm=''
2025-05-21 09:05:03:110 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000019' fcm=''
2025-05-21 09:05:03:269 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-21 09:05:03:273 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-21 09:05:03:273 (TRADIFY) Core.Connection.Statistics: connectAttempts=3/4079.6ms
2025-05-21 09:05:03:274 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-21 09:05:03:274 Server.HdsClient.Connect: type=HDS server='hds-us-nt-015.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-21 09:05:03:291 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:05:03:291 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:05:03:291 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-21 09:05:03:291 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-21 09:05:03:291 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-21 09:05:03:291 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-21 09:05:03:301 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:05:03:301 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:05:04:816 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:05:04:816 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='21/05/2025 12:00:00 AM' to='21/05/2025 12:00:00 AM' period='1 Minute'
2025-05-21 09:06:41:708 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='20/05/2025 12:00:00 AM' to='22/05/2025 12:00:00 AM' period='Daily'
2025-05-21 09:06:41:708 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='20/05/2025 12:00:00 AM' to='22/05/2025 12:00:00 AM' period='Daily'
2025-05-21 09:06:42:130 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='22/05/2025 12:00:00 AM' period='Daily'
2025-05-21 09:06:42:394 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='21/05/2025 12:00:00 AM' to='22/05/2025 12:00:00 AM' period='Daily'
2025-05-21 09:25:00:490 (TRADIFY) Cbi.Account.CreateOrder: orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Initialized instrument='MNQ JUN25' orderAction=SellShort orderType='Limit' limitPrice=21268 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39061 time='2025-05-21 09:25:00' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:25:00:550 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Initialized instrument='MNQ JUN25' orderAction=SellShort orderType='Limit' limitPrice=21268 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39061 time='2025-05-21 09:25:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:25:00:552 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Initialized instrument='MNQ JUN25' orderAction=SellShort orderType='Limit' limitPrice=21268 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39061 time='2025-05-21 09:25:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:25:00:553 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Submitted instrument='MNQ JUN25' orderAction=SellShort orderType='Limit' limitPrice=21268 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39061 time='2025-05-21 09:25:00' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:25:00:553 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Submitted instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:25:00' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:00:728 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Accepted instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:25:00' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:00:729 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Working instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:25:00' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:218 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=1 price=21268 thread=43
2025-05-21 09:25:02:218 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=1 price=21268
2025-05-21 09:25:02:218 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=PartFilled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=1 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:218 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='41cca1d1b02248439f37716ef486be43' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=1 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:238 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=1 marketPosition=Short operation=Add
2025-05-21 09:25:02:251 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=1 price=21268 thread=43
2025-05-21 09:25:02:252 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=1 price=21268
2025-05-21 09:25:02:252 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=PartFilled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=2 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:252 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='2884127652a74dcfb6bf5cebff7146ff' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=1 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:252 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=2 marketPosition=Short operation=Update
2025-05-21 09:25:02:253 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=1 price=21268 thread=43
2025-05-21 09:25:02:253 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=1 price=21268
2025-05-21 09:25:02:253 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=PartFilled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=3 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:253 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='373a01e282464e88951aa96ad8279750' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=1 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:254 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=3 marketPosition=Short operation=Update
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=1 price=21268 thread=43
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=1 price=21268
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=PartFilled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=4 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='2b2c1877b7cd4ddcacee82962ef23533' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=1 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=4 marketPosition=Short operation=Update
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=2 price=21268 thread=43
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=2 price=21268
2025-05-21 09:25:02:257 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=PartFilled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=6 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:258 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='87628649bdf64b62bf5147ab18cb547d' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=2 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:258 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=6 marketPosition=Short operation=Update
2025-05-21 09:25:02:312 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' maxFillQuantity=2 price=21268 thread=43
2025-05-21 09:25:02:312 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='54eb83e1410b4ce68b18c55b97dfabd0' fillQuantity=2 price=21268
2025-05-21 09:25:02:312 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='54eb83e1410b4ce68b18c55b97dfabd0' account='SimAccount4' name='SE' orderState=Filled instrument='MNQ JUN25' orderAction=SellShort limitPrice=21268 stopPrice=0 quantity=8 orderType='Limit' filled=8 averageFillPrice=21268 time='2025-05-21 09:25:02' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:02:330 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='58f8baf730d2446aa85feb13b0a2e88b' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21268 quantity=2 marketPosition=Short operation=Add orderID='54eb83e1410b4ce68b18c55b97dfabd0' isSod=False time='2025-05-21 09:25:02' statementDate='2025-05-21'
2025-05-21 09:25:02:334 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=8 marketPosition=Short operation=Update
2025-05-21 09:25:33:630 (TRADIFY) Cbi.Position.Close0: instrument='MNQ JUN25' account='SimAccount4' currentQuantity=-8 signalName='Close'
2025-05-21 09:25:33:645 (TRADIFY) Cbi.Position.Close2: instrument='MNQ JUN25' account='SimAccount4' currentQuantity=-8
2025-05-21 09:25:33:645 (TRADIFY) Cbi.Account.CreateOrder: orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Initialized instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39062 time='2025-05-21 09:25:33' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:25:33:647 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39062 time='2025-05-21 09:25:33' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:25:33:648 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39062 time='2025-05-21 09:25:33' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:25:33:648 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39062 time='2025-05-21 09:25:33' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:25:33:648 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Submitted instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:33:777 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Accepted instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:33:777 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Working instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=3
2025-05-21 09:25:33:780 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='e3a505f8a5134667aa8421d2c052a270' maxFillQuantity=2 price=21277.5 thread=43
2025-05-21 09:25:33:780 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='e3a505f8a5134667aa8421d2c052a270' fillQuantity=2 price=21277.5
2025-05-21 09:25:33:780 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=PartFilled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=2 averageFillPrice=21277.5 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:33:780 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='9c6b6ccd58eb49769c55d0307d33fd51' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21277.5 quantity=2 marketPosition=Long operation=Add orderID='e3a505f8a5134667aa8421d2c052a270' isSod=False time='2025-05-21 09:25:33' statementDate='2025-05-21'
2025-05-21 09:25:33:780 (TRADIFY) Cbi.Account.ExecutionUpdateCallback.PositionClose: currentQuantity=-6
2025-05-21 09:25:33:781 (TRADIFY) Cbi.Account.OnAddTrade: entryId='41cca1d1b02248439f37716ef486be43' exitId='9c6b6ccd58eb49769c55d0307d33fd51' profitCurrencyBeforeCommissionAndFees=-19
2025-05-21 09:25:33:781 (TRADIFY) Cbi.Account.OnAddTrade: entryId='2884127652a74dcfb6bf5cebff7146ff' exitId='9c6b6ccd58eb49769c55d0307d33fd51' profitCurrencyBeforeCommissionAndFees=-19
2025-05-21 09:25:33:781 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=6 marketPosition=Short operation=Update
2025-05-21 09:25:33:781 (TRADIFY) Cbi.Account.PositionUpdateCallback.PositionClose1: instrument='MNQ JUN25' account='SimAccount4' avgPrice=21268 quantity=6 marketPosition=Short operation=Update
2025-05-21 09:25:33:782 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='e3a505f8a5134667aa8421d2c052a270' maxFillQuantity=6 price=21277.75 thread=43
2025-05-21 09:25:33:782 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='e3a505f8a5134667aa8421d2c052a270' fillQuantity=6 price=21277.75
2025-05-21 09:25:33:782 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21277.6875 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:25:33:805 (TRADIFY) Cbi.Account.OrderUpdateCallback.PositionClose1: orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21277.6875 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=5
2025-05-21 09:25:33:805 (TRADIFY) Cbi.Account.OrderUpdateCallback.PositionClose2: orderId='e3a505f8a5134667aa8421d2c052a270' account='SimAccount4' name='Close' orderState=Filled instrument='MNQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=21277.6875 time='2025-05-21 09:25:33' statementDate='2025-05-21' error=NoError comment='' nr=5
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='9065f399c37249f08c17e9a7848f8577' account='SimAccount4' instrument='MNQ JUN25' exchange=Globex price=21277.75 quantity=6 marketPosition=Long operation=Add orderID='e3a505f8a5134667aa8421d2c052a270' isSod=False time='2025-05-21 09:25:33' statementDate='2025-05-21'
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.OnAddTrade: entryId='373a01e282464e88951aa96ad8279750' exitId='9065f399c37249f08c17e9a7848f8577' profitCurrencyBeforeCommissionAndFees=-19.5
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.OnAddTrade: entryId='2b2c1877b7cd4ddcacee82962ef23533' exitId='9065f399c37249f08c17e9a7848f8577' profitCurrencyBeforeCommissionAndFees=-19.5
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.OnAddTrade: entryId='87628649bdf64b62bf5147ab18cb547d' exitId='9065f399c37249f08c17e9a7848f8577' profitCurrencyBeforeCommissionAndFees=-39
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.OnAddTrade: entryId='58f8baf730d2446aa85feb13b0a2e88b' exitId='9065f399c37249f08c17e9a7848f8577' profitCurrencyBeforeCommissionAndFees=-39
2025-05-21 09:25:33:807 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='MNQ JUN25' account='SimAccount4' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:25:34:588 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount4' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:25:34:588 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount4' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:25:34:588 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount4' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:25:34:588 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount4' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:31:00:800 (TRADIFY) Cbi.Account.CreateOrder: orderId='97487a68bc354284a049a360cc55aa92' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39063 time='2025-05-21 09:31:00' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:31:00:819 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='97487a68bc354284a049a360cc55aa92' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39063 time='2025-05-21 09:31:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:00:819 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='97487a68bc354284a049a360cc55aa92' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39063 time='2025-05-21 09:31:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:00:820 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='97487a68bc354284a049a360cc55aa92' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39063 time='2025-05-21 09:31:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:00:843 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-21 09:31:00:843 (TRADIFY) Tradovate.Adapter.Submit1: orderId='97487a68bc354284a049a360cc55aa92' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39063 time='2025-05-21 09:31:00' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:01:289 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-21 09:31:01:323 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-21T13:31:01.148Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-21 09:31:01:405 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-21T13:31:01.148Z",
  "clOrdId": "97487a68bc354284a049a360cc55aa92",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-21 09:31:01:486 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='97487a68bc354284a049a360cc55aa92' orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:01' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-21 09:31:01:510 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 3,
  "orderType": "Market",
  "timeInForce": "GTC"
}'
2025-05-21 09:31:01:577 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-21T13:31:01.149Z",
  "commandStatus": "RiskRejected",
  "rejectReason": "LiquidationOnly",
  "text": "Your account is currently set to liquidation only due to Reset by MW. Please contact the Trade Desk for additional details."
}'
2025-05-21 09:31:01:643 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Rejected orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Rejected instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:01' statementDate='1800-01-01' error=OrderRejected comment='Your account is currently set to liquidation only due to Reset by MW. Please contact the Trade Desk for additional details.' nr=-1
2025-05-21 09:31:01:643 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-21T13:31:01.148Z",
  "clOrdId": "97487a68bc354284a049a360cc55aa92",
  "commandType": "New",
  "commandStatus": "RiskRejected",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-21 09:31:01:643 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-21T13:31:01.148Z",
  "action": "Sell",
  "ordStatus": "Rejected",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-21 09:31:06:707 (TRADIFY) Cbi.Account.CreateOrder: orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39064 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:31:06:715 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39064 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:715 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39064 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:715 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39064 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:31:06:715 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:837 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:837 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:838 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='deb11dee9c5349d9a929e88b182789ef' maxFillQuantity=1 price=21298.25 thread=42
2025-05-21 09:31:06:838 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='deb11dee9c5349d9a929e88b182789ef' fillQuantity=1 price=21298.25
2025-05-21 09:31:06:838 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=21298.25 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:838 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='0f29dc3fe6b2434cb50f6050a1a7d9f8' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21298.25 quantity=1 marketPosition=Short operation=Add orderID='deb11dee9c5349d9a929e88b182789ef' isSod=False time='2025-05-21 09:31:06' statementDate='2025-05-21'
2025-05-21 09:31:06:840 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21298.25 quantity=1 marketPosition=Short operation=Add
2025-05-21 09:31:06:840 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='deb11dee9c5349d9a929e88b182789ef' maxFillQuantity=2 price=21298 thread=42
2025-05-21 09:31:06:840 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='deb11dee9c5349d9a929e88b182789ef' fillQuantity=2 price=21298
2025-05-21 09:31:06:840 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='deb11dee9c5349d9a929e88b182789ef' account='SimAccount1' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=21298.********** time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:858 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='7f753e5860834e68bb783359d21130b3' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21298 quantity=2 marketPosition=Short operation=Add orderID='deb11dee9c5349d9a929e88b182789ef' isSod=False time='2025-05-21 09:31:06' statementDate='2025-05-21'
2025-05-21 09:31:06:858 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21298.********** quantity=3 marketPosition=Short operation=Update
2025-05-21 09:31:06:903 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='SimAccount1' instrument='NQ JUN25' id='*********' filled=3 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='deb11dee9c5349d9a929e88b182789ef+=3 ' outstandingOrders='' thread=18
2025-05-21 09:31:06:905 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='SimAccount1' instrument='NQ JUN25' id='*********' initialEntryOrderId='deb11dee9c5349d9a929e88b182789ef' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=3 exitOrders=''
2025-05-21 09:31:06:909 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='SimAccount1' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=3 limitPrice=0 stopPrice=21378.25 oco='f78f8c7877294bd4806f5b5c2915d455'
2025-05-21 09:31:06:909 (TRADIFY) Cbi.Account.CreateOrder: orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:31:06:918 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:918 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:918 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:31:06:918 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21378.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:06:925 (TRADIFY) Cbi.Account.CreateOrder: orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:31:06:935 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:935 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:31:06:935 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:31:06' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:31:06:935 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:31:06' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:07:027 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21378.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:07' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:07:048 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:31:07' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:07:048 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:31:07' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:31:005 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:31' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=21330.25 quantityChanged=3
2025-05-21 09:31:31:005 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21378.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:31' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:31:31:010 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21378.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:31' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 quantityChanged=3 stopPriceChanged=21330.25
2025-05-21 09:31:31:015 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21330.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39065 time='2025-05-21 09:31:31' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged='0' quantityChanged='3' stopPriceChanged='21330.25' delay=100
2025-05-21 09:31:31:015 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21330.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:31' statementDate='2025-05-21' error=NoError comment='' nr=4
2025-05-21 09:31:31:139 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21330.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:31:31' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21330.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='4418cf2cc418492685568828eae9ed5a' maxFillQuantity=2 price=21330.75 thread=42
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='4418cf2cc418492685568828eae9ed5a' fillQuantity=2 price=21330.75
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21330.25 quantity=3 orderType='Stop Market' filled=2 averageFillPrice=21330.75 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21330.25 quantity=3 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=2 averageFillPrice=21330.75 onBehalfOf='' id=39065 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21' order.MaxQuantitySeen=3 order2.maxQuantitySeen=3 e.Filled=2 order2.QuantityChanged=1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=21268.25 stopPriceChanged=0 quantityChanged=1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=21268.25 quantityChanged=1 stopPriceChanged=0
2025-05-21 09:32:12:810 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged='21268.25' quantityChanged='1' stopPriceChanged='0' delay=100
2025-05-21 09:32:12:811 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:811 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='3b79a488914549e3825805ff9854f3b5' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21330.75 quantity=2 marketPosition=Long operation=Add orderID='4418cf2cc418492685568828eae9ed5a' isSod=False time='2025-05-21 09:32:12' statementDate='2025-05-21'
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Account.OnAddTrade: entryId='0f29dc3fe6b2434cb50f6050a1a7d9f8' exitId='3b79a488914549e3825805ff9854f3b5' profitCurrencyBeforeCommissionAndFees=-650
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Account.OnAddTrade: entryId='7f753e5860834e68bb783359d21130b3' exitId='3b79a488914549e3825805ff9854f3b5' profitCurrencyBeforeCommissionAndFees=-655
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21298 quantity=1 marketPosition=Short operation=Update
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='4418cf2cc418492685568828eae9ed5a' maxFillQuantity=1 price=21331 thread=42
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='4418cf2cc418492685568828eae9ed5a' fillQuantity=1 price=21331
2025-05-21 09:32:12:817 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='4418cf2cc418492685568828eae9ed5a' account='SimAccount1' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21330.25 quantity=3 orderType='Stop Market' filled=3 averageFillPrice=21330.********** time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:838 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=ChangeSubmitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:838 (TRADIFY) Cbi.Account.Cancel0: realOrderState=ChangeSubmitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:838 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:839 (TRADIFY) Cbi.Account.QueueCancel: realOrderState=ChangeSubmitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:839 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='fb151ad3a25242a2916e32d31a3f9fd6' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21331 quantity=1 marketPosition=Long operation=Add orderID='4418cf2cc418492685568828eae9ed5a' isSod=False time='2025-05-21 09:32:12' statementDate='2025-05-21'
2025-05-21 09:32:12:841 (TRADIFY) Cbi.Account.OnAddTrade: entryId='7f753e5860834e68bb783359d21130b3' exitId='fb151ad3a25242a2916e32d31a3f9fd6' profitCurrencyBeforeCommissionAndFees=-660
2025-05-21 09:32:12:841 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:32:12:863 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:32:12:867 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:32:12:868 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='SimAccount1' currentQuantity=0 signalName='Close'
2025-05-21 09:32:12:868 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='SimAccount1'
2025-05-21 09:32:12:915 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:915 (TRADIFY) Cbi.Account.OrderUpdateCallback.RetryCancel: orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:915 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:916 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:12:916 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:12:916 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21268.25 stopPrice=0 quantity=1 tif=Gtc oco='f78f8c7877294bd4806f5b5c2915d455' filled=0 averageFillPrice=0 onBehalfOf='' id=39066 time='2025-05-21 09:32:12' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:32:12:916 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:12' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:13:026 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='3aab0eae91664207af0962a54ca664f1' account='SimAccount1' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21268.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:13' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:13:032 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:32:13:364 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:32:13:364 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:32:13:364 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:32:13:364 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:32:14:474 (TRADIFY) Cbi.Account.CreateOrder: orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39067 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:32:14:483 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39067 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:484 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39067 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:484 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39067 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:32:14:487 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:602 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:602 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:605 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='3f0e0ce5df054b6c82b1881e2d5fe844' maxFillQuantity=1 price=21331.25 thread=42
2025-05-21 09:32:14:605 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='3f0e0ce5df054b6c82b1881e2d5fe844' fillQuantity=1 price=21331.25
2025-05-21 09:32:14:605 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=21331.25 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:605 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='434132731009488f9f5f19349f118e8c' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21331.25 quantity=1 marketPosition=Long operation=Add orderID='3f0e0ce5df054b6c82b1881e2d5fe844' isSod=False time='2025-05-21 09:32:14' statementDate='2025-05-21'
2025-05-21 09:32:14:605 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21331.25 quantity=1 marketPosition=Long operation=Add
2025-05-21 09:32:14:606 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='3f0e0ce5df054b6c82b1881e2d5fe844' maxFillQuantity=2 price=21331.5 thread=42
2025-05-21 09:32:14:606 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='3f0e0ce5df054b6c82b1881e2d5fe844' fillQuantity=2 price=21331.5
2025-05-21 09:32:14:606 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='3f0e0ce5df054b6c82b1881e2d5fe844' account='SimAccount1' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=21331.********** time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:623 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='7902c12d41e549569be6b0aa18654317' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21331.5 quantity=2 marketPosition=Long operation=Add orderID='3f0e0ce5df054b6c82b1881e2d5fe844' isSod=False time='2025-05-21 09:32:14' statementDate='2025-05-21'
2025-05-21 09:32:14:623 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21331.********** quantity=3 marketPosition=Long operation=Update
2025-05-21 09:32:14:643 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='SimAccount1' instrument='NQ JUN25' id='*********' filled=3 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='3f0e0ce5df054b6c82b1881e2d5fe844+=3 ' outstandingOrders='' thread=18
2025-05-21 09:32:14:643 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='SimAccount1' instrument='NQ JUN25' id='*********' initialEntryOrderId='3f0e0ce5df054b6c82b1881e2d5fe844' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=3 exitOrders=''
2025-05-21 09:32:14:643 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='SimAccount1' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=3 limitPrice=0 stopPrice=21251.25 oco='49502e75d46b4dd2bacdab03be2585c0'
2025-05-21 09:32:14:643 (TRADIFY) Cbi.Account.CreateOrder: orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21251.25 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:32:14:650 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21251.25 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:650 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21251.25 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:650 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21251.25 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:32:14:650 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21251.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:658 (TRADIFY) Cbi.Account.CreateOrder: orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39069 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' id=-1 comment=''
2025-05-21 09:32:14:665 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39069 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:665 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39069 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:32:14:665 (TRADIFY) Cbi.Simulator.Submit: realOrderState=Initialized orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39069 time='2025-05-21 09:32:14' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:32:14:665 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:770 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21251.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:778 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:14:778 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-21 09:32:14' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:27:693 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21251.25 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:27' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=21297.75 quantityChanged=3
2025-05-21 09:32:27:693 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21251.25 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:27' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:27:700 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:27' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 quantityChanged=3 stopPriceChanged=21297.75
2025-05-21 09:32:27:700 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:32:27' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged='0' quantityChanged='3' stopPriceChanged='21297.75' delay=100
2025-05-21 09:32:27:700 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:27' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:32:27:807 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:32:27' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:39:03:529 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-21 09:39:03:641 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-21 09:39:03:991 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8868263' renewSecs='2399.********'
2025-05-21 09:39:03:991 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-21 09:42:54:893 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=Working orderId='58e294ba43d54404bc80ef9d00fa983a' maxFillQuantity=1 price=21361.25 thread=42
2025-05-21 09:42:54:894 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=Working orderId='58e294ba43d54404bc80ef9d00fa983a' fillQuantity=1 price=21361.25
2025-05-21 09:42:54:895 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=1 averageFillPrice=21361.25 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:898 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=1 averageFillPrice=21361.25 onBehalfOf='' id=39069 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' order.MaxQuantitySeen=3 order2.maxQuantitySeen=3 e.Filled=1 order2.QuantityChanged=2
2025-05-21 09:42:54:898 (TRADIFY) Cbi.Account.Change0: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=21297.75 quantityChanged=2
2025-05-21 09:42:54:898 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=3 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:898 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 quantityChanged=2 stopPriceChanged=21297.75
2025-05-21 09:42:54:899 (TRADIFY) Cbi.Simulator.Change: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged='0' quantityChanged='2' stopPriceChanged='21297.75' delay=100
2025-05-21 09:42:54:899 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:902 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='2bffeb80b9054b28909ae2ef021a2bcc' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21361.25 quantity=1 marketPosition=Short operation=Add orderID='58e294ba43d54404bc80ef9d00fa983a' isSod=False time='2025-05-21 09:42:54' statementDate='2025-05-21'
2025-05-21 09:42:54:903 (TRADIFY) Cbi.Account.OnAddTrade: entryId='434132731009488f9f5f19349f118e8c' exitId='2bffeb80b9054b28909ae2ef021a2bcc' profitCurrencyBeforeCommissionAndFees=600
2025-05-21 09:42:54:904 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21331.5 quantity=2 marketPosition=Long operation=Update
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' maxFillQuantity=1 price=21361.25 thread=42
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' fillQuantity=1 price=21361.25
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=2 averageFillPrice=21361.25 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21361.25 stopPrice=0 quantity=3 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=2 averageFillPrice=21361.25 onBehalfOf='' id=39069 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' order.MaxQuantitySeen=3 order2.maxQuantitySeen=3 e.Filled=2 order2.QuantityChanged=1
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Account.Change0: realOrderState=ChangeSubmitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21' limitPriceChanged=0 stopPriceChanged=21297.75 quantityChanged=1
2025-05-21 09:42:54:905 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:921 (TRADIFY) Cbi.Account.QueueChange: realOrderState=ChangeSubmitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:54:921 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='b2fadd82deb94fd49dccc5c6a181a93d' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21361.25 quantity=1 marketPosition=Short operation=Add orderID='58e294ba43d54404bc80ef9d00fa983a' isSod=False time='2025-05-21 09:42:54' statementDate='2025-05-21'
2025-05-21 09:42:54:921 (TRADIFY) Cbi.Account.OnAddTrade: entryId='7902c12d41e549569be6b0aa18654317' exitId='b2fadd82deb94fd49dccc5c6a181a93d' profitCurrencyBeforeCommissionAndFees=595
2025-05-21 09:42:54:921 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=21331.5 quantity=1 marketPosition=Long operation=Update
2025-05-21 09:42:54:927 (TRADIFY) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' maxFillQuantity=1 price=21361.25 thread=42
2025-05-21 09:42:54:927 (TRADIFY) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='58e294ba43d54404bc80ef9d00fa983a' fillQuantity=1 price=21361.25
2025-05-21 09:42:54:927 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='58e294ba43d54404bc80ef9d00fa983a' account='SimAccount1' name='Target1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=21361.25 stopPrice=0 quantity=3 orderType='Limit' filled=3 averageFillPrice=21361.25 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:963 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:54:963 (TRADIFY) Cbi.Account.Cancel0: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:54:963 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:54' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:54:963 (TRADIFY) Cbi.Account.QueueCancel: realOrderState=ChangePending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=2 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:54' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:54:963 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='7fb56171b4a8414b97e871ca1144698a' account='SimAccount1' instrument='NQ JUN25' exchange=Globex price=21361.25 quantity=1 marketPosition=Short operation=Add orderID='58e294ba43d54404bc80ef9d00fa983a' isSod=False time='2025-05-21 09:42:54' statementDate='2025-05-21'
2025-05-21 09:42:54:964 (TRADIFY) Cbi.Account.OnAddTrade: entryId='7902c12d41e549569be6b0aa18654317' exitId='7fb56171b4a8414b97e871ca1144698a' profitCurrencyBeforeCommissionAndFees=595
2025-05-21 09:42:54:964 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='SimAccount1' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:42:54:980 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:42:54:980 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:42:54:980 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='SimAccount1' currentQuantity=0 signalName='Close'
2025-05-21 09:42:54:980 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='SimAccount1'
2025-05-21 09:42:55:012 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:55' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:55:012 (TRADIFY) Cbi.Account.OrderUpdateCallback.RetryCancel: orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=1 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:55' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:55:012 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Accepted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=1 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:55' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:55:012 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:55' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:55:013 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=1 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:55' gtd='2099-12-01' statementDate='2025-05-21'
2025-05-21 09:42:55:013 (TRADIFY) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21297.75 quantity=1 tif=Gtc oco='49502e75d46b4dd2bacdab03be2585c0' filled=0 averageFillPrice=0 onBehalfOf='' id=39068 time='2025-05-21 09:42:55' gtd='2099-12-01' statementDate='2025-05-21' delay=100
2025-05-21 09:42:55:013 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:55' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:55:114 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='9c08a3bee2824768a21270981fc730b7' account='SimAccount1' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21297.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-21 09:42:55' statementDate='2025-05-21' error=NoError comment='' nr=-1
2025-05-21 09:42:55:119 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='SimAccount1' instrument='NQ JUN25' id='*********'
2025-05-21 09:42:55:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:42:55:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:42:55:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:42:55:141 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:44:410 (TRADIFY) Gui.ControlCenter.OnDisconnect
2025-05-21 09:44:45:382 (TRADIFY) Cbi.Connection.Disconnect
2025-05-21 09:44:45:384 (TRADIFY) Cbi.Connection.Disconnect: disabling strategy 'Momo v5.2/*********'
2025-05-21 09:44:45:406 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:409 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 09:44:45:410 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:44:45:411 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close1
2025-05-21 09:44:45:415 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close3
2025-05-21 09:44:45:419 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 09:44:45:419 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Disconnecting priceStatus=Disconnecting
2025-05-21 09:44:45:420 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=False
2025-05-21 09:44:45:420 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Disconnected
2025-05-21 09:44:45:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close4
2025-05-21 09:44:45:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close5
2025-05-21 09:44:45:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-05-21 09:44:45:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close7
2025-05-21 09:44:45:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close8
2025-05-21 09:44:45:432 (TRADIFY) Tradovate.Adapter.DisposeMarketData0
2025-05-21 09:44:45:954 (TRADIFY) Tradovate.Adapter.DisposeTrade0
2025-05-21 09:44:46:770 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount3' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:959 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount4' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:960 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:960 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000019' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:960 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 09:44:46:960 Flushing DB thread
2025-05-21 09:44:46:961 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:44:46:962 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 09:44:46:962 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Using args status=Disconnected
2025-05-21 09:44:47:305 (Playback) Gui.ControlCenter.OnConnect
2025-05-21 09:44:49:212 (Playback) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-21 runAsProcess=False
2025-05-21 09:44:49:212 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:44:49:212 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:44:49:212 (Playback) Cbi.Connection.Connect1
2025-05-21 09:44:49:217 (Playback) Cbi.Connection.Connect2
2025-05-21 09:44:49:217 (Playback) Cbi.Connection.Connect3
2025-05-21 09:44:49:218 (Playback) Cbi.Connection.CreateAccount: account='Playback101' displayName='Playback101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-21 09:44:49:218 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-21 09:44:49:218 (Playback) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Playback101'
2025-05-21 09:44:49:218 (Playback) Cbi.Connection.Connect4
2025-05-21 09:44:49:220 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-21 09:44:49:220 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connecting priceStatus=Connecting
2025-05-21 09:44:49:220 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-21 09:44:49:220 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-21 09:44:49:222 (Playback) Cbi.Connection.Connect5
2025-05-21 09:44:49:224 (Playback) Adapter.PlaybackAdapter.Connect
2025-05-21 09:44:50:225 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:44:50:225 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-21 09:44:50:227 (Playback) Core.Connection.Statistics: connectAttempts=4/3310.8ms
2025-05-21 09:44:50:227 (Playback) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-21 09:44:50:228 Server.HdsClient.Connect: type=HDS server='hds-us-nt-011.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-21 09:44:50:254 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:44:50:254 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-21 09:44:50:254 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connected priceStatus=Connected
2025-05-21 09:44:50:254 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-21 09:44:50:254 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-21 09:44:50:255 (Playback) Cbi.Connection.Connect9 ok
2025-05-21 09:44:50:461 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:44:50:464 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:50:465 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:44:59:192 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:44:59:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:44:59:194 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:45:04:054 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:04:056 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:04:057 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:45:25:813 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:45:25:816 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:45:25:816 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:45:25:819 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:45:25:819 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:45:25:820 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:46:32:085 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:46:32:086 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:46:32:087 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:46:32:087 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:47:00:860 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:47:00:861 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:00:862 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:47:13:103 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:47:13:105 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:47:13:106 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:47:13:107 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:48:13:716 -- SetDefaults --
2025-05-21 09:48:40:452 (Playback) Cbi.Account.CreateOrder: orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39070 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:48:40:460 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39070 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:460 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39070 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:461 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39070 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:48:40:461 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:488 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:493 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:498 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' maxFillQuantity=1 price=20531.75 thread=18
2025-05-21 09:48:40:498 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' fillQuantity=1 price=20531.75
2025-05-21 09:48:40:499 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=20531.75 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:505 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=1 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='cf08f2ff9e134ad7b6097b2f1e1fb1db+=1 ' outstandingOrders='' thread=18
2025-05-21 09:48:40:505 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=1 exitOrders=''
2025-05-21 09:48:40:505 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=1 limitPrice=0 stopPrice=20501.75 oco='f242bad344ee4b1d9c7e7d19f6a1b66e'
2025-05-21 09:48:40:506 (Playback) Cbi.Account.CreateOrder: orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39071 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:48:40:510 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39071 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:510 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39071 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:510 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39071 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:48:40:510 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:522 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:527 (Playback) Cbi.Account.CreateOrder: orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:48:40:533 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:533 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:533 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:48:40:533 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:540 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:545 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:550 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='0e1ad85d23194bc8b66921cd0ff92e61' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20531.75 quantity=1 marketPosition=Long operation=Add orderID='cf08f2ff9e134ad7b6097b2f1e1fb1db' isSod=False time='2025-03-04 09:32:00' statementDate='2025-03-04'
2025-05-21 09:48:40:551 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20531.75 quantity=1 marketPosition=Long operation=Add
2025-05-21 09:48:40:552 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' maxFillQuantity=2 price=20532 thread=18
2025-05-21 09:48:40:552 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' fillQuantity=2 price=20532
2025-05-21 09:48:40:552 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=20531.********** time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:579 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=3 outstanding=1 stopTargetHandling=PerEntryExecution filledOrders='cf08f2ff9e134ad7b6097b2f1e1fb1db+=3 ' outstandingOrders='82ae1520718f4e47b9fdacef45d72f98+=1/0 ' thread=18
2025-05-21 09:48:40:579 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' bracket=0 qty=3 stopOrdersOutstandingQuantity=1 quantity2Add=2 exitOrders='82ae1520718f4e47b9fdacef45d72f98+=1/0 8e41af7ffe5a4611837150cb6d1a961a+=1/0 '
2025-05-21 09:48:40:579 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=2 limitPrice=0 stopPrice=20501.75 oco='14bb1cb0eddf414db59e6a1770211c3e'
2025-05-21 09:48:40:579 (Playback) Cbi.Account.CreateOrder: orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39073 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:48:40:587 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39073 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:587 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39073 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:587 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39073 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:48:40:587 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:598 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:612 (Playback) Cbi.Account.CreateOrder: orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:48:40:619 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:619 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:48:40:619 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:48:40:619 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:626 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:632 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:00' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:48:40:638 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='98c4367a2d5e4a719fa19f99062a4208' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20532 quantity=2 marketPosition=Long operation=Add orderID='cf08f2ff9e134ad7b6097b2f1e1fb1db' isSod=False time='2025-03-04 09:32:00' statementDate='2025-03-04'
2025-05-21 09:48:40:639 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20531.********** quantity=3 marketPosition=Long operation=Update
2025-05-21 09:49:10:645 (Playback) Cbi.Account.CreateOrder: orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:30' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:10:672 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:30' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:10:672 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:30' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:10:672 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:30' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:10:672 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:30' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:10:689 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:30' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:582 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:584 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:584 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:584 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='82ae1520718f4e47b9fdacef45d72f98' maxFillQuantity=1 price=20497.75 thread=42
2025-05-21 09:49:23:584 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='82ae1520718f4e47b9fdacef45d72f98' fillQuantity=1 price=20497.75
2025-05-21 09:49:23:584 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=20497.75 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:607 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:607 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:608 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:608 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:608 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='01d531972ed741ab94c6011cdfc45c2c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20497.75 quantity=1 marketPosition=Short operation=Add orderID='82ae1520718f4e47b9fdacef45d72f98' isSod=False time='2025-03-04 09:32:43' statementDate='2025-03-04'
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OnAddTrade: entryId='0e1ad85d23194bc8b66921cd0ff92e61' exitId='01d531972ed741ab94c6011cdfc45c2c' profitCurrencyBeforeCommissionAndFees=-680
2025-05-21 09:49:23:608 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20532 quantity=2 marketPosition=Long operation=Update
2025-05-21 09:49:23:608 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='a0f638a9f48f49b49d529749d00eec85' maxFillQuantity=1 price=20497.75 thread=42
2025-05-21 09:49:23:608 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='a0f638a9f48f49b49d529749d00eec85' fillQuantity=1 price=20497.75
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=2 orderType='Stop Market' filled=1 averageFillPrice=20497.75 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:608 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=1 averageFillPrice=20497.75 onBehalfOf='' id=39073 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' order.MaxQuantitySeen=2 order2.maxQuantitySeen=2 e.Filled=1 order2.QuantityChanged=1
2025-05-21 09:49:23:608 (Playback) Cbi.Account.Change0: realOrderState=Working orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20561.75 stopPriceChanged=0 quantityChanged=1
2025-05-21 09:49:23:608 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:609 (Playback) Cbi.Account.Change1: realOrderState=ChangePending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20561.75 quantityChanged=1 stopPriceChanged=0
2025-05-21 09:49:23:609 (Playback) Cbi.Simulator.Change: realOrderState=ChangePending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged='20561.75' quantityChanged='1' stopPriceChanged='0' delay=0
2025-05-21 09:49:23:609 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:609 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:609 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:609 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='c70c5cc5a9f142dd843bd4dbc6c8655d' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20497.75 quantity=1 marketPosition=Short operation=Add orderID='a0f638a9f48f49b49d529749d00eec85' isSod=False time='2025-03-04 09:32:43' statementDate='2025-03-04'
2025-05-21 09:49:23:609 (Playback) Cbi.Account.OnAddTrade: entryId='98c4367a2d5e4a719fa19f99062a4208' exitId='c70c5cc5a9f142dd843bd4dbc6c8655d' profitCurrencyBeforeCommissionAndFees=-685
2025-05-21 09:49:23:609 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20532 quantity=1 marketPosition=Long operation=Update
2025-05-21 09:49:23:610 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='a0f638a9f48f49b49d529749d00eec85' maxFillQuantity=1 price=20497.5 thread=42
2025-05-21 09:49:23:610 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='a0f638a9f48f49b49d529749d00eec85' fillQuantity=1 price=20497.5
2025-05-21 09:49:23:610 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=20501.75 quantity=2 orderType='Stop Market' filled=2 averageFillPrice=20497.625 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:634 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:634 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:634 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:634 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:634 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:23:634 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:634 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=20561.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:635 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='138ad6f0874e4ebd94c1991da5e8bb0d' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20497.5 quantity=1 marketPosition=Short operation=Add orderID='a0f638a9f48f49b49d529749d00eec85' isSod=False time='2025-03-04 09:32:43' statementDate='2025-03-04'
2025-05-21 09:49:23:635 (Playback) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='Playback101' instrument='NQ JUN25' id='*********'
2025-05-21 09:49:23:635 (Playback) NinjaScript.AtmStrategy.CloseStrategy: account='Playback101' instrument='NQ JUN25' id='*********'
2025-05-21 09:49:23:635 (Playback) Cbi.Position.Close0: instrument='NQ JUN25' account='Playback101' currentQuantity=0 signalName='Close'
2025-05-21 09:49:23:635 (Playback) Cbi.Position.Close.Cancel1: realOrderState=Working orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:635 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:635 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:635 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:23:635 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:23:635 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:635 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=20500 stopPrice=20501.5 quantity=3 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:43' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:23:664 (Playback) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='Playback101' instrument='NQ JUN25' id='*********'
2025-05-21 09:49:23:667 (Playback) Cbi.Position.Close1: instrument='NQ JUN25' account='Playback101'
2025-05-21 09:49:23:667 (Playback) Cbi.Account.OnAddTrade: entryId='98c4367a2d5e4a719fa19f99062a4208' exitId='138ad6f0874e4ebd94c1991da5e8bb0d' profitCurrencyBeforeCommissionAndFees=-690
2025-05-21 09:49:23:667 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:49:24:594 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:49:24:594 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:49:24:594 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:49:24:594 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:49:32:424 (Playback) Cbi.Account.CreateOrder: orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39076 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:32:439 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39076 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:440 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39076 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:440 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39076 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:32:440 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:461 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:468 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:473 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='25a8f00b738741af8a539cb191b8bf70' maxFillQuantity=1 price=20503.75 thread=18
2025-05-21 09:49:32:473 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='25a8f00b738741af8a539cb191b8bf70' fillQuantity=1 price=20503.75
2025-05-21 09:49:32:473 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=20503.75 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:477 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=1 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='25a8f00b738741af8a539cb191b8bf70+=1 ' outstandingOrders='' thread=18
2025-05-21 09:49:32:478 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='25a8f00b738741af8a539cb191b8bf70' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=1 exitOrders=''
2025-05-21 09:49:32:478 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=1 limitPrice=0 stopPrice=20533.75 oco='1be502d7e44d42d69e2f68f8f2fc3bea'
2025-05-21 09:49:32:478 (Playback) Cbi.Account.CreateOrder: orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39077 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:32:483 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39077 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:483 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39077 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:483 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39077 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:32:483 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:489 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:495 (Playback) Cbi.Account.CreateOrder: orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:32:505 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:505 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:505 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:32:505 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:510 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:517 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:523 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='314c046f71464b0bbd81412f4da00017' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20503.75 quantity=1 marketPosition=Short operation=Add orderID='25a8f00b738741af8a539cb191b8bf70' isSod=False time='2025-03-04 09:32:51' statementDate='2025-03-04'
2025-05-21 09:49:32:523 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20503.75 quantity=1 marketPosition=Short operation=Add
2025-05-21 09:49:32:523 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='25a8f00b738741af8a539cb191b8bf70' maxFillQuantity=2 price=20503.5 thread=18
2025-05-21 09:49:32:523 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='25a8f00b738741af8a539cb191b8bf70' fillQuantity=2 price=20503.5
2025-05-21 09:49:32:523 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=20503.********** time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:551 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=3 outstanding=1 stopTargetHandling=PerEntryExecution filledOrders='25a8f00b738741af8a539cb191b8bf70+=3 ' outstandingOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 ' thread=18
2025-05-21 09:49:32:551 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='25a8f00b738741af8a539cb191b8bf70' bracket=0 qty=3 stopOrdersOutstandingQuantity=1 quantity2Add=2 exitOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 c9b3957911224b28ad71995a10997a9b+=1/0 '
2025-05-21 09:49:32:551 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=2 limitPrice=0 stopPrice=20533.75 oco='0ba9d422354f402f8e16755e08f527bb'
2025-05-21 09:49:32:551 (Playback) Cbi.Account.CreateOrder: orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39079 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:32:557 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39079 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:558 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39079 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:558 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39079 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:32:558 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:566 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:575 (Playback) Cbi.Account.CreateOrder: orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:32:582 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:582 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:32:582 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:32:582 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:595 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:602 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:51' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:32:609 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='14b774c927c6463c9efc38ac7e3a287b' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20503.5 quantity=2 marketPosition=Short operation=Add orderID='25a8f00b738741af8a539cb191b8bf70' isSod=False time='2025-03-04 09:32:51' statementDate='2025-03-04'
2025-05-21 09:49:32:609 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20503.********** quantity=3 marketPosition=Short operation=Update
2025-05-21 09:49:34:302 (Playback) Cbi.Account.CreateOrder: orderId='314e07984dfa446495f294065792a549' account='Playback101' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39081 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:34:318 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39081 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:318 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39081 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:318 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39081 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:34:318 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:334 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:340 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:345 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='314e07984dfa446495f294065792a549' maxFillQuantity=1 price=20489.5 thread=18
2025-05-21 09:49:34:345 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='314e07984dfa446495f294065792a549' fillQuantity=1 price=20489.5
2025-05-21 09:49:34:345 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=20489.5 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:356 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=4 outstanding=3 stopTargetHandling=PerEntryExecution filledOrders='25a8f00b738741af8a539cb191b8bf70+=3 314e07984dfa446495f294065792a549+=1 ' outstandingOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 935183534eae41a8baa55a86c4b72de9+=2/0 ' thread=18
2025-05-21 09:49:34:356 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='25a8f00b738741af8a539cb191b8bf70' bracket=0 qty=6 stopOrdersOutstandingQuantity=3 quantity2Add=1 exitOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 c9b3957911224b28ad71995a10997a9b+=1/0 935183534eae41a8baa55a86c4b72de9+=2/0 30836b978ea74dc5801beb4605f5e7ea+=2/0 '
2025-05-21 09:49:34:356 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=1 limitPrice=0 stopPrice=20533.75 oco='2f03277c06c24aafa923f7561e7a6b3d'
2025-05-21 09:49:34:356 (Playback) Cbi.Account.CreateOrder: orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39082 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:34:363 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39082 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:364 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39082 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:364 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39082 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:34:364 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:370 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:377 (Playback) Cbi.Account.CreateOrder: orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:34:382 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:382 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:383 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:34:383 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:391 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:397 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:409 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='407b38cfa1c94c1887730208107f8938' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20489.5 quantity=1 marketPosition=Short operation=Add orderID='314e07984dfa446495f294065792a549' isSod=False time='2025-03-04 09:32:53' statementDate='2025-03-04'
2025-05-21 09:49:34:409 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20500.0625 quantity=4 marketPosition=Short operation=Update
2025-05-21 09:49:34:409 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='314e07984dfa446495f294065792a549' maxFillQuantity=2 price=20489.25 thread=18
2025-05-21 09:49:34:409 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='314e07984dfa446495f294065792a549' fillQuantity=2 price=20489.25
2025-05-21 09:49:34:409 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=20489.********** time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:432 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=6 outstanding=4 stopTargetHandling=PerEntryExecution filledOrders='25a8f00b738741af8a539cb191b8bf70+=3 314e07984dfa446495f294065792a549+=3 ' outstandingOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 935183534eae41a8baa55a86c4b72de9+=2/0 591ba1ade0b4417c802b69266a19b4be+=1/0 ' thread=18
2025-05-21 09:49:34:433 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='25a8f00b738741af8a539cb191b8bf70' bracket=0 qty=6 stopOrdersOutstandingQuantity=4 quantity2Add=2 exitOrders='3d88cf2db9024e6db052792ef5268d50+=1/0 c9b3957911224b28ad71995a10997a9b+=1/0 935183534eae41a8baa55a86c4b72de9+=2/0 30836b978ea74dc5801beb4605f5e7ea+=2/0 591ba1ade0b4417c802b69266a19b4be+=1/0 ade2c200f79b4c3db0354b07991fe666+=1/0 '
2025-05-21 09:49:34:433 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=2 limitPrice=0 stopPrice=20533.75 oco='085e0e6cf8094dcda0d3a98438655066'
2025-05-21 09:49:34:433 (Playback) Cbi.Account.CreateOrder: orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39084 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:34:440 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39084 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:440 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39084 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:444 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39084 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:34:444 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:452 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:461 (Playback) Cbi.Account.CreateOrder: orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' id=-1 comment=''
2025-05-21 09:49:34:473 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:473 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:34:474 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:34:474 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:480 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:488 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:32:53' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:34:492 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='6387b3d87776478cbd5a0dccf1b20a33' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20489.25 quantity=2 marketPosition=Short operation=Add orderID='314e07984dfa446495f294065792a549' isSod=False time='2025-03-04 09:32:53' statementDate='2025-03-04'
2025-05-21 09:49:34:492 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20496.********** quantity=6 marketPosition=Short operation=Update
2025-05-21 09:49:51:688 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:688 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:689 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:689 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:689 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='3d88cf2db9024e6db052792ef5268d50' maxFillQuantity=1 price=20548 thread=42
2025-05-21 09:49:51:689 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='3d88cf2db9024e6db052792ef5268d50' fillQuantity=1 price=20548
2025-05-21 09:49:51:689 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=20548 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:719 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:719 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:719 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:719 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:719 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='e1b39c8f72f844ce90333492356b600c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548 quantity=1 marketPosition=Long operation=Add orderID='3d88cf2db9024e6db052792ef5268d50' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OnAddTrade: entryId='314c046f71464b0bbd81412f4da00017' exitId='e1b39c8f72f844ce90333492356b600c' profitCurrencyBeforeCommissionAndFees=-885
2025-05-21 09:49:51:720 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20495 quantity=5 marketPosition=Short operation=Update
2025-05-21 09:49:51:720 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='935183534eae41a8baa55a86c4b72de9' maxFillQuantity=1 price=20548 thread=42
2025-05-21 09:49:51:720 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='935183534eae41a8baa55a86c4b72de9' fillQuantity=1 price=20548
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=1 averageFillPrice=20548 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=1 averageFillPrice=20548 onBehalfOf='' id=39079 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' order.MaxQuantitySeen=2 order2.maxQuantitySeen=2 e.Filled=1 order2.QuantityChanged=1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.Change0: realOrderState=Working orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20473.75 stopPriceChanged=0 quantityChanged=1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.Change1: realOrderState=ChangePending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20473.75 quantityChanged=1 stopPriceChanged=0
2025-05-21 09:49:51:720 (Playback) Cbi.Simulator.Change: realOrderState=ChangePending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged='20473.75' quantityChanged='1' stopPriceChanged='0' delay=0
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:720 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='31f2e65b40ef459e8876b9d15e7a3a01' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548 quantity=1 marketPosition=Long operation=Add orderID='935183534eae41a8baa55a86c4b72de9' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:722 (Playback) Cbi.Account.OnAddTrade: entryId='14b774c927c6463c9efc38ac7e3a287b' exitId='31f2e65b40ef459e8876b9d15e7a3a01' profitCurrencyBeforeCommissionAndFees=-890
2025-05-21 09:49:51:722 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20492.875 quantity=4 marketPosition=Short operation=Update
2025-05-21 09:49:51:722 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='935183534eae41a8baa55a86c4b72de9' maxFillQuantity=1 price=20548.25 thread=42
2025-05-21 09:49:51:722 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='935183534eae41a8baa55a86c4b72de9' fillQuantity=1 price=20548.25
2025-05-21 09:49:51:722 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=2 averageFillPrice=20548.125 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:757 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:757 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:758 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:758 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:758 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:51:758 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:758 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:758 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='30eee5b35d154423839fd2d4873f6fb3' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548.25 quantity=1 marketPosition=Long operation=Add orderID='935183534eae41a8baa55a86c4b72de9' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:758 (Playback) Cbi.Account.OnAddTrade: entryId='14b774c927c6463c9efc38ac7e3a287b' exitId='30eee5b35d154423839fd2d4873f6fb3' profitCurrencyBeforeCommissionAndFees=-895
2025-05-21 09:49:51:758 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20489.********** quantity=3 marketPosition=Short operation=Update
2025-05-21 09:49:51:758 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='591ba1ade0b4417c802b69266a19b4be' maxFillQuantity=1 price=20548 thread=42
2025-05-21 09:49:51:758 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='591ba1ade0b4417c802b69266a19b4be' fillQuantity=1 price=20548
2025-05-21 09:49:51:758 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=20548 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:783 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:783 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:783 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:783 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:783 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:51:783 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:783 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:783 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='4ec883cfda904b7883194a6532f02cf3' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548 quantity=1 marketPosition=Long operation=Add orderID='591ba1ade0b4417c802b69266a19b4be' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:783 (Playback) Cbi.Account.OnAddTrade: entryId='407b38cfa1c94c1887730208107f8938' exitId='4ec883cfda904b7883194a6532f02cf3' profitCurrencyBeforeCommissionAndFees=-1170
2025-05-21 09:49:51:783 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20489.25 quantity=2 marketPosition=Short operation=Update
2025-05-21 09:49:51:784 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='949229933dec464a8df3de7751a539e8' maxFillQuantity=1 price=20548 thread=42
2025-05-21 09:49:51:784 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='949229933dec464a8df3de7751a539e8' fillQuantity=1 price=20548
2025-05-21 09:49:51:784 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=1 averageFillPrice=20548 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:784 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=1 averageFillPrice=20548 onBehalfOf='' id=39084 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' order.MaxQuantitySeen=2 order2.maxQuantitySeen=2 e.Filled=1 order2.QuantityChanged=1
2025-05-21 09:49:51:784 (Playback) Cbi.Account.Change0: realOrderState=Working orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20473.75 stopPriceChanged=0 quantityChanged=1
2025-05-21 09:49:51:784 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:784 (Playback) Cbi.Account.Change1: realOrderState=ChangePending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged=20473.75 quantityChanged=1 stopPriceChanged=0
2025-05-21 09:49:51:784 (Playback) Cbi.Simulator.Change: realOrderState=ChangePending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' limitPriceChanged='20473.75' quantityChanged='1' stopPriceChanged='0' delay=0
2025-05-21 09:49:51:784 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:784 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:784 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:785 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='132163f1f53f423cbedc49b98eabc5cd' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548 quantity=1 marketPosition=Long operation=Add orderID='949229933dec464a8df3de7751a539e8' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:785 (Playback) Cbi.Account.OnAddTrade: entryId='6387b3d87776478cbd5a0dccf1b20a33' exitId='132163f1f53f423cbedc49b98eabc5cd' profitCurrencyBeforeCommissionAndFees=-1175
2025-05-21 09:49:51:785 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20489.25 quantity=1 marketPosition=Short operation=Update
2025-05-21 09:49:51:786 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='949229933dec464a8df3de7751a539e8' maxFillQuantity=1 price=20548.25 thread=42
2025-05-21 09:49:51:786 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='949229933dec464a8df3de7751a539e8' fillQuantity=1 price=20548.25
2025-05-21 09:49:51:786 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20533.75 quantity=2 orderType='Stop Market' filled=2 averageFillPrice=20548.125 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:814 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:814 (Playback) Cbi.Account.Cancel0: realOrderState=Working orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:814 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:814 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:49:51:814 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04' delay=0
2025-05-21 09:49:51:814 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:814 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20473.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-04 09:34:13' statementDate='2025-03-04' error=NoError comment='' nr=-1
2025-05-21 09:49:51:814 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='9c456cec6c8947bf8ed32b0d890f8566' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20548.25 quantity=1 marketPosition=Long operation=Add orderID='949229933dec464a8df3de7751a539e8' isSod=False time='2025-03-04 09:34:13' statementDate='2025-03-04'
2025-05-21 09:49:51:815 (Playback) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate1: account='Playback101' instrument='NQ JUN25' id='*********'
2025-05-21 09:49:51:815 (Playback) Cbi.Account.OnAddTrade: entryId='6387b3d87776478cbd5a0dccf1b20a33' exitId='9c456cec6c8947bf8ed32b0d890f8566' profitCurrencyBeforeCommissionAndFees=-1180
2025-05-21 09:49:51:815 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:49:51:889 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:49:51:889 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:49:51:889 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:49:51:889 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='cf08f2ff9e134ad7b6097b2f1e1fb1db' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=3 averageFillPrice=20531.********** onBehalfOf='' id=39070 time='2025-03-04 09:32:00' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='82ae1520718f4e47b9fdacef45d72f98' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=1 averageFillPrice=20497.75 onBehalfOf='' id=39071 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='8e41af7ffe5a4611837150cb6d1a961a' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='f242bad344ee4b1d9c7e7d19f6a1b66e' filled=0 averageFillPrice=0 onBehalfOf='' id=39072 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='a0f638a9f48f49b49d529749d00eec85' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=20501.75 quantity=2 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=2 averageFillPrice=20497.625 onBehalfOf='' id=39073 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='b01149d2b44845c188aa01583a395836' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=20561.75 stopPrice=0 quantity=1 tif=Gtc oco='14bb1cb0eddf414db59e6a1770211c3e' filled=0 averageFillPrice=0 onBehalfOf='' id=39074 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='fde0ea6d1bb14c0193dff2f1ce198573' account='Playback101' name='Exit' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=20500 stopPrice=20501.5 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39075 time='2025-03-04 09:32:43' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='25a8f00b738741af8a539cb191b8bf70' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=3 averageFillPrice=20503.********** onBehalfOf='' id=39076 time='2025-03-04 09:32:51' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='3d88cf2db9024e6db052792ef5268d50' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=1 averageFillPrice=20548 onBehalfOf='' id=39077 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='c9b3957911224b28ad71995a10997a9b' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='1be502d7e44d42d69e2f68f8f2fc3bea' filled=0 averageFillPrice=0 onBehalfOf='' id=39078 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='935183534eae41a8baa55a86c4b72de9' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=2 averageFillPrice=20548.125 onBehalfOf='' id=39079 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='30836b978ea74dc5801beb4605f5e7ea' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='0ba9d422354f402f8e16755e08f527bb' filled=0 averageFillPrice=0 onBehalfOf='' id=39080 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='314e07984dfa446495f294065792a549' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=3 averageFillPrice=20489.********** onBehalfOf='' id=39081 time='2025-03-04 09:32:53' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='591ba1ade0b4417c802b69266a19b4be' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=1 averageFillPrice=20548 onBehalfOf='' id=39082 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='ade2c200f79b4c3db0354b07991fe666' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='2f03277c06c24aafa923f7561e7a6b3d' filled=0 averageFillPrice=0 onBehalfOf='' id=39083 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='949229933dec464a8df3de7751a539e8' account='Playback101' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20533.75 quantity=2 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=2 averageFillPrice=20548.125 onBehalfOf='' id=39084 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:145 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Cancelled orderId='45221ad6fb89447a9fc44430937482e5' account='Playback101' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20473.75 stopPrice=0 quantity=1 tif=Gtc oco='085e0e6cf8094dcda0d3a98438655066' filled=0 averageFillPrice=0 onBehalfOf='' id=39085 time='2025-03-04 09:34:13' gtd='2099-12-01' statementDate='2025-03-04'
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:03:165 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:50:13:285 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-05-21 09:50:13:287 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-21 09:50:13:288 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-05-21 09:51:54:986 (Playback) Cbi.Account.CreateOrder: orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39086 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' id=-1 comment=''
2025-05-21 09:51:54:998 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39086 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:54:999 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39086 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:54:999 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=39086 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:51:54:999 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:018 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:026 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:030 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='ecdb1adf368348e2abb14e7c382bc224' maxFillQuantity=2 price=20071.25 thread=18
2025-05-21 09:51:55:030 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='ecdb1adf368348e2abb14e7c382bc224' fillQuantity=2 price=20071.25
2025-05-21 09:51:55:030 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=2 averageFillPrice=20071.25 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:034 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=2 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='ecdb1adf368348e2abb14e7c382bc224+=2 ' outstandingOrders='' thread=18
2025-05-21 09:51:55:034 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='ecdb1adf368348e2abb14e7c382bc224' bracket=0 qty=3 stopOrdersOutstandingQuantity=0 quantity2Add=2 exitOrders=''
2025-05-21 09:51:55:034 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=2 limitPrice=0 stopPrice=20101.25 oco='6f9f2f70d2cf42c7856255090b9d0ece'
2025-05-21 09:51:55:034 (Playback) Cbi.Account.CreateOrder: orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' id=-1 comment=''
2025-05-21 09:51:55:039 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:039 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:040 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:51:55:040 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:045 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:051 (Playback) Cbi.Account.CreateOrder: orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39088 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' id=-1 comment=''
2025-05-21 09:51:55:056 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39088 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:056 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39088 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:056 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39088 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:51:55:056 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:063 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:068 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=2 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:073 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='42ca20cbee314960995ddbc73bb64d33' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20071.25 quantity=2 marketPosition=Short operation=Add orderID='ecdb1adf368348e2abb14e7c382bc224' isSod=False time='2025-03-10 09:32:12' statementDate='2025-03-10'
2025-05-21 09:51:55:073 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20071.25 quantity=2 marketPosition=Short operation=Add
2025-05-21 09:51:55:073 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='ecdb1adf368348e2abb14e7c382bc224' maxFillQuantity=1 price=20071 thread=18
2025-05-21 09:51:55:073 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='ecdb1adf368348e2abb14e7c382bc224' fillQuantity=1 price=20071
2025-05-21 09:51:55:073 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='ecdb1adf368348e2abb14e7c382bc224' account='Playback101' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=20071.********** time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:095 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders0: account='Playback101' instrument='NQ JUN25' id='*********' filled=3 outstanding=2 stopTargetHandling=PerEntryExecution filledOrders='ecdb1adf368348e2abb14e7c382bc224+=3 ' outstandingOrders='cf2b80b69772472c946d7821489eb4a3+=2/0 ' thread=18
2025-05-21 09:51:55:096 (Playback) NinjaScript.AtmStrategy.ManageBracketOrders1: account='Playback101' instrument='NQ JUN25' id='*********' initialEntryOrderId='ecdb1adf368348e2abb14e7c382bc224' bracket=0 qty=3 stopOrdersOutstandingQuantity=2 quantity2Add=1 exitOrders='cf2b80b69772472c946d7821489eb4a3+=2/0 be626f88173c4de189b6ced1a1640af3+=2/0 '
2025-05-21 09:51:55:096 (Playback) NinjaScript.AtmStrategy.ManageStopOrder: account='Playback101' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=1 limitPrice=0 stopPrice=20101.25 oco='ebf54b186663432f99ace12f7b7bcf5d'
2025-05-21 09:51:55:096 (Playback) Cbi.Account.CreateOrder: orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' id=-1 comment=''
2025-05-21 09:51:55:105 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:105 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:105 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:51:55:105 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:111 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:118 (Playback) Cbi.Account.CreateOrder: orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39090 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' id=-1 comment=''
2025-05-21 09:51:55:124 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39090 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:124 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39090 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:51:55:124 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39090 time='2025-03-10 09:32:12' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:51:55:125 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:132 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:139 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-03-10 09:32:12' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:51:55:145 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='deee68a151e7457c980f4ca8c2c3e448' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20071 quantity=1 marketPosition=Short operation=Add orderID='ecdb1adf368348e2abb14e7c382bc224' isSod=False time='2025-03-10 09:32:12' statementDate='2025-03-10'
2025-05-21 09:51:55:145 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20071.********** quantity=3 marketPosition=Short operation=Update
2025-05-21 09:52:05:371 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='be626f88173c4de189b6ced1a1640af3' maxFillQuantity=1 price=20040.5 thread=42
2025-05-21 09:52:05:371 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='be626f88173c4de189b6ced1a1640af3' fillQuantity=1 price=20040.5
2025-05-21 09:52:05:371 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=2 orderType='Limit' filled=1 averageFillPrice=20040.5 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:372 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerChange: orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=20041.25 stopPrice=0 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=1 averageFillPrice=20040.5 onBehalfOf='' id=39088 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' order.MaxQuantitySeen=2 order2.maxQuantitySeen=2 e.Filled=1 order2.QuantityChanged=1
2025-05-21 09:52:05:373 (Playback) Cbi.Account.Change0: realOrderState=Accepted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=2 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' limitPriceChanged=0 stopPriceChanged=20101.25 quantityChanged=1
2025-05-21 09:52:05:373 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=2 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:373 (Playback) Cbi.Account.Change1: realOrderState=ChangePending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=20101.25
2025-05-21 09:52:05:373 (Playback) Cbi.Simulator.Change: realOrderState=ChangePending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' limitPriceChanged='0' quantityChanged='1' stopPriceChanged='20101.25' delay=0
2025-05-21 09:52:05:373 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:374 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:374 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='ffdcdd8bc79b4a58b2529a8baf42ba88' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20040.5 quantity=1 marketPosition=Long operation=Add orderID='be626f88173c4de189b6ced1a1640af3' isSod=False time='2025-03-10 09:32:28' statementDate='2025-03-10'
2025-05-21 09:52:05:374 (Playback) Cbi.Account.OnAddTrade: entryId='42ca20cbee314960995ddbc73bb64d33' exitId='ffdcdd8bc79b4a58b2529a8baf42ba88' profitCurrencyBeforeCommissionAndFees=615
2025-05-21 09:52:05:374 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20071.125 quantity=2 marketPosition=Short operation=Update
2025-05-21 09:52:05:374 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='2c7bb0abe93c41e784a7aa69b0286c5a' maxFillQuantity=1 price=20040.5 thread=42
2025-05-21 09:52:05:374 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='2c7bb0abe93c41e784a7aa69b0286c5a' fillQuantity=1 price=20040.5
2025-05-21 09:52:05:374 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='2c7bb0abe93c41e784a7aa69b0286c5a' account='Playback101' name='Target1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=1 orderType='Limit' filled=1 averageFillPrice=20040.5 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:397 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Accepted orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:397 (Playback) Cbi.Account.Cancel0: realOrderState=Accepted orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:397 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:397 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:397 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='ebf54b186663432f99ace12f7b7bcf5d' filled=0 averageFillPrice=0 onBehalfOf='' id=39089 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:52:05:397 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:397 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='85dacdca9e5c49f8a203c07ad0a26f4f' account='Playback101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:397 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='a6a6177389e0444180072a5eee612302' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20040.5 quantity=1 marketPosition=Long operation=Add orderID='2c7bb0abe93c41e784a7aa69b0286c5a' isSod=False time='2025-03-10 09:32:28' statementDate='2025-03-10'
2025-05-21 09:52:05:397 (Playback) Cbi.Account.OnAddTrade: entryId='42ca20cbee314960995ddbc73bb64d33' exitId='a6a6177389e0444180072a5eee612302' profitCurrencyBeforeCommissionAndFees=615
2025-05-21 09:52:05:397 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=20071 quantity=1 marketPosition=Short operation=Update
2025-05-21 09:52:05:404 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='be626f88173c4de189b6ced1a1640af3' maxFillQuantity=1 price=20040.5 thread=42
2025-05-21 09:52:05:404 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='be626f88173c4de189b6ced1a1640af3' fillQuantity=1 price=20040.5
2025-05-21 09:52:05:404 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='be626f88173c4de189b6ced1a1640af3' account='Playback101' name='Target1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=20041.25 stopPrice=0 quantity=2 orderType='Limit' filled=2 averageFillPrice=20040.5 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:424 (Playback) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Accepted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:424 (Playback) Cbi.Account.Cancel0: realOrderState=Accepted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:424 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:424 (Playback) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10'
2025-05-21 09:52:05:424 (Playback) Cbi.Simulator.Cancel: realOrderState=CancelPending orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=20101.25 quantity=1 tif=Gtc oco='6f9f2f70d2cf42c7856255090b9d0ece' filled=0 averageFillPrice=0 onBehalfOf='' id=39087 time='2025-03-10 09:32:28' gtd='2099-12-01' statementDate='2025-03-10' delay=0
2025-05-21 09:52:05:424 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=-1
2025-05-21 09:52:05:425 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='cf2b80b69772472c946d7821489eb4a3' account='Playback101' name='Stop1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=20101.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-03-10 09:32:28' statementDate='2025-03-10' error=NoError comment='' nr=8
2025-05-21 09:52:05:425 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='3dc8773b03ee4657b7f1704e6781beb4' account='Playback101' instrument='NQ JUN25' exchange=Globex price=20040.5 quantity=1 marketPosition=Long operation=Add orderID='be626f88173c4de189b6ced1a1640af3' isSod=False time='2025-03-10 09:32:28' statementDate='2025-03-10'
2025-05-21 09:52:05:425 (Playback) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate1: account='Playback101' instrument='NQ JUN25' id='*********'
2025-05-21 09:52:05:425 (Playback) Cbi.Account.OnAddTrade: entryId='deee68a151e7457c980f4ca8c2c3e448' exitId='3dc8773b03ee4657b7f1704e6781beb4' profitCurrencyBeforeCommissionAndFees=610
2025-05-21 09:52:05:425 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-21 09:52:05:453 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-05-21 09:52:05:453 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:52:05:453 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:52:05:453 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-21 09:59:18:176 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-21 09:59:19:271 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-21 09:59:19:280 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=3.62ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.00ms
2025-05-21 10:10:15:595 Shutting down NinjaTrader
2025-05-21 10:10:15:600 Disconnecting 'Playback'
2025-05-21 10:10:15:601 (Playback) Cbi.Connection.Disconnect
2025-05-21 10:10:15:603 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-21 10:10:15:609 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-21 10:10:15:612 (Playback) Cbi.Connection.ConnectionStatusCallback.Close1
2025-05-21 10:10:15:634 (Playback) Cbi.Connection.ConnectionStatusCallback.Close3
2025-05-21 10:10:15:635 (Playback) Cbi.Connection.ConnectionStatusCallback.Close4
2025-05-21 10:10:15:635 (Playback) Cbi.Connection.ConnectionStatusCallback.Close5
2025-05-21 10:10:15:635 (Playback) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-05-21 10:10:15:635 (Playback) Cbi.Connection.ConnectionStatusCallback.Close7
2025-05-21 10:10:15:645 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-21 10:10:15:645 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-21 10:10:15:645 Flushing DB thread
2025-05-21 10:10:15:647 (Playback) Cbi.Connection.ConnectionStatusCallback.Close8
2025-05-21 10:10:15:647 Shutting down instrument management
2025-05-21 10:10:15:652 Shutting down instrument threads
2025-05-21 10:10:15:667 Shutting down BP thread
2025-05-21 10:10:15:667 Shutting down recorder
2025-05-21 10:10:15:729 Shutting down file type watcher
2025-05-21 10:10:15:730 Shutting down ATI server
2025-05-21 10:10:15:758 Shutting down auto trade component
2025-05-21 10:10:15:765 Shutting down SMTP server
2025-05-21 10:10:15:782 Shutting down adapter server
2025-05-21 10:10:15:824 Shutting down server(s)
2025-05-21 10:10:15:840 Shutting down mail thread
2025-05-21 10:10:15:841 Shutting down sound thread
2025-05-21 10:10:15:841 Shutting down timer
2025-05-21 10:10:15:841 Shutting down alerts timer
2025-05-21 10:10:15:841 Shutting down message timer
2025-05-21 10:10:15:841 Shutting down db
2025-05-21 10:10:15:849 Flushing DB thread
2025-05-21 10:10:16:154 Shutting down bars dictionary
2025-05-21 10:10:16:212 Shutting down user entitlement threads
2025-05-21 10:10:16:224 Shutting down logs
2025-05-21 10:10:16:236 Shutting down bars types
2025-05-21 10:10:16:256 Shutting down UI threads
2025-05-21 10:10:16:266 ************************ Session End ************************
