#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

#region Using declarations
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.Gui.Tools;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    /// <summary>
    /// Bala2Channels: Two TChannel3‐style channels plotted together.
    ///  • Channel 1: Dev1, XS1, XL1, RSIPeriod1, EMAPeriod1
    ///  • Channel 2: Dev2, XS2, XL2, RSIPeriod2, EMAPeriod2
    ///  • If RSI2 > XL2: plot dark‐green dot on Lower2 ⇒ “L” + dark‐green background.
    ///  • If RSI2 < XS2: plot maroon dot on Upper2 ⇒ “S” + maroon background.
    ///  • C3Trend = +1/−1 from Channel 1 slope (hidden for Strategy Builder).
    ///
    ///  Now with fully customizable “L”/“S” tag names, colors, offsets, font size,
    ///  and show/hide toggles (same pattern as TridentZeroLagHATEMA).
    /// </summary>
    [Gui.CategoryOrder("SMMA Options", 0)]
    [Gui.CategoryOrder("Channel 1",    10)]
    [Gui.CategoryOrder("Channel 2",    20)]
    [Gui.CategoryOrder("Data Series",  30)]
    [Gui.CategoryOrder("Setup",        40)]
    [Gui.CategoryOrder("Visual",       50)]
    [Gui.CategoryOrder("Text Signals", 55)] // New group
    [Gui.CategoryOrder("Plots",        60)]
    [Gui.CategoryOrder("Version",      80)]
    public class Bala2Channels : Indicator
    {
        #region Private variables

        // --- SMMA toggle ---
        private bool useSmma = true;

        // --- Channel 1 fields ---
        private EMA   ema1_1;      // EMA(Input, EMAPeriod1)
        private double avg1;       // ATR‐MA for Channel 1

        // --- Channel 2 fields ---
        private EMA   ema2_1;      // EMA(Input, EMAPeriod2)
        private double avg2;       // ATR‐MA for Channel 2

        // Counter to give each “L”/“S” a unique tag name
        private int textTagIndex = 0;

        // --- Background & Misc fields ---
        private string miscNote           = "DualRSI = +/- RSI on Zero line";
        private Brush  upBackgroundColor   = Brushes.CadetBlue; // default Up background
        private Brush  downBackgroundColor = Brushes.Purple;    // default Down background
        private double backgroundOpacity   = 0.5;

        // Current background state: 0 = neutral, +1 = Up, -1 = Down
        private int bgState = 0;

        // --- NEW: customizable “L”/“S” tag settings ---
        private string  lTagName      = "L";
        private string  sTagName      = "S";
        private Brush   lTextColor    = Brushes.DarkGreen;
        private Brush   sTextColor    = Brushes.Maroon;
        private int     lOffset       = 2;   // ticks below the L-dot
        private int     sOffset       = 2;   // ticks above the S-dot
        private int     textFontSize  = 8;   // default font size
        private bool    showLTag      = true;
        private bool    showSTag      = true;

        #endregion

        #region State Management

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description              = "Bala2Channels: Two TChannel3Tony-style channels.  " +
                                           "When Channel 2’s dark-green dot appears on Lower2 ⇒ “Long” + White background.  " +
                                           "When Channel 2’s maroon dot appears on Upper2 ⇒ “Short” + Blue background.  " +
                                           "Exposes C3Trend = +1/−1 from Channel 1 slope.  " +
                                           "Now fully customizable L/S tag names, colors, offsets, font size, and show/hide toggles.";
                Name                     = "Bala2Channels";
                IsSuspendedWhileInactive = true;
                IsOverlay                = true;  // Paint background behind price bars

                // --- SMMA toggle ---
                UseSMMA                  = true;

                // --- Channel 1 defaults ---
                Dev1                     = 2.0;
                XS1                      = 50.0;
                XL1                      = 50.0;
                RSIPeriod1               = 21;
                EMAPeriod1               = 21;

                // --- Channel 2 defaults ---
                Dev2                     = 1.0;
                XS2                      = 49.0;
                XL2                      = 50.0;
                RSIPeriod2               = 21;
                EMAPeriod2               = 21;

                // --- Background defaults ---
                UpBackgroundColor        = Brushes.MediumSpringGreen;
                DownBackgroundColor      = Brushes.OrangeRed;
                BackgroundOpacity        = 0.2;

                // --- NEW: Text Signal defaults (L/S) ---
                LTagName                 = "L-Ch";
                STagName                 = "S-Ch";
                LTextColor               = Brushes.Cyan;
                STextColor               = Brushes.Yellow;
                LOffset                  = 20;
                SOffset                  = 20;
                TextFontSize             = 12;
                ShowLTag                 = true;
                ShowSTag                 = true;

                // --- Channel 1 plots (0..4) ---
                AddPlot(new Stroke(Brushes.Gold, 1), PlotStyle.Line, "Middle1");
                AddPlot(new Stroke(Brushes.Transparent, 2), PlotStyle.Dot,  "UCrosses1");
                AddPlot(new Stroke(Brushes.Transparent, 2), PlotStyle.Dot,  "LCrosses1");
                AddPlot(new Stroke(Brushes.Maroon,       1), PlotStyle.Line, "Upper1");
                AddPlot(new Stroke(Brushes.Green,        1), PlotStyle.Line, "Lower1");
                Plots[4].DashStyleHelper = DashStyleHelper.Dot;

                // --- Channel 2 plots (5..9) ---
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "Middle2");
                AddPlot(new Stroke(Brushes.DeepPink,      4), PlotStyle.TriangleDown,  "UCrosses2");
                AddPlot(new Stroke(Brushes.RoyalBlue,   4), PlotStyle.TriangleUp,  "LCrosses2");
                AddPlot(new Stroke(Brushes.White,      1), PlotStyle.Dot,  "Upper2");
                AddPlot(new Stroke(Brushes.White,      1), PlotStyle.Dot,  "Lower2");
                Plots[9].DashStyleHelper = DashStyleHelper.Dot;

                // --- C3Trend (index 10), invisible for Strategy Builder ---
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "C3Trend");

                // --- Zero line, if drawn in separate panel ---
                AddLine(Brushes.Black, 0, NinjaTrader.Custom.Resource.NinjaScriptIndicatorNeutral);
            }
            else if (State == State.Configure)
            {
                // Build EMA series once we know EMAPeriods
                ema1_1 = EMA(Input, EMAPeriod1);
                ema2_1 = EMA(Input, EMAPeriod2);
            }
        }

        #endregion

        #region Bar Update

        protected override void OnBarUpdate()
        {
            // ===========================
            // CHANNEL 1 CALCULATIONS
            // ===========================
            Middle1[0] = ema1_1[0];

            if (!UseSMMA)
                avg1 = MAAverage(ATR(1), EMAPeriod1)[0];
            else
                avg1 = SMMA(ATR(1), EMAPeriod1)[0];

            Upper1[0] = Middle1[0] + avg1 * Dev1;
            Lower1[0] = Middle1[0] - avg1 * Dev1;

            double rsi1_0 = RSI(Input, RSIPeriod1, 1)[0];

            // Plot LCrosses1 if RSI1 > XL1
            if (rsi1_0 > XL1)
            {
                PlotBrushes[2][0] = Plots[2].Brush; // LCrosses1
                LCrosses1[0]     = Lower1[0];
            }
            else
                PlotBrushes[2][0] = Brushes.Transparent;

            // Plot UCrosses1 if RSI1 < XS1
            if (rsi1_0 < XS1)
            {
                PlotBrushes[1][0] = Plots[1].Brush; // UCrosses1
                UCrosses1[0]     = Upper1[0];
            }
            else
                PlotBrushes[1][0] = Brushes.Transparent;

            // ===========================
            // CHANNEL 2 CALCULATIONS
            // ===========================
            Middle2[0] = ema2_1[0];

            if (!UseSMMA)
                avg2 = MAAverage(ATR(1), EMAPeriod2)[0];
            else
                avg2 = SMMA(ATR(1), EMAPeriod2)[0];

            Upper2[0] = Middle2[0] + avg2 * Dev2;
            Lower2[0] = Middle2[0] - avg2 * Dev2;

            double rsi2_0 = RSI(Input, RSIPeriod2, 1)[0];

            // Plot LCrosses2 if RSI2 > XL2
            if (rsi2_0 > XL2)
            {
                PlotBrushes[7][0] = Plots[7].Brush; // LCrosses2
                LCrosses2[0]     = Lower2[0];
            }
            else
                PlotBrushes[7][0] = Brushes.Transparent;

            // Plot UCrosses2 if RSI2 < XS2
            if (rsi2_0 < XS2)
            {
                PlotBrushes[6][0] = Plots[6].Brush; // UCrosses2
                UCrosses2[0]     = Upper2[0];
            }
            else
                PlotBrushes[6][0] = Brushes.Transparent;

            // ===========================
            // C3TREND (Channel 1 slope)
            // ===========================
            if (CurrentBar == 0)
                C3TrendSeries[0] = 0;
            else
            {
                bool rising1  = Middle1[1] < Middle1[0];
                bool falling1 = Middle1[1] > Middle1[0];

                if      (rising1)   C3TrendSeries[0] = +1;
                else if (falling1)  C3TrendSeries[0] = -1;
                else                C3TrendSeries[0] =  0;
            }

            // ===========================
            // DRAW “L” / “S” ON FIRST CROSSING
            // ===========================
            if (CurrentBar >= 1)
            {
                // -- “L” = first appearance of LCrosses2[0] != 0 && LCrosses2[1] == 0
                if (LCrosses2[0] != 0 && LCrosses2[1] == 0)
                {
                    if (ShowLTag)
                    {
                        string tag = $"{LTagName}_{CurrentBar}_{++textTagIndex}";
                        Draw.Text(
                            this,
                            tag,
                            false,                        // autoScale = false
                            LTagName,                     // string to print
                            0,                            // barsAgo = 0 (current bar)
                            Lower2[0] - (TickSize * LOffset), // Y coordinate
                            0,                            // unused second Y
                            LTextColor,
                            new SimpleFont("Arial", TextFontSize),
                            TextAlignment.Center,
                            Brushes.Transparent,
                            Brushes.Transparent,
                            0);
                    }
                    bgState = +1;  // set background to UpBackgroundColor
                }

                // -- “S” = first appearance of UCrosses2[0] != 0 && UCrosses2[1] == 0
                if (UCrosses2[0] != 0 && UCrosses2[1] == 0)
                {
                    if (ShowSTag)
                    {
                        string tag = $"{STagName}_{CurrentBar}_{++textTagIndex}";
                        Draw.Text(
                            this,
                            tag,
                            false,                        // autoScale = false
                            STagName,                     // string to print
                            0,                            // barsAgo = 0
                            Upper2[0] + (TickSize * SOffset), // Y coordinate
                            0,                            // unused second Y
                            STextColor,
                            new SimpleFont("Arial", TextFontSize),
                            TextAlignment.Center,
                            Brushes.Transparent,
                            Brushes.Transparent,
                            0);
                    }
                    bgState = -1;  // set background to DownBackgroundColor
                }
            }

            // ===========================
            // PAINT BACKGROUND BY bgState
            // ===========================
            byte alpha = (byte)Math.Round(BackgroundOpacity * 255);

            if (bgState == +1)
            {
                Color c = (UpBackgroundColor as SolidColorBrush)?.Color ?? Colors.Transparent;
                BackBrushes[0] = new SolidColorBrush(Color.FromArgb(alpha, c.R, c.G, c.B));
            }
            else if (bgState == -1)
            {
                Color c = (DownBackgroundColor as SolidColorBrush)?.Color ?? Colors.Transparent;
                BackBrushes[0] = new SolidColorBrush(Color.FromArgb(alpha, c.R, c.G, c.B));
            }
            else
            {
                BackBrushes[0] = Brushes.Transparent;
            }
        }

        #endregion

        #region Series Properties

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Middle1
        {
            get { return Values[0]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> UCrosses1
        {
            get { return Values[1]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> LCrosses1
        {
            get { return Values[2]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Upper1
        {
            get { return Values[3]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Lower1
        {
            get { return Values[4]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Middle2
        {
            get { return Values[5]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> UCrosses2
        {
            get { return Values[6]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> LCrosses2
        {
            get { return Values[7]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Upper2
        {
            get { return Values[8]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Lower2
        {
            get { return Values[9]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> C3TrendSeries
        {
            get { return Values[10]; }
        }

        #endregion

        #region Parameters

        // --- SMMA Option ---
        [Display(ResourceType = typeof(Custom.Resource), Name = "Use SMMA instead", GroupName = "SMMA Options", Order = 0)]
        public bool UseSMMA
        {
            get { return useSmma; }
            set { useSmma = value; }
        }

        // --- Channel 1 Parameters ---
        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Dev (Ch1)",       GroupName = "Channel 1", Order = 0)]
        public double Dev1 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverBought XS (Ch1)", GroupName = "Channel 1", Order = 1)]
        public double XS1 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverSold XL (Ch1)",  GroupName = "Channel 1", Order = 2)]
        public double XL1 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "RSI Period (Ch1)", GroupName = "Channel 1", Order = 3)]
        public int RSIPeriod1 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "EMA Period (Ch1)", GroupName = "Channel 1", Order = 4)]
        public int EMAPeriod1 { get; set; }

        // --- Channel 2 Parameters ---
        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Dev (Ch2)",       GroupName = "Channel 2", Order = 0)]
        public double Dev2 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverBought XS (Ch2)", GroupName = "Channel 2", Order = 1)]
        public double XS2 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverSold XL (Ch2)",  GroupName = "Channel 2", Order = 2)]
        public double XL2 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "RSI Period (Ch2)", GroupName = "Channel 2", Order = 3)]
        public int RSIPeriod2 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "EMA Period (Ch2)", GroupName = "Channel 2", Order = 4)]
        public int EMAPeriod2 { get; set; }

        // --- Background Properties ---
        [XmlIgnore]
        [Display(Name = "Up Background Color", GroupName = "Visual", Order = 1)]
        public Brush UpBackgroundColor
        {
            get { return upBackgroundColor; }
            set { upBackgroundColor = value; }
        }

        [Browsable(false)]
        public string UpBackgroundColorSerialize
        {
            get { return Serialize.BrushToString(upBackgroundColor); }
            set { upBackgroundColor = Serialize.StringToBrush(value); }
        }

        [XmlIgnore]
        [Display(Name = "Down Background Color", GroupName = "Visual", Order = 2)]
        public Brush DownBackgroundColor
        {
            get { return downBackgroundColor; }
            set { downBackgroundColor = value; }
        }

        [Browsable(false)]
        public string DownBackgroundColorSerialize
        {
            get { return Serialize.BrushToString(downBackgroundColor); }
            set { downBackgroundColor = Serialize.StringToBrush(value); }
        }

        [Range(0.0, 1.0), NinjaScriptProperty]
        [Display(Name = "Background Opacity (0–1)", GroupName = "Visual", Order = 3)]
        public double BackgroundOpacity
        {
            get { return backgroundOpacity; }
            set { backgroundOpacity = value; }
        }

        // --- Version/Note ---
        [Display(ResourceType = typeof(Custom.Resource), Name = "Note", Description = "Miscellaneous note", GroupName = "Version", Order = 0)]
        public string MiscNote
        {
            get { return miscNote; }
        }

        // --- NEW: Text Signals (L/S) ---

        [Display(Name = "Long Tag Name", Description = "Text to print for a Long signal", GroupName = "Text Signals", Order = 0)]
        public string LTagName
        {
            get { return lTagName; }
            set { lTagName = value; }
        }

        [XmlIgnore]
        [Display(Name = "Long Text Color", Description = "Color for the Long tag text", GroupName = "Text Signals", Order = 1)]
        public Brush LTextColor
        {
            get { return lTextColor; }
            set { lTextColor = value; }
        }

        [Browsable(false)]
        public string LTextColorSerialize
        {
            get { return Serialize.BrushToString(lTextColor); }
            set { lTextColor = Serialize.StringToBrush(value); }
        }

        [Display(Name = "Long Offset (ticks)", Description = "How many ticks below the channel to place Long tag", GroupName = "Text Signals", Order = 2)]
        public int LOffset
        {
            get { return lOffset; }
            set { lOffset = Math.Max(0, value); }
        }

        [Display(Name = "Show Long Tag", Description = "If false, do not print Long tags", GroupName = "Text Signals", Order = 3)]
        public bool ShowLTag
        {
            get { return showLTag; }
            set { showLTag = value; }
        }

        [Display(Name = "Short Tag Name", Description = "Text to print for a Short signal", GroupName = "Text Signals", Order = 4)]
        public string STagName
        {
            get { return sTagName; }
            set { sTagName = value; }
        }

        [XmlIgnore]
        [Display(Name = "Short Text Color", Description = "Color for the Short tag text", GroupName = "Text Signals", Order = 5)]
        public Brush STextColor
        {
            get { return sTextColor; }
            set { sTextColor = value; }
        }

        [Browsable(false)]
        public string STextColorSerialize
        {
            get { return Serialize.BrushToString(sTextColor); }
            set { sTextColor = Serialize.StringToBrush(value); }
        }

        [Display(Name = "Short Offset (ticks)", Description = "How many ticks above the channel to place Short tag", GroupName = "Text Signals", Order = 6)]
        public int SOffset
        {
            get { return sOffset; }
            set { sOffset = Math.Max(0, value); }
        }

        [Display(Name = "Show Short Tag", Description = "If false, do not print Short tags", GroupName = "Text Signals", Order = 7)]
        public bool ShowSTag
        {
            get { return showSTag; }
            set { showSTag = value; }
        }

        [Display(Name = "Text Font Size", Description = "Font size for all tags (in pixels)", GroupName = "Text Signals", Order = 8)]
        public int TextFontSize
        {
            get { return textFontSize; }
            set { textFontSize = Math.Max(6, value); }
        }

        #endregion

        #region Miscellaneous

        public override string FormatPriceMarker(double price)
        {
            return Instrument.MasterInstrument.FormatPrice(Instrument.MasterInstrument.RoundToTickSize(price));
        }

        #endregion
    }
}


/*
namespace NinjaTrader.NinjaScript.Indicators
{
    /// <summary>
    /// Bala2Chanels: Two TChannel3Tony‐style channels plotted together.
    /// Channel 2’s dark‐green dot → “L” & sets background to DarkGreen.
    /// Channel 2’s maroon dot → “S” & sets background to Maroon.
    /// Exposes C3Trend = +1/–1 from Channel 1’s slope.  
    /// </summary>
    [Gui.CategoryOrder("SMMA Options", 0)]
    [Gui.CategoryOrder("Channel 1",    10)]
    [Gui.CategoryOrder("Channel 2",    20)]
    [Gui.CategoryOrder("Data Series",  30)]
    [Gui.CategoryOrder("Set up",       40)]
    [Gui.CategoryOrder("Visual",       50)]
    [Gui.CategoryOrder("Plots",        60)]
    [Gui.CategoryOrder("Version",      80)]
	
    public class Bala2Chanels : Indicator
    {
        #region Private variables

        // Shared SMMA toggle
        private bool useSmma = true;

        // ------- Channel 1 fields -------
        private EMA   ema1_1;      // EMA(Input, EMAPeriod1)
        private double avg1;       // ATR‐MA for Channel 1

        // ------- Channel 2 fields -------
        private EMA   ema2_1;      // EMA(Input, EMAPeriod2)
        private double avg2;       // ATR‐MA for Channel 2

        // Counter to give each “L”/“S” a unique tag
        private int textTagIndex = 0;

        // ------- Background & Misc fields -------
        private string miscNote           = "DualRSI = +/- RSI on Zero line";
        private Brush  upBackgroundColor   = Brushes.DarkGreen; // default Up background
        private Brush  downBackgroundColor = Brushes.Maroon;    // default Down background
        private double backgroundOpacity   = 0.5;

        // Current background state: 0 = neutral, +1 = Up, -1 = Down
        private int bgState = 0;

        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description              = "Channel 1 default: Dev=2, XS=50, XL=50, RSI=21, EMA=21.  " +
                                           "Channel 2 default: Dev=1, XS=49, XL=50, RSI=21, EMA=21.  " +
                                           "When Channel 2’s dark‐green dot appears at LCrosses2 ⇒ “L” + dark‐green background.  " +
                                           "When Channel 2’s maroon dot appears at UCrosses2 ⇒ “S” + maroon background.  " +
                                           "Exposes C3Trend = +1/–1 from Channel 1 slope.";
                Name                     = "Bala2Chanels";
                IsSuspendedWhileInactive = true;

                // Ensure BackBrushes paints the price panel, not a gray sub‐panel
                IsOverlay                = true;

                // ===== SMMA Toggle =====
                UseSMMA                  = true;

                // ===== Channel 1 Defaults =====
                Dev1                     = 2;
                XS1                      = 50;
                XL1                      = 50;
                RSIPeriod1               = 21;
                EMAPeriod1               = 21;

                // ===== Channel 2 Defaults =====
                Dev2                     = 1;
                XS2                      = 49;
                XL2                      = 50;
                RSIPeriod2               = 21;
                EMAPeriod2               = 21;

                // ===== Background Defaults =====
                UpBackgroundColor        = Brushes.DarkGreen;
                DownBackgroundColor      = Brushes.Maroon;
                BackgroundOpacity        = 0.5;

                // ===== Channel 1 Plots (Indices 0..4) =====
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "Middle1");   // invisible
                AddPlot(new Stroke(Brushes.Transparent,         2), PlotStyle.Dot,  "UCrosses1"); // red
                AddPlot(new Stroke(Brushes.Transparent,       2), PlotStyle.Dot,  "LCrosses1"); // green
                AddPlot(new Stroke(Brushes.Maroon,     1), PlotStyle.Line, "Upper1");    // magenta
                AddPlot(new Stroke(Brushes.Green,     1), PlotStyle.Line, "Lower1");    // magenta, dotted
                Plots[4].DashStyleHelper = DashStyleHelper.Dot;                            // Lower1 dotted

                // ===== Channel 2 Plots (Indices 5..9) =====
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "Middle2");   // invisible
                AddPlot(new Stroke(Brushes.Maroon,      2), PlotStyle.Dot,  "UCrosses2"); // maroon
                AddPlot(new Stroke(Brushes.DarkGreen,   2), PlotStyle.Dot,  "LCrosses2"); // dark‐green
                AddPlot(new Stroke(Brushes.White,      1), PlotStyle.Dot, "Upper2");    // yellow
                AddPlot(new Stroke(Brushes.White,      1), PlotStyle.Dot, "Lower2");    // yellow, dotted
                Plots[9].DashStyleHelper = DashStyleHelper.Dot;                            // Lower2 dotted

                // ===== C3Trend (Index 10) – invisible, for Strategy Builder =====
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "C3Trend");

                // ===== Zero‐line if drawn in a separate sub‐panel =====
                AddLine(Brushes.Black, 0, NinjaTrader.Custom.Resource.NinjaScriptIndicatorNeutral);
            }
            else if (State == State.Configure)
            {
                // Build EMAs once the parameters are known
                ema1_1 = EMA(Input, EMAPeriod1);
                ema2_1 = EMA(Input, EMAPeriod2);
            }
        }

        protected override void OnBarUpdate()
        {
            // ======================================================
            // CHANNEL 1 CALCULATIONS
            // ======================================================

            // 1) “Middle1” = EMA(Input, EMAPeriod1)[0]
            Middle1[0] = ema1_1[0];

            // 2) ATR‐MA for Ch 1 (MA vs SMMA)
            if (!UseSMMA)
                avg1 = MAAverage(ATR(1), EMAPeriod1)[0];
            else
                avg1 = SMMA(ATR(1), EMAPeriod1)[0];

            // 3) Build Upper1 / Lower1
            Upper1[0] = Middle1[0] + avg1 * Dev1;
            Lower1[0] = Middle1[0] - avg1 * Dev1;

            // 4) Compute RSI(Input, RSIPeriod1)
            double rsi1_0 = RSI(Input, RSIPeriod1, 1)[0];

            // 5) If RSI1 > XL1 ⇒ plot green dot on LCrosses1
            if (rsi1_0 > XL1)
            {
                PlotBrushes[2][0] = Plots[2].Brush;  // LCrosses1
                LCrosses1[0]      = Lower1[0];
            }
            else
            {
                PlotBrushes[2][0] = Brushes.Transparent;
            }

            // 6) If RSI1 < XS1 ⇒ plot red dot on UCrosses1
            if (rsi1_0 < XS1)
            {
                PlotBrushes[1][0] = Plots[1].Brush;  // UCrosses1
                UCrosses1[0]      = Upper1[0];
            }
            else
            {
                PlotBrushes[1][0] = Brushes.Transparent;
            }

            // ======================================================
            // CHANNEL 2 CALCULATIONS
            // ======================================================

            Middle2[0] = ema2_1[0];

            if (!UseSMMA)
                avg2 = MAAverage(ATR(1), EMAPeriod2)[0];
            else
                avg2 = SMMA(ATR(1), EMAPeriod2)[0];

            Upper2[0] = Middle2[0] + avg2 * Dev2;
            Lower2[0] = Middle2[0] - avg2 * Dev2;

            double rsi2_0 = RSI(Input, RSIPeriod2, 1)[0];

            // If RSI2 > XL2 ⇒ plot green dot (dark‐green) on LCrosses2
            if (rsi2_0 > XL2)
            {
                PlotBrushes[7][0] = Plots[7].Brush;  // LCrosses2
                LCrosses2[0]      = Lower2[0];
            }
            else
            {
                PlotBrushes[7][0] = Brushes.Transparent;
            }

            // If RSI2 < XS2 ⇒ plot maroon dot on UCrosses2
            if (rsi2_0 < XS2)
            {
                PlotBrushes[6][0] = Plots[6].Brush;  // UCrosses2
                UCrosses2[0]      = Upper2[0];
            }
            else
            {
                PlotBrushes[6][0] = Brushes.Transparent;
            }

            // ======================================================
            // C3TREND = +1/–1/0 based on Channel 1 slope
            // ======================================================
            if (CurrentBar == 0)
                C3TrendSeries[0] = 0;
            else
            {
                bool rising1  = Middle1[1] < Middle1[0];
                bool falling1 = Middle1[1] > Middle1[0];

                if      (rising1)   C3TrendSeries[0] = +1;
                else if (falling1)  C3TrendSeries[0] = -1;
                else                C3TrendSeries[0] =  0;
            }

            // ======================================================
            // DRAW “L” / “S” WHEN CHANNEL 2 FIRST DOT APPEARS
            // ======================================================
            //
            // “L” appears on the first bar where LCrosses2[0] != 0 and LCrosses2[1] == 0.
            // “S” appears on the first bar where UCrosses2[0] != 0 and UCrosses2[1] == 0.
            //
            if (CurrentBar >= 1)
            {
                // Check for “L” (first dark‐green dot)
                if (LCrosses2[0] != 0 && LCrosses2[1] == 0)
                {
                    string tag = $"L_{CurrentBar}_{++textTagIndex}";
                    // Draw “L” two ticks below the dot
                    Draw.Text(this, tag, "L", 0, Lower2[0] - TickSize * 2, Brushes.DarkGreen);

                    // Set background to UpBackgroundColor (DarkGreen) and persist
                    bgState = +1;
                }

                // Check for “S” (first maroon dot)
                if (UCrosses2[0] != 0 && UCrosses2[1] == 0)
                {
                    string tag = $"S_{CurrentBar}_{++textTagIndex}";
                    // Draw “S” two ticks above the dot
                    Draw.Text(this, tag, "S", 0, Upper2[0] + TickSize * 2, Brushes.Maroon);

                    // Set background to DownBackgroundColor (Maroon) and persist
                    bgState = -1;
                }
            }

            // ======================================================
            // PAINT PRICE‐PANEL BACKGROUND BASED ON bgState
            // ======================================================
            //
            // Because IsOverlay = true, BackBrushes[0] paints behind the price bars.
            // We preserve whichever color state (Up=+1, Down=-1) until changed by the next L/S.
            //
            byte alpha = (byte)Math.Round(BackgroundOpacity * 255);

            if (bgState == +1)
            {
                Color c = (UpBackgroundColor as SolidColorBrush)?.Color ?? Colors.Transparent;
                BackBrushes[0] = new SolidColorBrush(Color.FromArgb(alpha, c.R, c.G, c.B));
            }
            else if (bgState == -1)
            {
                Color c = (DownBackgroundColor as SolidColorBrush)?.Color ?? Colors.Transparent;
                BackBrushes[0] = new SolidColorBrush(Color.FromArgb(alpha, c.R, c.G, c.B));
            }
            else
            {
                BackBrushes[0] = Brushes.Transparent;
            }
        }

        #region Series Properties

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Middle1
        {
            get { return Values[0]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> UCrosses1
        {
            get { return Values[1]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> LCrosses1
        {
            get { return Values[2]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Upper1
        {
            get { return Values[3]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Lower1
        {
            get { return Values[4]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Middle2
        {
            get { return Values[5]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> UCrosses2
        {
            get { return Values[6]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> LCrosses2
        {
            get { return Values[7]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Upper2
        {
            get { return Values[8]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Lower2
        {
            get { return Values[9]; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> C3TrendSeries
        {
            get { return Values[10]; }
        }

        #endregion

        #region Parameters

        // ----- SMMA Option -----
        [Display(ResourceType = typeof(Custom.Resource), Name = "Use SMMA instead", GroupName = "SMMA Options", Order = 0)]
        public bool UseSMMA
        {
            get { return useSmma; }
            set { useSmma = value; }
        }

        // ----- Channel 1 Parameters -----
        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Dev (Ch1)",       GroupName = "Channel 1", Order = 0)]
        public double Dev1 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverBought XS (Ch1)", GroupName = "Channel 1", Order = 1)]
        public double XS1 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverSold XL (Ch1)",  GroupName = "Channel 1", Order = 2)]
        public double XL1 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "RSI Period (Ch1)", GroupName = "Channel 1", Order = 3)]
        public int RSIPeriod1 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "EMA Period (Ch1)", GroupName = "Channel 1", Order = 4)]
        public int EMAPeriod1 { get; set; }

        // ----- Channel 2 Parameters -----
        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Dev (Ch2)",       GroupName = "Channel 2", Order = 0)]
        public double Dev2 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverBought XS (Ch2)", GroupName = "Channel 2", Order = 1)]
        public double XS2 { get; set; }

        [Range(0.00001, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "OverSold XL (Ch2)",  GroupName = "Channel 2", Order = 2)]
        public double XL2 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "RSI Period (Ch2)", GroupName = "Channel 2", Order = 3)]
        public int RSIPeriod2 { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "EMA Period (Ch2)", GroupName = "Channel 2", Order = 4)]
        public int EMAPeriod2 { get; set; }

        // ----- Background Properties -----
        [XmlIgnore]
        [Display(Name = "Up Background Color", GroupName = "Background", Order = 1)]
        public Brush UpBackgroundColor
        {
            get { return upBackgroundColor; }
            set { upBackgroundColor = value; }
        }

        [Browsable(false)]
        public string UpBackgroundColorSerialize
        {
            get { return Serialize.BrushToString(upBackgroundColor); }
            set { upBackgroundColor = Serialize.StringToBrush(value); }
        }

        [XmlIgnore]
        [Display(Name = "Down Background Color", GroupName = "Background", Order = 2)]
        public Brush DownBackgroundColor
        {
            get { return downBackgroundColor; }
            set { downBackgroundColor = value; }
        }

        [Browsable(false)]
        public string DownBackgroundColorSerialize
        {
            get { return Serialize.BrushToString(downBackgroundColor); }
            set { downBackgroundColor = Serialize.StringToBrush(value); }
        }

        [Range(0.0, 1.0), NinjaScriptProperty]
        [Display(Name = "Background Opacity (0–1)", GroupName = "Background", Order = 3)]
        public double BackgroundOpacity
        {
            get { return backgroundOpacity; }
            set { backgroundOpacity = value; }
        }

        // ----- Version/Note -----
        [Display(ResourceType = typeof(Custom.Resource), Name = "Note", Description = "Miscellaneous note", GroupName = "Version", Order = 0)]
        public string MiscNote
        {
            get { return miscNote; }
        }

        #endregion

        #region Miscellaneous

        public override string FormatPriceMarker(double price)
        {
            return Instrument.MasterInstrument.FormatPrice(Instrument.MasterInstrument.RoundToTickSize(price));
        }

        #endregion
    }
}
*/


//=========================================================================

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private Bala2Channels[] cacheBala2Channels;
		public Bala2Channels Bala2Channels(double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			return Bala2Channels(Input, dev1, xS1, xL1, rSIPeriod1, eMAPeriod1, dev2, xS2, xL2, rSIPeriod2, eMAPeriod2, backgroundOpacity);
		}

		public Bala2Channels Bala2Channels(ISeries<double> input, double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			if (cacheBala2Channels != null)
				for (int idx = 0; idx < cacheBala2Channels.Length; idx++)
					if (cacheBala2Channels[idx] != null && cacheBala2Channels[idx].Dev1 == dev1 && cacheBala2Channels[idx].XS1 == xS1 && cacheBala2Channels[idx].XL1 == xL1 && cacheBala2Channels[idx].RSIPeriod1 == rSIPeriod1 && cacheBala2Channels[idx].EMAPeriod1 == eMAPeriod1 && cacheBala2Channels[idx].Dev2 == dev2 && cacheBala2Channels[idx].XS2 == xS2 && cacheBala2Channels[idx].XL2 == xL2 && cacheBala2Channels[idx].RSIPeriod2 == rSIPeriod2 && cacheBala2Channels[idx].EMAPeriod2 == eMAPeriod2 && cacheBala2Channels[idx].BackgroundOpacity == backgroundOpacity && cacheBala2Channels[idx].EqualsInput(input))
						return cacheBala2Channels[idx];
			return CacheIndicator<Bala2Channels>(new Bala2Channels(){ Dev1 = dev1, XS1 = xS1, XL1 = xL1, RSIPeriod1 = rSIPeriod1, EMAPeriod1 = eMAPeriod1, Dev2 = dev2, XS2 = xS2, XL2 = xL2, RSIPeriod2 = rSIPeriod2, EMAPeriod2 = eMAPeriod2, BackgroundOpacity = backgroundOpacity }, input, ref cacheBala2Channels);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.Bala2Channels Bala2Channels(double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			return indicator.Bala2Channels(Input, dev1, xS1, xL1, rSIPeriod1, eMAPeriod1, dev2, xS2, xL2, rSIPeriod2, eMAPeriod2, backgroundOpacity);
		}

		public Indicators.Bala2Channels Bala2Channels(ISeries<double> input , double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			return indicator.Bala2Channels(input, dev1, xS1, xL1, rSIPeriod1, eMAPeriod1, dev2, xS2, xL2, rSIPeriod2, eMAPeriod2, backgroundOpacity);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.Bala2Channels Bala2Channels(double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			return indicator.Bala2Channels(Input, dev1, xS1, xL1, rSIPeriod1, eMAPeriod1, dev2, xS2, xL2, rSIPeriod2, eMAPeriod2, backgroundOpacity);
		}

		public Indicators.Bala2Channels Bala2Channels(ISeries<double> input , double dev1, double xS1, double xL1, int rSIPeriod1, int eMAPeriod1, double dev2, double xS2, double xL2, int rSIPeriod2, int eMAPeriod2, double backgroundOpacity)
		{
			return indicator.Bala2Channels(input, dev1, xS1, xL1, rSIPeriod1, eMAPeriod1, dev2, xS2, xL2, rSIPeriod2, eMAPeriod2, backgroundOpacity);
		}
	}
}

#endregion
