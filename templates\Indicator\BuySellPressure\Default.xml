﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <BuySellPressure>
    <BuySellPressure xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <calculate2>OnEachTick</calculate2>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <Calculate>OnEachTick</Calculate>
      <Displacement>0</Displacement>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2025-03-13T00:00:00</From>
      <IsAutoScale>true</IsAutoScale>
      <Lines>
        <Line>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Solid</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>1</Width>
          <Name>Upper</Name>
          <Value>75</Value>
        </Line>
        <Line>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Solid</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>1</Width>
          <Name>Lower</Name>
          <Value>25</Value>
        </Line>
      </Lines>
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>Buy sell pressure</Name>
      <Panel>1</Panel>
      <Plots>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008B8B&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Solid</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>2</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Buy pressure</Name>
          <PlotStyle>Line</PlotStyle>
        </Plot>
        <Plot>
          <IsOpacityVisible>false</IsOpacityVisible>
          <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
          <DashStyleHelper>Solid</DashStyleHelper>
          <Opacity>100</Opacity>
          <Width>2</Width>
          <AutoWidth>false</AutoWidth>
          <Max>1.7976931348623157E+308</Max>
          <Min>-1.7976931348623157E+308</Min>
          <Name>Sell pressure</Name>
          <PlotStyle>Line</PlotStyle>
        </Plot>
      </Plots>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
      <To>2025-03-18T00:00:00</To>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>false</IsOverlay>
      <SelectedValueSeries>0</SelectedValueSeries>
      <InputPlot>0</InputPlot>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <PaintPriceMarkers>true</PaintPriceMarkers>
      <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
      <DrawVerticalGridLines>true</DrawVerticalGridLines>
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
      <IndicatorId>885</IndicatorId>
      <MaxSerialized>0</MaxSerialized>
      <MinSerialized>0</MinSerialized>
      <ZOrder>10004</ZOrder>
    </BuySellPressure>
  </BuySellPressure>
</NinjaTrader>