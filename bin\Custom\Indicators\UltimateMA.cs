#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public enum UMAType
    {
        SMA,
        EMA,
        WMA,
        HMA,
        VWMA,
        TEMA,
        T3
    }

    public class UltimatMA : Indicator
    {
        private SMA sma;
        private EMA ema;
        private WMA wma;
        private HMA hma;
        private VWMA vwma;
        private TEMA tema;
        private T3 t3;

        private Brush upColor = Brushes.Lime;
        private Brush downColor = Brushes.Red;
        private int period = 5;
        private int smooth = 2;
        private Series<double> signal;
        private UMAType selectedMAType = UMAType.SMA;

        [NinjaScriptProperty]
        [Display(Name = "MA Type", Order = 0, GroupName = "1. Parameters")]
        public UMAType SelectedMAType
        {
            get { return selectedMAType; }
            set { selectedMAType = value; }
        }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Period", Order = 1, GroupName = "1. Parameters")]
        public int Period
        {
            get { return period; }
            set { period = value; }
        }
				
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "TCount", GroupName = "2. T3 Parameters", Order = 0)]
        public int TCount
        { get; set; }

        [Range(0, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "VFactor", GroupName = "2. T3 Parameters", Order = 1)]
        public double VFactor
        { get; set; }
		
		[Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Smooth Length", Order = 0, GroupName = "3. Colors")]
        public int Smooth
        {
            get { return smooth; }
            set { smooth = value; }
        }

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"An indicator to plot a color-changing Moving Average with smoothing.";
                Name = "Ultimate MA";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                TCount = 3;
                VFactor = 0.7;

                AddPlot(Brushes.Orange, "MA");
                AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Line, "Signal");
            }
            else if (State == State.Configure)
            {
                signal = new Series<double>(this);
            }
            else if (State == State.DataLoaded)
            {
                switch (SelectedMAType)
                {
                    case UMAType.SMA:
                        sma = SMA(Close, Period);
                        break;
                    case UMAType.EMA:
                        ema = EMA(Close, Period);
                        break;
                    case UMAType.WMA:
                        wma = WMA(Close, Period);
                        break;
                    case UMAType.HMA:
                        hma = HMA(Close, Period);
                        break;
                    case UMAType.VWMA:
                        vwma = VWMA(Close, Period);
                        break;
                    case UMAType.TEMA:
                        tema = TEMA(Close, Period);
                        break;
                    case UMAType.T3:
                        t3 = T3(Close, Period, TCount, VFactor);
                        break;

                    default:
                        sma = SMA(Close, Period);
                        break;
                }
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < Period + Smooth)
                return;

            double maValue = 0;
            double smoothedValue = 0;
			
            switch (SelectedMAType)
            {
                case UMAType.SMA:
                    maValue = sma[0];
                    smoothedValue = sma[Smooth];
                    break;
                case UMAType.EMA:
                    maValue = ema[0];
                    smoothedValue = ema[Smooth];
                    break;
                case UMAType.WMA:
                    maValue = wma[0];
                    smoothedValue = wma[Smooth];
                    break;
                case UMAType.HMA:
                    maValue = hma[0];
                    smoothedValue = hma[Smooth];
                    break;
                case UMAType.VWMA:
                    maValue = vwma[0];
                    smoothedValue = vwma[Smooth];
                    break;
                case UMAType.TEMA:
                    maValue = tema[0];
                    smoothedValue = tema[Smooth];
                    break;
                case UMAType.T3:
                    maValue = t3[0];
                    smoothedValue = t3[Smooth];
                    break;
                default:
                    maValue = sma[0];
                    smoothedValue = sma[Smooth];
                    break;
            }

            Values[0][0] = maValue;

            if (maValue > smoothedValue)
            {
                PlotBrushes[0][0] = upColor;
                signal[0] = 1;
                Values[1][0] = 1;
            }
            else
            {
                PlotBrushes[0][0] = downColor;
                signal[0] = -1;
                Values[1][0] = -1;
            }
        }

        #region Properties
        [Browsable(false)]
        [XmlIgnore]
        public Series<double> Signal
        {
            get { return signal; }
        }
        #endregion
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private UltimatMA[] cacheUltimatMA;
		public UltimatMA UltimatMA(UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			return UltimatMA(Input, selectedMAType, period, tCount, vFactor, smooth);
		}

		public UltimatMA UltimatMA(ISeries<double> input, UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			if (cacheUltimatMA != null)
				for (int idx = 0; idx < cacheUltimatMA.Length; idx++)
					if (cacheUltimatMA[idx] != null && cacheUltimatMA[idx].SelectedMAType == selectedMAType && cacheUltimatMA[idx].Period == period && cacheUltimatMA[idx].TCount == tCount && cacheUltimatMA[idx].VFactor == vFactor && cacheUltimatMA[idx].Smooth == smooth && cacheUltimatMA[idx].EqualsInput(input))
						return cacheUltimatMA[idx];
			return CacheIndicator<UltimatMA>(new UltimatMA(){ SelectedMAType = selectedMAType, Period = period, TCount = tCount, VFactor = vFactor, Smooth = smooth }, input, ref cacheUltimatMA);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.UltimatMA UltimatMA(UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			return indicator.UltimatMA(Input, selectedMAType, period, tCount, vFactor, smooth);
		}

		public Indicators.UltimatMA UltimatMA(ISeries<double> input , UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			return indicator.UltimatMA(input, selectedMAType, period, tCount, vFactor, smooth);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.UltimatMA UltimatMA(UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			return indicator.UltimatMA(Input, selectedMAType, period, tCount, vFactor, smooth);
		}

		public Indicators.UltimatMA UltimatMA(ISeries<double> input , UMAType selectedMAType, int period, int tCount, double vFactor, int smooth)
		{
			return indicator.UltimatMA(input, selectedMAType, period, tCount, vFactor, smooth);
		}
	}
}

#endregion
