2025-05-27 00:00:42:814 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-27 00:00:42:814 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-27 00:00:43:160 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-27 00:00:43:160 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8996449' renewSecs='2399.94982245'
2025-05-27 00:00:47:638 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:00:47:638 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:00:52:628 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-27 00:00:53:630 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-27 00:00:53:633 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=2.78ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.00ms
2025-05-27 00:05:34:731 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:05:34:731 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:05:56:044 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:05:56:044 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:07:45:216 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:07:45:216 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:09:01:848 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:09:01:848 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:09:42:045 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-27 00:09:42:045 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-27 00:09:42:370 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.9039151' renewSecs='2399.********'
2025-05-27 00:10:00:657 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:00:657 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:21:198 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21187.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-26 23:58:04' gtd='2099-12-01' statementDate='1800-01-01' limitPriceChanged=21189.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:10:21:200 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21187.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:10:21' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:10:21:204 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21187.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 00:10:21' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21189.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:10:21:204 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:10:21:204 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21187.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 00:10:21' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:10:21:433 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:10:21:434 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:21.305Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:21:436 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:10:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:21:436 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21189.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:10:21:436 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:21.307Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:10:21:436 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:21.305Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:21:437 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:21.307Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:10:21:437 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:10:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:21:437 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:10:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:21:437 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:21.305Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:21:437 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:21.307Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:10:21:437 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T03:58:04.801Z",
  "clOrdId": "39704c3c826e4ad7b51d98c8b14de6f1",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:10:21:438 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:10:21.307Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:10:23:483 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:23:484 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:33:406 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:33:406 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:36:701 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:36:701 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:10:42:507 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21127.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:10:42' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21156.25 quantityChanged=1
2025-05-27 00:10:42:507 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21127.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:10:42' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:10:42:512 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21127.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:10:42' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21156.25
2025-05-27 00:10:42:512 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:10:42:512 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21127.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:10:42' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:10:42:735 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:10:42:735 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:42.609Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:42:735 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21156.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:10:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:42:736 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21156.25,
  "timeInForce": "GTC"
}'
2025-05-27 00:10:42:736 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:42.611Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:10:42:736 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:42.609Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:42:736 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:42.611Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:10:42:736 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21156.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:10:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:42:736 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21156.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:10:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:10:42:736 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:42.609Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:10:42:738 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0321,
  "commandId": ************,
  "timestamp": "2025-05-27T04:10:42.611Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:10:42:738 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T03:58:04.797Z",
  "clOrdId": "72572ba28a994ec4996caa84b3107293",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:10:42:738 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:10:42.611Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:12:29:748 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21156.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:12:29' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21157.5 quantityChanged=1
2025-05-27 00:12:29:749 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21156.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:12:29' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:12:29:754 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21156.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:12:29' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21157.5
2025-05-27 00:12:29:755 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:12:29:755 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21156.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:12:29' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:12:29:978 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:12:29:978 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:12:29.852Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:12:29:978 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21157.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:12:29' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:12:29:978 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21157.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:12:29:979 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0323,
  "commandId": ************,
  "timestamp": "2025-05-27T04:12:29.853Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:12:29:979 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:12:29.852Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:12:29:979 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:12:29.854Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:12:29:979 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21157.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:12:29' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:12:29:979 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21157.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:12:29' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:12:29:980 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:12:29.852Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:12:29:980 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0325,
  "commandId": ************,
  "timestamp": "2025-05-27T04:12:29.854Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:12:29:980 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:42.609Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:12:29:982 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.***************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:12:29.854Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:14:48:171 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:14:48:171 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:15:05:020 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:15:05:020 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:15:10:498 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21157.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:15:10' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21158.25 quantityChanged=1
2025-05-27 00:15:10:498 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21157.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:15:10' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:15:10:503 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21157.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:15:10' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21158.25
2025-05-27 00:15:10:503 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:15:10:503 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21157.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:15:10' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:15:10:727 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:15:10:727 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:15:10.600Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:15:10:727 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21158.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:15:10' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:15:10:728 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21158.25,
  "timeInForce": "GTC"
}'
2025-05-27 00:15:10:728 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0327,
  "commandId": ************,
  "timestamp": "2025-05-27T04:15:10.602Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:15:10:728 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:15:10.600Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:15:10:734 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:15:10.602Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:15:10:734 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21158.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:15:10' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:15:10:734 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21158.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:15:10' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:15:10:734 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:15:10.600Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:15:10:734 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:15:10.602Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:15:10:735 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:12:29.852Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:15:10:735 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:15:10.602Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.*****************"
}'
2025-05-27 00:16:07:905 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:16:07:905 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:16:17:078 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21158.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:16:17' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21161.5 quantityChanged=1
2025-05-27 00:16:17:078 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21158.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:17' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:16:17:082 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21158.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:16:17' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21161.5
2025-05-27 00:16:17:083 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:16:17:083 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21158.25 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 00:16:17' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:17:308 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:16:17:308 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:17.181Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:17:308 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:17' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21161.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0331,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:17.182Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:17.181Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:17.183Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:16:17:309 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:17' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:17:309 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:17' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:17.181Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:17.183Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:15:10.600Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:17:309 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:17.182Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:16:59:156 (TRADIFY) Cbi.Account.CreateOrder: orderId='a9598e1655f04a9b937fc24c4cfc59b7' account='TDYA150355451300000020' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=42019 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27' id=-1 comment=''
2025-05-27 00:16:59:165 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='a9598e1655f04a9b937fc24c4cfc59b7' account='TDYA150355451300000020' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=42019 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:165 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='a9598e1655f04a9b937fc24c4cfc59b7' account='TDYA150355451300000020' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=42019 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:166 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='a9598e1655f04a9b937fc24c4cfc59b7' account='TDYA150355451300000020' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=42019 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:166 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-27 00:16:59:166 (TRADIFY) Tradovate.Adapter.Submit1: orderId='a9598e1655f04a9b937fc24c4cfc59b7' account='TDYA150355451300000020' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=42019 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:444 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-27 00:16:59:444 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:444 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "clOrdId": "a9598e1655f04a9b937fc24c4cfc59b7",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:59:444 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='a9598e1655f04a9b937fc24c4cfc59b7' orderId='************' account='TDYA150355451300000020' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Market",
  "timeInForce": "GTC"
}'
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.268Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "clOrdId": "a9598e1655f04a9b937fc24c4cfc59b7",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.270Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:16:59:470 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:470 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "clOrdId": "a9598e1655f04a9b937fc24c4cfc59b7",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:16:59:470 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "action": "Buy",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:471 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.268Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:16:59:471 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.268Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Buy",
  "cumQty": 1,
  "avgPx": 21165.25,
  "lastQty": 1,
  "lastPx": 21165.25,
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:16:59:471 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ************,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.268Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "action": "Buy",
  "qty": 1,
  "price": 21165.25,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-27 00:16:59:471 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYA150355451300000020' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=1 averageFillPrice=21165.25 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:488 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYA150355451300000020' instrument='NQ JUN25' exchange=Globex price=21165.25 quantity=1 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-27 00:16:59' statementDate='1800-01-01'
2025-05-27 00:16:59:488 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 00:16:59:488 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 00:16:59:489 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.268Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "netPos": 2,
  "netPrice": 21161.375,
  "bought": 29,
  "boughtValue": 614613.5,
  "sold": 27,
  "soldValue": 572482.25,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ************
}'
2025-05-27 00:16:59:490 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYA150355451300000020' avgPrice=21161.375 quantity=2 marketPosition=Long operation=Update
2025-05-27 00:16:59:490 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.267Z",
  "action": "Buy",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:494 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********' filled=1 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='************+=1 ' outstandingOrders='' thread=13
2025-05-27 00:16:59:495 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********' initialEntryOrderId='************' bracket=0 qty=1 stopOrdersOutstandingQuantity=0 quantity2Add=1 exitOrders=''
2025-05-27 00:16:59:495 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=1 limitPrice=0 stopPrice=21135.25 oco='c1c41036aafe4e5abee530b0e48eff88'
2025-05-27 00:16:59:495 (TRADIFY) Cbi.Account.CreateOrder: orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27' id=-1 comment=''
2025-05-27 00:16:59:499 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:499 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:499 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:499 (TRADIFY) Cbi.Account.CreateOrder: orderId='238bf302133e4bf4851d27923b86fb97' account='TDYA150355451300000020' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27' id=-1 comment=''
2025-05-27 00:16:59:499 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-27 00:16:59:499 (TRADIFY) Tradovate.Adapter.Submit1: orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:507 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='238bf302133e4bf4851d27923b86fb97' account='TDYA150355451300000020' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:507 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='238bf302133e4bf4851d27923b86fb97' account='TDYA150355451300000020' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:507 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='8e93b53d1ecd41fc94dade587c0d1238' account='TDYA150355451300000020' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:507 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='238bf302133e4bf4851d27923b86fb97' account='TDYA150355451300000020' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:507 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-27 00:16:59:507 (TRADIFY) Tradovate.Adapter.Submit1: orderId='238bf302133e4bf4851d27923b86fb97' account='TDYA150355451300000020' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:16:59' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "clOrdId": "8e93b53d1ecd41fc94dade587c0d1238",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:723 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='8e93b53d1ecd41fc94dade587c0d1238' orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21135.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21135.25,
  "timeInForce": "GTC"
}'
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.599Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:16:59:723 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "clOrdId": "8e93b53d1ecd41fc94dade587c0d1238",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:724 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:724 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.599Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:16:59:724 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21135.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:724 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21135.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:724 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "clOrdId": "8e93b53d1ecd41fc94dade587c0d1238",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:724 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "action": "Sell",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:724 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.599Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:16:59:853 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-27 00:16:59:853 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:853 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "clOrdId": "238bf302133e4bf4851d27923b86fb97",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:853 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='238bf302133e4bf4851d27923b86fb97' orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21195.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21195.25,
  "timeInForce": "GTC"
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0347,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.619Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "clOrdId": "238bf302133e4bf4851d27923b86fb97",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0348,
  "commandId": ************,
  "timestamp": "2025-05-27T04:16:59.620Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:16:59:854 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21195.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:854 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21195.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:16:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "clOrdId": "238bf302133e4bf4851d27923b86fb97",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "action": "Sell",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 00:16:59:854 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0348,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.620Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:17:08:527 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:17:08' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21189.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:17:08:527 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21195.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:17:08' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:17:08:531 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:17:08' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21189.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:17:08:531 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:17:08:531 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21195.25 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:17:08' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:17:08:864 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:08.629Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:08:865 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:17:08' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21189.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0350,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:08.630Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:08.629Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0351,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:08.630Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:17:08:865 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:17:08' statementDate='1800-01-01' error=NoError comment='' nr=6
2025-05-27 00:17:08:865 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:17:08' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:08.629Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0352,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:08.631Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "clOrdId": "238bf302133e4bf4851d27923b86fb97",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:17:08:865 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0351,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:17:08.630Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:17:22:061 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:17:22' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21161.5 quantityChanged=1
2025-05-27 00:17:22:061 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21135.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:17:22' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:17:22:068 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:17:22' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21161.5
2025-05-27 00:17:22:069 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:17:22:069 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21135.25 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 00:17:22' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:17:22:293 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:17:22:293 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:22.168Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:22:293 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:17:22' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:17:22:293 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21161.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:17:22:294 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:22.169Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:17:22:294 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:22.168Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:22:294 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:22.169Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:17:22:294 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:17:22' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:17:22:295 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 00:17:22' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:17:22:295 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:22.168Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:17:22:295 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0356,
  "commandId": ************,
  "timestamp": "2025-05-27T04:17:22.169Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:17:22:295 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "clOrdId": "8e93b53d1ecd41fc94dade587c0d1238",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-27 00:17:22:295 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.***************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:17:22.169Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:26:31:358 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:26:31' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21292.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:26:31:358 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:31' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:26:31:362 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:26:31' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21292.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:26:31:362 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:26:31:362 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:26:31' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:26:31:681 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:26:31:681 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:31.556Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:31:682 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21292.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:31:682 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21292.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:26:31:682 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:31.558Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:26:31:682 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:31.556Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:31:682 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:31.558Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:26:31:682 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21292.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:31:683 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21292.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:31:683 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:31.556Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:31:683 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:31.558Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:26:31:683 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:08.629Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:31:684 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:26:31.558Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 00:26:42:593 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 00:26:42' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21189 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:26:42:593 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21189.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:42' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:26:42:597 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 00:26:42' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21189 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:26:42:598 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:26:42:598 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 00:26:42' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:26:42:821 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 00:26:42:821 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:42.695Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:42:821 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21189 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:42:821 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21189.0,
  "timeInForce": "GTC"
}'
2025-05-27 00:26:42:821 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:42.696Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:26:42:822 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:42.695Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:42:822 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:42.697Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:26:42:822 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21189 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:42:822 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21189 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:26:42' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:26:42:822 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:42.695Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:42:823 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0364,
  "commandId": ************,
  "timestamp": "2025-05-27T04:26:42.697Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:26:42:823 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:10:21.305Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:26:42:823 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:26:42.697Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:40:43:117 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-27 00:40:43:118 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-27 00:40:43:515 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-27 00:40:43:515 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8562441' renewSecs='2399.********'
2025-05-27 00:49:42:332 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-27 00:49:42:332 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-27 00:49:42:690 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8997842' renewSecs='2399.9498921'
2025-05-27 00:51:27:225 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:51:27:225 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:52:06:684 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21292.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:26:31' gtd='2099-12-01' statementDate='1800-01-01' limitPriceChanged=21240.75 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:52:06:686 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21292.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:06' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:52:06:692 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21292.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:52:06' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21240.75 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:52:06:692 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:52:06:692 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21292.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:52:06' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:52:06:888 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0365
}'
2025-05-27 00:52:06:888 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0365,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:06.793Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:06:891 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21240.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:06:891 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0365,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21240.75,
  "timeInForce": "GTC"
}'
2025-05-27 00:52:06:891 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0366,
  "commandId": ********0365,
  "timestamp": "2025-05-27T04:52:06.794Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0365,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:06.793Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0367,
  "commandId": ********0365,
  "timestamp": "2025-05-27T04:52:06.794Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:52:06:892 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21240.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:06:892 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21240.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0365,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:06.793Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0368,
  "commandId": ************,
  "timestamp": "2025-05-27T04:52:06.794Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:31.556Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:06:892 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0367,
  "commandId": ********0365,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:52:06.794Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 00:52:16:659 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21240.75 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:52:06' gtd='2099-12-01' statementDate='1800-01-01' limitPriceChanged=21201.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 00:52:16:662 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21240.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:16' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 00:52:16:667 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21240.75 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:52:16' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21201.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 00:52:16:667 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 00:52:16:667 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21240.75 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 00:52:16' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 00:52:16:907 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0369,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:16.798Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:16:907 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:16:907 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0369,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21201.5,
  "timeInForce": "GTC"
}'
2025-05-27 00:52:16:907 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0369
}'
2025-05-27 00:52:16:907 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0370,
  "commandId": ********0369,
  "timestamp": "2025-05-27T04:52:16.799Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0369,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:16.798Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0371,
  "commandId": ********0369,
  "timestamp": "2025-05-27T04:52:16.800Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 00:52:16:908 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:16:908 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 00:52:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0369,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:16.798Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0372,
  "commandId": ********0365,
  "timestamp": "2025-05-27T04:52:16.800Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0365,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:06.793Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 00:52:16:908 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0371,
  "commandId": ********0369,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:52:16.800Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.***************"
}'
2025-05-27 00:56:02:855 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:56:02:855 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:56:15:131 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:56:15:131 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:58:57:238 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 00:58:57:239 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:00:21:517 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:21' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21177.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 01:00:21:518 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21189 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:21' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:00:21:525 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:21' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21177.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 01:00:21:526 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:00:21:526 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21189 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:21' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:00:21:720 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 01:00:21:720 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:21.623Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:21:720 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21177.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:21:720 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21177.5,
  "timeInForce": "GTC"
}'
2025-05-27 01:00:21:721 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0374,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:21.625Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:00:21:721 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:21.623Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:21:721 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0375,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:21.626Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:00:21:721 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21177.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:21:721 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21177.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:21' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:21:721 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:21.623Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:21:722 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0376,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:21.626Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:00:21:722 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:26:42.695Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:21:722 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0375,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:00:21.626Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:00:36:332 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:00:36:332 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:00:47:638 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 01:00:47' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21173 quantityChanged=1
2025-05-27 01:00:47:638 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:00:47' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:00:47:644 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 01:00:47' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21173
2025-05-27 01:00:47:645 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:00:47:645 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42020 time='2025-05-27 01:00:47' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:00:47:892 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 01:00:47:892 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:47.784Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:47:892 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21173 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:00:47' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:47:892 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21173.0,
  "timeInForce": "GTC"
}'
2025-05-27 01:00:47:893 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0378,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:47.785Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:00:47:893 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:47.784Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:47:893 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:47.785Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:00:47:893 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21173 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:00:47' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:47:893 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21173 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:00:47' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:47:893 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:47.784Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:47:893 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:47.786Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:00:47:894 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:17:22.168Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:47:894 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:00:47.785Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:00:51:854 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:51' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21177 stopPriceChanged=0 quantityChanged=1
2025-05-27 01:00:51:854 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21177.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:51' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:00:51:864 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:51' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21177 quantityChanged=1 stopPriceChanged=0
2025-05-27 01:00:51:864 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:00:51:864 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:00:51' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:00:52:058 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-27 01:00:52:058 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:51.962Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:52:059 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21177 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:51' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:52:059 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21177.0,
  "timeInForce": "GTC"
}'
2025-05-27 01:00:52:059 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0382,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:51.964Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:00:52:059 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:51.962Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:52:059 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:51.964Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:00:52:059 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21177 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:51' statementDate='1800-01-01' error=NoError comment='' nr=18
2025-05-27 01:00:52:059 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21177 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:00:51' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:00:52:059 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:51.962Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:52:060 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-27T05:00:51.964Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:00:52:060 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:21.623Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:00:52:060 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:00:51.964Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:00:52:634 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-27 01:00:53:666 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-27 01:00:53:669 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.01ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=1.30ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.00ms
2025-05-27 01:01:05:983 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:05.889Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Sell",
  "cumQty": 1,
  "avgPx": 21173.0,
  "lastQty": 1,
  "lastPx": 21173.0,
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:01:05:983 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ************,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:05.889Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "action": "Sell",
  "qty": 1,
  "price": 21173.0,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-27 01:01:05:984 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21173 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=21173 time='2025-05-27 01:01:05' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:06:016 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21201.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 01:01:06' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:06:016 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21201.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 01:01:06' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:06:017 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:06' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:01:06:017 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21201.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 01:01:06' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:06:017 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYA150355451300000020' instrument='NQ JUN25' exchange=Globex price=21173 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-27 01:01:05' statementDate='1800-01-01'
2025-05-27 01:01:06:017 (TRADIFY) Tradovate.Adapter.Cancel0: count=1
2025-05-27 01:01:06:017 (TRADIFY) Tradovate.Adapter.Cancel1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21201.5 stopPrice=0 quantity=1 tif=Gtc oco='c1c41036aafe4e5abee530b0e48eff88' filled=0 averageFillPrice=0 onBehalfOf='' id=42021 time='2025-05-27 01:01:06' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:06:018 (TRADIFY) Cbi.Account.OnAddTrade: entryId='********0299_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=310
2025-05-27 01:01:06:018 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:01:06:018 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:018 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:019 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:020 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ********0391,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:05.889Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "netPos": 1,
  "netPrice": 21165.25,
  "bought": 29,
  "boughtValue": 614613.5,
  "sold": 28,
  "soldValue": 593655.25,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ************
}'
2025-05-27 01:01:06:020 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYA150355451300000020' avgPrice=21165.25 quantity=1 marketPosition=Long operation=Update
2025-05-27 01:01:06:020 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:01:06:020 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:020 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:06:020 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.597Z",
  "action": "Sell",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 01:01:06:031 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:01:06:031 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:01:06:032 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='TDYA150355451300000020' currentQuantity=0 signalName='Close'
2025-05-27 01:01:06:032 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='TDYA150355451300000020'
2025-05-27 01:01:06:324 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0393
}'
2025-05-27 01:01:06:324 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0393,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:06.216Z",
  "commandType": "Cancel",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:06:324 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0394,
  "commandId": ********0393,
  "timestamp": "2025-05-27T05:01:06.216Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:01:06:325 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0393,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:06.216Z",
  "commandType": "Cancel",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:06:325 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:06:325 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21201.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:06:327 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0395,
  "commandId": ********0369,
  "timestamp": "2025-05-27T05:01:06.217Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:01:06:327 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0369,
  "orderId": ************,
  "timestamp": "2025-05-27T04:52:16.798Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:06:327 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0394,
  "commandId": ********0393,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:06.216Z",
  "orderId": ************,
  "execType": "Canceled",
  "ordStatus": "Canceled",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:01:06:327 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T04:16:59.617Z",
  "action": "Sell",
  "ordStatus": "Canceled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 01:01:06:351 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:01:06:506 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:01:38:423 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:01:38' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21170.5 quantityChanged=1
2025-05-27 01:01:38:423 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21161.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:01:38' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:01:38:428 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:01:38' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21170.5
2025-05-27 01:01:38:429 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:01:38:430 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21161.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:01:38' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:38:623 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0396
}'
2025-05-27 01:01:38:623 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0396,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:38.528Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:38:623 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21170.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:01:38' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:38:623 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0396,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21170.5,
  "timeInForce": "GTC"
}'
2025-05-27 01:01:38:623 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0397,
  "commandId": ********0396,
  "timestamp": "2025-05-27T05:01:38.529Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:01:38:623 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0396,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:38.528Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:38:624 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0398,
  "commandId": ********0396,
  "timestamp": "2025-05-27T05:01:38.530Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:01:38:624 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21170.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:01:38' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:38:624 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21170.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:01:38' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:38:624 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0396,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:38.528Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:38:624 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0399,
  "commandId": ************,
  "timestamp": "2025-05-27T05:01:38.530Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:01:38:624 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T04:16:17.181Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:38:624 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0398,
  "commandId": ********0396,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:38.529Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.***************"
}'
2025-05-27 01:01:55:168 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:01:55' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21191.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 01:01:55:168 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21177 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:55' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:01:55:174 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:01:55' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21191.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 01:01:55:175 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:01:55:175 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21177 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:01:55' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:01:55:368 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0400
}'
2025-05-27 01:01:55:368 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0400,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:55.272Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:55:368 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21191.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:55' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:55:368 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0400,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21191.5,
  "timeInForce": "GTC"
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0401,
  "commandId": ********0400,
  "timestamp": "2025-05-27T05:01:55.274Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0400,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:55.272Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0402,
  "commandId": ********0400,
  "timestamp": "2025-05-27T05:01:55.274Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:01:55:369 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21191.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:55' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:55:369 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21191.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:01:55' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0400,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:55.272Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0403,
  "commandId": ************,
  "timestamp": "2025-05-27T05:01:55.275Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-27T05:00:51.962Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:01:55:369 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0402,
  "commandId": ********0400,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:01:55.274Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 01:02:01:619 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:02:01:619 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:02:14:010 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21170.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:02:14' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 stopPriceChanged=21167.25 quantityChanged=1
2025-05-27 01:02:14:010 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21170.5 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:02:14' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:02:14:016 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21170.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:02:14' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21167.25
2025-05-27 01:02:14:016 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:02:14:016 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Stop Market' limitPrice=0 stopPrice=21170.5 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42017 time='2025-05-27 01:02:14' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:02:14:208 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0404
}'
2025-05-27 01:02:14:208 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0404,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:14.114Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:14:209 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21167.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:02:14' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:02:14:209 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0404,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21167.25,
  "timeInForce": "GTC"
}'
2025-05-27 01:02:14:209 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0405,
  "commandId": ********0404,
  "timestamp": "2025-05-27T05:02:14.116Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:02:14:209 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0404,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:14.114Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:14:209 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0406,
  "commandId": ********0404,
  "timestamp": "2025-05-27T05:02:14.116Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:02:14:209 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21167.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:02:14' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:02:14:209 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21167.25 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-27 01:02:14' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:02:14:210 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0404,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:14.114Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:14:211 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0407,
  "commandId": ********0396,
  "timestamp": "2025-05-27T05:02:14.116Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:02:14:212 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0396,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:38.528Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:14:212 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0406,
  "commandId": ********0404,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:02:14.116Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:02:26:084 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21191.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:02:26' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21290.25 stopPriceChanged=0 quantityChanged=1
2025-05-27 01:02:26:084 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21191.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:02:26' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:02:26:090 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21191.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:02:26' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21290.25 quantityChanged=1 stopPriceChanged=0
2025-05-27 01:02:26:090 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:02:26:090 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21191.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:02:26' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0408
}'
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0408,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:26.293Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:26:403 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21290.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:02:26' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0408,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21290.25,
  "timeInForce": "GTC"
}'
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0409,
  "commandId": ********0408,
  "timestamp": "2025-05-27T05:02:26.295Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0408,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:26.293Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:26:403 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0410,
  "commandId": ********0408,
  "timestamp": "2025-05-27T05:02:26.295Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:02:26:407 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21290.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:02:26' statementDate='1800-01-01' error=NoError comment='' nr=26
2025-05-27 01:02:26:407 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21290.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:02:26' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:02:26:407 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0408,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:26.293Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:26:407 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0411,
  "commandId": ********0400,
  "timestamp": "2025-05-27T05:02:26.295Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:02:26:407 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0400,
  "orderId": ************,
  "timestamp": "2025-05-27T05:01:55.272Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:02:26:407 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0410,
  "commandId": ********0408,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:02:26.295Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "1.****************"
}'
2025-05-27 01:02:57:808 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:02:57:808 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:03:05:895 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21290.25 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:03:05' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21291.5 stopPriceChanged=0 quantityChanged=1
2025-05-27 01:03:05:895 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell limitPrice=21290.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:03:05' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:03:05:902 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21290.25 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:03:05' gtd='2099-12-01' statementDate='2025-05-27' limitPriceChanged=21291.5 quantityChanged=1 stopPriceChanged=0
2025-05-27 01:03:05:903 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-27 01:03:05:903 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangePending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21290.25 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:03:05' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:03:06:096 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0412
}'
2025-05-27 01:03:06:096 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0412,
  "orderId": ************,
  "timestamp": "2025-05-27T05:03:06.002Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:03:06:096 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:03:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:03:06:096 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ********0412,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21291.5,
  "timeInForce": "GTC"
}'
2025-05-27 01:03:06:096 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0413,
  "commandId": ********0412,
  "timestamp": "2025-05-27T05:03:06.003Z",
  "commandStatus": "RiskPassed"
}'
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0412,
  "orderId": ************,
  "timestamp": "2025-05-27T05:03:06.002Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0414,
  "commandId": ********0412,
  "timestamp": "2025-05-27T05:03:06.003Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:03:06:097 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:03:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:03:06:097 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:03:06' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0412,
  "orderId": ************,
  "timestamp": "2025-05-27T05:03:06.002Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0415,
  "commandId": ********0408,
  "timestamp": "2025-05-27T05:03:06.003Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0408,
  "orderId": ************,
  "timestamp": "2025-05-27T05:02:26.293Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:03:06:097 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0414,
  "commandId": ********0412,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:03:06.003Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:03:31:678 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:03:31:678 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:04:16:686 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0416,
  "commandId": ********0404,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:04:16.590Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Sell",
  "cumQty": 1,
  "avgPx": 21166.75,
  "lastQty": 1,
  "lastPx": 21166.75,
  "externalClOrdId": "0.****************"
}'
2025-05-27 01:04:16:686 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ********0416,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:04:16.590Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "action": "Sell",
  "qty": 1,
  "price": 21166.75,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-27 01:04:16:686 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYA150355451300000020' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=21167.25 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=21166.75 time='2025-05-27 01:04:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:04:16:699 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21291.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:04:16' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:04:16:699 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21291.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:04:16' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:04:16:699 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:04:16' statementDate='2025-05-27' error=NoError comment='' nr=-1
2025-05-27 01:04:16:699 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21291.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:04:16' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:04:16:699 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYA150355451300000020' instrument='NQ JUN25' exchange=Globex price=21166.75 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-27 01:04:16' statementDate='1800-01-01'
2025-05-27 01:04:16:700 (TRADIFY) Cbi.Account.OnAddTrade: entryId='************_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=30
2025-05-27 01:04:16:700 (TRADIFY) Tradovate.Adapter.Cancel0: count=1
2025-05-27 01:04:16:700 (TRADIFY) Tradovate.Adapter.Cancel1: orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Limit' limitPrice=21291.5 stopPrice=0 quantity=1 tif=Gtc oco='2083bd1b01c1485f92b36bcb5a2bf76b' filled=0 averageFillPrice=0 onBehalfOf='' id=42018 time='2025-05-27 01:04:16' gtd='2099-12-01' statementDate='2025-05-27'
2025-05-27 01:04:16:704 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:04:16:704 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:704 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:705 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:706 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ********0422,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:04:16.590Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 27
  },
  "netPos": 0,
  "bought": 29,
  "boughtValue": 614613.5,
  "sold": 29,
  "soldValue": 614822.0,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ********0416
}'
2025-05-27 01:04:16:706 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYA150355451300000020' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-27 01:04:16:707 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=CashValue currency=UsDollar value=*****
2025-05-27 01:04:16:707 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:707 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:16:708 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T03:58:04.797Z",
  "action": "Sell",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 01:04:16:724 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:04:16:725 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:04:16:725 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='TDYA150355451300000020' currentQuantity=0 signalName='Close'
2025-05-27 01:04:16:725 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='TDYA150355451300000020'
2025-05-27 01:04:16:899 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ********0424
}'
2025-05-27 01:04:16:899 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0424,
  "orderId": ************,
  "timestamp": "2025-05-27T05:04:16.804Z",
  "commandType": "Cancel",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:04:16:899 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0425,
  "commandId": ********0424,
  "timestamp": "2025-05-27T05:04:16.804Z",
  "commandStatus": "AtExecution"
}'
2025-05-27 01:04:16:899 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0424,
  "orderId": ************,
  "timestamp": "2025-05-27T05:04:16.804Z",
  "commandType": "Cancel",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:04:16:899 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='************' account='TDYA150355451300000020' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:04:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:04:16:900 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='************' account='TDYA150355451300000020' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21291.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-27 01:04:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-27 01:04:16:900 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ********0426,
  "commandId": ********0412,
  "timestamp": "2025-05-27T05:04:16.804Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-27 01:04:16:900 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ********0412,
  "orderId": ************,
  "timestamp": "2025-05-27T05:03:06.002Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-27 01:04:16:900 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ********0425,
  "commandId": ********0424,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T05:04:16.804Z",
  "orderId": ************,
  "execType": "Canceled",
  "ordStatus": "Canceled",
  "action": "Sell",
  "externalClOrdId": "0.*****************"
}'
2025-05-27 01:04:16:900 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-27T03:58:04.801Z",
  "action": "Sell",
  "ordStatus": "Canceled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-27 01:04:16:910 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='TDYA150355451300000020' instrument='NQ JUN25' id='*********'
2025-05-27 01:04:17:495 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000020' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-27 01:04:40:270 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:04:40:270 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:04:47:160 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:04:47:160 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:06:10:723 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:06:10:723 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:06:22:310 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:06:22:310 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='27/05/2025 12:00:00 AM' to='28/05/2025 12:00:00 AM' period='Daily'
2025-05-27 01:11:04:170 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage exception='The remote party closed the WebSocket connection without completing the close handshake. ' webSocketErrorCode='ConnectionClosedPrematurely'
2025-05-27 01:11:04:170 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='The remote party closed the WebSocket connection without completing the close handshake.' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-27 01:11:04:170 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 343
2025-05-27 01:11:04:171 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-27 01:11:04:175 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-27 01:11:04:175 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-27 01:11:04:176 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-27 01:11:04:177 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-27 01:11:04:177 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-27 01:11:04:683 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-27 01:11:05:197 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-27 01:11:05:198 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-27 01:11:05:198 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-27 01:11:05:198 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-27 01:11:05:422 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage exception='The remote party closed the WebSocket connection without completing the close handshake. ' webSocketErrorCode='ConnectionClosedPrematurely'
2025-05-27 01:11:05:422 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='The remote party closed the WebSocket connection without completing the close handshake.' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-27 01:11:05:422 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 1,596
2025-05-27 01:11:05:424 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-27 01:11:05:424 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-27 01:11:05:424 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-27 01:11:05:424 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-27 01:11:05:424 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-27 01:11:05:424 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
