﻿<?xml version="1.0" encoding="utf-8"?>
<StrategyTemplate>
  <StrategyType>NinjaTrader.NinjaScript.Strategies.IFVG</StrategyType>
  <OptimizerType>NinjaTrader.NinjaScript.Optimizers.DefaultOptimizer</OptimizerType>
  <OptimizerParameters>
    <ArrayOfParameterWrapper xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <ParameterWrapper>
        <DisplayName>IsStrategyGenerator</DisplayName>
        <Name>IsStrategyGenerator</Name>
        <Value xsi:type="xsd:boolean">false</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Keep best # results</DisplayName>
        <Name>KeepBestResults</Name>
        <Value xsi:type="xsd:int">10</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>LogTypeName</DisplayName>
        <Name>LogTypeName</Name>
        <Value xsi:type="xsd:string">Optimizer</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Visible</DisplayName>
        <Name>IsVisible</Name>
        <Value xsi:type="xsd:boolean">true</Value>
      </ParameterWrapper>
    </ArrayOfParameterWrapper>
  </OptimizerParameters>
  <OptimizationFitness>NinjaTrader.NinjaScript.OptimizationFitnesses.MaxProfitFactor</OptimizationFitness>
  <OptimizationParameters>
    <ArrayOfParameter xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>Backtest</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>LogIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>MaxLossIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">500</Max>
        <Min xsi:type="xsd:double">500</Min>
        <Name>MaxSessionLoss</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>500</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>MaxWinIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">500</Max>
        <Min xsi:type="xsd:double">500</Min>
        <Name>MaxSessionProfit</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>500</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">1</Max>
        <Min xsi:type="xsd:int">1</Min>
        <Name>PosSize1</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">2</Max>
        <Min xsi:type="xsd:int">2</Min>
        <Name>PosSize2</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>2</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">3</Max>
        <Min xsi:type="xsd:int">3</Min>
        <Name>PosSize3</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>3</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Manual</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>MyPositionSizingMode</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.Strategies.IFVG+PositionSizingMode, IFVGBot, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Manual</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">500</Max>
        <Min xsi:type="xsd:double">500</Min>
        <Name>MaxRisk</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>500</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>TradeWindow1IsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">10</Max>
        <Min xsi:type="xsd:int">10</Min>
        <Name>MinGapSizeTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>10</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">10</Max>
        <Min xsi:type="xsd:int">10</Min>
        <Name>MaxBarsWait</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>10</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">2</Max>
        <Min xsi:type="xsd:double">2</Min>
        <Name>ProfitTarget1Ratio</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>2</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">3</Max>
        <Min xsi:type="xsd:double">3</Min>
        <Name>ProfitTarget2Ratio</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>3</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">4</Max>
        <Min xsi:type="xsd:double">4</Min>
        <Name>ProfitTarget3Ratio</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>4</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>StopOffsetTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>BreakEvenIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Target1Hit</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>MyBreakEvenStartType</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.Strategies.IFVG+BreakEvenStartType, IFVGBot, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Target1Hit</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:double">1</Max>
        <Min xsi:type="xsd:double">1</Min>
        <Name>BreakEvenOffset</Name>
        <ParameterTypeSerializable>System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>1</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:boolean">false</Max>
        <Min xsi:type="xsd:boolean">false</Min>
        <Name>TrailIsOn</Name>
        <ParameterTypeSerializable>System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>False</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable>
          <string>Target1Hit</string>
        </EnumValuesSerializable>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name>MyTrailStartType</Name>
        <ParameterTypeSerializable>NinjaTrader.NinjaScript.Strategies.IFVG+TrailStartType, IFVGBot, Version=*******, Culture=neutral, PublicKeyToken=null</ParameterTypeSerializable>
        <ValueSerializable>Target1Hit</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">30</Max>
        <Min xsi:type="xsd:int">30</Min>
        <Name>TrailByTicks</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>30</ValueSerializable>
      </Parameter>
      <Parameter>
        <EnumValuesSerializable />
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">5</Max>
        <Min xsi:type="xsd:int">5</Min>
        <Name>TrailFrequency</Name>
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>5</ValueSerializable>
      </Parameter>
    </ArrayOfParameter>
  </OptimizationParameters>
  <Strategy>
    <IFVG xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2025-02-26T03:34:16</From>
      <Panel>-1</Panel>
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>true</ShowTransparentPlotsInDataBox>
      <To>2025-03-21T03:34:16</To>
      <Calculate>OnBarClose</Calculate>
      <Displacement>0</Displacement>
      <IsAutoScale>true</IsAutoScale>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>IFVG Bot - Vincere Trading V1.5</Name>
      <Plots />
      <SelectedValueSeries>0</SelectedValueSeries>
      <BarsPeriodParameter>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name />
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </BarsPeriodParameter>
      <BarsRequiredToTrade>0</BarsRequiredToTrade>
      <Category>NinjaScript</Category>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <DaysToLoad>5</DaysToLoad>
      <DefaultQuantity>1</DefaultQuantity>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <EntriesPerDirection>1</EntriesPerDirection>
      <EntryHandling>AllEntries</EntryHandling>
      <ExitOnSessionCloseSeconds>30</ExitOnSessionCloseSeconds>
      <IncludeCommission>false</IncludeCommission>
      <InstrumentOrInstrumentList>MNQ JUN25</InstrumentOrInstrumentList>
      <IsAggregated>false</IsAggregated>
      <IsExitOnSessionCloseStrategy>false</IsExitOnSessionCloseStrategy>
      <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
      <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <IsWaitUntilFlat>false</IsWaitUntilFlat>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <OptimizationPeriod>10</OptimizationPeriod>
      <OrderFillResolution>Standard</OrderFillResolution>
      <OrderFillResolutionType>Minute</OrderFillResolutionType>
      <OrderFillResolutionValue>1</OrderFillResolutionValue>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
      <SetOrderQuantity>Strategy</SetOrderQuantity>
      <Slippage>0</Slippage>
      <StartBehavior>WaitUntilFlat</StartBehavior>
      <StopTargetHandling>PerEntryExecution</StopTargetHandling>
      <SupportsOptimizationGraph>true</SupportsOptimizationGraph>
      <TestPeriod>28</TestPeriod>
      <TradingHoursSerializable />
      <Gtd>1800-01-01T00:00:00</Gtd>
      <Template />
      <TimeInForce>Gtc</TimeInForce>
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ZOrder>-**********</ZOrder>
      <LicenseKey>V-A78583-88786BD0-1B5375W</LicenseKey>
      <Backtest>false</Backtest>
      <LogIsOn>true</LogIsOn>
      <PlotStops>true</PlotStops>
      <MaxLossIsOn>false</MaxLossIsOn>
      <MaxSessionLoss>500</MaxSessionLoss>
      <MaxWinIsOn>false</MaxWinIsOn>
      <MaxSessionProfit>500</MaxSessionProfit>
      <PosSize1>8</PosSize1>
      <PosSize2>3</PosSize2>
      <PosSize3>4</PosSize3>
      <MyPositionSizingMode>MaxRisk</MyPositionSizingMode>
      <MaxRisk>1000</MaxRisk>
      <TradeWindow1IsOn>true</TradeWindow1IsOn>
      <TradeStart1>2020-01-01T00:00:00</TradeStart1>
      <TradeEnd1>2020-01-01T12:00:00</TradeEnd1>
      <MinGapSizeTicks>15</MinGapSizeTicks>
      <MaxBarsWait>10</MaxBarsWait>
      <MondayFilter>false</MondayFilter>
      <TuesdayFilter>false</TuesdayFilter>
      <WednesdayFilter>false</WednesdayFilter>
      <ThursdayFilter>false</ThursdayFilter>
      <FridayFilter>false</FridayFilter>
      <SaturdayFilter>true</SaturdayFilter>
      <SundayFilter>true</SundayFilter>
      <ProfitTarget1Ratio>0.5</ProfitTarget1Ratio>
      <ProfitTarget2Ratio>2</ProfitTarget2Ratio>
      <ProfitTarget3Ratio>3</ProfitTarget3Ratio>
      <StopOffsetTicks>40</StopOffsetTicks>
      <BreakEvenIsOn>true</BreakEvenIsOn>
      <MyBreakEvenStartType>Target1Hit</MyBreakEvenStartType>
      <BreakEvenOffset>1</BreakEvenOffset>
      <TrailIsOn>false</TrailIsOn>
      <MyTrailStartType>Target2Hit</MyTrailStartType>
      <TrailByTicks>100</TrailByTicks>
      <TrailFrequency>25</TrailFrequency>
    </IFVG>
  </Strategy>
</StrategyTemplate>