﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTTabPage>
    <InstrumentLink>0</InstrumentLink>
    <IntervalLink>0</IntervalLink>
    <ChartTraderVisibility>Visible</ChartTraderVisibility>
    <SeriesCount>1</SeriesCount>
    <DataSeries>
      <BarsProperties>
        <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <BarsPeriod>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriod>
          <RangeType>Days</RangeType>
          <BarsBack>50</BarsBack>
          <DaysBack>5</DaysBack>
          <From>2025-05-22T00:00:00</From>
          <IsStableSession>true</IsStableSession>
          <To>2099-12-01T00:00:00</To>
          <TradingHoursSerializable />
          <AutoScale>true</AutoScale>
          <CenterPriceOnScale>false</CenterPriceOnScale>
          <DisplayInDataBox>true</DisplayInDataBox>
          <Label>NQ JUN25</Label>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <Panel>0</Panel>
          <PriceMarker>
            <BackgroundSerialize>DEFAULT</BackgroundSerialize>
            <IsVisible>true</IsVisible>
          </PriceMarker>
          <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
          <ScaleJustification>Right</ScaleJustification>
          <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
          <TradingHoursBreakPenSerialize>&lt;Pen Brush="#FF2D2D2F" Thickness="1" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
          <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
          <PlotExecutions>TextAndMarker</PlotExecutions>
          <MarkerSize>5</MarkerSize>
          <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
          <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
          <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
          <BarsSeriesId>a60ea43ca22a4bdc9f69d7edb48ba83d</BarsSeriesId>
          <Id>a60ea43ca22a4bdc9f69d7edb48ba83d</Id>
          <Instrument>NQ JUN25</Instrument>
          <IsLinked>true</IsLinked>
          <IsPrimarySeries>true</IsPrimarySeries>
          <ZOrder>1</ZOrder>
        </BarsProperties>
        <ChartStyles>
          <ChartStyle>
            <CandleStyle>
              <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <BarWidth>2.3555092757935183</BarWidth>
                <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                <DownBrushSerialize>DEFAULT</DownBrushSerialize>
                <UpBrushSerialize>DEFAULT</UpBrushSerialize>
                <StrokeSerialize>DEFAULT</StrokeSerialize>
                <Stroke2Serialize>DEFAULT</Stroke2Serialize>
              </CandleStyle>
            </CandleStyle>
          </ChartStyle>
        </ChartStyles>
      </BarsProperties>
    </DataSeries>
    <Indicators>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1" DisplayName="Price line(100,100,100,False,False,True)">
        <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-05-22T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-27T00:00:00</To>
          <Calculate>OnPriceChange</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>Price line</Name>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Ask line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Bid line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>1</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>Last line</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>2</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10005</ZOrder>
          <ShowAskLine>false</ShowAskLine>
          <ShowBidLine>false</ShowBidLine>
          <ShowLastLine>true</ShowLastLine>
          <AskLineLength>100</AskLineLength>
          <BidLineLength>100</BidLineLength>
          <LastLineLength>100</LastLineLength>
          <AskStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </AskStroke>
          <BidStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </BidStroke>
          <LastStroke>
            <IsOpacityVisible>true</IsOpacityVisible>
            <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
            <DashStyleHelper>Dash</DashStyleHelper>
            <Opacity>100</Opacity>
            <Width>1</Width>
          </LastStroke>
        </PriceLine>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1" DisplayName="Bar timer">
        <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>false</DisplayInDataBox>
          <From>2025-05-22T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-27T00:00:00</To>
          <Calculate>OnEachTick</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>Bar timer</Name>
          <Plots />
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>false</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>5</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10006</ZOrder>
        </BarTimer>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.TradeSaber.MultiSeriesHL" Panel="-1" DisplayName="MultiSeriesHL(TradeSaber(Dre),False,#FF8B0000,Dash,True,False,2,False,#FFFF6347,Dash,True,False,2,https://discord.gg/2YU9GDme8j,False,False,#FF0000FF,Dash,True,False,2,#FFFF0000,Dash,True,False,2,True,#FF32CD32,Dash,True,False,2,False,#FFFFC0CB,Dash,True,False,2,False,#FFFFA500,Dash,True,False,2,True,#FF800080,Dash,True,False,2,True,#FFFFFFFF,Dash,True,False,2,False,#FFDAA520,Dash,True,False,2,True,Monday,https://tradesaber.com/,Version 1.2.0 // January 2023,https://youtu.be/krEhUfAxW8Y)">
        <MultiSeriesHL xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>false</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-05-22T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-27T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>MultiSeriesHL</Name>
          <Plots />
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>8</IndicatorId>
          <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10007</ZOrder>
          <min1>false</min1>
          <min5>false</min5>
          <min15>false</min15>
          <min30>false</min30>
          <min60>true</min60>
          <min240>true</min240>
          <minDaily>true</minDaily>
          <currentDaily>false</currentDaily>
          <minWeekly>false</minWeekly>
          <currentWeekly>false</currentWeekly>
          <min1ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</min1ColorSerializable>
          <min1Dash>Dash</min1Dash>
          <min1Width>2</min1Width>
          <min1ShowMedian>false</min1ShowMedian>
          <min1ShowLabel>true</min1ShowLabel>
          <min5ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFA500&lt;/SolidColorBrush&gt;</min5ColorSerializable>
          <min5Dash>Dash</min5Dash>
          <min5Width>2</min5Width>
          <min5ShowMedian>false</min5ShowMedian>
          <min5ShowLabel>true</min5ShowLabel>
          <min15ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</min15ColorSerializable>
          <min15Dash>Dash</min15Dash>
          <min15Width>2</min15Width>
          <min15ShowMedian>false</min15ShowMedian>
          <min15ShowLabel>true</min15ShowLabel>
          <min30ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFC0CB&lt;/SolidColorBrush&gt;</min30ColorSerializable>
          <min30Dash>Dash</min30Dash>
          <min30Width>2</min30Width>
          <min30ShowMedian>false</min30ShowMedian>
          <min30ShowLabel>true</min30ShowLabel>
          <min60ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF800080&lt;/SolidColorBrush&gt;</min60ColorSerializable>
          <min60Dash>Dash</min60Dash>
          <min60Width>2</min60Width>
          <min60ShowMedian>false</min60ShowMedian>
          <min60ShowLabel>true</min60ShowLabel>
          <min240ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</min240ColorSerializable>
          <min240Dash>Dash</min240Dash>
          <min240Width>2</min240Width>
          <min240ShowMedian>false</min240ShowMedian>
          <min240ShowLabel>true</min240ShowLabel>
          <minDailyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFFFF&lt;/SolidColorBrush&gt;</minDailyColorSerializable>
          <minDailyDash>Dash</minDailyDash>
          <minDailyWidth>2</minDailyWidth>
          <minDailyShowMedian>false</minDailyShowMedian>
          <minDailyShowLabel>true</minDailyShowLabel>
          <currentDailyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF8B0000&lt;/SolidColorBrush&gt;</currentDailyColorSerializable>
          <currentDailyDash>Dash</currentDailyDash>
          <currentDailyWidth>2</currentDailyWidth>
          <currentDailyShowMedian>false</currentDailyShowMedian>
          <currentDailyShowLabel>true</currentDailyShowLabel>
          <minWeeklyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDAA520&lt;/SolidColorBrush&gt;</minWeeklyColorSerializable>
          <minWeeklyDash>Dash</minWeeklyDash>
          <minWeeklyWidth>2</minWeeklyWidth>
          <minWeeklyShowMedian>false</minWeeklyShowMedian>
          <minWeeklyShowLabel>true</minWeeklyShowLabel>
          <currentWeeklyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF6347&lt;/SolidColorBrush&gt;</currentWeeklyColorSerializable>
          <currentWeeklyDash>Dash</currentWeeklyDash>
          <currentWeeklyWidth>2</currentWeeklyWidth>
          <currentWeeklyShowMedian>false</currentWeeklyShowMedian>
          <currentWeeklyShowLabel>true</currentWeeklyShowLabel>
          <StartWeekFromDay>Monday</StartWeekFromDay>
          <ShowSocials>true</ShowSocials>
          <Youtube>https://youtu.be/krEhUfAxW8Y</Youtube>
          <Discord>https://discord.gg/2YU9GDme8j</Discord>
          <TradeSaber>https://tradesaber.com/</TradeSaber>
          <Author>TradeSaber(Dre)</Author>
          <Version>Version 1.2.0 // January 2023</Version>
        </MultiSeriesHL>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1" DisplayName="SMA(20)">
        <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-05-22T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-27T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>SMA</Name>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>SMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>174</IndicatorId>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10008</ZOrder>
          <Period>20</Period>
        </SMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
      <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1" DisplayName="SMA(50)">
        <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <IsVisible>true</IsVisible>
          <AreLinesConfigurable>true</AreLinesConfigurable>
          <ArePlotsConfigurable>true</ArePlotsConfigurable>
          <BarsPeriodSerializable>
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriodSerializable>
          <BarsToLoad>0</BarsToLoad>
          <DisplayInDataBox>true</DisplayInDataBox>
          <From>2025-05-22T00:00:00</From>
          <Panel>-1</Panel>
          <ScaleJustification>Right</ScaleJustification>
          <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
          <To>2025-05-27T00:00:00</To>
          <Calculate>OnBarClose</Calculate>
          <Displacement>0</Displacement>
          <IsAutoScale>true</IsAutoScale>
          <IsDataSeriesRequired>true</IsDataSeriesRequired>
          <IsOverlay>true</IsOverlay>
          <Lines />
          <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
          <Name>SMA</Name>
          <Plots>
            <Plot>
              <IsOpacityVisible>false</IsOpacityVisible>
              <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF1E90FF&lt;/SolidColorBrush&gt;</BrushSerialize>
              <DashStyleHelper>Solid</DashStyleHelper>
              <Opacity>100</Opacity>
              <Width>2</Width>
              <AutoWidth>false</AutoWidth>
              <Max>1.7976931348623157E+308</Max>
              <Min>-1.7976931348623157E+308</Min>
              <Name>SMA</Name>
              <PlotStyle>Line</PlotStyle>
            </Plot>
          </Plots>
          <SelectedValueSeries>0</SelectedValueSeries>
          <InputPlot>0</InputPlot>
          <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
          <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
          <DrawVerticalGridLines>true</DrawVerticalGridLines>
          <DrawOnPricePanel>true</DrawOnPricePanel>
          <PaintPriceMarkers>true</PaintPriceMarkers>
          <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
          <IndicatorId>175</IndicatorId>
          <MaxSerialized>0</MaxSerialized>
          <MinSerialized>0</MinSerialized>
          <ZOrder>10009</ZOrder>
          <Period>50</Period>
        </SMA>
        <Input>
          <PriceType>Close</PriceType>
        </Input>
      </Indicator>
    </Indicators>
    <CrosshairType>Local</CrosshairType>
    <StayInDrawMode>False</StayInDrawMode>
    <Properties>
      <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AlwaysOnTop>false</AlwaysOnTop>
        <AreTabsVisible>true</AreTabsVisible>
        <AllowSelectionDragging>true</AllowSelectionDragging>
        <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
        <EquidistantBarSpacing>true</EquidistantBarSpacing>
        <LabelFont>
          <Bold>false</Bold>
          <FamilySerialize>#Montserrat</FamilySerialize>
          <Italic>false</Italic>
          <Size>11</Size>
        </LabelFont>
        <BarDistance>7.85169744</BarDistance>
        <BarMarginRightUser>8</BarMarginRightUser>
        <ChartTraderVisibility>Visible</ChartTraderVisibility>
        <ShowDateRange>false</ShowDateRange>
        <ShowScrollBar>true</ShowScrollBar>
        <SnapMode>Bar</SnapMode>
        <TabName>@INSTRUMENT_FULL</TabName>
        <LoadBackgroundImage>false</LoadBackgroundImage>
        <BackgroundImageStretch>Fill</BackgroundImageStretch>
        <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
        <ChartTextSerialize>DEFAULT</ChartTextSerialize>
        <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
        <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
        <AreHGridLinesVisible>true</AreHGridLinesVisible>
        <AreVGridLinesVisible>true</AreVGridLinesVisible>
        <AxisPenSerialize>DEFAULT</AxisPenSerialize>
        <CrosshairPen>DEFAULT</CrosshairPen>
        <CrosshairIsLocked>false</CrosshairIsLocked>
        <CrosshairCrosshairType>Local</CrosshairCrosshairType>
        <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
        <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
        <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
        <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
      </ChartControlProperties>
    </Properties>
    <ChartPanels>
      <ChartPanel>
        <Height>880</Height>
        <HoldChartTraderOrders>false</HoldChartTraderOrders>
        <IsMaximized>false</IsMaximized>
        <Right>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Fixed</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>21447.153557024787</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>21176.346442975213</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Right>
        <Left>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Left>
        <Overlay>
          <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <YAxisRangeType>Automatic</YAxisRangeType>
            <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
            <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
            <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
            <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
            <AutoScaleMarginType>Percent</AutoScaleMarginType>
            <AutoScaleMarginLower>6</AutoScaleMarginLower>
            <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
            <YAxisScalingType>Linear</YAxisScalingType>
            <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
            <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
          </ChartScaleProperties>
        </Overlay>
      </ChartPanel>
    </ChartPanels>
    <ChartAlerts />
  </NTTabPage>
  <ChartTrader>
    <Properties>
      <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
        <AutoScale>true</AutoScale>
        <OrderDisplayBarLength>25</OrderDisplayBarLength>
        <PnLDisplayUnit>Points</PnLDisplayUnit>
        <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
        <ScaleQuantity>0</ScaleQuantity>
        <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
        <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
        <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
        <StopLimitOffsetValue>0</StopLimitOffsetValue>
        <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
        <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
        <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
        <LimitSerialize>&lt;Pen Brush="#FF00FFFF" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
        <MitSerialize>&lt;Pen Brush="#FF00FF7F" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
        <PositionEntryPriceSerialize>&lt;Pen Brush="#FFDEB887" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
        <ProfitTargetSerialize>&lt;Pen Brush="#FF32CD32" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
        <StopLimitSerialize>&lt;Pen Brush="#FFEE82EE" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
        <StopLossSerialize>&lt;Pen Brush="#FFFF0000" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
        <StopMarketSerialize>&lt;Pen Brush="#FFFFC0CB" Thickness="2" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
      </ChartTraderProperties>
    </Properties>
    <Account>TDYG150355451300000018</Account>
    <ATM>NQ</ATM>
    <Instrument>NQ JUN25</Instrument>
    <Quantity>3</Quantity>
    <TIF>Gtc</TIF>
  </ChartTrader>
</NinjaTrader>