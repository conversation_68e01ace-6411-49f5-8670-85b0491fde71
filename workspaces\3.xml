﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <NTWindows>
    <Chart-b2cc3f0909d14b6a951333d003703721>
      <Class Assembly="NinjaTrader.Gui">NinjaTrader.Gui.Chart.Chart</Class>
      <WindowState>Maximized</WindowState>
      <Location>130;130</Location>
      <Size>878;705</Size>
      <ZOrder>0</ZOrder>
      <Topmost>False</Topmost>
      <TraderWidth>166</TraderWidth>
      <TabControl>
        <SelectedIndex>0</SelectedIndex>
        <Tab-1fbca39fd9704ecca7f5866473365edd>
          <InstrumentLink>0</InstrumentLink>
          <IntervalLink>0</IntervalLink>
          <DataSeries>
            <BarsProperties>
              <BarsProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <BarsPeriod>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriod>
                <RangeType>Days</RangeType>
                <BarsBack>50</BarsBack>
                <DaysBack>5</DaysBack>
                <From>2025-05-25T00:00:00</From>
                <IsStableSession>true</IsStableSession>
                <IsTickReplay>false</IsTickReplay>
                <To>2025-03-18T00:00:00</To>
                <TradingHoursSerializable />
                <AutoScale>true</AutoScale>
                <CenterPriceOnScale>false</CenterPriceOnScale>
                <DisplayInDataBox>true</DisplayInDataBox>
                <Label>NQ 06-25</Label>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <Panel>0</Panel>
                <PriceMarker>
                  <BackgroundSerialize>DEFAULT</BackgroundSerialize>
                  <IsVisible>true</IsVisible>
                </PriceMarker>
                <ShowGlobalDrawObjects>true</ShowGlobalDrawObjects>
                <ScaleJustification>Right</ScaleJustification>
                <TradingHoursVisibility>AllSessions</TradingHoursVisibility>
                <TradingHoursBreakPenSerialize>&lt;Pen Thickness="1" Brush="#FF2D2D2F" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</TradingHoursBreakPenSerialize>
                <LongExecutionBrushSerialize>DEFAULT</LongExecutionBrushSerialize>
                <PlotExecutions>TextAndMarker</PlotExecutions>
                <MarkerSize>5</MarkerSize>
                <PositionPenLoserSerialize>DEFAULT</PositionPenLoserSerialize>
                <PositionPenWinnerSerialize>DEFAULT</PositionPenWinnerSerialize>
                <ShortExecutionBrushSerialize>DEFAULT</ShortExecutionBrushSerialize>
                <BarsSeriesId>c6482ccddd2043c18fa4bcf3488637af</BarsSeriesId>
                <Id>7dcd1b1f1a474f4192d86bce4412f4fc</Id>
                <Instrument>NQ JUN25</Instrument>
                <IsLinked>true</IsLinked>
                <IsPrimarySeries>true</IsPrimarySeries>
                <ZOrder>1</ZOrder>
              </BarsProperties>
              <ChartStyles>
                <ChartStyle>
                  <CandleStyle>
                    <CandleStyle xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <IsVisible>true</IsVisible>
                      <BarWidth>1.5243171334768002</BarWidth>
                      <ChartStyleTypeSerialize>1</ChartStyleTypeSerialize>
                      <DownBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFF8F8FF&lt;/SolidColorBrush&gt;</DownBrushSerialize>
                      <UpBrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF00FF00&lt;/SolidColorBrush&gt;</UpBrushSerialize>
                      <StrokeSerialize>&lt;Pen Thickness="1" Brush="#FF000000" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StrokeSerialize>
                      <Stroke2Serialize>DEFAULT</Stroke2Serialize>
                    </CandleStyle>
                  </CandleStyle>
                </ChartStyle>
              </ChartStyles>
            </BarsProperties>
          </DataSeries>
          <Strategies>
            <Strategy0 BarsIndex="0">357445343</Strategy0>
            <Strategy1 BarsIndex="0">357445404</Strategy1>
          </Strategies>
          <Indicators>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.PriceLine" Panel="-1">
              <PriceLine xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnPriceChange</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnPriceChange</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Price line</Name>
                <Panel>-1</Panel>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Ask line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#00FFFFFF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Bid line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Last line</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>458</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10005</ZOrder>
                <ShowAskLine>false</ShowAskLine>
                <ShowBidLine>false</ShowBidLine>
                <ShowLastLine>true</ShowLastLine>
                <AskLineLength>100</AskLineLength>
                <BidLineLength>100</BidLineLength>
                <LastLineLength>100</LastLineLength>
                <AskStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF006400&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </AskStroke>
                <BidStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </BidStroke>
                <LastStroke>
                  <IsOpacityVisible>true</IsOpacityVisible>
                  <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFF00&lt;/SolidColorBrush&gt;</BrushSerialize>
                  <DashStyleHelper>Dash</DashStyleHelper>
                  <Opacity>100</Opacity>
                  <Width>1</Width>
                </LastStroke>
              </PriceLine>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BarTimer" Panel="-1">
              <BarTimer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnEachTick</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>false</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Bar timer</Name>
                <Panel>-1</Panel>
                <Plots />
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>461</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10006</ZOrder>
              </BarTimer>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.TradeSaber.MultiSeriesHL" Panel="-1">
              <MultiSeriesHL xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnBarClose</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>false</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnBarClose</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>MultiSeriesHL</Name>
                <Panel>-1</Panel>
                <Plots />
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>464</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10007</ZOrder>
                <min1>false</min1>
                <min5>false</min5>
                <min15>false</min15>
                <min30>false</min30>
                <min60>true</min60>
                <min240>true</min240>
                <minDaily>true</minDaily>
                <currentDaily>false</currentDaily>
                <minWeekly>false</minWeekly>
                <currentWeekly>false</currentWeekly>
                <min1ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF0000&lt;/SolidColorBrush&gt;</min1ColorSerializable>
                <min1Dash>Dash</min1Dash>
                <min1Width>2</min1Width>
                <min1ShowMedian>false</min1ShowMedian>
                <min1ShowLabel>true</min1ShowLabel>
                <min5ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFA500&lt;/SolidColorBrush&gt;</min5ColorSerializable>
                <min5Dash>Dash</min5Dash>
                <min5Width>2</min5Width>
                <min5ShowMedian>false</min5ShowMedian>
                <min5ShowLabel>true</min5ShowLabel>
                <min15ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF0000FF&lt;/SolidColorBrush&gt;</min15ColorSerializable>
                <min15Dash>Dash</min15Dash>
                <min15Width>2</min15Width>
                <min15ShowMedian>false</min15ShowMedian>
                <min15ShowLabel>true</min15ShowLabel>
                <min30ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFC0CB&lt;/SolidColorBrush&gt;</min30ColorSerializable>
                <min30Dash>Dash</min30Dash>
                <min30Width>2</min30Width>
                <min30ShowMedian>false</min30ShowMedian>
                <min30ShowLabel>true</min30ShowLabel>
                <min60ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF800080&lt;/SolidColorBrush&gt;</min60ColorSerializable>
                <min60Dash>Dash</min60Dash>
                <min60Width>2</min60Width>
                <min60ShowMedian>false</min60ShowMedian>
                <min60ShowLabel>true</min60ShowLabel>
                <min240ColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF32CD32&lt;/SolidColorBrush&gt;</min240ColorSerializable>
                <min240Dash>Dash</min240Dash>
                <min240Width>2</min240Width>
                <min240ShowMedian>false</min240ShowMedian>
                <min240ShowLabel>true</min240ShowLabel>
                <minDailyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFFFFFF&lt;/SolidColorBrush&gt;</minDailyColorSerializable>
                <minDailyDash>Dash</minDailyDash>
                <minDailyWidth>2</minDailyWidth>
                <minDailyShowMedian>false</minDailyShowMedian>
                <minDailyShowLabel>true</minDailyShowLabel>
                <currentDailyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF8B0000&lt;/SolidColorBrush&gt;</currentDailyColorSerializable>
                <currentDailyDash>Dash</currentDailyDash>
                <currentDailyWidth>2</currentDailyWidth>
                <currentDailyShowMedian>false</currentDailyShowMedian>
                <currentDailyShowLabel>true</currentDailyShowLabel>
                <minWeeklyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDAA520&lt;/SolidColorBrush&gt;</minWeeklyColorSerializable>
                <minWeeklyDash>Dash</minWeeklyDash>
                <minWeeklyWidth>2</minWeeklyWidth>
                <minWeeklyShowMedian>false</minWeeklyShowMedian>
                <minWeeklyShowLabel>true</minWeeklyShowLabel>
                <currentWeeklyColorSerializable>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFFF6347&lt;/SolidColorBrush&gt;</currentWeeklyColorSerializable>
                <currentWeeklyDash>Dash</currentWeeklyDash>
                <currentWeeklyWidth>2</currentWeeklyWidth>
                <currentWeeklyShowMedian>false</currentWeeklyShowMedian>
                <currentWeeklyShowLabel>true</currentWeeklyShowLabel>
                <StartWeekFromDay>Monday</StartWeekFromDay>
                <ShowSocials>false</ShowSocials>
                <Youtube>https://youtu.be/krEhUfAxW8Y</Youtube>
                <Discord>https://discord.gg/2YU9GDme8j</Discord>
                <TradeSaber>https://tradesaber.com/</TradeSaber>
                <Author>TradeSaber(Dre)</Author>
                <Version>Version 1.2.0 // January 2023</Version>
              </MultiSeriesHL>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1">
              <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnBarClose</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnBarClose</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>SMA</Name>
                <Panel>-1</Panel>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>SMA</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>467</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10008</ZOrder>
                <Period>20</Period>
              </SMA>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.SMA" Panel="-1">
              <SMA xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnBarClose</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnBarClose</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>SMA</Name>
                <Panel>-1</Panel>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF1E90FF&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>SMA</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>470</IndicatorId>
                <InstrumentDeserialized>NQ JUN25 Globex</InstrumentDeserialized>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10009</ZOrder>
                <Period>50</Period>
              </SMA>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.BuySellPressure" Panel="1">
              <BuySellPressure xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnEachTick</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines>
                  <Line>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <Name>Upper</Name>
                    <Value>75</Value>
                  </Line>
                  <Line>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF696969&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>1</Width>
                    <Name>Lower</Name>
                    <Value>25</Value>
                  </Line>
                </Lines>
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Buy sell pressure</Name>
                <Panel>1</Panel>
                <Plots>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FF008B8B&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Buy pressure</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                  <Plot>
                    <IsOpacityVisible>false</IsOpacityVisible>
                    <BrushSerialize>&lt;SolidColorBrush xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;#FFDC143C&lt;/SolidColorBrush&gt;</BrushSerialize>
                    <DashStyleHelper>Solid</DashStyleHelper>
                    <Opacity>100</Opacity>
                    <Width>2</Width>
                    <AutoWidth>false</AutoWidth>
                    <Max>1.7976931348623157E+308</Max>
                    <Min>-1.7976931348623157E+308</Min>
                    <Name>Sell pressure</Name>
                    <PlotStyle>Line</PlotStyle>
                  </Plot>
                </Plots>
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>false</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>true</DrawHorizontalGridLines>
                <DrawVerticalGridLines>true</DrawVerticalGridLines>
                <DrawOnPricePanel>false</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>885</IndicatorId>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>10004</ZOrder>
              </BuySellPressure>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
            <Indicator BarsIndex="0" Instrument="NQ JUN25 Globex" Name="NinjaTrader.NinjaScript.Indicators.Prop_Trader_Tools.PropTraderAccountTool" Panel="-1">
              <PropTraderAccountTool xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <IsVisible>true</IsVisible>
                <calculate2>OnEachTick</calculate2>
                <AreLinesConfigurable>true</AreLinesConfigurable>
                <ArePlotsConfigurable>true</ArePlotsConfigurable>
                <BarsPeriodSerializable>
                  <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
                  <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
                  <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
                  <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
                  <MarketDataType>Last</MarketDataType>
                  <PointAndFigurePriceType>Close</PointAndFigurePriceType>
                  <ReversalType>Tick</ReversalType>
                  <Value>1</Value>
                  <Value2>1</Value2>
                </BarsPeriodSerializable>
                <BarsToLoad>0</BarsToLoad>
                <Calculate>OnEachTick</Calculate>
                <Displacement>0</Displacement>
                <DisplayInDataBox>true</DisplayInDataBox>
                <From>2025-03-13T00:00:00</From>
                <IsAutoScale>true</IsAutoScale>
                <Lines />
                <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
                <Name>Prop Trader Account Tool</Name>
                <Panel>-1</Panel>
                <Plots />
                <ScaleJustification>Right</ScaleJustification>
                <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
                <To>2025-03-18T00:00:00</To>
                <IsDataSeriesRequired>true</IsDataSeriesRequired>
                <IsOverlay>true</IsOverlay>
                <SelectedValueSeries>0</SelectedValueSeries>
                <InputPlot>0</InputPlot>
                <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
                <PaintPriceMarkers>true</PaintPriceMarkers>
                <DrawHorizontalGridLines>false</DrawHorizontalGridLines>
                <DrawVerticalGridLines>false</DrawVerticalGridLines>
                <DrawOnPricePanel>true</DrawOnPricePanel>
                <ChartHashCodeDeserialized>0</ChartHashCodeDeserialized>
                <IndicatorId>1112</IndicatorId>
                <MaxSerialized>0</MaxSerialized>
                <MinSerialized>0</MinSerialized>
                <ZOrder>50</ZOrder>
                <selectedAccountName>Playback101</selectedAccountName>
                <AccountStartValue>150000</AccountStartValue>
                <Prop_DD>5000</Prop_DD>
                <Prop_AutoLiquidationValue>160680</Prop_AutoLiquidationValue>
                <ResetAccountInfo>false</ResetAccountInfo>
                <stoplossExposureWarning>0.6</stoplossExposureWarning>
                <usePredSL>64</usePredSL>
                <usePredTP>64</usePredTP>
                <_set_BE>false</_set_BE>
                <_be_ticks_offset>10</_be_ticks_offset>
                <_set_Trailing_Stop>false</_set_Trailing_Stop>
                <_tr_start>16</_tr_start>
                <_tr_offset>10</_tr_offset>
                <Show_Trailing_DD_ChartLines>true</Show_Trailing_DD_ChartLines>
                <Show_Trailing_DD_flat>true</Show_Trailing_DD_flat>
                <Line_1>10</Line_1>
                <Line_2>30</Line_2>
                <Line_3>80</Line_3>
              </PropTraderAccountTool>
              <Input>
                <PriceType>Close</PriceType>
              </Input>
            </Indicator>
          </Indicators>
          <CrosshairType>Local</CrosshairType>
          <StayInDrawMode>False</StayInDrawMode>
          <Properties>
            <ChartControlProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <AlwaysOnTop>false</AlwaysOnTop>
              <AreTabsVisible>true</AreTabsVisible>
              <AllowSelectionDragging>true</AllowSelectionDragging>
              <AreDrawingToolsHidden>false</AreDrawingToolsHidden>
              <EquidistantBarSpacing>true</EquidistantBarSpacing>
              <LabelFont>
                <Bold>false</Bold>
                <FamilySerialize>#Montserrat</FamilySerialize>
                <Italic>false</Italic>
                <Size>11</Size>
              </LabelFont>
              <BarDistance>5.081057</BarDistance>
              <BarMarginRightUser>8</BarMarginRightUser>
              <ChartTraderVisibility>Visible</ChartTraderVisibility>
              <ShowDateRange>false</ShowDateRange>
              <ShowScrollBar>true</ShowScrollBar>
              <ShowSelectedDrawingMarkers>true</ShowSelectedDrawingMarkers>
              <SnapMode>Bar</SnapMode>
              <TabName>@INSTRUMENT_FULL</TabName>
              <LoadBackgroundImage>false</LoadBackgroundImage>
              <BackgroundImageStretch>Fill</BackgroundImageStretch>
              <ChartBackgroundSerialize>DEFAULT</ChartBackgroundSerialize>
              <ChartTextSerialize>DEFAULT</ChartTextSerialize>
              <CrosshairLabelBackgroundSerialize>DEFAULT</CrosshairLabelBackgroundSerialize>
              <InactivePriceMarkersBrushSerialize>DEFAULT</InactivePriceMarkersBrushSerialize>
              <SelectedMarkerBrushSerialize>DEFAULT</SelectedMarkerBrushSerialize>
              <AreHGridLinesVisible>true</AreHGridLinesVisible>
              <AreVGridLinesVisible>true</AreVGridLinesVisible>
              <AxisPenSerialize>DEFAULT</AxisPenSerialize>
              <CrosshairPen>DEFAULT</CrosshairPen>
              <CrosshairIsLocked>false</CrosshairIsLocked>
              <CrosshairCrosshairType>Local</CrosshairCrosshairType>
              <CrosshairDrawCursorOnly>false</CrosshairDrawCursorOnly>
              <GridLineHPenSerialize>DEFAULT</GridLineHPenSerialize>
              <GridLineVPenSerialize>DEFAULT</GridLineVPenSerialize>
              <PanelSplitterPenSerialize>DEFAULT</PanelSplitterPenSerialize>
              <StartTime>2025-01-01T09:00:00</StartTime>
              <EndTime>2025-01-01T17:00:00</EndTime>
              <TimeHighBrushSerialize>DEFAULT</TimeHighBrushSerialize>
              <Opacity>25</Opacity>
              <IsVisible>false</IsVisible>
            </ChartControlProperties>
          </Properties>
          <ChartPanels>
            <ChartPanel>
              <Height>574</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>20037.5</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>19698.13560209424</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0.25</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
            <ChartPanel>
              <Height>172</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>72.399999999999991</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>27.6</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
            <ChartPanel>
              <Height>172</Height>
              <HoldChartTraderOrders>false</HoldChartTraderOrders>
              <IsMaximized>false</IsMaximized>
              <Right>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>37.75</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>-137</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Right>
              <Left>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Left>
              <Overlay>
                <ChartScaleProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                  <YAxisRangeType>Automatic</YAxisRangeType>
                  <AutoScaleDateRangeType>ScreenDateRange</AutoScaleDateRangeType>
                  <HorizontalGridlinesCalculation>Automatic</HorizontalGridlinesCalculation>
                  <HorizontalGridlinesIntervalType>Points</HorizontalGridlinesIntervalType>
                  <HorizontalGridlinesInterval>0</HorizontalGridlinesInterval>
                  <AutoScaleMarginType>Percent</AutoScaleMarginType>
                  <AutoScaleMarginLower>6</AutoScaleMarginLower>
                  <AutoScaleMarginUpper>6</AutoScaleMarginUpper>
                  <YAxisScalingType>Linear</YAxisScalingType>
                  <FixedScaleMaxSerialize>0</FixedScaleMaxSerialize>
                  <FixedScaleMinSerialize>0</FixedScaleMinSerialize>
                  <IsInverted>false</IsInverted>
                </ChartScaleProperties>
              </Overlay>
            </ChartPanel>
          </ChartPanels>
          <ChartAlerts />
          <TabName>@INSTRUMENT_FULL</TabName>
          <Type>ChartTab</Type>
        </Tab-1fbca39fd9704ecca7f5866473365edd>
      </TabControl>
      <ChartTrader>
        <Properties>
          <ChartTraderProperties xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <AtmStrategySelectionMode>SelectActiveAtmStrategyOnOrderSubmission</AtmStrategySelectionMode>
            <AutoScale>true</AutoScale>
            <OrderDisplayBarLength>25</OrderDisplayBarLength>
            <PnLDisplayUnit>Points</PnLDisplayUnit>
            <QuantityModificationForStocks>IncreaseQuantity</QuantityModificationForStocks>
            <ScaleQuantity>0</ScaleQuantity>
            <ShowRealizedPnLWhenFlat>false</ShowRealizedPnLWhenFlat>
            <SimulatedOrderVolumeTrigger>0</SimulatedOrderVolumeTrigger>
            <StopLimitOffsetEnabled>false</StopLimitOffsetEnabled>
            <StopLimitOffsetValue>0</StopLimitOffsetValue>
            <ActionButtonsBackgroundSerialize>DEFAULT</ActionButtonsBackgroundSerialize>
            <BuyButtonsBackgroundSerialize>DEFAULT</BuyButtonsBackgroundSerialize>
            <SellButtonsBackgroundSerialize>DEFAULT</SellButtonsBackgroundSerialize>
            <LimitSerialize>&lt;Pen Thickness="2" Brush="#FF00FFFF" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</LimitSerialize>
            <MitSerialize>&lt;Pen Thickness="2" Brush="#FF00FF7F" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</MitSerialize>
            <PositionEntryPriceSerialize>&lt;Pen Thickness="2" Brush="#FFDEB887" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</PositionEntryPriceSerialize>
            <ProfitTargetSerialize>&lt;Pen Thickness="2" Brush="#FF32CD32" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</ProfitTargetSerialize>
            <StopLimitSerialize>&lt;Pen Thickness="2" Brush="#FFEE82EE" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLimitSerialize>
            <StopLossSerialize>&lt;Pen Thickness="2" Brush="#FFFF0000" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopLossSerialize>
            <StopMarketSerialize>&lt;Pen Thickness="2" Brush="#FFFFC0CB" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
  &lt;Pen.DashStyle&gt;
    &lt;DashStyle /&gt;
  &lt;/Pen.DashStyle&gt;
&lt;/Pen&gt;</StopMarketSerialize>
          </ChartTraderProperties>
        </Properties>
        <Account>Playback101</Account>
        <ATM>ATM_IMPROVED</ATM>
        <Instrument>NQ JUN25</Instrument>
        <Quantity>1</Quantity>
        <TIF>Gtc</TIF>
      </ChartTrader>
    </Chart-b2cc3f0909d14b6a951333d003703721>
  </NTWindows>
</NinjaTrader>