2025-06-09 02:45:47:978|2|4|Session Break (Version 8.1.5.0)
2025-06-09 02:46:03:078|1|4|Global simulation mode disabled
2025-06-09 02:46:03:970|1|4|Vendor assembly 'AIDuplicateAccountActions' version='25.1.16.1' loaded.
2025-06-09 02:46:03:970|1|4|Vendor assembly 'IFVGBOT' version='1.0.0.5' loaded.
2025-06-09 02:46:03:970|1|4|Vendor assembly 'IGRIDPACK2CLIENT' version='1.0.0.3' loaded.
2025-06-09 02:46:03:970|1|4|Vendor assembly 'IGRIDPACK2_Telegram@Val1312q' version='6.9.1.8' loaded.
2025-06-09 02:46:03:973|1|4|Vendor assembly 'ORBBOT' version='1.0.0.4' loaded.
2025-06-09 02:46:05:548|1|2|Using IS (is-us-nt-006.ninjatrader.com/31658)
2025-06-09 02:46:05:557|1|4|Automated trading disabled
2025-06-09 02:46:05:781|1|32|Order entry hot keys disabled
2025-06-09 02:46:05:783|1|4|Auto close enabled=False
2025-06-09 02:46:09:162|1|4|Restoring workspace '3'...
2025-06-09 03:10:47:704|1|2|Playback: Primary connection=Connecting, Price feed=Connecting
2025-06-09 03:10:49:096|1|2|Playback: Primary connection=Connected, Price feed=Connected
2025-06-09 03:10:49:110|1|2|Time to auto close position='12:00:00 AM', Enabled=False
2025-06-09 03:10:49:117|1|2|Using HDS (hds-us-nt-007.ninjatrader.com/31655)
2025-06-09 03:10:49:978|1|2|Simulation account 'Playback101' reset
2025-06-09 03:13:40:579|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat Position=NQ JUN25 1S EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On bar close IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:13:48:194|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:13:48:203|1|2|Simulation account 'Playback101' reset
2025-06-09 03:13:48:656|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat Position=NQ JUN25 8S EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On bar close IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:13:56:419|1|4|NinjaScript strategy 'sparkzy/*********' submitting order
2025-06-09 03:13:56:528|1|32|Order='48fed20597f844b08e4221e780619ab7/Playback101' Name='Initial Long Entry' New state='Submitted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:56:542|1|32|Order='48fed20597f844b08e4221e780619ab7/Playback101' Name='Initial Long Entry' New state='Accepted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:56:542|1|32|Order='48fed20597f844b08e4221e780619ab7/Playback101' Name='Initial Long Entry' New state='Working' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:56:563|1|32|Order='48fed20597f844b08e4221e780619ab7/Playback101' Name='Initial Long Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19814.5 Error='No error' Native error=''
2025-06-09 03:13:56:581|1|8|Execution='96007fc6b0e24d39a9f04c2194d59cf0' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19814.5 Quantity=1 Market position=Long Operation=Operation_Add Order='48fed20597f844b08e4221e780619ab7' Time='3/17/2025 12:38 AM'
2025-06-09 03:13:56:621|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19814.5 Quantity=1 Market position=Long Operation=Operation_Add
2025-06-09 03:13:56:697|1|32|Order='48fed20597f844b08e4221e780619ab7/Playback101' Name='Initial Long Entry' New state='Filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19814.71875 Error='No error' Native error=''
2025-06-09 03:13:56:699|1|8|Execution='4506f1bcd7374a37b0c9f49e318b2870' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19814.75 Quantity=7 Market position=Long Operation=Operation_Add Order='48fed20597f844b08e4221e780619ab7' Time='3/17/2025 12:38 AM'
2025-06-09 03:13:56:700|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19814.71875 Quantity=8 Market position=Long Operation=Update
2025-06-09 03:13:57:832|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:13:57:862|1|32|Order='32562d0819fe49f0875e4bf6ac5cfa27/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:863|1|32|Order='32562d0819fe49f0875e4bf6ac5cfa27/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:863|1|32|Order='32562d0819fe49f0875e4bf6ac5cfa27/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:864|1|32|Order='32562d0819fe49f0875e4bf6ac5cfa27/Playback101' Name='Close position' New state='Partially filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19802.25 Error='No error' Native error=''
2025-06-09 03:13:57:864|1|8|Execution='e01b42c3317b479db648d5e422419ae6' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19802.25 Quantity=1 Market position=Short Operation=Operation_Add Order='32562d0819fe49f0875e4bf6ac5cfa27' Time='3/17/2025 12:57 AM'
2025-06-09 03:13:57:869|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19814.75 Quantity=7 Market position=Long Operation=Update
2025-06-09 03:13:57:920|1|32|Order='32562d0819fe49f0875e4bf6ac5cfa27/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19802.03125 Error='No error' Native error=''
2025-06-09 03:13:57:921|1|8|Execution='69c71782eaee45f1aa6c367207cdb4d0' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19802 Quantity=7 Market position=Short Operation=Operation_Add Order='32562d0819fe49f0875e4bf6ac5cfa27' Time='3/17/2025 12:57 AM'
2025-06-09 03:13:57:922|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:13:57:922|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:13:57:979|1|32|Order='05bae75056aa49f9bc8f12aef614621d/Playback101' Name='Initial Short Entry' New state='Submitted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:979|1|32|Order='05bae75056aa49f9bc8f12aef614621d/Playback101' Name='Initial Short Entry' New state='Accepted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:980|1|32|Order='05bae75056aa49f9bc8f12aef614621d/Playback101' Name='Initial Short Entry' New state='Working' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:13:57:980|1|32|Order='05bae75056aa49f9bc8f12aef614621d/Playback101' Name='Initial Short Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19802.25 Error='No error' Native error=''
2025-06-09 03:13:57:981|1|8|Execution='c3fd69e6a9c94975b438a1250c47d209' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19802.25 Quantity=1 Market position=Short Operation=Operation_Add Order='05bae75056aa49f9bc8f12aef614621d' Time='3/17/2025 12:57 AM'
2025-06-09 03:13:57:981|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19802.25 Quantity=1 Market position=Short Operation=Operation_Add
2025-06-09 03:13:58:031|1|32|Order='05bae75056aa49f9bc8f12aef614621d/Playback101' Name='Initial Short Entry' New state='Filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19802.03125 Error='No error' Native error=''
2025-06-09 03:13:58:032|1|8|Execution='b63108b2ab8c45839762e9d7daeabe0d' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19802 Quantity=7 Market position=Short Operation=Operation_Add Order='05bae75056aa49f9bc8f12aef614621d' Time='3/17/2025 12:57 AM'
2025-06-09 03:13:58:032|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19802.03125 Quantity=8 Market position=Short Operation=Update
2025-06-09 03:14:00:236|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:00:279|1|32|Order='d98ab5d29d104c40b656fae40c4bd3ff/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:280|1|32|Order='d98ab5d29d104c40b656fae40c4bd3ff/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:280|1|32|Order='d98ab5d29d104c40b656fae40c4bd3ff/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:281|1|32|Order='d98ab5d29d104c40b656fae40c4bd3ff/Playback101' Name='Close position' New state='Partially filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=4 Fill price=19809 Error='No error' Native error=''
2025-06-09 03:14:00:281|1|8|Execution='8192f1ea551449489d97fece0b1695f8' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19809 Quantity=4 Market position=Long Operation=Operation_Add Order='d98ab5d29d104c40b656fae40c4bd3ff' Time='3/17/2025 1:18 AM'
2025-06-09 03:14:00:289|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19802 Quantity=4 Market position=Short Operation=Update
2025-06-09 03:14:00:324|1|32|Order='d98ab5d29d104c40b656fae40c4bd3ff/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19809.125 Error='No error' Native error=''
2025-06-09 03:14:00:341|1|8|Execution='a4827b588c5246ac897f5f13ed0861d1' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19809.25 Quantity=4 Market position=Long Operation=Operation_Add Order='d98ab5d29d104c40b656fae40c4bd3ff' Time='3/17/2025 1:18 AM'
2025-06-09 03:14:00:342|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:14:00:341|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:00:381|1|32|Order='e0ef6c84b80e4c338c0fefebe6fc36ca/Playback101' Name='Initial Long Entry' New state='Submitted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:382|1|32|Order='e0ef6c84b80e4c338c0fefebe6fc36ca/Playback101' Name='Initial Long Entry' New state='Accepted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:382|1|32|Order='e0ef6c84b80e4c338c0fefebe6fc36ca/Playback101' Name='Initial Long Entry' New state='Working' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:00:391|1|32|Order='e0ef6c84b80e4c338c0fefebe6fc36ca/Playback101' Name='Initial Long Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=4 Fill price=19809 Error='No error' Native error=''
2025-06-09 03:14:00:391|1|8|Execution='66acdd5d92c04a7daa37b594f53f2c51' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19809 Quantity=4 Market position=Long Operation=Operation_Add Order='e0ef6c84b80e4c338c0fefebe6fc36ca' Time='3/17/2025 1:18 AM'
2025-06-09 03:14:00:392|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19809 Quantity=4 Market position=Long Operation=Operation_Add
2025-06-09 03:14:00:437|1|32|Order='e0ef6c84b80e4c338c0fefebe6fc36ca/Playback101' Name='Initial Long Entry' New state='Filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19809.125 Error='No error' Native error=''
2025-06-09 03:14:00:437|1|8|Execution='6054cd3fc0e9419d9e21cbdd9eab9d0a' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19809.25 Quantity=4 Market position=Long Operation=Operation_Add Order='e0ef6c84b80e4c338c0fefebe6fc36ca' Time='3/17/2025 1:18 AM'
2025-06-09 03:14:00:438|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19809.125 Quantity=8 Market position=Long Operation=Update
2025-06-09 03:14:04:696|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:04:732|1|32|Order='d123163dda2a41179d8c6293a8f8ecb4/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:733|1|32|Order='d123163dda2a41179d8c6293a8f8ecb4/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:734|1|32|Order='d123163dda2a41179d8c6293a8f8ecb4/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:734|1|32|Order='d123163dda2a41179d8c6293a8f8ecb4/Playback101' Name='Close position' New state='Partially filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19798 Error='No error' Native error=''
2025-06-09 03:14:04:734|1|8|Execution='51ec660f298b4bf49d90cd332ff20cc1' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19798 Quantity=2 Market position=Short Operation=Operation_Add Order='d123163dda2a41179d8c6293a8f8ecb4' Time='3/17/2025 2:10 AM'
2025-06-09 03:14:04:734|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19809.************ Quantity=6 Market position=Long Operation=Update
2025-06-09 03:14:04:769|1|32|Order='d123163dda2a41179d8c6293a8f8ecb4/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19797.8125 Error='No error' Native error=''
2025-06-09 03:14:04:770|1|8|Execution='d09bbdc2ad254631a41efd54fbc1d419' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19797.75 Quantity=6 Market position=Short Operation=Operation_Add Order='d123163dda2a41179d8c6293a8f8ecb4' Time='3/17/2025 2:10 AM'
2025-06-09 03:14:04:770|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:14:04:770|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:04:809|1|32|Order='9ef23ffcb7f540c185087787b411427b/Playback101' Name='Initial Short Entry' New state='Submitted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:810|1|32|Order='9ef23ffcb7f540c185087787b411427b/Playback101' Name='Initial Short Entry' New state='Accepted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:811|1|32|Order='9ef23ffcb7f540c185087787b411427b/Playback101' Name='Initial Short Entry' New state='Working' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:04:812|1|32|Order='9ef23ffcb7f540c185087787b411427b/Playback101' Name='Initial Short Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19798 Error='No error' Native error=''
2025-06-09 03:14:04:812|1|8|Execution='991da4055710436ab0b76127fd54619c' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19798 Quantity=2 Market position=Short Operation=Operation_Add Order='9ef23ffcb7f540c185087787b411427b' Time='3/17/2025 2:10 AM'
2025-06-09 03:14:04:812|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19798 Quantity=2 Market position=Short Operation=Operation_Add
2025-06-09 03:14:04:868|1|32|Order='9ef23ffcb7f540c185087787b411427b/Playback101' Name='Initial Short Entry' New state='Filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19797.8125 Error='No error' Native error=''
2025-06-09 03:14:04:868|1|8|Execution='9efa198110154d42ae80ea5c5b65c66d' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19797.75 Quantity=6 Market position=Short Operation=Operation_Add Order='9ef23ffcb7f540c185087787b411427b' Time='3/17/2025 2:10 AM'
2025-06-09 03:14:04:869|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.8125 Quantity=8 Market position=Short Operation=Update
2025-06-09 03:14:05:978|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:06:013|1|32|Order='68e1e9e152244b60b83a2c7a27d94fd6/Playback101' Name='TP1 Auto Exit' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:013|1|32|Order='68e1e9e152244b60b83a2c7a27d94fd6/Playback101' Name='TP1 Auto Exit' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:014|1|32|Order='68e1e9e152244b60b83a2c7a27d94fd6/Playback101' Name='TP1 Auto Exit' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:014|1|32|Order='68e1e9e152244b60b83a2c7a27d94fd6/Playback101' Name='TP1 Auto Exit' New state='Partially filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19784.5 Error='No error' Native error=''
2025-06-09 03:14:06:015|1|8|Execution='d31faa008b5d4b928262b96ad04a0ea5' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19784.5 Quantity=1 Market position=Long Operation=Operation_Add Order='68e1e9e152244b60b83a2c7a27d94fd6' Time='3/17/2025 2:19 AM'
2025-06-09 03:14:06:015|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.************ Quantity=7 Market position=Short Operation=Update
2025-06-09 03:14:06:061|1|32|Order='68e1e9e152244b60b83a2c7a27d94fd6/Playback101' Name='TP1 Auto Exit' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19784.625 Error='No error' Native error=''
2025-06-09 03:14:06:062|1|8|Execution='3acd24c5e31e40ca86bffe8e7b9f3ad0' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19784.75 Quantity=1 Market position=Long Operation=Operation_Add Order='68e1e9e152244b60b83a2c7a27d94fd6' Time='3/17/2025 2:19 AM'
2025-06-09 03:14:06:062|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.75 Quantity=6 Market position=Short Operation=Update
2025-06-09 03:14:06:063|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:06:117|1|32|Order='cc9aae1ebc5242118fa29514be3c48d9/Playback101' Name='Partial Profit Short' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:118|1|32|Order='cc9aae1ebc5242118fa29514be3c48d9/Playback101' Name='Partial Profit Short' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:118|1|32|Order='cc9aae1ebc5242118fa29514be3c48d9/Playback101' Name='Partial Profit Short' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:174|1|32|Order='cc9aae1ebc5242118fa29514be3c48d9/Playback101' Name='Partial Profit Short' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19784.5 Error='No error' Native error=''
2025-06-09 03:14:06:175|1|8|Execution='c82cfc7c396443cca163f0d0b8ef8bad' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19784.5 Quantity=1 Market position=Long Operation=Operation_Add Order='cc9aae1ebc5242118fa29514be3c48d9' Time='3/17/2025 2:19 AM'
2025-06-09 03:14:06:175|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.75 Quantity=5 Market position=Short Operation=Update
2025-06-09 03:14:06:846|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:06:884|1|32|Order='30878004ba0b4235b1277c8fddeb0271/Playback101' Name='TP2 Auto Exit' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=3 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:887|1|32|Order='30878004ba0b4235b1277c8fddeb0271/Playback101' Name='TP2 Auto Exit' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=3 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:888|1|32|Order='30878004ba0b4235b1277c8fddeb0271/Playback101' Name='TP2 Auto Exit' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=3 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:06:888|1|32|Order='30878004ba0b4235b1277c8fddeb0271/Playback101' Name='TP2 Auto Exit' New state='Partially filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=3 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19781.5 Error='No error' Native error=''
2025-06-09 03:14:06:889|1|8|Execution='292ad01789cc452280bfab76c587494c' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19781.5 Quantity=1 Market position=Long Operation=Operation_Add Order='30878004ba0b4235b1277c8fddeb0271' Time='3/17/2025 2:21 AM'
2025-06-09 03:14:06:889|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.75 Quantity=4 Market position=Short Operation=Update
2025-06-09 03:14:06:944|1|32|Order='30878004ba0b4235b1277c8fddeb0271/Playback101' Name='TP2 Auto Exit' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=3 Type='Market' Time in force=GTC Oco='' Filled=3 Fill price=19781.************ Error='No error' Native error=''
2025-06-09 03:14:06:945|1|8|Execution='7e167835e41f4b118358d98f480540fe' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19781.75 Quantity=2 Market position=Long Operation=Operation_Add Order='30878004ba0b4235b1277c8fddeb0271' Time='3/17/2025 2:21 AM'
2025-06-09 03:14:06:946|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19797.75 Quantity=2 Market position=Short Operation=Update
2025-06-09 03:14:09:560|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:09:588|1|32|Order='442b95825e3242a2952cdd64b8205ad4/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:588|1|32|Order='442b95825e3242a2952cdd64b8205ad4/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:589|1|32|Order='442b95825e3242a2952cdd64b8205ad4/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:633|1|32|Order='442b95825e3242a2952cdd64b8205ad4/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19787.5 Error='No error' Native error=''
2025-06-09 03:14:09:633|1|8|Execution='a15efd964a1f4d4884d86cfaa2c751d2' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19787.5 Quantity=2 Market position=Long Operation=Operation_Add Order='442b95825e3242a2952cdd64b8205ad4' Time='3/17/2025 2:41 AM'
2025-06-09 03:14:09:634|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:14:09:634|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:09:680|1|32|Order='b71531865fdd46878c74ebfc951e7073/Playback101' Name='Initial Long Entry' New state='Submitted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:680|1|32|Order='b71531865fdd46878c74ebfc951e7073/Playback101' Name='Initial Long Entry' New state='Accepted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:680|1|32|Order='b71531865fdd46878c74ebfc951e7073/Playback101' Name='Initial Long Entry' New state='Working' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:09:681|1|32|Order='b71531865fdd46878c74ebfc951e7073/Playback101' Name='Initial Long Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19787.5 Error='No error' Native error=''
2025-06-09 03:14:09:681|1|8|Execution='0345b7c8963f48b8b42c25e32cbed7f6' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19787.5 Quantity=2 Market position=Long Operation=Operation_Add Order='b71531865fdd46878c74ebfc951e7073' Time='3/17/2025 2:41 AM'
2025-06-09 03:14:09:691|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19787.5 Quantity=2 Market position=Long Operation=Operation_Add
2025-06-09 03:14:09:732|1|32|Order='b71531865fdd46878c74ebfc951e7073/Playback101' Name='Initial Long Entry' New state='Filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19787.6875 Error='No error' Native error=''
2025-06-09 03:14:09:732|1|8|Execution='c92a1a21990240da9166848564ba325c' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19787.75 Quantity=6 Market position=Long Operation=Operation_Add Order='b71531865fdd46878c74ebfc951e7073' Time='3/17/2025 2:41 AM'
2025-06-09 03:14:09:733|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19787.6875 Quantity=8 Market position=Long Operation=Update
2025-06-09 03:14:13:060|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:13:085|1|32|Order='084ffd9a02ae45e994d5f29429d2a22e/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:085|1|32|Order='084ffd9a02ae45e994d5f29429d2a22e/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:086|1|32|Order='084ffd9a02ae45e994d5f29429d2a22e/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:086|1|32|Order='084ffd9a02ae45e994d5f29429d2a22e/Playback101' Name='Close position' New state='Partially filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19766 Error='No error' Native error=''
2025-06-09 03:14:13:086|1|8|Execution='2602722fe88647ea999f2d864335590b' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19766 Quantity=1 Market position=Short Operation=Operation_Add Order='084ffd9a02ae45e994d5f29429d2a22e' Time='3/17/2025 3:19 AM'
2025-06-09 03:14:13:086|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19787.************ Quantity=7 Market position=Long Operation=Update
2025-06-09 03:14:13:104|1|32|Order='084ffd9a02ae45e994d5f29429d2a22e/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19765.78125 Error='No error' Native error=''
2025-06-09 03:14:13:105|1|8|Execution='85ac54ec6e9941d497d1b9287a701812' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19765.75 Quantity=7 Market position=Short Operation=Operation_Add Order='084ffd9a02ae45e994d5f29429d2a22e' Time='3/17/2025 3:19 AM'
2025-06-09 03:14:13:110|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:14:13:110|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:13:131|1|32|Order='e975e16de8e34cbf9bef4c29478d637c/Playback101' Name='Initial Short Entry' New state='Submitted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:131|1|32|Order='e975e16de8e34cbf9bef4c29478d637c/Playback101' Name='Initial Short Entry' New state='Accepted' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:132|1|32|Order='e975e16de8e34cbf9bef4c29478d637c/Playback101' Name='Initial Short Entry' New state='Working' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:13:137|1|32|Order='e975e16de8e34cbf9bef4c29478d637c/Playback101' Name='Initial Short Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19766 Error='No error' Native error=''
2025-06-09 03:14:13:138|1|8|Execution='6a7ddb16f57444fd9028c96144557a51' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19766 Quantity=1 Market position=Short Operation=Operation_Add Order='e975e16de8e34cbf9bef4c29478d637c' Time='3/17/2025 3:19 AM'
2025-06-09 03:14:13:140|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19766 Quantity=1 Market position=Short Operation=Operation_Add
2025-06-09 03:14:13:165|1|32|Order='e975e16de8e34cbf9bef4c29478d637c/Playback101' Name='Initial Short Entry' New state='Filled' Instrument='NQ JUN25' Action='Sell short' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19765.78125 Error='No error' Native error=''
2025-06-09 03:14:13:166|1|8|Execution='6421907c898f4baf8764844d45f56d51' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19765.75 Quantity=7 Market position=Short Operation=Operation_Add Order='e975e16de8e34cbf9bef4c29478d637c' Time='3/17/2025 3:19 AM'
2025-06-09 03:14:13:166|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19765.78125 Quantity=8 Market position=Short Operation=Update
2025-06-09 03:14:14:120|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:14:154|1|32|Order='e453e4a9c53c41438625e68b832e0e55/Playback101' Name='Close position' New state='Submitted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:155|1|32|Order='e453e4a9c53c41438625e68b832e0e55/Playback101' Name='Close position' New state='Accepted' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:156|1|32|Order='e453e4a9c53c41438625e68b832e0e55/Playback101' Name='Close position' New state='Working' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:159|1|32|Order='e453e4a9c53c41438625e68b832e0e55/Playback101' Name='Close position' New state='Partially filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19788.75 Error='No error' Native error=''
2025-06-09 03:14:14:159|1|8|Execution='eb92eca7df7f4770bba8bde2bdb2878b' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19788.75 Quantity=1 Market position=Long Operation=Operation_Add Order='e453e4a9c53c41438625e68b832e0e55' Time='3/17/2025 3:27 AM'
2025-06-09 03:14:14:159|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19765.75 Quantity=7 Market position=Short Operation=Update
2025-06-09 03:14:14:200|1|32|Order='e453e4a9c53c41438625e68b832e0e55/Playback101' Name='Close position' New state='Filled' Instrument='NQ JUN25' Action='Buy to cover' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19788.96875 Error='No error' Native error=''
2025-06-09 03:14:14:200|1|8|Execution='26edeb42f7db40c69d047444a824f6de' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19789 Quantity=7 Market position=Long Operation=Operation_Add Order='e453e4a9c53c41438625e68b832e0e55' Time='3/17/2025 3:27 AM'
2025-06-09 03:14:14:201|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Flat Operation=Remove
2025-06-09 03:14:14:202|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:14:235|1|32|Order='93f683d759f24c9d898df8839a899ee9/Playback101' Name='Initial Long Entry' New state='Submitted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:236|1|32|Order='93f683d759f24c9d898df8839a899ee9/Playback101' Name='Initial Long Entry' New state='Accepted' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:237|1|32|Order='93f683d759f24c9d898df8839a899ee9/Playback101' Name='Initial Long Entry' New state='Working' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:14:237|1|32|Order='93f683d759f24c9d898df8839a899ee9/Playback101' Name='Initial Long Entry' New state='Partially filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19788.75 Error='No error' Native error=''
2025-06-09 03:14:14:238|1|8|Execution='01105b2b65914232afe1e466c97d4f83' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19788.75 Quantity=1 Market position=Long Operation=Operation_Add Order='93f683d759f24c9d898df8839a899ee9' Time='3/17/2025 3:27 AM'
2025-06-09 03:14:14:238|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19788.75 Quantity=1 Market position=Long Operation=Operation_Add
2025-06-09 03:14:14:280|1|32|Order='93f683d759f24c9d898df8839a899ee9/Playback101' Name='Initial Long Entry' New state='Filled' Instrument='NQ JUN25' Action='Buy' Limit price=0 Stop price=0 Quantity=8 Type='Market' Time in force=GTC Oco='' Filled=8 Fill price=19788.96875 Error='No error' Native error=''
2025-06-09 03:14:14:280|1|8|Execution='19af561807764ef7868fa024ee77049c' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19789 Quantity=7 Market position=Long Operation=Operation_Add Order='93f683d759f24c9d898df8839a899ee9' Time='3/17/2025 3:27 AM'
2025-06-09 03:14:14:281|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19788.96875 Quantity=8 Market position=Long Operation=Update
2025-06-09 03:14:18:389|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:18:419|1|32|Order='3059f4ab9a3a4dbcb282597f46e86e9a/Playback101' Name='TP1 Auto Exit' New state='Submitted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:421|1|32|Order='3059f4ab9a3a4dbcb282597f46e86e9a/Playback101' Name='TP1 Auto Exit' New state='Accepted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:422|1|32|Order='3059f4ab9a3a4dbcb282597f46e86e9a/Playback101' Name='TP1 Auto Exit' New state='Working' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:422|1|32|Order='3059f4ab9a3a4dbcb282597f46e86e9a/Playback101' Name='TP1 Auto Exit' New state='Partially filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19806.75 Error='No error' Native error=''
2025-06-09 03:14:18:423|1|8|Execution='b13d524c7f1e47bcab313ad95a6f304e' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19806.75 Quantity=1 Market position=Short Operation=Operation_Add Order='3059f4ab9a3a4dbcb282597f46e86e9a' Time='3/17/2025 4:12 AM'
2025-06-09 03:14:18:423|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19789 Quantity=7 Market position=Long Operation=Update
2025-06-09 03:14:18:467|1|32|Order='3059f4ab9a3a4dbcb282597f46e86e9a/Playback101' Name='TP1 Auto Exit' New state='Filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=2 Type='Market' Time in force=GTC Oco='' Filled=2 Fill price=19806.625 Error='No error' Native error=''
2025-06-09 03:14:18:468|1|8|Execution='17875b28f1fb47e798dd69b2de91700c' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19806.5 Quantity=1 Market position=Short Operation=Operation_Add Order='3059f4ab9a3a4dbcb282597f46e86e9a' Time='3/17/2025 4:12 AM'
2025-06-09 03:14:18:469|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19789 Quantity=6 Market position=Long Operation=Update
2025-06-09 03:14:18:469|1|16|NinjaScript strategy 'Sparkzy/*********' submitting order
2025-06-09 03:14:18:521|1|32|Order='b2d04b00fa324d9e88005aea83742476/Playback101' Name='Partial Profit Long' New state='Submitted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:522|1|32|Order='b2d04b00fa324d9e88005aea83742476/Playback101' Name='Partial Profit Long' New state='Accepted' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:522|1|32|Order='b2d04b00fa324d9e88005aea83742476/Playback101' Name='Partial Profit Long' New state='Working' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=0 Fill price=0 Error='No error' Native error=''
2025-06-09 03:14:18:573|1|32|Order='b2d04b00fa324d9e88005aea83742476/Playback101' Name='Partial Profit Long' New state='Filled' Instrument='NQ JUN25' Action='Sell' Limit price=0 Stop price=0 Quantity=1 Type='Market' Time in force=GTC Oco='' Filled=1 Fill price=19806.75 Error='No error' Native error=''
2025-06-09 03:14:18:573|1|8|Execution='def58726da004626a7d3d283a73692f8' Instrument='NQ JUN25' Account='Playback101' Exchange=Default Price=19806.75 Quantity=1 Market position=Short Operation=Operation_Add Order='b2d04b00fa324d9e88005aea83742476' Time='3/17/2025 4:12 AM'
2025-06-09 03:14:18:573|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=19789 Quantity=5 Market position=Long Operation=Update
2025-06-09 03:14:23:029|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:14:23:033|1|64|Instrument='NQ JUN25' Account='Playback101' Average price=0 Quantity=0 Market position=Long Operation=Remove
2025-06-09 03:14:23:051|1|2|Simulation account 'Playback101' reset
2025-06-09 03:14:23:611|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat Position=NQ JUN25 8S EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On bar close IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:15:45:520|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:34:58:258|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:35:55:660|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:35:55:741|1|2|Simulation account 'Playback101' reset
2025-06-09 03:35:57:330|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:38:35:445|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:38:35:529|1|2|Simulation account 'Playback101' reset
2025-06-09 03:38:36:718|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:39:34:849|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:41:32:231|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:41:32:253|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:41:32:317|1|2|Simulation account 'Playback101' reset
2025-06-09 03:41:34:664|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:41:35:153|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:41:44:406|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:41:50:276|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:49:01:924|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:52:37:239|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:52:50:757|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:53:37:275|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:53:37:291|1|2|Simulation account 'Playback101' reset
2025-06-09 03:53:37:682|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:53:38:450|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:53:54:935|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:54:13:258|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:54:13:269|1|2|Simulation account 'Playback101' reset
2025-06-09 03:54:13:596|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:54:15:159|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 03:54:21:275|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 03:56:37:368|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 03:59:13:380|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:01:15:725|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:10:52:405|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 04:10:56:498|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 04:10:57:192|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 04:10:57:203|1|2|Simulation account 'Playback101' reset
2025-06-09 04:10:57:685|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 04:12:49:243|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:15:37:213|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:18:07:842|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:20:45:533|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:23:25:720|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:27:39:924|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:30:20:768|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:33:21:717|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:36:30:940|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:39:48:400|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:41:22:662|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:41:45:564|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:42:12:934|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:42:44:709|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:43:19:382|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:44:34:993|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:45:04:614|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:45:36:214|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:46:04:524|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:46:26:735|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:48:48:488|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:49:23:584|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:50:35:436|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:51:27:160|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:54:27:449|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:55:11:844|1|128|Strategy 'sparkzy/*********': Exit on session close handling started
2025-06-09 04:55:28:798|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 04:55:31:475|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 04:55:41:197|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 04:55:41:210|1|2|Simulation account 'Playback101' reset
2025-06-09 04:55:41:587|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
2025-06-09 04:57:46:374|1|4|Disabling NinjaScript strategy 'sparkzy/*********'
2025-06-09 04:59:36:485|1|4|Enabling NinjaScript strategy 'sparkzy/*********' : On starting a real-time strategy - StartBehavior=WaitUntilFlat EntryHandling=All entries EntriesPerDirection=1 StopTargetHandling=Per entry execution ErrorHandling=Stop strategy, cancel orders, close positions ExitOnSessionClose=True / triggering 30 seconds before close SetOrderQuantityBy=Strategy ConnectionLossHandling=Recalculate DisconnectDelaySeconds=10 CancelEntriesOnStrategyDisable=False CancelExitsOnStrategyDisable=False Calculate=On price change IsUnmanaged=False MaxRestarts=4 in 5 minutes
