﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <Version>*******</Version>
  <TypeName>NinjaTrader.NinjaScript.BarsTypes.SecondBarsType</TypeName>
  <BarsType>
    <SecondBarsType xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <BarsPeriod>
        <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>30</Value>
        <Value2>1</Value2>
      </BarsPeriod>
      <BuiltFrom>Tick</BuiltFrom>
      <DayCount>0</DayCount>
      <DaysToLoad>3</DaysToLoad>
      <IsIntraday>true</IsIntraday>
      <IsTimeBased>true</IsTimeBased>
      <TickCount>42</TickCount>
      <TicksOnLastSecond>0</TicksOnLastSecond>
    </SecondBarsType>
  </BarsType>
</NinjaTrader>