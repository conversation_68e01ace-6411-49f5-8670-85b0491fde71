#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class ALMA : Indicator
    {
        private double length = 21;
        private double offset = 0.85;
        private double sigma = 6.0;
        private double almaValue;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"ALMA (Arnaud Legoux Moving Average)";
                Name = "ALMA";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                AddPlot(Brushes.Orange, "ALMA");
            }
            else if (State == State.Configure)
            {
            }
        }

        protected override void OnBarUpdate()
		{
		    if (CurrentBar < length)
		        return;
		
		    almaValue = 0.0;
		    double sumWeights = 0.0;
		
		    for (int i = 0; i < length; i++)
		    {
		        double weight = Math.Exp(-Math.Pow((i - offset * (length - 1)) / (sigma * (length - 1)), 2) / 2);
		        almaValue += weight * Input[i];
		        sumWeights += weight;
		    }
		
		    almaValue /= sumWeights;
		    Values[0][0] = almaValue;
		
		    if (Close[0] > almaValue)
		    {
		        PlotBrushes[0][0] = Brushes.Lime;
		    }
		    else if (Close[0] < almaValue)
		    {
		        PlotBrushes[0][0] = Brushes.Red;
		    }
		    else
		    {
		        PlotBrushes[0][0] = Brushes.Orange;
		    }
		}

        #region Properties
        [Range(1, int.MaxValue), NinjaScriptProperty]
        public double Length
        {
            get { return length; }
            set { length = value; }
        }

        [Range(0.0, 1.0), NinjaScriptProperty]
        public double Offset
        {
            get { return offset; }
            set { offset = value; }
        }

        [Range(0.1, double.MaxValue), NinjaScriptProperty]
        public double Sigma
        {
            get { return sigma; }
            set { sigma = value; }
        }
        #endregion
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private ALMA[] cacheALMA;
		public ALMA ALMA(double length, double offset, double sigma)
		{
			return ALMA(Input, length, offset, sigma);
		}

		public ALMA ALMA(ISeries<double> input, double length, double offset, double sigma)
		{
			if (cacheALMA != null)
				for (int idx = 0; idx < cacheALMA.Length; idx++)
					if (cacheALMA[idx] != null && cacheALMA[idx].Length == length && cacheALMA[idx].Offset == offset && cacheALMA[idx].Sigma == sigma && cacheALMA[idx].EqualsInput(input))
						return cacheALMA[idx];
			return CacheIndicator<ALMA>(new ALMA(){ Length = length, Offset = offset, Sigma = sigma }, input, ref cacheALMA);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.ALMA ALMA(double length, double offset, double sigma)
		{
			return indicator.ALMA(Input, length, offset, sigma);
		}

		public Indicators.ALMA ALMA(ISeries<double> input , double length, double offset, double sigma)
		{
			return indicator.ALMA(input, length, offset, sigma);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.ALMA ALMA(double length, double offset, double sigma)
		{
			return indicator.ALMA(Input, length, offset, sigma);
		}

		public Indicators.ALMA ALMA(ISeries<double> input , double length, double offset, double sigma)
		{
			return indicator.ALMA(input, length, offset, sigma);
		}
	}
}

#endregion
