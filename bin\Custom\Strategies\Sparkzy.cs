#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using System.Net.Http;
using System.IO;
using System.Net;   
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    // Order type enums for dropdown menus
    public enum EntryOrderType
    {
        Market,
        Limit,
        StopMarket,
        StopLimit
    }

    public enum TakeProfitOrderType
    {
        Market,
        Limit,
        StopLimit
    }

    public enum StopLossOrderType
    {
        Market,
        StopMarket,
        StopLimit
    }

    public enum EmergencyExitOrderType
    {
        Market,
        StopMarket,
        StopLimit
    }

    // Helper class for batching Discord alerts
    public class AlertMessage
    {
        public string Title { get; set; }
        public string Message { get; set; }
        public string Color { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class sparkzy : Strategy
    {
        #region Variables and Parameters
        // Williams %R Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "WPR Length", Order = 1, GroupName = "Williams %R")]
        public int WprLength { get; set; } = 21;

        [Range(1, int.MaxValue)]
        [Display(Name = "WPR EMA Length", Order = 2, GroupName = "Williams %R")]
        public int WprEmaLength { get; set; } = 13;

        [Display(Name = "WPR Overbought", Order = 3, GroupName = "Williams %R")]
        public double WprOverbought { get; set; } = -20;

        [Display(Name = "WPR Oversold", Order = 4, GroupName = "Williams %R")]
        public double WprOversold { get; set; } = -80;

        // MACD Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "MACD Fast", Order = 5, GroupName = "MACD")]
        public int FastLength { get; set; } = 5;

        [Range(1, int.MaxValue)]
        [Display(Name = "MACD Slow", Order = 6, GroupName = "MACD")]
        public int SlowLength { get; set; } = 8;

        [Range(1, int.MaxValue)]
        [Display(Name = "Signal Length", Order = 7, GroupName = "MACD")]
        public int SignalLength { get; set; } = 5;

        [Display(Name = "MACD Threshold", Order = 8, GroupName = "MACD")]
        public double MacdThreshold { get; set; } = 0.5;

        // Range Filter Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "RF Period", Order = 9, GroupName = "Range Filter")]
        public int RfPeriod { get; set; } = 10;

        [Display(Name = "RF Multiplier", Order = 10, GroupName = "Range Filter")]
        public double RfMultiplier { get; set; } = 1.0;

        // ALMA & TEMA Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "ALMA Window", Order = 11, GroupName = "ALMA")]
        public int AlmaWindow { get; set; } = 34;

        [Display(Name = "ALMA Offset", Order = 12, GroupName = "ALMA")]
        public double AlmaOffset { get; set; } = 0.85;

        [Display(Name = "ALMA Sigma", Order = 13, GroupName = "ALMA")]
        public double AlmaSigma { get; set; } = 6;

        [Range(1, int.MaxValue)]
        [Display(Name = "TEMA Length", Order = 14, GroupName = "TEMA")]
        public int TemaLength { get; set; } = 9;

        // Strategy Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "Initial Position Size", Order = 15, GroupName = "Position Management")]
        public int InitialContracts { get; set; } = 8;

        [Range(1, int.MaxValue)]
        [Display(Name = "Additional Position Size", Order = 16, GroupName = "Position Management")]
        public int AdditionalContracts { get; set; } = 2;

        [Range(1, int.MaxValue)]
        [Display(Name = "Maximum Total Positions", Order = 17, GroupName = "Position Management")]
        public int MaxTotalPositions { get; set; } = 20;

        [Display(Name = "Signal Exit Only", Order = 18, GroupName = "Position Management")]
        public bool SignalExitOnly { get; set; } = false;

        [Display(Name = "Use ATM Strategy", Order = 19, GroupName = "Position Management")]
        public bool UseAtmStrategy { get; set; } = false;

        [Display(Name = "ATM Template Name", Order = 20, GroupName = "Position Management")]
        public string AtmTemplateName { get; set; } = "AtmStrategyTemplate";

        [Display(Name = "Use Emergency Stop", Order = 21, GroupName = "Risk Management")]
        public bool UseEmergencyStop { get; set; } = false;

        [Display(Name = "Emergency Stop ATR Multiplier", Order = 22, GroupName = "Risk Management")]
        public double EmergencyStopAtrMultiplier { get; set; } = 3.0;

        [Range(1, int.MaxValue)]
        [Display(Name = "ATR Period", Order = 21, GroupName = "Risk Management")]
        public int AtrPeriod { get; set; } = 14;

        [Display(Name = "Use Time-Based Exit", Order = 22, GroupName = "Risk Management")]
        public bool UseTimeBasedExit { get; set; } = false;

        [Range(1, int.MaxValue)]
        [Display(Name = "Max Bars Without Signal", Order = 23, GroupName = "Risk Management")]
        public int MaxBarsWithoutSignal { get; set; } = 50;

        [Display(Name = "Use Partial Profit Taking", Order = 24, GroupName = "Profit Management")]
        public bool UsePartialProfitTaking { get; set; } = false;

        [Display(Name = "Partial Exit Profit Multiplier", Order = 25, GroupName = "Profit Management")]
        public double PartialExitProfitMultiplier { get; set; } = 2.0;

        [Display(Name = "Partial Exit Percentage", Order = 26, GroupName = "Profit Management")]
        public double PartialExitPercentage { get; set; } = 0.3; // 30% of position

        [Display(Name = "Show Take Profit Levels", Order = 27, GroupName = "Profit Management")]
        public bool ShowTakeProfitLevels { get; set; } = true;

        [Range(1, 5)]
        [Display(Name = "Number of TP Levels", Order = 28, GroupName = "Profit Management")]
        public int NumberOfTakeProfitLevels { get; set; } = 3;

        [Display(Name = "TP Level Spacing Multiplier", Order = 29, GroupName = "Profit Management")]
        public double TakeProfitSpacing { get; set; } = 1.0;

        [Display(Name = "Enable Automated TP Exits", Order = 30, GroupName = "Profit Management")]
        public bool EnableAutomatedTpExits { get; set; } = true;

        [Display(Name = "TP1 Exit Percentage", Order = 31, GroupName = "Profit Management")]
        public double Tp1ExitPercentage { get; set; } = 0.30; // 30%

        [Display(Name = "TP2 Exit Percentage", Order = 32, GroupName = "Profit Management")]
        public double Tp2ExitPercentage { get; set; } = 0.40; // 40%

        [Display(Name = "TP3 Exit Percentage", Order = 33, GroupName = "Profit Management")]
        public double Tp3ExitPercentage { get; set; } = 0.30; // 30% (final exit)

        [Display(Name = "Runner Percentage", Order = 34, GroupName = "Profit Management")]
        public double RunnerPercentage { get; set; } = 0.0; // No runner - full exit

        // Legacy parameters (kept for backward compatibility but not used when SignalExitOnly is true)
        [Display(Name = "Stop Loss Ticks (Legacy)", Order = 35, GroupName = "Legacy")]
        public int StopLossTicks { get; set; } = 20;

        [Display(Name = "Profit Target Ticks (Legacy)", Order = 36, GroupName = "Legacy")]
        public int ProfitTargetTicks { get; set; } = 40;

        [Display(Name = "Use Stop Loss (Legacy)", Order = 37, GroupName = "Legacy")]
        public bool UseStopLoss { get; set; } = false;

        [Display(Name = "Use Profit Target (Legacy)", Order = 38, GroupName = "Legacy")]
        public bool UseProfitTarget { get; set; } = false;

        // Discord Webhook Parameters
        [Display(Name = "Enable Discord Alerts", Order = 39, GroupName = "Discord Notifications")]
        public bool EnableDiscordAlerts { get; set; } = false;

        [Display(Name = "Discord Webhook URL", Order = 40, GroupName = "Discord Notifications")]
        public string DiscordWebhookUrl { get; set; } = "";

        [Display(Name = "Alert Username", Order = 41, GroupName = "Discord Notifications")]
        public string AlertUsername { get; set; } = "NinjaTrader Bot";

        [Display(Name = "Include Chart Symbol", Order = 42, GroupName = "Discord Notifications")]
        public bool IncludeSymbol { get; set; } = true;

        [Display(Name = "Alert Color (Hex)", Order = 43, GroupName = "Discord Notifications")]
        public string AlertColor { get; set; } = "3447003"; // Blue color in decimal

        // Profit Milestone Parameters
        [Display(Name = "Enable Profit Milestones", Order = 44, GroupName = "Profit Milestones")]
        public bool EnableProfitMilestones { get; set; } = true;

        [Display(Name = "Milestone 1 ($)", Order = 45, GroupName = "Profit Milestones")]
        public double Milestone1 { get; set; } = 500.0;

        [Display(Name = "Milestone 2 ($)", Order = 46, GroupName = "Profit Milestones")]
        public double Milestone2 { get; set; } = 1000.0;

        [Display(Name = "Milestone 3 ($)", Order = 47, GroupName = "Profit Milestones")]
        public double Milestone3 { get; set; } = 1500.0;

        [Display(Name = "Use @everyone for Milestones", Order = 48, GroupName = "Profit Milestones")]
        public bool UseEveryoneTag { get; set; } = true;

        // Time Window Parameters
        [Display(Name = "Use Time Window", Order = 49, GroupName = "Time Window")]
        public bool UseTimeWindow { get; set; } = false;

        [Display(Name = "Start Time (HH:mm)", Order = 50, GroupName = "Time Window")]
        public string StartTime { get; set; } = "09:30";

        [Display(Name = "End Time (HH:mm)", Order = 51, GroupName = "Time Window")]
        public string EndTime { get; set; } = "16:00";

        [Display(Name = "Exit at Window End", Order = 52, GroupName = "Time Window")]
        public bool ExitAtWindowEnd { get; set; } = true;

        // Order Type Parameters
        [Display(Name = "Entry Order Type", Order = 53, GroupName = "Order Management")]
        public EntryOrderType EntryOrderType { get; set; } = EntryOrderType.Market;

        [Display(Name = "Take Profit Order Type", Order = 54, GroupName = "Order Management")]
        public TakeProfitOrderType TpOrderType { get; set; } = TakeProfitOrderType.Limit;

        [Display(Name = "Stop Loss Order Type", Order = 55, GroupName = "Order Management")]
        public StopLossOrderType SlOrderType { get; set; } = StopLossOrderType.StopMarket;

        [Display(Name = "Emergency Exit Order Type", Order = 56, GroupName = "Order Management")]
        public EmergencyExitOrderType EmergencyOrderType { get; set; } = EmergencyExitOrderType.Market;

        // Series for calculations
        private Series<double> almaSeries;
        private Series<double> temaSeries;
        private Series<double> wprSeries;
        private Series<double> wprEmaSeries;
        private Series<double> filtSeries;
        private Series<double> upwardSeries;
        private Series<double> downwardSeries;
        private Series<double> macdLineSeries;
        private Series<double> signalLineSeries;
        private Series<double> histSeries;
        private Series<double> absDiffSeries;
        private Series<bool> buySignalSeries;
        private Series<bool> sellSignalSeries;

        // Performance optimization series
        private Series<double> atrSeries;

        // Order management
        private Order entryOrder = null;
        private Order stopLossOrder = null;
        private Order profitTargetOrder = null;

        // Advanced Position Management
        private int totalLongContracts = 0;
        private int totalShortContracts = 0;
        private double averageLongPrice = 0.0;
        private double averageShortPrice = 0.0;
        private int barsInPosition = 0;
        private int barsSinceLastSignal = 0;
        private bool partialExitTaken = false;
        private MarketPosition lastPositionDirection = MarketPosition.Flat;

        // Signal tracking
        private bool LastBuySignalState = false;
        private bool LastSellSignalState = false;

        // Automated Take Profit Tracking
        private bool tp1Hit = false;
        private bool tp2Hit = false;
        private bool tp3Hit = false;
        private int originalPositionSize = 0;
        private int remainingPositionSize = 0;
        private double[] takeProfitLevels = new double[5]; // Support up to 5 TP levels

        // Profit milestone tracking
        private double dailyStartingBalance = 0.0;
        private double maxDailyProfit = 0.0;
        private bool milestone1Hit = false;
        private bool milestone2Hit = false;
        private bool milestone3Hit = false;
        private DateTime lastResetDate = DateTime.MinValue;
        private double dailyRealizedPnL = 0.0;
        private double sessionStartingPnL = 0.0;

        // Performance optimization variables
        private double[] almaWeights;
        private double almaNorm;
        private int almaM;
        private double almaS;

        // Time window variables
        private TimeSpan startTimeSpan;
        private TimeSpan endTimeSpan;

        // Discord alert batching
        private List<AlertMessage> pendingAlerts = new List<AlertMessage>();
        private DateTime lastAlertBatch = DateTime.MinValue;
        private readonly TimeSpan alertBatchInterval = TimeSpan.FromSeconds(5);

        // Take profit orders tracking
        private Order[] takeProfitOrders = new Order[3];

        // ATM Strategy variables
        private string atmStrategyId = string.Empty;
        private string orderId = string.Empty;
        private bool isAtmStrategyCreated = false;

        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Trading strategy based on ALMA, TEMA, WPR, MACD, and Range Filter signals";
                Name = "Sparkzy";
                Calculate = Calculate.OnPriceChange; // Changed for tick-level exit evaluation
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                IsInstantiatedOnEachOptimizationIteration = true;

                // Parse time window settings
                if (UseTimeWindow)
                {
                    if (TimeSpan.TryParse(StartTime, out startTimeSpan) &&
                        TimeSpan.TryParse(EndTime, out endTimeSpan))
                    {
                        Print($"Time window configured: {StartTime} to {EndTime}");
                    }
                    else
                    {
                        Print("Invalid time format. Using default 09:30 to 16:00");
                        startTimeSpan = new TimeSpan(9, 30, 0);
                        endTimeSpan = new TimeSpan(16, 0, 0);
                    }
                }
            }
            else if (State == State.DataLoaded)
            {
                almaSeries = new Series<double>(this);
                temaSeries = new Series<double>(this);
                wprSeries = new Series<double>(this);
                wprEmaSeries = new Series<double>(this);
                filtSeries = new Series<double>(this);
                upwardSeries = new Series<double>(this);
                downwardSeries = new Series<double>(this);
                macdLineSeries = new Series<double>(this);
                signalLineSeries = new Series<double>(this);
                histSeries = new Series<double>(this);
                absDiffSeries = new Series<double>(this);
                buySignalSeries = new Series<bool>(this);
                sellSignalSeries = new Series<bool>(this);
                atrSeries = new Series<double>(this);

                // Pre-calculate ALMA weights for performance optimization
                PreCalculateAlmaWeights();

                // Initialize profit tracking
                ResetDailyProfitTracking();
            }
        }

        private void PreCalculateAlmaWeights()
        {
            try
            {
                Print($"[INIT] Starting ALMA weights pre-calculation for window {AlmaWindow}");

                almaWeights = new double[AlmaWindow];
                almaNorm = 0.0;
                almaM = (int)(AlmaOffset * (AlmaWindow - 1));
                almaS = AlmaWindow / (double)AlmaSigma;

                for (int i = 0; i < AlmaWindow; i++)
                {
                    double weight = Math.Exp(-((i - almaM) * (i - almaM)) / (2 * almaS * almaS));
                    almaWeights[i] = weight;
                    almaNorm += weight;
                }

                Print($"[INIT] ALMA weights pre-calculated successfully - Window: {AlmaWindow}, Norm: {almaNorm:F6}, Offset: {AlmaOffset}, Sigma: {AlmaSigma}");
            }
            catch (Exception ex)
            {
                Print($"[ERROR] Failed to pre-calculate ALMA weights: {ex.Message}");
                // Fallback to original calculation method
                almaWeights = null;
                almaNorm = 0;
            }
        }

        #region Core Calculations
        protected override void OnBarUpdate()
        {
            try
            {
                int minBarsRequired = Math.Max(AlmaWindow,
                                      Math.Max(3 * TemaLength,
                                      Math.Max(WprLength,
                                      Math.Max(SlowLength + SignalLength,
                                      RfPeriod * 2))));

                if (CurrentBar < minBarsRequired)
                {
                    if (CurrentBar % 10 == 0) // Log every 10 bars to avoid spam
                        Print($"[INIT] Waiting for minimum bars: {CurrentBar}/{minBarsRequired}");
                    return;
                }

                // Calculate indicators only on first tick of bar to minimize CPU overhead
                if (IsFirstTickOfBar)
                {
                    Print($"[BAR] New bar {CurrentBar} at {Time[0]:HH:mm:ss} - OHLC: {Open[0]:F2}/{High[0]:F2}/{Low[0]:F2}/{Close[0]:F2}");

                    // Cache ATR value for performance
                    atrSeries[0] = ATR(AtrPeriod)[0];
                    Print($"[INDICATORS] ATR cached: {atrSeries[0]:F4}");

                    // Calculate all indicators
                    almaSeries[0] = CalculateAlmaOptimized();
                    temaSeries[0] = TEMA(Close, TemaLength)[0];
                    CalculateWilliamsR();
                    CalculateMacd();
                    CalculateRangeFilter();

                    Print($"[INDICATORS] ALMA: {almaSeries[0]:F4}, TEMA: {temaSeries[0]:F4}, WPR: {wprSeries[0]:F2}, MACD Hist: {histSeries[0]:F4}");

                // Calculate signal conditions
                bool currentLongCondition = filtSeries[0] > filtSeries[1] &&
                                            histSeries[0] > MacdThreshold &&
                                            wprSeries[0] > WprOverbought;

                bool currentShortCondition = filtSeries[0] < filtSeries[1] &&
                                             histSeries[0] < -MacdThreshold &&
                                             wprSeries[0] < WprOversold;

                Print($"[SIGNALS] Long condition: {currentLongCondition} (Filter: {filtSeries[0] > filtSeries[1]}, MACD: {histSeries[0] > MacdThreshold} [{histSeries[0]:F4}>{MacdThreshold}], WPR: {wprSeries[0] > WprOverbought} [{wprSeries[0]:F2}>{WprOverbought}])");
                Print($"[SIGNALS] Short condition: {currentShortCondition} (Filter: {filtSeries[0] < filtSeries[1]}, MACD: {histSeries[0] < -MacdThreshold} [{histSeries[0]:F4}<{-MacdThreshold}], WPR: {wprSeries[0] < WprOversold} [{wprSeries[0]:F2}<{WprOversold}])");

                // Generate signals
                buySignalSeries[0] = currentLongCondition && !sellSignalSeries[1];
                sellSignalSeries[0] = currentShortCondition && !buySignalSeries[1];

                // Ensure mutual exclusivity
                if (buySignalSeries[0] && sellSignalSeries[0])
                {
                    Print("[WARNING] Both buy and sell signals detected - clearing both for safety");
                    buySignalSeries[0] = false;
                    sellSignalSeries[0] = false;
                }

                if (buySignalSeries[0])
                    Print($"[SIGNAL] *** BUY SIGNAL GENERATED *** at {Time[0]:HH:mm:ss}");
                else if (sellSignalSeries[0])
                    Print($"[SIGNAL] *** SELL SIGNAL GENERATED *** at {Time[0]:HH:mm:ss}");
                else
                {
                    // Analyze how close we are to signals
                    AnalyzeSignalProximity(currentLongCondition, currentShortCondition);
                }

                // Draw signals for visualization
                DrawSignals();

                // Send pending Discord alerts at end of bar
                SendPendingAlerts();
            }

            // Execute trading logic on every price change for tick-level exits
            ExecuteTradingLogic();
            }
            catch (Exception ex)
            {
                Print($"[ERROR] OnBarUpdate failed: {ex.Message}");
                Print($"[ERROR] Stack trace: {ex.StackTrace}");
            }
        }

        private double CalculateAlmaOptimized()
        {
            if (almaWeights == null || almaNorm == 0)
                return Close[0]; // Fallback if weights not calculated

            double sum = 0.0;
            for (int i = 0; i < AlmaWindow; i++)
            {
                sum += Close[AlmaWindow - 1 - i] * almaWeights[i];
            }
            return sum / almaNorm;
        }

        // Keep original method for compatibility
        private double CalculateAlma()
        {
            double sum = 0.0;
            double norm = 0.0;
            int m = (int)(AlmaOffset * (AlmaWindow - 1));
            double s = AlmaWindow / (double)AlmaSigma;

            for (int i = 0; i < AlmaWindow; i++)
            {
                double weight = Math.Exp(-((i - m) * (i - m)) / (2 * s * s));
                sum += Close[AlmaWindow - 1 - i] * weight;
                norm += weight;
            }
            return sum / norm;
        }

        private void CalculateWilliamsR()
        {
            double highest = High[HighestBar(High, WprLength)];
            double lowest = Low[LowestBar(Low, WprLength)];

            if (Math.Abs(highest - lowest) < Double.Epsilon)
            {
                wprSeries[0] = wprSeries[1];
            }
            else
            {
                wprSeries[0] = (highest - Close[0]) / (highest - lowest) * -100;
            }
            wprEmaSeries[0] = EMA(wprSeries, WprEmaLength)[0];
        }

        private void CalculateMacd()
        {
            double fastMA = EMA(Close, FastLength)[0];
            double slowMA = EMA(Close, SlowLength)[0];

            macdLineSeries[0] = fastMA - slowMA;
            signalLineSeries[0] = EMA(macdLineSeries, SignalLength)[0];
            histSeries[0] = macdLineSeries[0] - signalLineSeries[0];
        }

        private void CalculateRangeFilter()
        {
            absDiffSeries[0] = Math.Abs(Close[0] - Close[1]);

            double smrng = RfMultiplier * EMA(EMA(absDiffSeries, RfPeriod), RfPeriod * 2 - 1)[0];

            if (CurrentBar == 0)
            {
                filtSeries[0] = Close[0];
            }
            else
            {
                if (Close[0] > filtSeries[1])
                    filtSeries[0] = Math.Max(Close[0] - smrng, filtSeries[1]);
                else if (Close[0] < filtSeries[1])
                    filtSeries[0] = Math.Min(Close[0] + smrng, filtSeries[1]);
                else
                    filtSeries[0] = filtSeries[1];
            }

            upwardSeries[0] = filtSeries[0] > filtSeries[1] ? upwardSeries[1] + 1 : 0;
            downwardSeries[0] = filtSeries[0] < filtSeries[1] ? downwardSeries[1] + 1 : 0;
        }

        private void AnalyzeSignalProximity(bool longCondition, bool shortCondition)
        {
            // Only log every 5 bars to avoid spam, but show when we're close to signals
            if (CurrentBar % 5 == 0)
            {
                bool filterUp = filtSeries[0] > filtSeries[1];
                bool filterDown = filtSeries[0] < filtSeries[1];
                bool macdBullish = histSeries[0] > MacdThreshold;
                bool macdBearish = histSeries[0] < -MacdThreshold;
                bool wprOverbought = wprSeries[0] > WprOverbought;
                bool wprOversold = wprSeries[0] < WprOversold;

                int longScore = (filterUp ? 1 : 0) + (macdBullish ? 1 : 0) + (wprOverbought ? 1 : 0);
                int shortScore = (filterDown ? 1 : 0) + (macdBearish ? 1 : 0) + (wprOversold ? 1 : 0);

                if (longScore >= 2 || shortScore >= 2)
                {
                    string direction = longScore > shortScore ? "LONG" : "SHORT";
                    int score = Math.Max(longScore, shortScore);
                    Print($"[ANALYSIS] Close to {direction} signal ({score}/3 conditions met) - ATR: {atrSeries[0]:F2}");

                    if (longScore >= 2)
                    {
                        double wprDistance = WprOverbought - wprSeries[0];
                        double macdDistance = MacdThreshold - histSeries[0];
                        Print($"[ANALYSIS] Long needs: WPR +{wprDistance:F2} points, MACD +{macdDistance:F4} points");
                    }

                    if (shortScore >= 2)
                    {
                        double wprDistance = wprSeries[0] - WprOversold;
                        double macdDistance = histSeries[0] - (-MacdThreshold);
                        Print($"[ANALYSIS] Short needs: WPR -{wprDistance:F2} points, MACD -{macdDistance:F4} points");
                    }
                }
            }
        }
        #endregion



        #region Trading Logic
        private void ExecuteTradingLogic()
        {
            try
            {
                // PRIORITY 1: Emergency exits first with early returns for critical stops
                if (Position.MarketPosition != MarketPosition.Flat)
                {
                    if (CheckEmergencyExits())
                    {
                        Print("[TRADING] Emergency exit triggered - stopping all other logic");
                        return; // Exit immediately if emergency stop triggered
                    }

                    if (UseTimeWindow && IsTimeWindowEnd())
                    {
                        Print("[TRADING] Time window ending - closing positions");
                        ExitAtTimeWindowEnd();
                        return; // Exit immediately if time window ended
                    }
                }

                // PRIORITY 2: Check time window for new entries (only on first tick of bar)
                if (IsFirstTickOfBar && UseTimeWindow && !IsInTimeWindow())
                {
                    Print("[TRADING] Outside time window - skipping trading logic");
                    return; // Skip trading logic if outside time window
                }

            // PRIORITY 3: Update position tracking
            UpdatePositionTracking();

            // PRIORITY 4: Handle new signals (only on first tick of bar)
            if (IsFirstTickOfBar)
            {
                // Check for new buy signal
                if (buySignalSeries[0] && !LastBuySignalState)
                {
                    Print($"[TRADING] Processing new BUY signal - buySignalSeries[0]: {buySignalSeries[0]}, LastBuySignalState: {LastBuySignalState}");
                    HandleBuySignal();
                    barsSinceLastSignal = 0;
                }
                // Check for new sell signal
                else if (sellSignalSeries[0] && !LastSellSignalState)
                {
                    Print($"[TRADING] Processing new SELL signal - sellSignalSeries[0]: {sellSignalSeries[0]}, LastSellSignalState: {LastSellSignalState}");
                    HandleSellSignal();
                    barsSinceLastSignal = 0;
                }
                else
                {
                    barsSinceLastSignal++;
                }

                // Update signal states AFTER handling signals
                LastBuySignalState = buySignalSeries[0];
                LastSellSignalState = sellSignalSeries[0];
            }

            // PRIORITY 5: Check automated take profit exits (tick-level)
            CheckAutomatedTakeProfitExits();

            // PRIORITY 6: Check partial profit taking (tick-level)
            CheckPartialProfitTaking();

            // PRIORITY 7: Check profit milestones (only on first tick of bar)
            if (IsFirstTickOfBar)
            {
                CheckProfitMilestones();
            }

            // PRIORITY 8: Update ATM strategy status (if using ATM)
            if (UseAtmStrategy)
            {
                UpdateAtmStrategy();
            }
            }
            catch (Exception ex)
            {
                Print($"[ERROR] ExecuteTradingLogic failed: {ex.Message}");
                Print($"[ERROR] Current position: {Position.MarketPosition} {Position.Quantity}");
            }
        }

        private bool IsInTimeWindow()
        {
            if (!UseTimeWindow)
            {
                Print("[TIME] Time window disabled - allowing trading");
                return true;
            }

            try
            {
                TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                TimeSpan currentTime = easternTime.TimeOfDay;

                bool inWindow;
                // Handle overnight sessions (end time < start time)
                if (endTimeSpan < startTimeSpan)
                {
                    inWindow = currentTime >= startTimeSpan || currentTime <= endTimeSpan;
                    Print($"[TIME] Overnight session - Current: {currentTime:hh\\:mm\\:ss}, Window: {startTimeSpan:hh\\:mm\\:ss} to {endTimeSpan:hh\\:mm\\:ss}, In window: {inWindow}");
                }
                else
                {
                    inWindow = currentTime >= startTimeSpan && currentTime <= endTimeSpan;
                    Print($"[TIME] Regular session - Current: {currentTime:hh\\:mm\\:ss}, Window: {startTimeSpan:hh\\:mm\\:ss} to {endTimeSpan:hh\\:mm\\:ss}, In window: {inWindow}");
                }

                return inWindow;
            }
            catch (Exception ex)
            {
                Print($"[ERROR] Time window check failed: {ex.Message}. Allowing trading.");
                return true; // Allow trading if time check fails
            }
        }

        private bool IsTimeWindowEnd()
        {
            if (!UseTimeWindow || !ExitAtWindowEnd)
                return false;

            try
            {
                TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                TimeSpan currentTime = easternTime.TimeOfDay;

                // Check if we're within 1 minute of end time
                TimeSpan timeDiff = endTimeSpan - currentTime;
                return Math.Abs(timeDiff.TotalMinutes) <= 1.0;
            }
            catch (Exception ex)
            {
                Print($"Time window end check failed: {ex.Message}");
                return false;
            }
        }

        private void ExitAtTimeWindowEnd()
        {
            if (Position.MarketPosition == MarketPosition.Long)
            {
                ExitLong("Time Window End");
                QueueAlert("⏰ TIME WINDOW EXIT",
                    $"**LONG POSITION CLOSED - TIME WINDOW END**\n" +
                    $"Contracts: {Position.Quantity}\n" +
                    $"Entry Price: {Position.AveragePrice:F2}\n" +
                    $"Exit Price: {Close[0]:F2}\n" +
                    $"P&L: {((Close[0] - Position.AveragePrice) * Position.Quantity):F2} points",
                    "********"); // Orange
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                ExitShort("Time Window End");
                QueueAlert("⏰ TIME WINDOW EXIT",
                    $"**SHORT POSITION CLOSED - TIME WINDOW END**\n" +
                    $"Contracts: {Math.Abs(Position.Quantity)}\n" +
                    $"Entry Price: {Position.AveragePrice:F2}\n" +
                    $"Exit Price: {Close[0]:F2}\n" +
                    $"P&L: {((Position.AveragePrice - Close[0]) * Math.Abs(Position.Quantity)):F2} points",
                    "********"); // Orange
            }

            Print($"Position closed due to time window end at {DateTime.Now:HH:mm:ss}");
        }

        private void UpdatePositionTracking()
        {
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                barsInPosition++;
                
                // Update position direction tracking
                if (lastPositionDirection != Position.MarketPosition)
                {
                    // Position direction changed, reset counters and TP tracking
                    barsInPosition = 1;
                    partialExitTaken = false;
                    lastPositionDirection = Position.MarketPosition;
                    
                    // Reset automated TP tracking for new position
                    tp1Hit = false;
                    tp2Hit = false;
                    tp3Hit = false;
                    originalPositionSize = Math.Abs(Position.Quantity);
                    remainingPositionSize = originalPositionSize;
                    
                    Print($"New position detected: {Position.MarketPosition} {originalPositionSize} contracts - TP tracking reset");
                }
            }
            else
            {
                // Flat position, reset all tracking
                barsInPosition = 0;
                totalLongContracts = 0;
                totalShortContracts = 0;
                averageLongPrice = 0.0;
                averageShortPrice = 0.0;
                partialExitTaken = false;
                lastPositionDirection = MarketPosition.Flat;
                
                // Reset automated TP tracking
                tp1Hit = false;
                tp2Hit = false;
                tp3Hit = false;
                originalPositionSize = 0;
                remainingPositionSize = 0;
            }
        }

        private void HandleBuySignal()
        {
            Print($"[SIGNAL] Buy Signal Detected at {Time[0]} | Current Position: {Position.MarketPosition} | Quantity: {Position.Quantity}");

            if (SignalExitOnly)
            {
                Print("[SIGNAL] Signal-Only Mode: Buy signal detected but no trades will be placed");
                return;
            }

            if (Position.MarketPosition == MarketPosition.Short)
            {
                // Close all short positions and enter long
                ExitShort("Signal Exit Short");
                Print($"Closing {totalShortContracts} short contracts due to buy signal");
                
                // Send Discord alert for position reversal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🔄 POSITION REVERSAL", 
                        $"**CLOSED ALL SHORT POSITIONS**\n" +
                        $"Closed: {totalShortContracts} contracts\n" +
                        $"Average Exit Price: {Close[0]:F2}\n\n" +
                        $"**ENTERING LONG POSITION**\n" +
                        $"New Position: {InitialContracts} contracts\n" +
                        $"Entry Price: {Close[0]:F2}", 
                        "65280"); // Green color
                }
                
                // Enter initial long position
                PlaceEntryOrder(MarketPosition.Long, InitialContracts, "Initial Long Entry");

                // Place pre-positioned stop and take profit orders (only for market orders)
                if (EntryOrderType == EntryOrderType.Market)
                {
                    PlaceStopAndTakeProfitOrders(MarketPosition.Long, InitialContracts);
                }
            }
            else if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Enter initial long position
                PlaceEntryOrder(MarketPosition.Long, InitialContracts, "Initial Long Entry");

                // Place pre-positioned stop and take profit orders (only for market orders)
                if (EntryOrderType == EntryOrderType.Market)
                {
                    PlaceStopAndTakeProfitOrders(MarketPosition.Long, InitialContracts);
                }

                // Send Discord alert for new position (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🚀 NEW LONG POSITION", 
                        $"**ENTERING BULLISH POSITION**\n" +
                        $"Contracts: {InitialContracts}\n" +
                        $"Entry Price: {Close[0]:F2}\n" +
                        $"Signal: Buy detected", 
                        "65280"); // Green color
                }
            }
            else if (Position.MarketPosition == MarketPosition.Long)
            {
                // Single entry strategy - no scaling in
                Print($"Buy signal detected but already long {Position.Quantity} contracts - ignoring additional entry");
                
                // Send Discord alert for ignored signal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("📊 SIGNAL IGNORED", 
                        $"**BUY SIGNAL WHILE LONG**\n" +
                        $"Current Position: {Position.Quantity} contracts\n" +
                        $"Entry Price: {Position.AveragePrice:F2}\n" +
                        $"Current Price: {Close[0]:F2}\n" +
                        $"Strategy: Single entry only", 
                        "********"); // Orange color
                }
            }
        }

        private void HandleSellSignal()
        {
            Print($"[SIGNAL] Sell Signal Detected at {Time[0]} | Current Position: {Position.MarketPosition} | Quantity: {Position.Quantity}");

            if (SignalExitOnly)
            {
                Print("[SIGNAL] Signal-Only Mode: Sell signal detected but no trades will be placed");
                return;
            }

            if (Position.MarketPosition == MarketPosition.Long)
            {
                // Close all long positions and enter short
                ExitLong("Signal Exit Long");
                Print($"Closing {totalLongContracts} long contracts due to sell signal");
                
                // Send Discord alert for position reversal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🔄 POSITION REVERSAL", 
                        $"**CLOSED ALL LONG POSITIONS**\n" +
                        $"Closed: {totalLongContracts} contracts\n" +
                        $"Average Exit Price: {Close[0]:F2}\n\n" +
                        $"**ENTERING SHORT POSITION**\n" +
                        $"New Position: {InitialContracts} contracts\n" +
                        $"Entry Price: {Close[0]:F2}", 
                        "16711680"); // Red color
                }
                
                // Enter initial short position
                PlaceEntryOrder(MarketPosition.Short, InitialContracts, "Initial Short Entry");

                // Place pre-positioned stop and take profit orders (only for market orders)
                if (EntryOrderType == EntryOrderType.Market)
                {
                    PlaceStopAndTakeProfitOrders(MarketPosition.Short, InitialContracts);
                }
            }
            else if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Enter initial short position
                PlaceEntryOrder(MarketPosition.Short, InitialContracts, "Initial Short Entry");

                // Place pre-positioned stop and take profit orders (only for market orders)
                if (EntryOrderType == EntryOrderType.Market)
                {
                    PlaceStopAndTakeProfitOrders(MarketPosition.Short, InitialContracts);
                }

                // Send Discord alert for new position (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🐻 NEW SHORT POSITION", 
                        $"**ENTERING BEARISH POSITION**\n" +
                        $"Contracts: {InitialContracts}\n" +
                        $"Entry Price: {Close[0]:F2}\n" +
                        $"Signal: Sell detected", 
                        "16711680"); // Red color
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                // Single entry strategy - no scaling in
                Print($"Sell signal detected but already short {Math.Abs(Position.Quantity)} contracts - ignoring additional entry");
                
                // Send Discord alert for ignored signal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("📊 SIGNAL IGNORED", 
                        $"**SELL SIGNAL WHILE SHORT**\n" +
                        $"Current Position: {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Entry Price: {Position.AveragePrice:F2}\n" +
                        $"Current Price: {Close[0]:F2}\n" +
                        $"Strategy: Single entry only", 
                        "********"); // Orange color
                }
            }
        }

        private void PlaceStopAndTakeProfitOrders(MarketPosition direction, int quantity)
        {
            if (!EnableAutomatedTpExits && !UseEmergencyStop)
                return;

            double atrValue = atrSeries[0]; // Use cached ATR value

            // Place emergency stop orders if enabled
            if (UseEmergencyStop && !SignalExitOnly)
            {
                double emergencyStopDistance = EmergencyStopAtrMultiplier * atrValue;
                if (direction == MarketPosition.Long)
                {
                    double stopPrice = Close[0] - emergencyStopDistance;
                    PlaceStopLossOrder(direction, quantity, stopPrice, "Emergency Stop");
                    Print($"Pre-placed emergency {SlOrderType} stop for long position at {stopPrice:F2}");
                }
                else if (direction == MarketPosition.Short)
                {
                    double stopPrice = Close[0] + emergencyStopDistance;
                    PlaceStopLossOrder(direction, quantity, stopPrice, "Emergency Stop");
                    Print($"Pre-placed emergency {SlOrderType} stop for short position at {stopPrice:F2}");
                }
            }

            // Place take profit orders if enabled
            if (EnableAutomatedTpExits)
            {
                PlaceTakeProfitOrders(direction, quantity, atrValue);
            }
        }

        private void PlaceEntryOrder(MarketPosition direction, int quantity, string orderName)
        {
            try
            {
                Print($"[ORDER] Placing {direction} entry order - Type: {EntryOrderType}, Quantity: {quantity}, Name: {orderName}");

                // Check if we should use ATM strategy
                if (UseAtmStrategy)
                {
                    PlaceAtmEntry(direction, quantity, orderName);
                    return;
                }

                switch (EntryOrderType)
                {
                    case EntryOrderType.Market:
                        if (direction == MarketPosition.Long)
                            EnterLong(quantity, orderName);
                        else if (direction == MarketPosition.Short)
                            EnterShort(quantity, orderName);
                        Print($"[ORDER] Placed {direction} {EntryOrderType} entry order: {quantity} contracts at market");
                        break;

                case EntryOrderType.Limit:
                    // For limit entries, place order slightly better than current price
                    double limitPrice = direction == MarketPosition.Long ?
                        Close[0] - (2 * TickSize) : Close[0] + (2 * TickSize);
                    if (direction == MarketPosition.Long)
                        EnterLongLimit(0, true, quantity, limitPrice, orderName);
                    else if (direction == MarketPosition.Short)
                        EnterShortLimit(0, true, quantity, limitPrice, orderName);
                    Print($"Placed {direction} {EntryOrderType} entry order: {quantity} contracts at {limitPrice:F2}");
                    break;

                case EntryOrderType.StopMarket:
                    // For stop market entries, place order slightly worse than current price
                    double stopPrice = direction == MarketPosition.Long ?
                        Close[0] + (2 * TickSize) : Close[0] - (2 * TickSize);
                    if (direction == MarketPosition.Long)
                        EnterLongStopMarket(0, true, quantity, stopPrice, orderName);
                    else if (direction == MarketPosition.Short)
                        EnterShortStopMarket(0, true, quantity, stopPrice, orderName);
                    Print($"Placed {direction} {EntryOrderType} entry order: {quantity} contracts at {stopPrice:F2}");
                    break;

                case EntryOrderType.StopLimit:
                    // For stop limit entries, use stop price slightly worse and limit price at current
                    double stopLimitPrice = direction == MarketPosition.Long ?
                        Close[0] + (2 * TickSize) : Close[0] - (2 * TickSize);
                    double limitLimitPrice = Close[0];
                    if (direction == MarketPosition.Long)
                        EnterLongStopLimit(0, true, quantity, limitLimitPrice, stopLimitPrice, orderName);
                    else if (direction == MarketPosition.Short)
                        EnterShortStopLimit(0, true, quantity, limitLimitPrice, stopLimitPrice, orderName);
                    Print($"[ORDER] Placed {direction} {EntryOrderType} entry order: {quantity} contracts, stop: {stopLimitPrice:F2}, limit: {limitLimitPrice:F2}");
                    break;
            }
            }
            catch (Exception ex)
            {
                Print($"[ERROR] Failed to place entry order: {ex.Message}");
                Print($"[ERROR] Direction: {direction}, Quantity: {quantity}, Order Type: {EntryOrderType}");
            }
        }

        private void PlaceStopLossOrder(MarketPosition direction, int quantity, double stopPrice, string orderName)
        {
            switch (SlOrderType)
            {
                case StopLossOrderType.Market:
                    // Market orders are placed when stop is triggered in CheckEmergencyExits
                    Print($"Stop loss configured as Market order - will execute when price hits {stopPrice:F2}");
                    break;

                case StopLossOrderType.StopMarket:
                    if (direction == MarketPosition.Long)
                        ExitLongStopMarket(0, true, quantity, stopPrice, orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortStopMarket(0, true, quantity, stopPrice, orderName, "Short Entry");
                    break;

                case StopLossOrderType.StopLimit:
                    // For stop limit, use stop price as trigger and limit price slightly worse
                    double limitPrice = direction == MarketPosition.Long ?
                        stopPrice - (2 * TickSize) : stopPrice + (2 * TickSize);
                    if (direction == MarketPosition.Long)
                        ExitLongStopLimit(0, true, quantity, limitPrice, stopPrice, orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortStopLimit(0, true, quantity, limitPrice, stopPrice, orderName, "Short Entry");
                    break;
            }
        }

        private void PlaceTakeProfitOrder(MarketPosition direction, int quantity, double targetPrice, string orderName)
        {
            switch (TpOrderType)
            {
                case TakeProfitOrderType.Market:
                    // Market orders are placed when target is hit in CheckAutomatedTakeProfitExits
                    Print($"Take profit configured as Market order - will execute when price hits {targetPrice:F2}");
                    break;

                case TakeProfitOrderType.Limit:
                    if (direction == MarketPosition.Long)
                        ExitLongLimit(0, true, quantity, targetPrice, orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortLimit(0, true, quantity, targetPrice, orderName, "Short Entry");
                    break;

                case TakeProfitOrderType.StopLimit:
                    // For stop limit TP, use target as trigger and limit slightly better
                    double limitPrice = direction == MarketPosition.Long ?
                        targetPrice + (2 * TickSize) : targetPrice - (2 * TickSize);
                    if (direction == MarketPosition.Long)
                        ExitLongStopLimit(0, true, quantity, limitPrice, targetPrice, orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortStopLimit(0, true, quantity, limitPrice, targetPrice, orderName, "Short Entry");
                    break;
            }
        }

        private void PlaceEmergencyExitOrder(MarketPosition direction, int quantity, string orderName)
        {
            switch (EmergencyOrderType)
            {
                case EmergencyExitOrderType.Market:
                    if (direction == MarketPosition.Long)
                        ExitLong(orderName);
                    else if (direction == MarketPosition.Short)
                        ExitShort(orderName);
                    break;

                case EmergencyExitOrderType.StopMarket:
                    // Use current price as stop trigger for immediate execution
                    if (direction == MarketPosition.Long)
                        ExitLongStopMarket(0, true, quantity, Close[0], orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortStopMarket(0, true, quantity, Close[0], orderName, "Short Entry");
                    break;

                case EmergencyExitOrderType.StopLimit:
                    // Use current price as stop and slightly worse price as limit
                    double limitPrice = direction == MarketPosition.Long ?
                        Close[0] - (5 * TickSize) : Close[0] + (5 * TickSize);
                    if (direction == MarketPosition.Long)
                        ExitLongStopLimit(0, true, quantity, limitPrice, Close[0], orderName, "Long Entry");
                    else if (direction == MarketPosition.Short)
                        ExitShortStopLimit(0, true, quantity, limitPrice, Close[0], orderName, "Short Entry");
                    break;
            }
        }

        private void PlaceTakeProfitOrders(MarketPosition direction, int quantity, double atrValue)
        {
            double profitTargetDistance = PartialExitProfitMultiplier * atrValue;

            // TP1 - 30% exit
            if (NumberOfTakeProfitLevels >= 1)
            {
                double tp1Price = direction == MarketPosition.Long ?
                    Close[0] + (profitTargetDistance * TakeProfitSpacing) :
                    Close[0] - (profitTargetDistance * TakeProfitSpacing);
                int tp1Quantity = (int)(quantity * Tp1ExitPercentage);
                if (tp1Quantity > 0)
                {
                    PlaceTakeProfitOrder(direction, tp1Quantity, tp1Price, "TP1");
                    Print($"Pre-placed TP1 {TpOrderType} order: {tp1Quantity} contracts at {tp1Price:F2}");
                }
            }

            // TP2 - 40% exit
            if (NumberOfTakeProfitLevels >= 2)
            {
                double tp2Price = direction == MarketPosition.Long ?
                    Close[0] + (profitTargetDistance * TakeProfitSpacing * 2) :
                    Close[0] - (profitTargetDistance * TakeProfitSpacing * 2);
                int tp2Quantity = (int)(quantity * Tp2ExitPercentage);
                if (tp2Quantity > 0)
                {
                    PlaceTakeProfitOrder(direction, tp2Quantity, tp2Price, "TP2");
                    Print($"Pre-placed TP2 {TpOrderType} order: {tp2Quantity} contracts at {tp2Price:F2}");
                }
            }

            // TP3 - Final exit (remaining 30%)
            if (NumberOfTakeProfitLevels >= 3)
            {
                double tp3Price = direction == MarketPosition.Long ?
                    Close[0] + (profitTargetDistance * TakeProfitSpacing * 3) :
                    Close[0] - (profitTargetDistance * TakeProfitSpacing * 3);
                int tp3Quantity = (int)(quantity * Tp3ExitPercentage);
                if (tp3Quantity > 0)
                {
                    PlaceTakeProfitOrder(direction, tp3Quantity, tp3Price, "TP3");
                    Print($"Pre-placed TP3 {TpOrderType} order: {tp3Quantity} contracts at {tp3Price:F2}");
                }
            }
        }

        private bool CheckEmergencyExits()
        {
            try
            {
                if (!UseEmergencyStop || Position.MarketPosition == MarketPosition.Flat)
                    return false;

                double atrValue = atrSeries[0]; // Use cached ATR value
                double emergencyStopDistance = EmergencyStopAtrMultiplier * atrValue;

                Print($"[EMERGENCY] Checking emergency exits - ATR: {atrValue:F4}, Stop distance: {emergencyStopDistance:F2}");

            if (Position.MarketPosition == MarketPosition.Long)
            {
                double emergencyStopPrice = averageLongPrice - emergencyStopDistance;
                if (Close[0] <= emergencyStopPrice)
                {
                    PlaceEmergencyExitOrder(MarketPosition.Long, totalLongContracts, "Emergency Stop Long");
                    Print($"Emergency stop triggered for long position at {Close[0]} (stop level: {emergencyStopPrice}) using {EmergencyOrderType}");

                    // Send critical Discord alert immediately
                    if (State == State.Realtime)
                    {
                        QueueAlert("🚨 EMERGENCY STOP",
                            $"**LONG POSITION STOPPED OUT**\n" +
                            $"Contracts: {totalLongContracts}\n" +
                            $"Entry Price: {averageLongPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"Stop Level: {emergencyStopPrice:F2}\n" +
                            $"Order Type: {EmergencyOrderType}\n" +
                            $"Loss: {(Close[0] - averageLongPrice) * totalLongContracts:F2} points",
                            "16711680"); // Red color
                        SendPendingAlerts(); // Send immediately for critical alerts
                    }
                    return true; // Emergency exit triggered
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                double emergencyStopPrice = averageShortPrice + emergencyStopDistance;
                if (Close[0] >= emergencyStopPrice)
                {
                    PlaceEmergencyExitOrder(MarketPosition.Short, totalShortContracts, "Emergency Stop Short");
                    Print($"Emergency stop triggered for short position at {Close[0]} (stop level: {emergencyStopPrice}) using {EmergencyOrderType}");

                    // Send critical Discord alert immediately
                    if (State == State.Realtime)
                    {
                        QueueAlert("🚨 EMERGENCY STOP",
                            $"**SHORT POSITION STOPPED OUT**\n" +
                            $"Contracts: {totalShortContracts}\n" +
                            $"Entry Price: {averageShortPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"Stop Level: {emergencyStopPrice:F2}\n" +
                            $"Order Type: {EmergencyOrderType}\n" +
                            $"Loss: {(averageShortPrice - Close[0]) * totalShortContracts:F2} points",
                            "16711680"); // Red color
                        SendPendingAlerts(); // Send immediately for critical alerts
                    }
                    return true; // Emergency exit triggered
                }
            }

            return false; // No emergency exit triggered
            }
            catch (Exception ex)
            {
                Print($"[ERROR] CheckEmergencyExits failed: {ex.Message}");
                return false;
            }
        }

        private void CheckAutomatedTakeProfitExits()
        {
            // This method now primarily tracks TP hits via OnOrderUpdate
            // Pre-placed limit orders handle the actual exits
            if (!EnableAutomatedTpExits || Position.MarketPosition == MarketPosition.Flat)
                return;

            double atrValue = atrSeries[0]; // Use cached ATR value
            double profitTargetDistance = PartialExitProfitMultiplier * atrValue;
            
            // Calculate TP levels
            for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
            {
                double multiplier = i * TakeProfitSpacing;
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    takeProfitLevels[i-1] = Position.AveragePrice + (profitTargetDistance * multiplier);
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    takeProfitLevels[i-1] = Position.AveragePrice - (profitTargetDistance * multiplier);
                }
            }

            // Check TP1 (30% exit)
            if (!tp1Hit && NumberOfTakeProfitLevels >= 1)
            {
                bool tp1Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[0])
                    tp1Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[0])
                    tp1Triggered = true;

                if (tp1Triggered)
                {
                    int exitSize = (int)(originalPositionSize * Tp1ExitPercentage);
                    if (exitSize > 0 && exitSize <= Math.Abs(Position.Quantity))
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong(exitSize, "TP1 Auto Exit", "");
                            Print($"TP1 HIT: Exiting {exitSize} long contracts at {Close[0]:F2} (30% of original position)");
                        }
                        else
                        {
                            ExitShort(exitSize, "TP1 Auto Exit", "");
                            Print($"TP1 HIT: Exiting {exitSize} short contracts at {Close[0]:F2} (30% of original position)");
                        }
                        
                        tp1Hit = true;
                        remainingPositionSize -= exitSize;
                        
                        // Discord alert for TP1
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP1 AUTO EXIT", 
                                $"**AUTOMATED TAKE PROFIT 1**\n" +
                                $"Contracts Exited: {exitSize} ({Tp1ExitPercentage*100:F0}%)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[0]:F2}\n" +
                                $"Remaining: {remainingPositionSize} contracts\n" +
                                $"Position: {Position.MarketPosition}", 
                                "65280"); // Green
                        }
                    }
                }
            }

            // Check TP2 (40% exit)
            if (!tp2Hit && tp1Hit && NumberOfTakeProfitLevels >= 2)
            {
                bool tp2Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[1])
                    tp2Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[1])
                    tp2Triggered = true;

                if (tp2Triggered)
                {
                    int exitSize = (int)(originalPositionSize * Tp2ExitPercentage);
                    if (exitSize > 0 && exitSize <= Math.Abs(Position.Quantity))
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong(exitSize, "TP2 Auto Exit", "");
                            Print($"TP2 HIT: Exiting {exitSize} long contracts at {Close[0]:F2} (40% of original position)");
                        }
                        else
                        {
                            ExitShort(exitSize, "TP2 Auto Exit", "");
                            Print($"TP2 HIT: Exiting {exitSize} short contracts at {Close[0]:F2} (40% of original position)");
                        }
                        
                        tp2Hit = true;
                        remainingPositionSize -= exitSize;
                        
                        // Discord alert for TP2
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP2 AUTO EXIT", 
                                $"**AUTOMATED TAKE PROFIT 2**\n" +
                                $"Contracts Exited: {exitSize} ({Tp2ExitPercentage*100:F0}%)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[1]:F2}\n" +
                                $"Remaining: {remainingPositionSize} contracts\n" +
                                $"Position: {Position.MarketPosition}", 
                                "32768"); // Dark green
                        }
                    }
                }
            }

            // Check TP3 (30% exit - FINAL EXIT)
            if (!tp3Hit && tp2Hit && NumberOfTakeProfitLevels >= 3)
            {
                bool tp3Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[2])
                    tp3Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[2])
                    tp3Triggered = true;

                if (tp3Triggered)
                {
                    // Exit ALL remaining position (100% of what's left)
                    int exitSize = Math.Abs(Position.Quantity);
                    if (exitSize > 0)
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong("TP3 Final Exit");
                            Print($"TP3 HIT: Exiting ALL remaining {exitSize} long contracts at {Close[0]:F2} (FINAL EXIT)");
                        }
                        else
                        {
                            ExitShort("TP3 Final Exit");
                            Print($"TP3 HIT: Exiting ALL remaining {exitSize} short contracts at {Close[0]:F2} (FINAL EXIT)");
                        }
                        
                        tp3Hit = true;
                        remainingPositionSize = 0;
                        
                        // Discord alert for TP3 FINAL EXIT
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP3 FINAL EXIT", 
                                $"**COMPLETE POSITION CLOSURE**\n" +
                                $"Contracts Exited: {exitSize} (100% remaining)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[2]:F2}\n" +
                                $"Position: FLAT\n" +
                                $"Strategy: Single entry complete", 
                                "********"); // Gold
                        }
                    }
                }
            }
        }

        private void CheckPartialProfitTaking()
        {
            if (!UsePartialProfitTaking || partialExitTaken || Position.MarketPosition == MarketPosition.Flat)
                return;

            double atrValue = atrSeries[0]; // Use cached ATR value
            double profitTarget = PartialExitProfitMultiplier * atrValue;

            if (Position.MarketPosition == MarketPosition.Long)
            {
                double profitTargetPrice = averageLongPrice + profitTarget;
                if (Close[0] >= profitTargetPrice)
                {
                    int contractsToExit = (int)(Position.Quantity * PartialExitPercentage);
                    if (contractsToExit > 0)
                    {
                        ExitLong(contractsToExit, "Partial Profit Long", "");
                        partialExitTaken = true;
                        Print($"Partial profit exit: {contractsToExit} long contracts at {Close[0]} (target: {profitTargetPrice})");
                        
                        // Send Discord alert for partial profit taking (only in real-time)
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("💰 PARTIAL PROFIT TARGET HIT", 
                                $"**LONG POSITION PARTIAL EXIT**\n" +
                                $"Contracts Sold: {contractsToExit}\n" +
                                $"Remaining: {Position.Quantity - contractsToExit} contracts\n" +
                                $"Entry Price: {averageLongPrice:F2}\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"Target Price: {profitTargetPrice:F2}\n" +
                                $"Profit: {((Close[0] - averageLongPrice) * contractsToExit):F2} points", 
                                "65280"); // Green color
                        }
                    }
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                double profitTargetPrice = averageShortPrice - profitTarget;
                if (Close[0] <= profitTargetPrice)
                {
                    int contractsToExit = (int)(Position.Quantity * PartialExitPercentage);
                    if (contractsToExit > 0)
                    {
                        ExitShort(contractsToExit, "Partial Profit Short", "");
                        partialExitTaken = true;
                        Print($"Partial profit exit: {contractsToExit} short contracts at {Close[0]} (target: {profitTargetPrice})");
                        
                        // Send Discord alert for partial profit taking (only in real-time)
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("💰 PARTIAL PROFIT TARGET HIT", 
                                $"**SHORT POSITION PARTIAL EXIT**\n" +
                                $"Contracts Covered: {contractsToExit}\n" +
                                $"Remaining: {Position.Quantity - contractsToExit} contracts\n" +
                                $"Entry Price: {averageShortPrice:F2}\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"Target Price: {profitTargetPrice:F2}\n" +
                                $"Profit: {((averageShortPrice - Close[0]) * contractsToExit):F2} points", 
                                "65280"); // Green color
                        }
                    }
                }
            }
        }

        private void CheckProfitMilestones()
        {
            // Check if we need to reset daily tracking (new trading day)
            DateTime currentDate = Time[0].Date;
            if (lastResetDate != currentDate)
            {
                ResetDailyProfitTracking();
                lastResetDate = currentDate;
            }

            // Calculate current daily P&L properly for futures
            double currentSessionPnL = Account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar);
            double dailyPnL = currentSessionPnL - sessionStartingPnL;
            
            // Add unrealized P&L from current position
            double unrealizedPnL = 0.0;
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
            }
            
            double totalDailyPnL = dailyPnL + unrealizedPnL;
            
            // Update max daily profit if current is higher
            if (totalDailyPnL > maxDailyProfit)
            {
                maxDailyProfit = totalDailyPnL;
            }

            // Check milestones based on daily P&L (including unrealized) - only in real-time
            if (EnableProfitMilestones && State == State.Realtime)
            {
                string everyoneTag = UseEveryoneTag ? "@everyone " : "";
                string timeframe = GetTimeframeString();
                
                // Get tick value for the instrument (MNQ = $5 per tick)
                double tickValue = Instrument.MasterInstrument.PointValue;
                
                if (!milestone1Hit && maxDailyProfit >= Milestone1)
                {
                    milestone1Hit = true;
                    Print($"Milestone 1 reached: ${Milestone1} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    QueueAlert("🎯 PROFIT MILESTONE 1",
                        $"{everyoneTag}**${Milestone1} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET",
                        "65280"); // Green
                }
                else if (!milestone2Hit && maxDailyProfit >= Milestone2)
                {
                    milestone2Hit = true;
                    Print($"Milestone 2 reached: ${Milestone2} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    QueueAlert("🚀 PROFIT MILESTONE 2",
                        $"{everyoneTag}**${Milestone2} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET",
                        "32768"); // Dark green
                }
                else if (!milestone3Hit && maxDailyProfit >= Milestone3)
                {
                    milestone3Hit = true;
                    Print($"Milestone 3 reached: ${Milestone3} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    QueueAlert("💎 PROFIT MILESTONE 3",
                        $"{everyoneTag}**${Milestone3} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET",
                        "********"); // Gold
                }
            }
        }

        private void ResetDailyProfitTracking()
        {
            sessionStartingPnL = Account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar);
            maxDailyProfit = 0.0;
            milestone1Hit = false;
            milestone2Hit = false;
            milestone3Hit = false;
            lastResetDate = DateTime.Now.Date;
            dailyRealizedPnL = 0.0;
            Print($"Daily profit tracking reset. Starting P&L: ${sessionStartingPnL:F2}");
        }

        private string GetTimeframeString()
        {
            switch (BarsPeriod.BarsPeriodType)
            {
                case BarsPeriodType.Tick:
                    return $"{BarsPeriod.Value} Tick";
                case BarsPeriodType.Volume:
                    return $"{BarsPeriod.Value} Volume";
                case BarsPeriodType.Range:
                    return $"{BarsPeriod.Value} Range";
                case BarsPeriodType.Second:
                    return $"{BarsPeriod.Value}s";
                case BarsPeriodType.Minute:
                    return $"{BarsPeriod.Value}m";
                case BarsPeriodType.Day:
                    return $"{BarsPeriod.Value}D";
                case BarsPeriodType.Week:
                    return $"{BarsPeriod.Value}W";
                case BarsPeriodType.Month:
                    return $"{BarsPeriod.Value}M";
                case BarsPeriodType.Year:
                    return $"{BarsPeriod.Value}Y";
                default:
                    return "Unknown";
            }
        }

        private void PlaceAtmEntry(MarketPosition direction, int quantity, string orderName)
        {
            try
            {
                // Make sure this strategy does not execute against historical data
                if (State == State.Historical)
                {
                    Print("[ATM] ATM strategies cannot be used with historical data");
                    return;
                }

                // Check if we already have an active ATM strategy
                if (orderId.Length > 0 || atmStrategyId.Length > 0)
                {
                    Print("[ATM] ATM strategy already active - skipping new entry");
                    return;
                }

                // Validate ATM template name
                if (string.IsNullOrEmpty(AtmTemplateName))
                {
                    Print("[ATM] ATM Template Name is empty - cannot create ATM strategy");
                    return;
                }

                Print($"[ATM] Creating ATM strategy - Direction: {direction}, Quantity: {quantity}, Template: {AtmTemplateName}");

                // Reset ATM strategy state
                isAtmStrategyCreated = false;
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = GetAtmStrategyUniqueId();

                // Determine order action and price based on entry order type
                OrderAction orderAction = direction == MarketPosition.Long ? OrderAction.Buy : OrderAction.Sell;
                double orderPrice = Close[0];
                OrderType orderType = OrderType.Market;

                // Adjust price and type based on entry order type
                switch (EntryOrderType)
                {
                    case EntryOrderType.Market:
                        orderType = OrderType.Market;
                        orderPrice = 0; // Market orders don't need price
                        break;

                    case EntryOrderType.Limit:
                        orderType = OrderType.Limit;
                        orderPrice = direction == MarketPosition.Long ?
                            Close[0] - (2 * TickSize) : Close[0] + (2 * TickSize);
                        break;

                    case EntryOrderType.StopMarket:
                        orderType = OrderType.StopMarket;
                        orderPrice = direction == MarketPosition.Long ?
                            Close[0] + (2 * TickSize) : Close[0] - (2 * TickSize);
                        break;

                    case EntryOrderType.StopLimit:
                        orderType = OrderType.StopLimit;
                        orderPrice = direction == MarketPosition.Long ?
                            Close[0] + (2 * TickSize) : Close[0] - (2 * TickSize);
                        break;
                }

                // Create ATM strategy
                AtmStrategyCreate(orderAction, orderType, orderPrice, 0, TimeInForce.Day, orderId, AtmTemplateName, atmStrategyId,
                    (atmCallbackErrorCode, atmCallBackId) => {
                        if (atmCallbackErrorCode == ErrorCode.NoError && atmCallBackId == atmStrategyId)
                        {
                            isAtmStrategyCreated = true;
                            Print($"[ATM] ATM strategy created successfully - ID: {atmStrategyId}");
                        }
                        else
                        {
                            Print($"[ATM] ATM strategy creation failed - Error: {atmCallbackErrorCode}");
                            // Reset IDs on failure
                            atmStrategyId = string.Empty;
                            orderId = string.Empty;
                        }
                    });

                Print($"[ATM] ATM strategy creation initiated - Order Type: {orderType}, Price: {orderPrice:F2}");
            }
            catch (Exception ex)
            {
                Print($"[ATM] Error creating ATM strategy: {ex.Message}");
                // Reset IDs on error
                atmStrategyId = string.Empty;
                orderId = string.Empty;
                isAtmStrategyCreated = false;
            }
        }

        private void UpdateAtmStrategy()
        {
            try
            {
                // Check that ATM strategy was created before checking other properties
                if (!isAtmStrategyCreated)
                    return;

                // Check for a pending entry order
                if (orderId.Length > 0)
                {
                    string[] status = GetAtmStrategyEntryOrderStatus(orderId);

                    // If the status call can't find the order specified, the return array length will be zero
                    if (status.GetLength(0) > 0)
                    {
                        Print($"[ATM] Entry order status - Fill Price: {status[0]}, Filled: {status[1]}, State: {status[2]}");

                        // If the order state is terminal, reset the order id value
                        if (status[2] == "Filled" || status[2] == "Cancelled" || status[2] == "Rejected")
                        {
                            if (status[2] == "Filled")
                            {
                                Print($"[ATM] Entry order filled - Price: {status[0]}, Quantity: {status[1]}");

                                // Send Discord alert for ATM entry
                                if (State == State.Realtime && EnableDiscordAlerts)
                                {
                                    MarketPosition position = GetAtmStrategyMarketPosition(atmStrategyId);
                                    string direction = position == MarketPosition.Long ? "LONG" : "SHORT";
                                    string emoji = position == MarketPosition.Long ? "🚀" : "🐻";

                                    QueueAlert($"{emoji} ATM {direction} ENTRY",
                                        $"**ATM STRATEGY ACTIVATED**\n" +
                                        $"Template: {AtmTemplateName}\n" +
                                        $"Direction: {direction}\n" +
                                        $"Quantity: {status[1]}\n" +
                                        $"Entry Price: {status[0]}\n" +
                                        $"ATM ID: {atmStrategyId.Substring(0, 8)}...",
                                        position == MarketPosition.Long ? "65280" : "16711680");
                                }
                            }
                            orderId = string.Empty;
                        }
                    }
                }
                // If the strategy has terminated, reset the strategy id
                else if (atmStrategyId.Length > 0 && GetAtmStrategyMarketPosition(atmStrategyId) == MarketPosition.Flat)
                {
                    Print($"[ATM] ATM strategy completed - Position flat, resetting strategy ID");

                    // Send Discord alert for ATM completion
                    if (State == State.Realtime && EnableDiscordAlerts)
                    {
                        QueueAlert("✅ ATM STRATEGY COMPLETED",
                            $"**POSITION CLOSED**\n" +
                            $"Template: {AtmTemplateName}\n" +
                            $"Final P&L: ${GetAtmStrategyRealizedProfitLoss(atmStrategyId):F2}\n" +
                            $"ATM ID: {atmStrategyId.Substring(0, 8)}...",
                            "********"); // Gold
                    }

                    atmStrategyId = string.Empty;
                    isAtmStrategyCreated = false;
                }

                // Print ATM strategy information (every 10 bars to avoid spam)
                if (atmStrategyId.Length > 0 && CurrentBar % 10 == 0)
                {
                    MarketPosition atmPosition = GetAtmStrategyMarketPosition(atmStrategyId);
                    if (atmPosition != MarketPosition.Flat)
                    {
                        double atmQuantity = GetAtmStrategyPositionQuantity(atmStrategyId);
                        double atmAvgPrice = GetAtmStrategyPositionAveragePrice(atmStrategyId);
                        double atmPnL = GetAtmStrategyUnrealizedProfitLoss(atmStrategyId);

                        Print($"[ATM] Position: {atmPosition} {atmQuantity} @ {atmAvgPrice:F2}, PnL: ${atmPnL:F2}");
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"[ATM] Error updating ATM strategy: {ex.Message}");
            }
        }
        #endregion

        #region Discord Webhook Methods
        private async void SendDiscordAlert(string title, string message, string color = null)
        {
            if (!EnableDiscordAlerts || string.IsNullOrEmpty(DiscordWebhookUrl))
            {
                Print($"[DISCORD] Alert sending disabled - Title: {title}");
                return;
            }

            try
            {
                Print($"[DISCORD] Sending alert - Title: {title}");
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(5); // Low latency timeout
                    
                    string symbolInfo = IncludeSymbol ? $"[{Instrument.FullName}] " : "";
                    string timeframe = GetTimeframeString();
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    string timestamp = easternTime.ToString("HH:mm:ss");
                    
                    string embedColor = color != null ? color : AlertColor;
                    
                    // Add timeframe to message if not already included
                    string enhancedMessage = message;
                    if (!message.Contains("Timeframe:"))
                    {
                        enhancedMessage = $"Timeframe: {timeframe}\n{message}";
                    }
                    
                    // Manual JSON construction to avoid Newtonsoft.Json dependency
                    string jsonPayload = "{\n" +
                        "    \"username\": \"" + EscapeJsonString(AlertUsername) + "\",\n" +
                        "    \"embeds\": [{\n" +
                        "        \"title\": \"" + EscapeJsonString(symbolInfo + title) + "\",\n" +
                        "        \"description\": \"" + EscapeJsonString(enhancedMessage) + "\",\n" +
                        "        \"color\": " + embedColor + ",\n" +
                        "        \"timestamp\": \"" + DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") + "\",\n" +
                        "        \"footer\": {\n" +
                        "            \"text\": \"Time: " + timestamp + " ET\"\n" +
                        "        }\n" +
                        "    }]\n" +
                        "}";

                    var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(DiscordWebhookUrl, content);

                    if (response.IsSuccessStatusCode)
                    {
                        Print($"[DISCORD] Alert sent successfully - Title: {title}");
                    }
                    else
                    {
                        Print($"[DISCORD] Webhook failed: {response.StatusCode} - Title: {title}");
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"[DISCORD] Alert error: {ex.Message} - Title: {title}");
            }
        }

        private string EscapeJsonString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "";
                
            return input.Replace("\\", "\\\\")
                       .Replace("\"", "\\\"")
                       .Replace("\n", "\\n")
                       .Replace("\r", "\\r")
                       .Replace("\t", "\\t");
        }

        private void SendDiscordAlertSync(string title, string message, string color = null)
        {
            if (!EnableDiscordAlerts || string.IsNullOrEmpty(DiscordWebhookUrl))
                return;

            // Use Task.Run for fire-and-forget async operation to maintain low latency
            Task.Run(() => SendDiscordAlert(title, message, color));
        }

        private void QueueAlert(string title, string message, string color = null)
        {
            if (!EnableDiscordAlerts || string.IsNullOrEmpty(DiscordWebhookUrl))
            {
                Print($"[DISCORD] Alert queuing disabled or no webhook URL - Title: {title}");
                return;
            }

            pendingAlerts.Add(new AlertMessage
            {
                Title = title,
                Message = message,
                Color = color ?? AlertColor,
                Timestamp = DateTime.Now
            });

            Print($"[DISCORD] Alert queued - Title: {title}, Total pending: {pendingAlerts.Count}");
        }

        private void SendPendingAlerts()
        {
            if (pendingAlerts.Count == 0)
            {
                Print("[DISCORD] No pending alerts to send");
                return;
            }

            if (DateTime.Now - lastAlertBatch < alertBatchInterval)
            {
                Print($"[DISCORD] Batch interval not reached - {pendingAlerts.Count} alerts waiting");
                return;
            }

            Print($"[DISCORD] Sending {pendingAlerts.Count} pending alerts");
            foreach (var alert in pendingAlerts)
            {
                Task.Run(() => SendDiscordAlert(alert.Title, alert.Message, alert.Color));
            }

            pendingAlerts.Clear();
            lastAlertBatch = DateTime.Now;
            Print("[DISCORD] All pending alerts sent and queue cleared");
        }
        #endregion

        #region Order Management
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string comment)
        {
            try
            {
                Print($"[ORDER_UPDATE] Order: {order.Name}, State: {orderState}, Filled: {filled}/{quantity}, Price: {averageFillPrice:F2}, Error: {error}");

                // Handle entry order updates
                if (order.Name.Contains("Long Entry") || order.Name.Contains("Short Entry"))
                {
                    entryOrder = order;
                    Print($"[ORDER_UPDATE] Entry order updated - {order.Name}: {orderState}");

                    // If the entry order is filled, update position tracking
                    if (orderState == OrderState.Filled)
                {
                    if (order.Name.Contains("Long Entry"))
                    {
                        UpdateLongPositionTracking(order.Filled, order.AverageFillPrice);

                        // Place stop and take profit orders for non-market entry orders
                        if (EntryOrderType != EntryOrderType.Market)
                        {
                            PlaceStopAndTakeProfitOrders(MarketPosition.Long, order.Filled);
                            Print($"Entry order filled: {order.Filled} long contracts at {order.AverageFillPrice:F2}. Placing protective orders.");
                        }
                        // For market orders, emergency stops are already placed in PlaceStopAndTakeProfitOrders
                        else if (UseEmergencyStop && !SignalExitOnly)
                        {
                            double emergencyStopPrice = order.AverageFillPrice - EmergencyStopAtrMultiplier * atrSeries[0];
                            PlaceStopLossOrder(MarketPosition.Long, order.Filled, emergencyStopPrice, "Emergency Stop");
                        }
                    }
                    else if (order.Name.Contains("Short Entry"))
                    {
                        UpdateShortPositionTracking(order.Filled, order.AverageFillPrice);

                        // Place stop and take profit orders for non-market entry orders
                        if (EntryOrderType != EntryOrderType.Market)
                        {
                            PlaceStopAndTakeProfitOrders(MarketPosition.Short, order.Filled);
                            Print($"Entry order filled: {order.Filled} short contracts at {order.AverageFillPrice:F2}. Placing protective orders.");
                        }
                        // For market orders, emergency stops are already placed in PlaceStopAndTakeProfitOrders
                        else if (UseEmergencyStop && !SignalExitOnly)
                        {
                            double emergencyStopPrice = order.AverageFillPrice + EmergencyStopAtrMultiplier * atrSeries[0];
                            PlaceStopLossOrder(MarketPosition.Short, order.Filled, emergencyStopPrice, "Emergency Stop");
                        }
                    }
                    
                    Print($"Order Filled: {order.Name} | Quantity: {order.Filled} | Price: {order.AverageFillPrice:F2} | Time: {time}");
                }
            }
            
            // Handle automated TP exit orders
            if (order.Name.Contains("TP") && order.Name.Contains("Auto Exit") && orderState == OrderState.Filled)
            {
                Print($"Automated TP Exit Filled: {order.Name} | Quantity: {order.Filled} | Price: {order.AverageFillPrice:F2}");
                
                // Update remaining position size tracking
                remainingPositionSize = Math.Abs(Position.Quantity);
                
                // Log the successful TP exit
                double exitProfit = 0;
                if (order.Name.Contains("Long"))
                {
                    exitProfit = (order.AverageFillPrice - Position.AveragePrice) * order.Filled;
                }
                else if (order.Name.Contains("Short"))
                {
                    exitProfit = (Position.AveragePrice - order.AverageFillPrice) * order.Filled;
                }
                
                Print($"TP Exit Profit: ${exitProfit:F2} | Remaining Position: {remainingPositionSize} contracts");
            }

            // Reset order references when orders are cancelled or rejected
            if (orderState == OrderState.Cancelled || orderState == OrderState.Rejected)
            {
                if (order == entryOrder)
                    entryOrder = null;
                else if (order == stopLossOrder)
                    stopLossOrder = null;
                else if (order == profitTargetOrder)
                    profitTargetOrder = null;

                Print($"[ORDER_UPDATE] Order {orderState}: {order.Name} | Reason: {comment}");
            }
            }
            catch (Exception ex)
            {
                Print($"[ERROR] OnOrderUpdate failed: {ex.Message}");
                Print($"[ERROR] Order: {order?.Name ?? "null"}, State: {orderState}");
            }
        }

        private void UpdateLongPositionTracking(int filledQuantity, double fillPrice)
        {
            // Use NinjaTrader's built-in position tracking instead of manual tracking
            totalLongContracts = Position.Quantity;
            averageLongPrice = Position.AveragePrice;

            Print($"[POSITION] Long Position Updated: {totalLongContracts} contracts @ avg price {averageLongPrice:F2} (filled: {filledQuantity} @ {fillPrice:F2})");
        }

        private void UpdateShortPositionTracking(int filledQuantity, double fillPrice)
        {
            // Use NinjaTrader's built-in position tracking instead of manual tracking
            totalShortContracts = Math.Abs(Position.Quantity); // Short positions are negative
            averageShortPrice = Position.AveragePrice;

            Print($"[POSITION] Short Position Updated: {totalShortContracts} contracts @ avg price {averageShortPrice:F2} (filled: {filledQuantity} @ {fillPrice:F2})");
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            // Get current ATR for market regime analysis
            double currentATR = ATR(AtrPeriod)[0];
            
            // Determine actual stop and target prices from your strategy
            double stopPrice = 0;
            double targetPrice = 0;
            string exitSignal = "";
            
            // Extract actual stop/target from your strategy logic
            if (UseEmergencyStop && Position.MarketPosition != MarketPosition.Flat)
            {
                double emergencyStopDistance = EmergencyStopAtrMultiplier * currentATR;
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    stopPrice = Position.AveragePrice - emergencyStopDistance;
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    stopPrice = Position.AveragePrice + emergencyStopDistance;
                }
            }
            
            // Enhanced target price calculation using multiple methods
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                if (UsePartialProfitTaking)
                {
                    double profitTargetDistance = PartialExitProfitMultiplier * currentATR;
                    if (Position.MarketPosition == MarketPosition.Long)
                    {
                        targetPrice = Position.AveragePrice + profitTargetDistance;
                    }
                    else
                    {
                        targetPrice = Position.AveragePrice - profitTargetDistance;
                    }
                }
            }
            
            // Determine exit signal based on your strategy logic
            if (buySignalSeries[0] && Position.MarketPosition == MarketPosition.Short)
            {
                exitSignal = "Buy_Signal_Reversal";
            }
            else if (sellSignalSeries[0] && Position.MarketPosition == MarketPosition.Long)
            {
                exitSignal = "Sell_Signal_Reversal";
            }
            else if (execution.Order.Name.Contains("Emergency"))
            {
                exitSignal = "Emergency_Stop";
            }
            else if (execution.Order.Name.Contains("Time"))
            {
                exitSignal = "Time_Based_Exit";
            }
            else if (execution.Order.Name.Contains("Partial"))
            {
                exitSignal = "Partial_Profit";
            }
            else if (execution.Order.Name.Contains("Signal Exit"))
            {
                exitSignal = "Signal_Exit";
            }
            
            // Log with REAL strategy data for accurate quant analysis
            // TradeLogger.LogExecution(this, execution, Position, currentATR, stopPrice, targetPrice, exitSignal);
            
            // Handle execution updates for detailed logging
            if (execution.Order != null)
            {
                string action = execution.Order.OrderAction == OrderAction.Buy ? "BOUGHT" : "SOLD";
                Print($"EXECUTION: {action} {quantity} @ {price:F2} | Order: {execution.Order.Name} | Time: {time}");
                
                // Enhanced position analysis
                if (Position.MarketPosition != MarketPosition.Flat)
                {
                    double unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                    double riskAmount = stopPrice > 0 ? Math.Abs(Position.AveragePrice - stopPrice) * Position.Quantity : 0;
                    double rMultiple = riskAmount > 0 ? unrealizedPnL / riskAmount : 0;
                    
                    Print($"Position Summary: {Position.MarketPosition} {Position.Quantity} @ {Position.AveragePrice:F2}");
                    Print($"Unrealized P&L: {unrealizedPnL:C} | Risk: {riskAmount:F2} | R-Multiple: {rMultiple:F2}");
                    Print($"ATR: {currentATR:F2} | Stop: {stopPrice:F2} | Target: {targetPrice:F2}");
                }
                else
                {
                    Print("Position: FLAT");
                }
            }
        }

        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            // Update MAE/MFE with real market data for accurate analysis
            if (position.MarketPosition != MarketPosition.Flat)
            {
                double currentATR = ATR(AtrPeriod)[0];
                
                // Determine market condition based on your indicators
                string marketCondition = "Normal";
                
                // Use your strategy's indicators for market regime classification
                if (wprSeries[0] > WprOverbought && histSeries[0] > MacdThreshold)
                {
                    marketCondition = "Bullish_Momentum";
                }
                else if (wprSeries[0] < WprOversold && histSeries[0] < -MacdThreshold)
                {
                    marketCondition = "Bearish_Momentum";
                }
                else if (Math.Abs(histSeries[0]) < MacdThreshold / 2)
                {
                    marketCondition = "Sideways";
                }
                else if (currentATR > ATR(AtrPeriod * 2)[0] * 1.5)
                {
                    marketCondition = "High_Volatility";
                }
                else if (currentATR < ATR(AtrPeriod * 2)[0] * 0.7)
                {
                    marketCondition = "Low_Volatility";
                }
                
                // TradeLogger.UpdatePosition(this, position, Close[0], currentATR, marketCondition);
            }
        }
        #endregion

        #region Visualization
        private void DrawSignals()
        {
            // Draw buy signals with enhanced information
            if (buySignalSeries[0] && !LastBuySignalState)
            {
                Draw.ArrowUp(this, "BuySignalArrow_" + CurrentBar, true, 0, Low[0] - 5 * TickSize, Brushes.Lime);
                
                string signalText = Position.MarketPosition == MarketPosition.Flat ? 
                    $"BUY {InitialContracts}" : 
                    $"ADD {AdditionalContracts}";
                    
                Draw.TextFixed(this, "BuySignal_" + CurrentBar, signalText, TextPosition.BottomLeft, 
                    Brushes.Lime, new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
            }

            // Draw sell signals with enhanced information
            if (sellSignalSeries[0] && !LastSellSignalState)
            {
                Draw.ArrowDown(this, "SellSignalArrow_" + CurrentBar, true, 0, High[0] + 5 * TickSize, Brushes.Orange);
                
                string signalText = Position.MarketPosition == MarketPosition.Flat ? 
                    $"SELL {InitialContracts}" : 
                    $"ADD {AdditionalContracts}";
                    
                Draw.TextFixed(this, "SellSignal_" + CurrentBar, signalText, TextPosition.TopLeft, 
                    Brushes.Orange, new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
            }

            // Draw position information
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                string positionInfo = $"{Position.MarketPosition}: {Position.Quantity}@{Position.AveragePrice:F2}";
                double unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                string pnlInfo = $"P&L: {unrealizedPnL:C}";
                
                // Add automated TP tracking info if enabled
                if (EnableAutomatedTpExits && originalPositionSize > 0)
                {
                    string tpStatus = "";
                    if (tp1Hit) tpStatus += "TP1✅ ";
                    if (tp2Hit) tpStatus += "TP2✅ ";
                    if (tp3Hit) tpStatus += "TP3✅ FLAT ";
                    if (string.IsNullOrEmpty(tpStatus)) tpStatus = "No TPs hit";
                    
                    positionInfo += $" | Orig:{originalPositionSize} | {tpStatus.Trim()}";
                }
                
                Draw.TextFixed(this, "PositionInfo", positionInfo, TextPosition.TopRight, 
                    Position.MarketPosition == MarketPosition.Long ? Brushes.Lime : Brushes.Orange, 
                    new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
                    
                Draw.TextFixed(this, "PnLInfo", pnlInfo, TextPosition.TopRight, 
                    unrealizedPnL >= 0 ? Brushes.Green : Brushes.Red, 
                    new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 20);
            }
            else
            {
                RemoveDrawObject("PositionInfo");
                RemoveDrawObject("PnLInfo");
            }

            // Draw take profit levels when in position - ENHANCED VERSION
            // SINGLE ENTRY + GRADUATED SCALE OUT STRATEGY:
            // - Single entry only (no scaling in)
            // - TP1: Exit 30% of position
            // - TP2: Exit 40% of position  
            // - TP3: Exit remaining 30% (FINAL EXIT - position goes FLAT)
            // - ShowTakeProfitLevels: Enable/disable the display
            // - NumberOfTakeProfitLevels: How many levels to show (1-5)
            // - TakeProfitSpacing: Multiplier for spacing between levels
            // - PartialExitProfitMultiplier: Base distance for TP1 (in ATR units)
            if (Position.MarketPosition != MarketPosition.Flat && ShowTakeProfitLevels)
            {
                double atrValue = ATR(AtrPeriod)[0];
                double profitTargetDistance = PartialExitProfitMultiplier * atrValue;
                
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    // Calculate current profit for display
                    double currentProfit = (Close[0] - Position.AveragePrice) * Position.Quantity;
                    
                    // Show current profit status
                    Brush profitColor = currentProfit >= 0 ? Brushes.Green : Brushes.Red;
                    string currentProfitText = $"Current P&L: ${currentProfit:F0}";
                    Draw.TextFixed(this, "CurrentProfitText", currentProfitText, TextPosition.TopRight, 
                        profitColor, new SimpleFont("Arial", 12), Brushes.Transparent, Brushes.Transparent, 40);
                    
                    // Draw multiple take profit levels based on user preference
                    for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
                    {
                        double multiplier = i * TakeProfitSpacing;
                        double takeProfitLevel = Position.AveragePrice + (profitTargetDistance * multiplier);
                        double targetProfit = (takeProfitLevel - Position.AveragePrice) * Position.Quantity;
                        
                        // Color intensity decreases with each level
                        Brush lineColor = i == 1 ? Brushes.LimeGreen : 
                                         i == 2 ? Brushes.Green : 
                                         i == 3 ? Brushes.DarkGreen : 
                                         i == 4 ? Brushes.ForestGreen : Brushes.DarkOliveGreen;
                        
                        int lineWidth = i == 1 ? 2 : 1;
                        
                        Draw.HorizontalLine(this, $"TakeProfit{i}Level", takeProfitLevel, lineColor, DashStyleHelper.Dot, lineWidth);
                        
                        // Create text label
                        string tpText;
                        if (i == 1 && UsePartialProfitTaking)
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} ({(int)(PartialExitPercentage * 100)}% exit) [${targetProfit:F0}]";
                        }
                        else
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} [${targetProfit:F0}]";
                        }
                        
                        // Add TP hit status if automated exits are enabled
                        if (EnableAutomatedTpExits)
                        {
                            bool tpHit = (i == 1 && tp1Hit) || (i == 2 && tp2Hit) || (i == 3 && tp3Hit);
                            if (tpHit)
                            {
                                tpText += " ✅ HIT";
                                lineColor = Brushes.Gray; // Gray out hit levels
                            }
                            else if (i == 1)
                            {
                                tpText += $" ({Tp1ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 2)
                            {
                                tpText += $" ({Tp2ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 3)
                            {
                                tpText += $" (FINAL EXIT - 100% remaining)";
                            }
                        }
                        
                        Draw.Text(this, $"TakeProfitText{i}", tpText, 0, takeProfitLevel + (2 * TickSize), lineColor);
                    }
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    // Calculate current profit for display
                    double currentProfit = (Position.AveragePrice - Close[0]) * Math.Abs(Position.Quantity);
                    
                    // Show current profit status
                    Brush profitColor = currentProfit >= 0 ? Brushes.Green : Brushes.Red;
                    string currentProfitText = $"Current P&L: ${currentProfit:F0}";
                    Draw.TextFixed(this, "CurrentProfitText", currentProfitText, TextPosition.TopRight, 
                        profitColor, new SimpleFont("Arial", 12), Brushes.Transparent, Brushes.Transparent, 40);
                    
                    // Draw multiple take profit levels based on user preference
                    for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
                    {
                        double multiplier = i * TakeProfitSpacing;
                        double takeProfitLevel = Position.AveragePrice - (profitTargetDistance * multiplier);
                        double targetProfit = (Position.AveragePrice - takeProfitLevel) * Math.Abs(Position.Quantity);
                        
                        // Color intensity decreases with each level
                        Brush lineColor = i == 1 ? Brushes.LimeGreen : 
                                         i == 2 ? Brushes.Green : 
                                         i == 3 ? Brushes.DarkGreen : 
                                         i == 4 ? Brushes.ForestGreen : Brushes.DarkOliveGreen;
                        
                        int lineWidth = i == 1 ? 2 : 1;
                        
                        Draw.HorizontalLine(this, $"TakeProfit{i}Level", takeProfitLevel, lineColor, DashStyleHelper.Dot, lineWidth);
                        
                        // Create text label
                        string tpText;
                        if (i == 1 && UsePartialProfitTaking)
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} ({(int)(PartialExitPercentage * 100)}% exit) [${targetProfit:F0}]";
                        }
                        else
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} [${targetProfit:F0}]";
                        }
                        
                        // Add TP hit status if automated exits are enabled
                        if (EnableAutomatedTpExits)
                        {
                            bool tpHit = (i == 1 && tp1Hit) || (i == 2 && tp2Hit) || (i == 3 && tp3Hit);
                            if (tpHit)
                            {
                                tpText += " ✅ HIT";
                                lineColor = Brushes.Gray; // Gray out hit levels
                            }
                            else if (i == 1)
                            {
                                tpText += $" ({Tp1ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 2)
                            {
                                tpText += $" ({Tp2ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 3)
                            {
                                tpText += $" (FINAL EXIT - 100% remaining)";
                            }
                        }
                        
                        Draw.Text(this, $"TakeProfitText{i}", tpText, 0, takeProfitLevel - (2 * TickSize), lineColor);
                    }
                }
            }
            else
            {
                // Remove all take profit lines when flat or disabled
                for (int i = 1; i <= 5; i++) // Remove up to 5 levels to be safe
                {
                    RemoveDrawObject($"TakeProfit{i}Level");
                    RemoveDrawObject($"TakeProfitText{i}");
                }
                RemoveDrawObject("CurrentProfitText");
            }

            // Draw emergency stop levels if enabled
            if (UseEmergencyStop && Position.MarketPosition != MarketPosition.Flat)
            {
                double atrValue = ATR(AtrPeriod)[0];
                double emergencyStopDistance = EmergencyStopAtrMultiplier * atrValue;
                
                if (Position.MarketPosition == MarketPosition.Long && averageLongPrice > 0)
                {
                    double stopLevel = averageLongPrice - emergencyStopDistance;
                    Draw.HorizontalLine(this, "EmergencyStop", stopLevel, Brushes.Red, DashStyleHelper.Dash, 1);
                    
                    // Add text label for the emergency stop level
                    string stopText = $"Emergency Stop: {stopLevel:F2}";
                    Draw.Text(this, "EmergencyStopText", stopText, 0, stopLevel - (2 * TickSize), Brushes.Red);
                }
                else if (Position.MarketPosition == MarketPosition.Short && averageShortPrice > 0)
                {
                    double stopLevel = averageShortPrice + emergencyStopDistance;
                    Draw.HorizontalLine(this, "EmergencyStop", stopLevel, Brushes.Red, DashStyleHelper.Dash, 1);
                    
                    // Add text label for the emergency stop level
                    string stopText = $"Emergency Stop: {stopLevel:F2}";
                    Draw.Text(this, "EmergencyStopText", stopText, 0, stopLevel + (2 * TickSize), Brushes.Red);
                }
            }
            else
            {
                RemoveDrawObject("EmergencyStop");
                RemoveDrawObject("EmergencyStopText");
            }
        }
        #endregion

        #region Properties
        [Browsable(false)]
        [XmlIgnore]
        public Series<double> ALMA
        {
            get { return almaSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> TEMA_Series
        {
            get { return temaSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> RangeFilter
        {
            get { return filtSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<bool> BuySignals
        {
            get { return buySignalSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<bool> SellSignals
        {
            get { return sellSignalSeries; }
        }
        #endregion
    }
} 