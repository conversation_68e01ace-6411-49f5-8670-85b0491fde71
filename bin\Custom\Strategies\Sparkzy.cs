#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using System.Net.Http;
using System.IO;
using System.Net;   
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class sparkzy : Strategy
    {
        #region Variables and Parameters
        // Williams %R Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "WPR Length", Order = 1, GroupName = "Williams %R")]
        public int WprLength { get; set; } = 21;

        [Range(1, int.MaxValue)]
        [Display(Name = "WPR EMA Length", Order = 2, GroupName = "Williams %R")]
        public int WprEmaLength { get; set; } = 13;

        [Display(Name = "WPR Overbought", Order = 3, GroupName = "Williams %R")]
        public double WprOverbought { get; set; } = -20;

        [Display(Name = "WPR Oversold", Order = 4, GroupName = "Williams %R")]
        public double WprOversold { get; set; } = -80;

        // MACD Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "MACD Fast", Order = 5, GroupName = "MACD")]
        public int FastLength { get; set; } = 5;

        [Range(1, int.MaxValue)]
        [Display(Name = "MACD Slow", Order = 6, GroupName = "MACD")]
        public int SlowLength { get; set; } = 8;

        [Range(1, int.MaxValue)]
        [Display(Name = "Signal Length", Order = 7, GroupName = "MACD")]
        public int SignalLength { get; set; } = 5;

        [Display(Name = "MACD Threshold", Order = 8, GroupName = "MACD")]
        public double MacdThreshold { get; set; } = 0.5;

        // Range Filter Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "RF Period", Order = 9, GroupName = "Range Filter")]
        public int RfPeriod { get; set; } = 10;

        [Display(Name = "RF Multiplier", Order = 10, GroupName = "Range Filter")]
        public double RfMultiplier { get; set; } = 1.0;

        // ALMA & TEMA Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "ALMA Window", Order = 11, GroupName = "ALMA")]
        public int AlmaWindow { get; set; } = 34;

        [Display(Name = "ALMA Offset", Order = 12, GroupName = "ALMA")]
        public double AlmaOffset { get; set; } = 0.85;

        [Display(Name = "ALMA Sigma", Order = 13, GroupName = "ALMA")]
        public double AlmaSigma { get; set; } = 6;

        [Range(1, int.MaxValue)]
        [Display(Name = "TEMA Length", Order = 14, GroupName = "TEMA")]
        public int TemaLength { get; set; } = 9;

        // Strategy Parameters
        [Range(1, int.MaxValue)]
        [Display(Name = "Initial Position Size", Order = 15, GroupName = "Position Management")]
        public int InitialContracts { get; set; } = 8;

        [Range(1, int.MaxValue)]
        [Display(Name = "Additional Position Size", Order = 16, GroupName = "Position Management")]
        public int AdditionalContracts { get; set; } = 2;

        [Range(1, int.MaxValue)]
        [Display(Name = "Maximum Total Positions", Order = 17, GroupName = "Position Management")]
        public int MaxTotalPositions { get; set; } = 20;

        [Display(Name = "Signal Exit Only", Order = 18, GroupName = "Position Management")]
        public bool SignalExitOnly { get; set; } = true;

        [Display(Name = "Use Emergency Stop", Order = 19, GroupName = "Risk Management")]
        public bool UseEmergencyStop { get; set; } = false;

        [Display(Name = "Emergency Stop ATR Multiplier", Order = 20, GroupName = "Risk Management")]
        public double EmergencyStopAtrMultiplier { get; set; } = 3.0;

        [Range(1, int.MaxValue)]
        [Display(Name = "ATR Period", Order = 21, GroupName = "Risk Management")]
        public int AtrPeriod { get; set; } = 14;

        [Display(Name = "Use Time-Based Exit", Order = 22, GroupName = "Risk Management")]
        public bool UseTimeBasedExit { get; set; } = false;

        [Range(1, int.MaxValue)]
        [Display(Name = "Max Bars Without Signal", Order = 23, GroupName = "Risk Management")]
        public int MaxBarsWithoutSignal { get; set; } = 50;

        [Display(Name = "Use Partial Profit Taking", Order = 24, GroupName = "Profit Management")]
        public bool UsePartialProfitTaking { get; set; } = false;

        [Display(Name = "Partial Exit Profit Multiplier", Order = 25, GroupName = "Profit Management")]
        public double PartialExitProfitMultiplier { get; set; } = 2.0;

        [Display(Name = "Partial Exit Percentage", Order = 26, GroupName = "Profit Management")]
        public double PartialExitPercentage { get; set; } = 0.3; // 30% of position

        [Display(Name = "Show Take Profit Levels", Order = 27, GroupName = "Profit Management")]
        public bool ShowTakeProfitLevels { get; set; } = true;

        [Range(1, 5)]
        [Display(Name = "Number of TP Levels", Order = 28, GroupName = "Profit Management")]
        public int NumberOfTakeProfitLevels { get; set; } = 3;

        [Display(Name = "TP Level Spacing Multiplier", Order = 29, GroupName = "Profit Management")]
        public double TakeProfitSpacing { get; set; } = 1.0;

        [Display(Name = "Enable Automated TP Exits", Order = 30, GroupName = "Profit Management")]
        public bool EnableAutomatedTpExits { get; set; } = true;

        [Display(Name = "TP1 Exit Percentage", Order = 31, GroupName = "Profit Management")]
        public double Tp1ExitPercentage { get; set; } = 0.30; // 30%

        [Display(Name = "TP2 Exit Percentage", Order = 32, GroupName = "Profit Management")]
        public double Tp2ExitPercentage { get; set; } = 0.40; // 40%

        [Display(Name = "TP3 Exit Percentage", Order = 33, GroupName = "Profit Management")]
        public double Tp3ExitPercentage { get; set; } = 0.30; // 30% (final exit)

        [Display(Name = "Runner Percentage", Order = 34, GroupName = "Profit Management")]
        public double RunnerPercentage { get; set; } = 0.0; // No runner - full exit

        // Legacy parameters (kept for backward compatibility but not used when SignalExitOnly is true)
        [Display(Name = "Stop Loss Ticks (Legacy)", Order = 35, GroupName = "Legacy")]
        public int StopLossTicks { get; set; } = 20;

        [Display(Name = "Profit Target Ticks (Legacy)", Order = 36, GroupName = "Legacy")]
        public int ProfitTargetTicks { get; set; } = 40;

        [Display(Name = "Use Stop Loss (Legacy)", Order = 37, GroupName = "Legacy")]
        public bool UseStopLoss { get; set; } = false;

        [Display(Name = "Use Profit Target (Legacy)", Order = 38, GroupName = "Legacy")]
        public bool UseProfitTarget { get; set; } = false;

        // Discord Webhook Parameters
        [Display(Name = "Enable Discord Alerts", Order = 39, GroupName = "Discord Notifications")]
        public bool EnableDiscordAlerts { get; set; } = false;

        [Display(Name = "Discord Webhook URL", Order = 40, GroupName = "Discord Notifications")]
        public string DiscordWebhookUrl { get; set; } = "";

        [Display(Name = "Alert Username", Order = 41, GroupName = "Discord Notifications")]
        public string AlertUsername { get; set; } = "NinjaTrader Bot";

        [Display(Name = "Include Chart Symbol", Order = 42, GroupName = "Discord Notifications")]
        public bool IncludeSymbol { get; set; } = true;

        [Display(Name = "Alert Color (Hex)", Order = 43, GroupName = "Discord Notifications")]
        public string AlertColor { get; set; } = "3447003"; // Blue color in decimal

        // Profit Milestone Parameters
        [Display(Name = "Enable Profit Milestones", Order = 44, GroupName = "Profit Milestones")]
        public bool EnableProfitMilestones { get; set; } = true;

        [Display(Name = "Milestone 1 ($)", Order = 45, GroupName = "Profit Milestones")]
        public double Milestone1 { get; set; } = 500.0;

        [Display(Name = "Milestone 2 ($)", Order = 46, GroupName = "Profit Milestones")]
        public double Milestone2 { get; set; } = 1000.0;

        [Display(Name = "Milestone 3 ($)", Order = 47, GroupName = "Profit Milestones")]
        public double Milestone3 { get; set; } = 1500.0;

        [Display(Name = "Use @everyone for Milestones", Order = 48, GroupName = "Profit Milestones")]
        public bool UseEveryoneTag { get; set; } = true;

        // Series for calculations
        private Series<double> almaSeries;
        private Series<double> temaSeries;
        private Series<double> wprSeries;
        private Series<double> wprEmaSeries;
        private Series<double> filtSeries;
        private Series<double> upwardSeries;
        private Series<double> downwardSeries;
        private Series<double> macdLineSeries;
        private Series<double> signalLineSeries;
        private Series<double> histSeries;
        private Series<double> absDiffSeries;
        private Series<bool> buySignalSeries;
        private Series<bool> sellSignalSeries;

        // Order management
        private Order entryOrder = null;
        private Order stopLossOrder = null;
        private Order profitTargetOrder = null;

        // Advanced Position Management
        private int totalLongContracts = 0;
        private int totalShortContracts = 0;
        private double averageLongPrice = 0.0;
        private double averageShortPrice = 0.0;
        private int barsInPosition = 0;
        private int barsSinceLastSignal = 0;
        private bool partialExitTaken = false;
        private MarketPosition lastPositionDirection = MarketPosition.Flat;

        // Signal tracking
        private bool LastBuySignalState = false;
        private bool LastSellSignalState = false;

        // Automated Take Profit Tracking
        private bool tp1Hit = false;
        private bool tp2Hit = false;
        private bool tp3Hit = false;
        private int originalPositionSize = 0;
        private int remainingPositionSize = 0;
        private double[] takeProfitLevels = new double[5]; // Support up to 5 TP levels

        // Profit milestone tracking
        private double dailyStartingBalance = 0.0;
        private double maxDailyProfit = 0.0;
        private bool milestone1Hit = false;
        private bool milestone2Hit = false;
        private bool milestone3Hit = false;
        private DateTime lastResetDate = DateTime.MinValue;
        private double dailyRealizedPnL = 0.0;
        private double sessionStartingPnL = 0.0;

        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Trading strategy based on ALMA, TEMA, WPR, MACD, and Range Filter signals";
                Name = "Sparkzy";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                IsInstantiatedOnEachOptimizationIteration = true;
            }
            else if (State == State.DataLoaded)
            {
                almaSeries = new Series<double>(this);
                temaSeries = new Series<double>(this);
                wprSeries = new Series<double>(this);
                wprEmaSeries = new Series<double>(this);
                filtSeries = new Series<double>(this);
                upwardSeries = new Series<double>(this);
                downwardSeries = new Series<double>(this);
                macdLineSeries = new Series<double>(this);
                signalLineSeries = new Series<double>(this);
                histSeries = new Series<double>(this);
                absDiffSeries = new Series<double>(this);
                buySignalSeries = new Series<bool>(this);
                sellSignalSeries = new Series<bool>(this);
                
                // Initialize profit tracking
                ResetDailyProfitTracking();
            }
        }

        #region Core Calculations
        protected override void OnBarUpdate()
        {
            int minBarsRequired = Math.Max(AlmaWindow,
                                  Math.Max(3 * TemaLength,
                                  Math.Max(WprLength,
                                  Math.Max(SlowLength + SignalLength,
                                  RfPeriod * 2))));

            if (CurrentBar < minBarsRequired)
                return;

            // Calculate all indicators
            almaSeries[0] = CalculateAlma();
            temaSeries[0] = TEMA(Close, TemaLength)[0];
            CalculateWilliamsR();
            CalculateMacd();
            CalculateRangeFilter();

            // Calculate signal conditions
            bool currentLongCondition = filtSeries[0] > filtSeries[1] &&
                                        histSeries[0] > MacdThreshold &&
                                        wprSeries[0] > WprOverbought;

            bool currentShortCondition = filtSeries[0] < filtSeries[1] &&
                                         histSeries[0] < -MacdThreshold &&
                                         wprSeries[0] < WprOversold;

            // Generate signals
            buySignalSeries[0] = currentLongCondition && !sellSignalSeries[1];
            sellSignalSeries[0] = currentShortCondition && !buySignalSeries[1];

            // Ensure mutual exclusivity
            if (buySignalSeries[0] && sellSignalSeries[0])
            {
                buySignalSeries[0] = false;
                sellSignalSeries[0] = false;
            }

            // Execute trading logic
            ExecuteTradingLogic();

            // Draw signals for visualization
            DrawSignals();

            // Update signal states
            LastBuySignalState = buySignalSeries[0];
            LastSellSignalState = sellSignalSeries[0];
        }

        private double CalculateAlma()
        {
            double sum = 0.0;
            double norm = 0.0;
            int m = (int)(AlmaOffset * (AlmaWindow - 1));
            double s = AlmaWindow / (double)AlmaSigma;

            for (int i = 0; i < AlmaWindow; i++)
            {
                double weight = Math.Exp(-((i - m) * (i - m)) / (2 * s * s));
                sum += Close[AlmaWindow - 1 - i] * weight;
                norm += weight;
            }
            return sum / norm;
        }

        private void CalculateWilliamsR()
        {
            double highest = High[HighestBar(High, WprLength)];
            double lowest = Low[LowestBar(Low, WprLength)];

            if (Math.Abs(highest - lowest) < Double.Epsilon)
            {
                wprSeries[0] = wprSeries[1];
            }
            else
            {
                wprSeries[0] = (highest - Close[0]) / (highest - lowest) * -100;
            }
            wprEmaSeries[0] = EMA(wprSeries, WprEmaLength)[0];
        }

        private void CalculateMacd()
        {
            double fastMA = EMA(Close, FastLength)[0];
            double slowMA = EMA(Close, SlowLength)[0];

            macdLineSeries[0] = fastMA - slowMA;
            signalLineSeries[0] = EMA(macdLineSeries, SignalLength)[0];
            histSeries[0] = macdLineSeries[0] - signalLineSeries[0];
        }

        private void CalculateRangeFilter()
        {
            absDiffSeries[0] = Math.Abs(Close[0] - Close[1]);

            double smrng = RfMultiplier * EMA(EMA(absDiffSeries, RfPeriod), RfPeriod * 2 - 1)[0];

            if (CurrentBar == 0)
            {
                filtSeries[0] = Close[0];
            }
            else
            {
                if (Close[0] > filtSeries[1])
                    filtSeries[0] = Math.Max(Close[0] - smrng, filtSeries[1]);
                else if (Close[0] < filtSeries[1])
                    filtSeries[0] = Math.Min(Close[0] + smrng, filtSeries[1]);
                else
                    filtSeries[0] = filtSeries[1];
            }

            upwardSeries[0] = filtSeries[0] > filtSeries[1] ? upwardSeries[1] + 1 : 0;
            downwardSeries[0] = filtSeries[0] < filtSeries[1] ? downwardSeries[1] + 1 : 0;
        }
        #endregion

        #region Trading Logic
        private void ExecuteTradingLogic()
        {
            // Update position tracking
            UpdatePositionTracking();
            
            // Check for new buy signal
            if (buySignalSeries[0] && !LastBuySignalState)
            {
                HandleBuySignal();
                barsSinceLastSignal = 0;
            }
            // Check for new sell signal
            else if (sellSignalSeries[0] && !LastSellSignalState)
            {
                HandleSellSignal();
                barsSinceLastSignal = 0;
            }
            else
            {
                barsSinceLastSignal++;
            }

            // Check emergency exits
            CheckEmergencyExits();
            
            // Check automated take profit exits
            CheckAutomatedTakeProfitExits();
            
            // Check partial profit taking
            CheckPartialProfitTaking();
            
            // Check profit milestones
            CheckProfitMilestones();
        }

        private void UpdatePositionTracking()
        {
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                barsInPosition++;
                
                // Update position direction tracking
                if (lastPositionDirection != Position.MarketPosition)
                {
                    // Position direction changed, reset counters and TP tracking
                    barsInPosition = 1;
                    partialExitTaken = false;
                    lastPositionDirection = Position.MarketPosition;
                    
                    // Reset automated TP tracking for new position
                    tp1Hit = false;
                    tp2Hit = false;
                    tp3Hit = false;
                    originalPositionSize = Math.Abs(Position.Quantity);
                    remainingPositionSize = originalPositionSize;
                    
                    Print($"New position detected: {Position.MarketPosition} {originalPositionSize} contracts - TP tracking reset");
                }
            }
            else
            {
                // Flat position, reset all tracking
                barsInPosition = 0;
                totalLongContracts = 0;
                totalShortContracts = 0;
                averageLongPrice = 0.0;
                averageShortPrice = 0.0;
                partialExitTaken = false;
                lastPositionDirection = MarketPosition.Flat;
                
                // Reset automated TP tracking
                tp1Hit = false;
                tp2Hit = false;
                tp3Hit = false;
                originalPositionSize = 0;
                remainingPositionSize = 0;
            }
        }

        private void HandleBuySignal()
        {
            Print($"Buy Signal Detected at {Time[0]} | Current Position: {Position.MarketPosition} | Quantity: {Position.Quantity}");

            if (Position.MarketPosition == MarketPosition.Short)
            {
                // Close all short positions and enter long
                ExitShort("Signal Exit Short");
                Print($"Closing {totalShortContracts} short contracts due to buy signal");
                
                // Send Discord alert for position reversal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🔄 POSITION REVERSAL", 
                        $"**CLOSED ALL SHORT POSITIONS**\n" +
                        $"Closed: {totalShortContracts} contracts\n" +
                        $"Average Exit Price: {Close[0]:F2}\n\n" +
                        $"**ENTERING LONG POSITION**\n" +
                        $"New Position: {InitialContracts} contracts\n" +
                        $"Entry Price: {Close[0]:F2}", 
                        "65280"); // Green color
                }
                
                // Enter initial long position
                EnterLong(InitialContracts, "Initial Long Entry");
                Print($"Entering {InitialContracts} long contracts (initial position)");
            }
            else if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Enter initial long position
                EnterLong(InitialContracts, "Initial Long Entry");
                Print($"Entering {InitialContracts} long contracts (initial position)");
                
                // Send Discord alert for new position (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🚀 NEW LONG POSITION", 
                        $"**ENTERING BULLISH POSITION**\n" +
                        $"Contracts: {InitialContracts}\n" +
                        $"Entry Price: {Close[0]:F2}\n" +
                        $"Signal: Buy detected", 
                        "65280"); // Green color
                }
            }
            else if (Position.MarketPosition == MarketPosition.Long)
            {
                // Single entry strategy - no scaling in
                Print($"Buy signal detected but already long {Position.Quantity} contracts - ignoring additional entry");
                
                // Send Discord alert for ignored signal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("📊 SIGNAL IGNORED", 
                        $"**BUY SIGNAL WHILE LONG**\n" +
                        $"Current Position: {Position.Quantity} contracts\n" +
                        $"Entry Price: {Position.AveragePrice:F2}\n" +
                        $"Current Price: {Close[0]:F2}\n" +
                        $"Strategy: Single entry only", 
                        "********"); // Orange color
                }
            }
        }

        private void HandleSellSignal()
        {
            Print($"Sell Signal Detected at {Time[0]} | Current Position: {Position.MarketPosition} | Quantity: {Position.Quantity}");

            if (Position.MarketPosition == MarketPosition.Long)
            {
                // Close all long positions and enter short
                ExitLong("Signal Exit Long");
                Print($"Closing {totalLongContracts} long contracts due to sell signal");
                
                // Send Discord alert for position reversal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🔄 POSITION REVERSAL", 
                        $"**CLOSED ALL LONG POSITIONS**\n" +
                        $"Closed: {totalLongContracts} contracts\n" +
                        $"Average Exit Price: {Close[0]:F2}\n\n" +
                        $"**ENTERING SHORT POSITION**\n" +
                        $"New Position: {InitialContracts} contracts\n" +
                        $"Entry Price: {Close[0]:F2}", 
                        "16711680"); // Red color
                }
                
                // Enter initial short position
                EnterShort(InitialContracts, "Initial Short Entry");
                Print($"Entering {InitialContracts} short contracts (initial position)");
            }
            else if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Enter initial short position
                EnterShort(InitialContracts, "Initial Short Entry");
                Print($"Entering {InitialContracts} short contracts (initial position)");
                
                // Send Discord alert for new position (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("🐻 NEW SHORT POSITION", 
                        $"**ENTERING BEARISH POSITION**\n" +
                        $"Contracts: {InitialContracts}\n" +
                        $"Entry Price: {Close[0]:F2}\n" +
                        $"Signal: Sell detected", 
                        "16711680"); // Red color
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                // Single entry strategy - no scaling in
                Print($"Sell signal detected but already short {Math.Abs(Position.Quantity)} contracts - ignoring additional entry");
                
                // Send Discord alert for ignored signal (only in real-time)
                if (State == State.Realtime)
                {
                    SendDiscordAlertSync("📊 SIGNAL IGNORED", 
                        $"**SELL SIGNAL WHILE SHORT**\n" +
                        $"Current Position: {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Entry Price: {Position.AveragePrice:F2}\n" +
                        $"Current Price: {Close[0]:F2}\n" +
                        $"Strategy: Single entry only", 
                        "********"); // Orange color
                }
            }
        }

        private void CheckEmergencyExits()
        {
            if (!UseEmergencyStop || Position.MarketPosition == MarketPosition.Flat)
                return;

            double atrValue = ATR(AtrPeriod)[0];
            double emergencyStopDistance = EmergencyStopAtrMultiplier * atrValue;

            if (Position.MarketPosition == MarketPosition.Long)
            {
                double emergencyStopPrice = averageLongPrice - emergencyStopDistance;
                if (Close[0] <= emergencyStopPrice)
                {
                    ExitLong("Emergency Stop Long");
                    Print($"Emergency stop triggered for long position at {Close[0]} (stop level: {emergencyStopPrice})");
                    
                    // Send Discord alert for emergency stop (only in real-time)
                    if (State == State.Realtime)
                    {
                        SendDiscordAlertSync("🚨 EMERGENCY STOP", 
                            $"**LONG POSITION STOPPED OUT**\n" +
                            $"Contracts: {totalLongContracts}\n" +
                            $"Entry Price: {averageLongPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"Stop Level: {emergencyStopPrice:F2}\n" +
                            $"Loss: {((Close[0] - averageLongPrice) * totalLongContracts):F2} points", 
                            "16711680"); // Red color
                    }
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                double emergencyStopPrice = averageShortPrice + emergencyStopDistance;
                if (Close[0] >= emergencyStopPrice)
                {
                    ExitShort("Emergency Stop Short");
                    Print($"Emergency stop triggered for short position at {Close[0]} (stop level: {emergencyStopPrice})");
                    
                    // Send Discord alert for emergency stop (only in real-time)
                    if (State == State.Realtime)
                    {
                        SendDiscordAlertSync("🚨 EMERGENCY STOP", 
                            $"**SHORT POSITION STOPPED OUT**\n" +
                            $"Contracts: {totalShortContracts}\n" +
                            $"Entry Price: {averageShortPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"Stop Level: {emergencyStopPrice:F2}\n" +
                            $"Loss: {((averageShortPrice - Close[0]) * totalShortContracts):F2} points", 
                            "16711680"); // Red color
                    }
                }
            }

            // Time-based exit check
            if (UseTimeBasedExit && barsSinceLastSignal >= MaxBarsWithoutSignal)
            {
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    ExitLong("Time Exit Long");
                    Print($"Time-based exit for long position after {barsSinceLastSignal} bars without signal");
                    
                    // Send Discord alert for time-based exit (only in real-time)
                    if (State == State.Realtime)
                    {
                        SendDiscordAlertSync("⏰ TIME-BASED EXIT", 
                            $"**LONG POSITION CLOSED**\n" +
                            $"Reason: {barsSinceLastSignal} bars without signal\n" +
                            $"Contracts: {totalLongContracts}\n" +
                            $"Entry Price: {averageLongPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"P&L: {((Close[0] - averageLongPrice) * totalLongContracts):F2} points", 
                            "********"); // Orange color
                    }
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    ExitShort("Time Exit Short");
                    Print($"Time-based exit for short position after {barsSinceLastSignal} bars without signal");
                    
                    // Send Discord alert for time-based exit (only in real-time)
                    if (State == State.Realtime)
                    {
                        SendDiscordAlertSync("⏰ TIME-BASED EXIT", 
                            $"**SHORT POSITION CLOSED**\n" +
                            $"Reason: {barsSinceLastSignal} bars without signal\n" +
                            $"Contracts: {totalShortContracts}\n" +
                            $"Entry Price: {averageShortPrice:F2}\n" +
                            $"Exit Price: {Close[0]:F2}\n" +
                            $"P&L: {((averageShortPrice - Close[0]) * totalShortContracts):F2} points", 
                            "********"); // Orange color
                    }
                }
            }
        }

        private void CheckAutomatedTakeProfitExits()
        {
            if (!EnableAutomatedTpExits || Position.MarketPosition == MarketPosition.Flat)
                return;

            double atrValue = ATR(AtrPeriod)[0];
            double profitTargetDistance = PartialExitProfitMultiplier * atrValue;
            
            // Calculate TP levels
            for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
            {
                double multiplier = i * TakeProfitSpacing;
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    takeProfitLevels[i-1] = Position.AveragePrice + (profitTargetDistance * multiplier);
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    takeProfitLevels[i-1] = Position.AveragePrice - (profitTargetDistance * multiplier);
                }
            }

            // Check TP1 (30% exit)
            if (!tp1Hit && NumberOfTakeProfitLevels >= 1)
            {
                bool tp1Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[0])
                    tp1Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[0])
                    tp1Triggered = true;

                if (tp1Triggered)
                {
                    int exitSize = (int)(originalPositionSize * Tp1ExitPercentage);
                    if (exitSize > 0 && exitSize <= Math.Abs(Position.Quantity))
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong(exitSize, "TP1 Auto Exit", "");
                            Print($"TP1 HIT: Exiting {exitSize} long contracts at {Close[0]:F2} (30% of original position)");
                        }
                        else
                        {
                            ExitShort(exitSize, "TP1 Auto Exit", "");
                            Print($"TP1 HIT: Exiting {exitSize} short contracts at {Close[0]:F2} (30% of original position)");
                        }
                        
                        tp1Hit = true;
                        remainingPositionSize -= exitSize;
                        
                        // Discord alert for TP1
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP1 AUTO EXIT", 
                                $"**AUTOMATED TAKE PROFIT 1**\n" +
                                $"Contracts Exited: {exitSize} ({Tp1ExitPercentage*100:F0}%)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[0]:F2}\n" +
                                $"Remaining: {remainingPositionSize} contracts\n" +
                                $"Position: {Position.MarketPosition}", 
                                "65280"); // Green
                        }
                    }
                }
            }

            // Check TP2 (40% exit)
            if (!tp2Hit && tp1Hit && NumberOfTakeProfitLevels >= 2)
            {
                bool tp2Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[1])
                    tp2Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[1])
                    tp2Triggered = true;

                if (tp2Triggered)
                {
                    int exitSize = (int)(originalPositionSize * Tp2ExitPercentage);
                    if (exitSize > 0 && exitSize <= Math.Abs(Position.Quantity))
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong(exitSize, "TP2 Auto Exit", "");
                            Print($"TP2 HIT: Exiting {exitSize} long contracts at {Close[0]:F2} (40% of original position)");
                        }
                        else
                        {
                            ExitShort(exitSize, "TP2 Auto Exit", "");
                            Print($"TP2 HIT: Exiting {exitSize} short contracts at {Close[0]:F2} (40% of original position)");
                        }
                        
                        tp2Hit = true;
                        remainingPositionSize -= exitSize;
                        
                        // Discord alert for TP2
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP2 AUTO EXIT", 
                                $"**AUTOMATED TAKE PROFIT 2**\n" +
                                $"Contracts Exited: {exitSize} ({Tp2ExitPercentage*100:F0}%)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[1]:F2}\n" +
                                $"Remaining: {remainingPositionSize} contracts\n" +
                                $"Position: {Position.MarketPosition}", 
                                "32768"); // Dark green
                        }
                    }
                }
            }

            // Check TP3 (30% exit - FINAL EXIT)
            if (!tp3Hit && tp2Hit && NumberOfTakeProfitLevels >= 3)
            {
                bool tp3Triggered = false;
                if (Position.MarketPosition == MarketPosition.Long && Close[0] >= takeProfitLevels[2])
                    tp3Triggered = true;
                else if (Position.MarketPosition == MarketPosition.Short && Close[0] <= takeProfitLevels[2])
                    tp3Triggered = true;

                if (tp3Triggered)
                {
                    // Exit ALL remaining position (100% of what's left)
                    int exitSize = Math.Abs(Position.Quantity);
                    if (exitSize > 0)
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            ExitLong("TP3 Final Exit");
                            Print($"TP3 HIT: Exiting ALL remaining {exitSize} long contracts at {Close[0]:F2} (FINAL EXIT)");
                        }
                        else
                        {
                            ExitShort("TP3 Final Exit");
                            Print($"TP3 HIT: Exiting ALL remaining {exitSize} short contracts at {Close[0]:F2} (FINAL EXIT)");
                        }
                        
                        tp3Hit = true;
                        remainingPositionSize = 0;
                        
                        // Discord alert for TP3 FINAL EXIT
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("🎯 TP3 FINAL EXIT", 
                                $"**COMPLETE POSITION CLOSURE**\n" +
                                $"Contracts Exited: {exitSize} (100% remaining)\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"TP Level: {takeProfitLevels[2]:F2}\n" +
                                $"Position: FLAT\n" +
                                $"Strategy: Single entry complete", 
                                "********"); // Gold
                        }
                    }
                }
            }
        }

        private void CheckPartialProfitTaking()
        {
            if (!UsePartialProfitTaking || partialExitTaken || Position.MarketPosition == MarketPosition.Flat)
                return;

            double atrValue = ATR(AtrPeriod)[0];
            double profitTarget = PartialExitProfitMultiplier * atrValue;

            if (Position.MarketPosition == MarketPosition.Long)
            {
                double profitTargetPrice = averageLongPrice + profitTarget;
                if (Close[0] >= profitTargetPrice)
                {
                    int contractsToExit = (int)(Position.Quantity * PartialExitPercentage);
                    if (contractsToExit > 0)
                    {
                        ExitLong(contractsToExit, "Partial Profit Long", "");
                        partialExitTaken = true;
                        Print($"Partial profit exit: {contractsToExit} long contracts at {Close[0]} (target: {profitTargetPrice})");
                        
                        // Send Discord alert for partial profit taking (only in real-time)
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("💰 PARTIAL PROFIT TARGET HIT", 
                                $"**LONG POSITION PARTIAL EXIT**\n" +
                                $"Contracts Sold: {contractsToExit}\n" +
                                $"Remaining: {Position.Quantity - contractsToExit} contracts\n" +
                                $"Entry Price: {averageLongPrice:F2}\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"Target Price: {profitTargetPrice:F2}\n" +
                                $"Profit: {((Close[0] - averageLongPrice) * contractsToExit):F2} points", 
                                "65280"); // Green color
                        }
                    }
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                double profitTargetPrice = averageShortPrice - profitTarget;
                if (Close[0] <= profitTargetPrice)
                {
                    int contractsToExit = (int)(Position.Quantity * PartialExitPercentage);
                    if (contractsToExit > 0)
                    {
                        ExitShort(contractsToExit, "Partial Profit Short", "");
                        partialExitTaken = true;
                        Print($"Partial profit exit: {contractsToExit} short contracts at {Close[0]} (target: {profitTargetPrice})");
                        
                        // Send Discord alert for partial profit taking (only in real-time)
                        if (State == State.Realtime)
                        {
                            SendDiscordAlertSync("💰 PARTIAL PROFIT TARGET HIT", 
                                $"**SHORT POSITION PARTIAL EXIT**\n" +
                                $"Contracts Covered: {contractsToExit}\n" +
                                $"Remaining: {Position.Quantity - contractsToExit} contracts\n" +
                                $"Entry Price: {averageShortPrice:F2}\n" +
                                $"Exit Price: {Close[0]:F2}\n" +
                                $"Target Price: {profitTargetPrice:F2}\n" +
                                $"Profit: {((averageShortPrice - Close[0]) * contractsToExit):F2} points", 
                                "65280"); // Green color
                        }
                    }
                }
            }
        }

        private void CheckProfitMilestones()
        {
            // Check if we need to reset daily tracking (new trading day)
            DateTime currentDate = Time[0].Date;
            if (lastResetDate != currentDate)
            {
                ResetDailyProfitTracking();
                lastResetDate = currentDate;
            }

            // Calculate current daily P&L properly for futures
            double currentSessionPnL = Account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar);
            double dailyPnL = currentSessionPnL - sessionStartingPnL;
            
            // Add unrealized P&L from current position
            double unrealizedPnL = 0.0;
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
            }
            
            double totalDailyPnL = dailyPnL + unrealizedPnL;
            
            // Update max daily profit if current is higher
            if (totalDailyPnL > maxDailyProfit)
            {
                maxDailyProfit = totalDailyPnL;
            }

            // Check milestones based on daily P&L (including unrealized) - only in real-time
            if (EnableProfitMilestones && State == State.Realtime)
            {
                string everyoneTag = UseEveryoneTag ? "@everyone " : "";
                string timeframe = GetTimeframeString();
                
                // Get tick value for the instrument (MNQ = $5 per tick)
                double tickValue = Instrument.MasterInstrument.PointValue;
                
                if (!milestone1Hit && maxDailyProfit >= Milestone1)
                {
                    milestone1Hit = true;
                    Print($"Milestone 1 reached: ${Milestone1} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    SendDiscordAlertSync("🎯 PROFIT MILESTONE 1", 
                        $"{everyoneTag}**${Milestone1} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET", 
                        "65280"); // Green
                }
                else if (!milestone2Hit && maxDailyProfit >= Milestone2)
                {
                    milestone2Hit = true;
                    Print($"Milestone 2 reached: ${Milestone2} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    SendDiscordAlertSync("🚀 PROFIT MILESTONE 2", 
                        $"{everyoneTag}**${Milestone2} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET", 
                        "32768"); // Dark green
                }
                else if (!milestone3Hit && maxDailyProfit >= Milestone3)
                {
                    milestone3Hit = true;
                    Print($"Milestone 3 reached: ${Milestone3} daily profit");
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    
                    SendDiscordAlertSync("💎 PROFIT MILESTONE 3", 
                        $"{everyoneTag}**${Milestone3} DAILY PROFIT REACHED!**\n" +
                        $"Timeframe: {timeframe}\n" +
                        $"Current Daily P&L: ${totalDailyPnL:F2}\n" +
                        $"Max Daily Profit: ${maxDailyProfit:F2}\n" +
                        $"Realized P&L: ${dailyPnL:F2}\n" +
                        $"Unrealized P&L: ${unrealizedPnL:F2}\n" +
                        $"Current Position: {Position.MarketPosition} {Math.Abs(Position.Quantity)} contracts\n" +
                        $"Tick Value: ${tickValue}/tick\n" +
                        $"Time: {easternTime:HH:mm:ss} ET", 
                        "********"); // Gold
                }
            }
        }

        private void ResetDailyProfitTracking()
        {
            sessionStartingPnL = Account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar);
            maxDailyProfit = 0.0;
            milestone1Hit = false;
            milestone2Hit = false;
            milestone3Hit = false;
            lastResetDate = DateTime.Now.Date;
            dailyRealizedPnL = 0.0;
            Print($"Daily profit tracking reset. Starting P&L: ${sessionStartingPnL:F2}");
        }

        private string GetTimeframeString()
        {
            switch (BarsPeriod.BarsPeriodType)
            {
                case BarsPeriodType.Tick:
                    return $"{BarsPeriod.Value} Tick";
                case BarsPeriodType.Volume:
                    return $"{BarsPeriod.Value} Volume";
                case BarsPeriodType.Range:
                    return $"{BarsPeriod.Value} Range";
                case BarsPeriodType.Second:
                    return $"{BarsPeriod.Value}s";
                case BarsPeriodType.Minute:
                    return $"{BarsPeriod.Value}m";
                case BarsPeriodType.Day:
                    return $"{BarsPeriod.Value}D";
                case BarsPeriodType.Week:
                    return $"{BarsPeriod.Value}W";
                case BarsPeriodType.Month:
                    return $"{BarsPeriod.Value}M";
                case BarsPeriodType.Year:
                    return $"{BarsPeriod.Value}Y";
                default:
                    return "Unknown";
            }
        }
        #endregion

        #region Discord Webhook Methods
        private async void SendDiscordAlert(string title, string message, string color = null)
        {
            if (!EnableDiscordAlerts || string.IsNullOrEmpty(DiscordWebhookUrl))
                return;

            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(5); // Low latency timeout
                    
                    string symbolInfo = IncludeSymbol ? $"[{Instrument.FullName}] " : "";
                    string timeframe = GetTimeframeString();
                    
                    // Convert to Eastern Time
                    TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                    DateTime easternTime = TimeZoneInfo.ConvertTime(DateTime.Now, easternZone);
                    string timestamp = easternTime.ToString("HH:mm:ss");
                    
                    string embedColor = color != null ? color : AlertColor;
                    
                    // Add timeframe to message if not already included
                    string enhancedMessage = message;
                    if (!message.Contains("Timeframe:"))
                    {
                        enhancedMessage = $"Timeframe: {timeframe}\n{message}";
                    }
                    
                    // Manual JSON construction to avoid Newtonsoft.Json dependency
                    string jsonPayload = "{\n" +
                        "    \"username\": \"" + EscapeJsonString(AlertUsername) + "\",\n" +
                        "    \"embeds\": [{\n" +
                        "        \"title\": \"" + EscapeJsonString(symbolInfo + title) + "\",\n" +
                        "        \"description\": \"" + EscapeJsonString(enhancedMessage) + "\",\n" +
                        "        \"color\": " + embedColor + ",\n" +
                        "        \"timestamp\": \"" + DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") + "\",\n" +
                        "        \"footer\": {\n" +
                        "            \"text\": \"Time: " + timestamp + " ET\"\n" +
                        "        }\n" +
                        "    }]\n" +
                        "}";

                    var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(DiscordWebhookUrl, content);
                    
                    if (!response.IsSuccessStatusCode)
                    {
                        Print($"Discord webhook failed: {response.StatusCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Discord alert error: {ex.Message}");
            }
        }

        private string EscapeJsonString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "";
                
            return input.Replace("\\", "\\\\")
                       .Replace("\"", "\\\"")
                       .Replace("\n", "\\n")
                       .Replace("\r", "\\r")
                       .Replace("\t", "\\t");
        }

        private void SendDiscordAlertSync(string title, string message, string color = null)
        {
            if (!EnableDiscordAlerts || string.IsNullOrEmpty(DiscordWebhookUrl))
                return;

            // Use Task.Run for fire-and-forget async operation to maintain low latency
            Task.Run(() => SendDiscordAlert(title, message, color));
        }
        #endregion

        #region Order Management
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string comment)
        {
            // Handle entry order updates
            if (order.Name.Contains("Long Entry") || order.Name.Contains("Short Entry"))
            {
                entryOrder = order;

                // If the entry order is filled, update position tracking
                if (orderState == OrderState.Filled)
                {
                    if (order.Name.Contains("Long Entry"))
                    {
                        UpdateLongPositionTracking(order.Filled, order.AverageFillPrice);
                        
                        // Set emergency stop if enabled (but not for signal-only mode)
                        if (UseEmergencyStop && !SignalExitOnly)
                        {
                            double emergencyStopPrice = order.AverageFillPrice - EmergencyStopAtrMultiplier * ATR(AtrPeriod)[0];
                            ExitLongStopMarket(0, true, order.Filled, emergencyStopPrice, "Emergency Stop", order.Name);
                        }
                    }
                    else if (order.Name.Contains("Short Entry"))
                    {
                        UpdateShortPositionTracking(order.Filled, order.AverageFillPrice);
                        
                        // Set emergency stop if enabled (but not for signal-only mode)
                        if (UseEmergencyStop && !SignalExitOnly)
                        {
                            double emergencyStopPrice = order.AverageFillPrice + EmergencyStopAtrMultiplier * ATR(AtrPeriod)[0];
                            ExitShortStopMarket(0, true, order.Filled, emergencyStopPrice, "Emergency Stop", order.Name);
                        }
                    }
                    
                    Print($"Order Filled: {order.Name} | Quantity: {order.Filled} | Price: {order.AverageFillPrice:F2} | Time: {time}");
                }
            }
            
            // Handle automated TP exit orders
            if (order.Name.Contains("TP") && order.Name.Contains("Auto Exit") && orderState == OrderState.Filled)
            {
                Print($"Automated TP Exit Filled: {order.Name} | Quantity: {order.Filled} | Price: {order.AverageFillPrice:F2}");
                
                // Update remaining position size tracking
                remainingPositionSize = Math.Abs(Position.Quantity);
                
                // Log the successful TP exit
                double exitProfit = 0;
                if (order.Name.Contains("Long"))
                {
                    exitProfit = (order.AverageFillPrice - Position.AveragePrice) * order.Filled;
                }
                else if (order.Name.Contains("Short"))
                {
                    exitProfit = (Position.AveragePrice - order.AverageFillPrice) * order.Filled;
                }
                
                Print($"TP Exit Profit: ${exitProfit:F2} | Remaining Position: {remainingPositionSize} contracts");
            }

            // Reset order references when orders are cancelled or rejected
            if (orderState == OrderState.Cancelled || orderState == OrderState.Rejected)
            {
                if (order == entryOrder)
                    entryOrder = null;
                else if (order == stopLossOrder)
                    stopLossOrder = null;
                else if (order == profitTargetOrder)
                    profitTargetOrder = null;
                    
                Print($"Order {orderState}: {order.Name} | Reason: {comment}");
            }
        }

        private void UpdateLongPositionTracking(int filledQuantity, double fillPrice)
        {
            // Use NinjaTrader's built-in position tracking instead of manual tracking
            totalLongContracts = Position.Quantity;
            averageLongPrice = Position.AveragePrice;
            
            Print($"Long Position Updated: {totalLongContracts} contracts @ avg price {averageLongPrice:F2}");
        }

        private void UpdateShortPositionTracking(int filledQuantity, double fillPrice)
        {
            // Use NinjaTrader's built-in position tracking instead of manual tracking  
            totalShortContracts = Math.Abs(Position.Quantity); // Short positions are negative
            averageShortPrice = Position.AveragePrice;
            
            Print($"Short Position Updated: {totalShortContracts} contracts @ avg price {averageShortPrice:F2}");
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            // Get current ATR for market regime analysis
            double currentATR = ATR(AtrPeriod)[0];
            
            // Determine actual stop and target prices from your strategy
            double stopPrice = 0;
            double targetPrice = 0;
            string exitSignal = "";
            
            // Extract actual stop/target from your strategy logic
            if (UseEmergencyStop && Position.MarketPosition != MarketPosition.Flat)
            {
                double emergencyStopDistance = EmergencyStopAtrMultiplier * currentATR;
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    stopPrice = Position.AveragePrice - emergencyStopDistance;
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    stopPrice = Position.AveragePrice + emergencyStopDistance;
                }
            }
            
            // Enhanced target price calculation using multiple methods
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                if (UsePartialProfitTaking)
                {
                    double profitTargetDistance = PartialExitProfitMultiplier * currentATR;
                    if (Position.MarketPosition == MarketPosition.Long)
                    {
                        targetPrice = Position.AveragePrice + profitTargetDistance;
                    }
                    else
                    {
                        targetPrice = Position.AveragePrice - profitTargetDistance;
                    }
                }
            }
            
            // Determine exit signal based on your strategy logic
            if (buySignalSeries[0] && Position.MarketPosition == MarketPosition.Short)
            {
                exitSignal = "Buy_Signal_Reversal";
            }
            else if (sellSignalSeries[0] && Position.MarketPosition == MarketPosition.Long)
            {
                exitSignal = "Sell_Signal_Reversal";
            }
            else if (execution.Order.Name.Contains("Emergency"))
            {
                exitSignal = "Emergency_Stop";
            }
            else if (execution.Order.Name.Contains("Time"))
            {
                exitSignal = "Time_Based_Exit";
            }
            else if (execution.Order.Name.Contains("Partial"))
            {
                exitSignal = "Partial_Profit";
            }
            else if (execution.Order.Name.Contains("Signal Exit"))
            {
                exitSignal = "Signal_Exit";
            }
            
            // Log with REAL strategy data for accurate quant analysis
            // TradeLogger.LogExecution(this, execution, Position, currentATR, stopPrice, targetPrice, exitSignal);
            
            // Handle execution updates for detailed logging
            if (execution.Order != null)
            {
                string action = execution.Order.OrderAction == OrderAction.Buy ? "BOUGHT" : "SOLD";
                Print($"EXECUTION: {action} {quantity} @ {price:F2} | Order: {execution.Order.Name} | Time: {time}");
                
                // Enhanced position analysis
                if (Position.MarketPosition != MarketPosition.Flat)
                {
                    double unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                    double riskAmount = stopPrice > 0 ? Math.Abs(Position.AveragePrice - stopPrice) * Position.Quantity : 0;
                    double rMultiple = riskAmount > 0 ? unrealizedPnL / riskAmount : 0;
                    
                    Print($"Position Summary: {Position.MarketPosition} {Position.Quantity} @ {Position.AveragePrice:F2}");
                    Print($"Unrealized P&L: {unrealizedPnL:C} | Risk: {riskAmount:F2} | R-Multiple: {rMultiple:F2}");
                    Print($"ATR: {currentATR:F2} | Stop: {stopPrice:F2} | Target: {targetPrice:F2}");
                }
                else
                {
                    Print("Position: FLAT");
                }
            }
        }

        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            // Update MAE/MFE with real market data for accurate analysis
            if (position.MarketPosition != MarketPosition.Flat)
            {
                double currentATR = ATR(AtrPeriod)[0];
                
                // Determine market condition based on your indicators
                string marketCondition = "Normal";
                
                // Use your strategy's indicators for market regime classification
                if (wprSeries[0] > WprOverbought && histSeries[0] > MacdThreshold)
                {
                    marketCondition = "Bullish_Momentum";
                }
                else if (wprSeries[0] < WprOversold && histSeries[0] < -MacdThreshold)
                {
                    marketCondition = "Bearish_Momentum";
                }
                else if (Math.Abs(histSeries[0]) < MacdThreshold / 2)
                {
                    marketCondition = "Sideways";
                }
                else if (currentATR > ATR(AtrPeriod * 2)[0] * 1.5)
                {
                    marketCondition = "High_Volatility";
                }
                else if (currentATR < ATR(AtrPeriod * 2)[0] * 0.7)
                {
                    marketCondition = "Low_Volatility";
                }
                
                // TradeLogger.UpdatePosition(this, position, Close[0], currentATR, marketCondition);
            }
        }
        #endregion

        #region Visualization
        private void DrawSignals()
        {
            // Draw buy signals with enhanced information
            if (buySignalSeries[0] && !LastBuySignalState)
            {
                Draw.ArrowUp(this, "BuySignalArrow_" + CurrentBar, true, 0, Low[0] - 5 * TickSize, Brushes.Lime);
                
                string signalText = Position.MarketPosition == MarketPosition.Flat ? 
                    $"BUY {InitialContracts}" : 
                    $"ADD {AdditionalContracts}";
                    
                Draw.TextFixed(this, "BuySignal_" + CurrentBar, signalText, TextPosition.BottomLeft, 
                    Brushes.Lime, new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
            }

            // Draw sell signals with enhanced information
            if (sellSignalSeries[0] && !LastSellSignalState)
            {
                Draw.ArrowDown(this, "SellSignalArrow_" + CurrentBar, true, 0, High[0] + 5 * TickSize, Brushes.Orange);
                
                string signalText = Position.MarketPosition == MarketPosition.Flat ? 
                    $"SELL {InitialContracts}" : 
                    $"ADD {AdditionalContracts}";
                    
                Draw.TextFixed(this, "SellSignal_" + CurrentBar, signalText, TextPosition.TopLeft, 
                    Brushes.Orange, new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
            }

            // Draw position information
            if (Position.MarketPosition != MarketPosition.Flat)
            {
                string positionInfo = $"{Position.MarketPosition}: {Position.Quantity}@{Position.AveragePrice:F2}";
                double unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                string pnlInfo = $"P&L: {unrealizedPnL:C}";
                
                // Add automated TP tracking info if enabled
                if (EnableAutomatedTpExits && originalPositionSize > 0)
                {
                    string tpStatus = "";
                    if (tp1Hit) tpStatus += "TP1✅ ";
                    if (tp2Hit) tpStatus += "TP2✅ ";
                    if (tp3Hit) tpStatus += "TP3✅ FLAT ";
                    if (string.IsNullOrEmpty(tpStatus)) tpStatus = "No TPs hit";
                    
                    positionInfo += $" | Orig:{originalPositionSize} | {tpStatus.Trim()}";
                }
                
                Draw.TextFixed(this, "PositionInfo", positionInfo, TextPosition.TopRight, 
                    Position.MarketPosition == MarketPosition.Long ? Brushes.Lime : Brushes.Orange, 
                    new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 0);
                    
                Draw.TextFixed(this, "PnLInfo", pnlInfo, TextPosition.TopRight, 
                    unrealizedPnL >= 0 ? Brushes.Green : Brushes.Red, 
                    new SimpleFont("Arial", 10), Brushes.Transparent, Brushes.Transparent, 20);
            }
            else
            {
                RemoveDrawObject("PositionInfo");
                RemoveDrawObject("PnLInfo");
            }

            // Draw take profit levels when in position - ENHANCED VERSION
            // SINGLE ENTRY + GRADUATED SCALE OUT STRATEGY:
            // - Single entry only (no scaling in)
            // - TP1: Exit 30% of position
            // - TP2: Exit 40% of position  
            // - TP3: Exit remaining 30% (FINAL EXIT - position goes FLAT)
            // - ShowTakeProfitLevels: Enable/disable the display
            // - NumberOfTakeProfitLevels: How many levels to show (1-5)
            // - TakeProfitSpacing: Multiplier for spacing between levels
            // - PartialExitProfitMultiplier: Base distance for TP1 (in ATR units)
            if (Position.MarketPosition != MarketPosition.Flat && ShowTakeProfitLevels)
            {
                double atrValue = ATR(AtrPeriod)[0];
                double profitTargetDistance = PartialExitProfitMultiplier * atrValue;
                
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    // Calculate current profit for display
                    double currentProfit = (Close[0] - Position.AveragePrice) * Position.Quantity;
                    
                    // Show current profit status
                    Brush profitColor = currentProfit >= 0 ? Brushes.Green : Brushes.Red;
                    string currentProfitText = $"Current P&L: ${currentProfit:F0}";
                    Draw.TextFixed(this, "CurrentProfitText", currentProfitText, TextPosition.TopRight, 
                        profitColor, new SimpleFont("Arial", 12), Brushes.Transparent, Brushes.Transparent, 40);
                    
                    // Draw multiple take profit levels based on user preference
                    for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
                    {
                        double multiplier = i * TakeProfitSpacing;
                        double takeProfitLevel = Position.AveragePrice + (profitTargetDistance * multiplier);
                        double targetProfit = (takeProfitLevel - Position.AveragePrice) * Position.Quantity;
                        
                        // Color intensity decreases with each level
                        Brush lineColor = i == 1 ? Brushes.LimeGreen : 
                                         i == 2 ? Brushes.Green : 
                                         i == 3 ? Brushes.DarkGreen : 
                                         i == 4 ? Brushes.ForestGreen : Brushes.DarkOliveGreen;
                        
                        int lineWidth = i == 1 ? 2 : 1;
                        
                        Draw.HorizontalLine(this, $"TakeProfit{i}Level", takeProfitLevel, lineColor, DashStyleHelper.Dot, lineWidth);
                        
                        // Create text label
                        string tpText;
                        if (i == 1 && UsePartialProfitTaking)
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} ({(int)(PartialExitPercentage * 100)}% exit) [${targetProfit:F0}]";
                        }
                        else
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} [${targetProfit:F0}]";
                        }
                        
                        // Add TP hit status if automated exits are enabled
                        if (EnableAutomatedTpExits)
                        {
                            bool tpHit = (i == 1 && tp1Hit) || (i == 2 && tp2Hit) || (i == 3 && tp3Hit);
                            if (tpHit)
                            {
                                tpText += " ✅ HIT";
                                lineColor = Brushes.Gray; // Gray out hit levels
                            }
                            else if (i == 1)
                            {
                                tpText += $" ({Tp1ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 2)
                            {
                                tpText += $" ({Tp2ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 3)
                            {
                                tpText += $" (FINAL EXIT - 100% remaining)";
                            }
                        }
                        
                        Draw.Text(this, $"TakeProfitText{i}", tpText, 0, takeProfitLevel + (2 * TickSize), lineColor);
                    }
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    // Calculate current profit for display
                    double currentProfit = (Position.AveragePrice - Close[0]) * Math.Abs(Position.Quantity);
                    
                    // Show current profit status
                    Brush profitColor = currentProfit >= 0 ? Brushes.Green : Brushes.Red;
                    string currentProfitText = $"Current P&L: ${currentProfit:F0}";
                    Draw.TextFixed(this, "CurrentProfitText", currentProfitText, TextPosition.TopRight, 
                        profitColor, new SimpleFont("Arial", 12), Brushes.Transparent, Brushes.Transparent, 40);
                    
                    // Draw multiple take profit levels based on user preference
                    for (int i = 1; i <= NumberOfTakeProfitLevels; i++)
                    {
                        double multiplier = i * TakeProfitSpacing;
                        double takeProfitLevel = Position.AveragePrice - (profitTargetDistance * multiplier);
                        double targetProfit = (Position.AveragePrice - takeProfitLevel) * Math.Abs(Position.Quantity);
                        
                        // Color intensity decreases with each level
                        Brush lineColor = i == 1 ? Brushes.LimeGreen : 
                                         i == 2 ? Brushes.Green : 
                                         i == 3 ? Brushes.DarkGreen : 
                                         i == 4 ? Brushes.ForestGreen : Brushes.DarkOliveGreen;
                        
                        int lineWidth = i == 1 ? 2 : 1;
                        
                        Draw.HorizontalLine(this, $"TakeProfit{i}Level", takeProfitLevel, lineColor, DashStyleHelper.Dot, lineWidth);
                        
                        // Create text label
                        string tpText;
                        if (i == 1 && UsePartialProfitTaking)
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} ({(int)(PartialExitPercentage * 100)}% exit) [${targetProfit:F0}]";
                        }
                        else
                        {
                            tpText = $"TP{i}: {takeProfitLevel:F2} [${targetProfit:F0}]";
                        }
                        
                        // Add TP hit status if automated exits are enabled
                        if (EnableAutomatedTpExits)
                        {
                            bool tpHit = (i == 1 && tp1Hit) || (i == 2 && tp2Hit) || (i == 3 && tp3Hit);
                            if (tpHit)
                            {
                                tpText += " ✅ HIT";
                                lineColor = Brushes.Gray; // Gray out hit levels
                            }
                            else if (i == 1)
                            {
                                tpText += $" ({Tp1ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 2)
                            {
                                tpText += $" ({Tp2ExitPercentage*100:F0}% exit)";
                            }
                            else if (i == 3)
                            {
                                tpText += $" (FINAL EXIT - 100% remaining)";
                            }
                        }
                        
                        Draw.Text(this, $"TakeProfitText{i}", tpText, 0, takeProfitLevel - (2 * TickSize), lineColor);
                    }
                }
            }
            else
            {
                // Remove all take profit lines when flat or disabled
                for (int i = 1; i <= 5; i++) // Remove up to 5 levels to be safe
                {
                    RemoveDrawObject($"TakeProfit{i}Level");
                    RemoveDrawObject($"TakeProfitText{i}");
                }
                RemoveDrawObject("CurrentProfitText");
            }

            // Draw emergency stop levels if enabled
            if (UseEmergencyStop && Position.MarketPosition != MarketPosition.Flat)
            {
                double atrValue = ATR(AtrPeriod)[0];
                double emergencyStopDistance = EmergencyStopAtrMultiplier * atrValue;
                
                if (Position.MarketPosition == MarketPosition.Long && averageLongPrice > 0)
                {
                    double stopLevel = averageLongPrice - emergencyStopDistance;
                    Draw.HorizontalLine(this, "EmergencyStop", stopLevel, Brushes.Red, DashStyleHelper.Dash, 1);
                    
                    // Add text label for the emergency stop level
                    string stopText = $"Emergency Stop: {stopLevel:F2}";
                    Draw.Text(this, "EmergencyStopText", stopText, 0, stopLevel - (2 * TickSize), Brushes.Red);
                }
                else if (Position.MarketPosition == MarketPosition.Short && averageShortPrice > 0)
                {
                    double stopLevel = averageShortPrice + emergencyStopDistance;
                    Draw.HorizontalLine(this, "EmergencyStop", stopLevel, Brushes.Red, DashStyleHelper.Dash, 1);
                    
                    // Add text label for the emergency stop level
                    string stopText = $"Emergency Stop: {stopLevel:F2}";
                    Draw.Text(this, "EmergencyStopText", stopText, 0, stopLevel + (2 * TickSize), Brushes.Red);
                }
            }
            else
            {
                RemoveDrawObject("EmergencyStop");
                RemoveDrawObject("EmergencyStopText");
            }
        }
        #endregion

        #region Properties
        [Browsable(false)]
        [XmlIgnore]
        public Series<double> ALMA
        {
            get { return almaSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> TEMA_Series
        {
            get { return temaSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<double> RangeFilter
        {
            get { return filtSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<bool> BuySignals
        {
            get { return buySignalSeries; }
        }

        [Browsable(false)]
        [XmlIgnore]
        public Series<bool> SellSignals
        {
            get { return sellSignalSeries; }
        }
        #endregion
    }
} 