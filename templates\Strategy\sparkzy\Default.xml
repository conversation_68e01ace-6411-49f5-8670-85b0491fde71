﻿<?xml version="1.0" encoding="utf-8"?>
<StrategyTemplate>
  <StrategyType>NinjaTrader.NinjaScript.Strategies.sparkzy</StrategyType>
  <OptimizerType>NinjaTrader.NinjaScript.Optimizers.DefaultOptimizer</OptimizerType>
  <OptimizerParameters>
    <ArrayOfParameterWrapper xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <ParameterWrapper>
        <DisplayName>IsStrategyGenerator</DisplayName>
        <Name>IsStrategyGenerator</Name>
        <Value xsi:type="xsd:boolean">false</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Keep best # results</DisplayName>
        <Name>KeepBestResults</Name>
        <Value xsi:type="xsd:int">10</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>LogTypeName</DisplayName>
        <Name>LogTypeName</Name>
        <Value xsi:type="xsd:string">Optimizer</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Visible</DisplayName>
        <Name>IsVisible</Name>
        <Value xsi:type="xsd:boolean">true</Value>
      </ParameterWrapper>
      <ParameterWrapper>
        <DisplayName>Name</DisplayName>
        <Name>Name</Name>
        <Value xsi:type="xsd:string">Default</Value>
      </ParameterWrapper>
    </ArrayOfParameterWrapper>
  </OptimizerParameters>
  <OptimizationFitness>NinjaTrader.NinjaScript.OptimizationFitnesses.MaxProfitFactor</OptimizationFitness>
  <OptimizationParameters>
    <ArrayOfParameter xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
  </OptimizationParameters>
  <Strategy>
    <sparkzy xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <IsVisible>true</IsVisible>
      <calculate2>OnPriceChange</calculate2>
      <AreLinesConfigurable>true</AreLinesConfigurable>
      <ArePlotsConfigurable>true</ArePlotsConfigurable>
      <BarsPeriodSerializable>
        <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
        <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
        <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
        <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
        <MarketDataType>Last</MarketDataType>
        <PointAndFigurePriceType>Close</PointAndFigurePriceType>
        <ReversalType>Tick</ReversalType>
        <Value>1</Value>
        <Value2>1</Value2>
      </BarsPeriodSerializable>
      <BarsToLoad>0</BarsToLoad>
      <Calculate>OnPriceChange</Calculate>
      <Displacement>0</Displacement>
      <DisplayInDataBox>true</DisplayInDataBox>
      <From>2025-02-26T09:55:42</From>
      <IsAutoScale>true</IsAutoScale>
      <Lines />
      <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
      <Name>Sparkzy</Name>
      <Panel>-1</Panel>
      <Plots />
      <ScaleJustification>Right</ScaleJustification>
      <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
      <To>2025-03-03T09:55:42</To>
      <IsDataSeriesRequired>true</IsDataSeriesRequired>
      <IsOverlay>true</IsOverlay>
      <SelectedValueSeries>0</SelectedValueSeries>
      <Gtd>1800-01-01T00:00:00</Gtd>
      <Template />
      <TimeInForce>Gtc</TimeInForce>
      <BarsPeriodParameter>
        <Increment>1</Increment>
        <Max xsi:type="xsd:int">0</Max>
        <Min xsi:type="xsd:int">0</Min>
        <Name />
        <ParameterTypeSerializable>System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</ParameterTypeSerializable>
        <ValueSerializable>0</ValueSerializable>
      </BarsPeriodParameter>
      <BarsRequiredToTrade>20</BarsRequiredToTrade>
      <Category>NinjaScript</Category>
      <ConnectionLossHandling>Recalculate</ConnectionLossHandling>
      <DaysToLoad>5</DaysToLoad>
      <DefaultQuantity>1</DefaultQuantity>
      <DisconnectDelaySeconds>10</DisconnectDelaySeconds>
      <EntriesPerDirection>1</EntriesPerDirection>
      <EntryHandling>AllEntries</EntryHandling>
      <ExitOnSessionCloseSeconds>30</ExitOnSessionCloseSeconds>
      <IncludeCommission>false</IncludeCommission>
      <InstrumentOrInstrumentList>NQ JUN25</InstrumentOrInstrumentList>
      <IsAggregated>false</IsAggregated>
      <IsExitOnSessionCloseStrategy>true</IsExitOnSessionCloseStrategy>
      <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
      <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
      <IsStableSession>true</IsStableSession>
      <IsTickReplay>false</IsTickReplay>
      <IsTradingHoursBreakLineVisible>true</IsTradingHoursBreakLineVisible>
      <IsWaitUntilFlat>false</IsWaitUntilFlat>
      <NumberRestartAttempts>4</NumberRestartAttempts>
      <OptimizationPeriod>10</OptimizationPeriod>
      <OrderFillResolution>Standard</OrderFillResolution>
      <OrderFillResolutionType>Tick</OrderFillResolutionType>
      <OrderFillResolutionValue>1</OrderFillResolutionValue>
      <RestartsWithinMinutes>5</RestartsWithinMinutes>
      <SetOrderQuantity>Strategy</SetOrderQuantity>
      <Slippage>0</Slippage>
      <StartBehavior>WaitUntilFlat</StartBehavior>
      <StopTargetHandling>PerEntryExecution</StopTargetHandling>
      <SupportsOptimizationGraph>true</SupportsOptimizationGraph>
      <TestPeriod>28</TestPeriod>
      <TradingHoursSerializable />
      <DrawOnPricePanel>false</DrawOnPricePanel>
      <ZOrder>-2147483648</ZOrder>
      <WprLength>21</WprLength>
      <WprEmaLength>13</WprEmaLength>
      <WprOverbought>-20</WprOverbought>
      <WprOversold>-80</WprOversold>
      <FastLength>5</FastLength>
      <SlowLength>8</SlowLength>
      <SignalLength>5</SignalLength>
      <MacdThreshold>0.5</MacdThreshold>
      <RfPeriod>10</RfPeriod>
      <RfMultiplier>1</RfMultiplier>
      <AlmaWindow>34</AlmaWindow>
      <AlmaOffset>0.85</AlmaOffset>
      <AlmaSigma>6</AlmaSigma>
      <TemaLength>9</TemaLength>
      <InitialContracts>8</InitialContracts>
      <AdditionalContracts>2</AdditionalContracts>
      <MaxTotalPositions>20</MaxTotalPositions>
      <SignalExitOnly>false</SignalExitOnly>
      <UseAtmStrategy>true</UseAtmStrategy>
      <AtmTemplateName>ATM_RANGING3</AtmTemplateName>
      <UseEmergencyStop>false</UseEmergencyStop>
      <EmergencyStopAtrMultiplier>3</EmergencyStopAtrMultiplier>
      <AtrPeriod>14</AtrPeriod>
      <UseTimeBasedExit>false</UseTimeBasedExit>
      <MaxBarsWithoutSignal>50</MaxBarsWithoutSignal>
      <UsePartialProfitTaking>false</UsePartialProfitTaking>
      <PartialExitProfitMultiplier>2</PartialExitProfitMultiplier>
      <PartialExitPercentage>0.3</PartialExitPercentage>
      <ShowTakeProfitLevels>true</ShowTakeProfitLevels>
      <NumberOfTakeProfitLevels>3</NumberOfTakeProfitLevels>
      <TakeProfitSpacing>1</TakeProfitSpacing>
      <EnableAutomatedTpExits>true</EnableAutomatedTpExits>
      <Tp1ExitPercentage>0.3</Tp1ExitPercentage>
      <Tp2ExitPercentage>0.4</Tp2ExitPercentage>
      <Tp3ExitPercentage>0.3</Tp3ExitPercentage>
      <RunnerPercentage>0</RunnerPercentage>
      <StopLossTicks>20</StopLossTicks>
      <ProfitTargetTicks>40</ProfitTargetTicks>
      <UseStopLoss>false</UseStopLoss>
      <UseProfitTarget>false</UseProfitTarget>
      <EnableDiscordAlerts>false</EnableDiscordAlerts>
      <DiscordWebhookUrl />
      <AlertUsername>NinjaTrader Bot</AlertUsername>
      <IncludeSymbol>true</IncludeSymbol>
      <AlertColor>3447003</AlertColor>
      <EnableProfitMilestones>true</EnableProfitMilestones>
      <Milestone1>500</Milestone1>
      <Milestone2>1000</Milestone2>
      <Milestone3>1500</Milestone3>
      <UseEveryoneTag>true</UseEveryoneTag>
      <UseTimeWindow>true</UseTimeWindow>
      <StartTime>09:30</StartTime>
      <EndTime>10:00</EndTime>
      <ExitAtWindowEnd>true</ExitAtWindowEnd>
      <EntryOrderType>Market</EntryOrderType>
      <TpOrderType>Limit</TpOrderType>
      <SlOrderType>StopMarket</SlOrderType>
      <EmergencyOrderType>Market</EmergencyOrderType>
    </sparkzy>
  </Strategy>
</StrategyTemplate>