******************* Session Start (Version *******) *******************
2025-06-09 02:45:56:505 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-06-09 02:45:56:521 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-06-09 02:45:56:956 Core.Instrumentation.ActivitySource: enabled=True randomPercent=59.72948 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-06-09 02:45:57:011 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-06-09 02:45:58:120 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-06-09 02:45:58:125 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.0668458' renewSecs='2400.0334229'
2025-06-09 02:45:59:075 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-06-09 02:45:59:890 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-06-09 02:46:00:897 PrimaryMonitorWPFDPIScale=1.00
2025-06-09 02:46:01:472 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-06-09 02:46:01:694 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-06-09 02:46:01:694 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-06-09 02:46:01:694 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-06-09 02:46:01:695 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-06-09 02:46:01:695 OSLanguage='en-US'
2025-06-09 02:46:01:695 OSEnvironment='64bit'
2025-06-09 02:46:01:695 Processors=8
2025-06-09 02:46:01:695 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-06-09 02:46:02:771 ProcessorSpeed=2.4 GHz
2025-06-09 02:46:02:771 PhysicalMemory=8192 MB
2025-06-09 02:46:02:994 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-06-09 02:46:02:995 Monitors=2/1280x720|1920x1080
2025-06-09 02:46:02:995 .NET/CLR Version='4.8'/64bit
2025-06-09 02:46:02:997 SQLiteVersion='1.0.116.0'
2025-06-09 02:46:02:998 ApplicationTimezone=EST +0 hour(s)
2025-06-09 02:46:02:998 ApplicationTimezone=UTC -4 hour(s)
2025-06-09 02:46:02:998 LocalTimezone=EST +0 hour(s)
2025-06-09 02:46:02:998 LocalTimezone=UTC -4 hour(s)
2025-06-09 02:46:03:078 DirectXRenderingHW
2025-06-09 02:46:03:078 Copying custom assemblies...
2025-06-09 02:46:03:114 Loading custom assemblies...
2025-06-09 02:46:03:115 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-06-09 02:46:03:373 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-06-09 02:46:03:390 Deleting temporary files...
2025-06-09 02:46:03:820 Copying db and restoring templates...
2025-06-09 02:46:03:879 Loading third party assemblies...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-06-09 02:46:03:973 Initializing database...
2025-06-09 02:46:03:973 Loading master instruments...
2025-06-09 02:46:04:292 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-06-09 02:46:04:298 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-06-09 02:46:04:787 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-06-09 02:46:04:941 Loading instruments...
2025-06-09 02:46:05:327 Loading accounts...
2025-06-09 02:46:05:463 Loading users...
2025-06-09 02:46:05:501 Downloading server info...
2025-06-09 02:46:05:502 Starting instrument management...
2025-06-09 02:46:05:514 Starting timer...
2025-06-09 02:46:05:514 Creating file type watcher...
2025-06-09 02:46:05:515 Setting ATI...
2025-06-09 02:46:05:547 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-06-09 02:46:05:557 Connecting ATI server...
2025-06-09 02:46:05:557 Server.AtiServer.Connect0
2025-06-09 02:46:05:558 Starting adapter server...
2025-06-09 02:46:05:566 Server.AtiServer.Connect1: Port='36973'
2025-06-09 02:46:05:572 Server.AtiServer.Connect2
2025-06-09 02:46:05:576 Starting bars dictionary...
2025-06-09 02:46:05:577 Starting recorder...
2025-06-09 02:46:05:580 Starting server(s)...
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3133
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9783
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=12155
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5061
2025-06-09 02:46:05:708 Required resource key 'brushOrderWorking' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderAccepted' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderPartFilled' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderInitialized' is missing.
2025-06-09 02:46:05:765 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarTick='' SnapModeBarObject='' SnapModeDisabled='' SnapModeTick='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-06-09 02:46:05:776 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Replace='Ctrl+H' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-06-09 02:46:05:777 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-09 02:46:05:778 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-06-09 02:46:05:780 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='' SimulatedOrder=''
2025-06-09 02:46:05:781 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-09 02:46:05:781 OrderEntryHotKeys=disabled
2025-06-09 02:46:05:783 AutoClose=disabled
2025-06-09 02:46:06:660 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-06-09 02:46:10:026 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 02:46:10:222 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=1 RolloverCollection=260 TradingHours=0
2025-06-09 02:46:10:688 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 02:46:14:565 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.14ms InstrumentLists=0.92ms MasterInstruments=0.05ms Messages=0.79ms Risks=16.21ms RolloverCollection=4322.82ms TradingHours=0.08ms
2025-06-09 02:46:14:566 Starting server message polling timer with interval 3600 seconds...
2025-06-09 02:46:16:490 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-06-09 02:46:16:622 1 Chart Chart1Tab1 1 Ser 7 Ind 0 DrawObj 
