******************* Session Start (Version *******) *******************
2025-06-09 02:45:56:505 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-06-09 02:45:56:521 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-06-09 02:45:56:956 Core.Instrumentation.ActivitySource: enabled=True randomPercent=59.72948 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-06-09 02:45:57:011 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-06-09 02:45:58:120 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-06-09 02:45:58:125 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4800.0668458' renewSecs='2400.0334229'
2025-06-09 02:45:59:075 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-06-09 02:45:59:890 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-06-09 02:46:00:897 PrimaryMonitorWPFDPIScale=1.00
2025-06-09 02:46:01:472 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-06-09 02:46:01:694 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-06-09 02:46:01:694 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-06-09 02:46:01:694 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-06-09 02:46:01:695 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-06-09 02:46:01:695 OSLanguage='en-US'
2025-06-09 02:46:01:695 OSEnvironment='64bit'
2025-06-09 02:46:01:695 Processors=8
2025-06-09 02:46:01:695 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-06-09 02:46:02:771 ProcessorSpeed=2.4 GHz
2025-06-09 02:46:02:771 PhysicalMemory=8192 MB
2025-06-09 02:46:02:994 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-06-09 02:46:02:995 Monitors=2/1280x720|1920x1080
2025-06-09 02:46:02:995 .NET/CLR Version='4.8'/64bit
2025-06-09 02:46:02:997 SQLiteVersion='1.0.116.0'
2025-06-09 02:46:02:998 ApplicationTimezone=EST +0 hour(s)
2025-06-09 02:46:02:998 ApplicationTimezone=UTC -4 hour(s)
2025-06-09 02:46:02:998 LocalTimezone=EST +0 hour(s)
2025-06-09 02:46:02:998 LocalTimezone=UTC -4 hour(s)
2025-06-09 02:46:03:078 DirectXRenderingHW
2025-06-09 02:46:03:078 Copying custom assemblies...
2025-06-09 02:46:03:114 Loading custom assemblies...
2025-06-09 02:46:03:115 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-06-09 02:46:03:373 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-06-09 02:46:03:390 Deleting temporary files...
2025-06-09 02:46:03:820 Copying db and restoring templates...
2025-06-09 02:46:03:879 Loading third party assemblies...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-06-09 02:46:03:970 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-06-09 02:46:03:973 Initializing database...
2025-06-09 02:46:03:973 Loading master instruments...
2025-06-09 02:46:04:292 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-06-09 02:46:04:298 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-06-09 02:46:04:787 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-06-09 02:46:04:941 Loading instruments...
2025-06-09 02:46:05:327 Loading accounts...
2025-06-09 02:46:05:463 Loading users...
2025-06-09 02:46:05:501 Downloading server info...
2025-06-09 02:46:05:502 Starting instrument management...
2025-06-09 02:46:05:514 Starting timer...
2025-06-09 02:46:05:514 Creating file type watcher...
2025-06-09 02:46:05:515 Setting ATI...
2025-06-09 02:46:05:547 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-06-09 02:46:05:557 Connecting ATI server...
2025-06-09 02:46:05:557 Server.AtiServer.Connect0
2025-06-09 02:46:05:558 Starting adapter server...
2025-06-09 02:46:05:566 Server.AtiServer.Connect1: Port='36973'
2025-06-09 02:46:05:572 Server.AtiServer.Connect2
2025-06-09 02:46:05:576 Starting bars dictionary...
2025-06-09 02:46:05:577 Starting recorder...
2025-06-09 02:46:05:580 Starting server(s)...
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3133
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9783
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=12155
2025-06-09 02:46:05:637 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5061
2025-06-09 02:46:05:708 Required resource key 'brushOrderWorking' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderAccepted' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderPartFilled' is missing.
2025-06-09 02:46:05:708 Required resource key 'brushOrderInitialized' is missing.
2025-06-09 02:46:05:765 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarTick='' SnapModeBarObject='' SnapModeDisabled='' SnapModeTick='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-06-09 02:46:05:776 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Replace='Ctrl+H' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-06-09 02:46:05:777 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-09 02:46:05:778 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-06-09 02:46:05:780 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='' SimulatedOrder=''
2025-06-09 02:46:05:781 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-06-09 02:46:05:781 OrderEntryHotKeys=disabled
2025-06-09 02:46:05:783 AutoClose=disabled
2025-06-09 02:46:06:660 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-06-09 02:46:10:026 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 02:46:10:222 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=1 RolloverCollection=260 TradingHours=0
2025-06-09 02:46:10:688 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 02:46:14:565 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.14ms InstrumentLists=0.92ms MasterInstruments=0.05ms Messages=0.79ms Risks=16.21ms RolloverCollection=4322.82ms TradingHours=0.08ms
2025-06-09 02:46:14:566 Starting server message polling timer with interval 3600 seconds...
2025-06-09 02:46:16:490 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-06-09 02:46:16:622 1 Chart Chart1Tab1 1 Ser 7 Ind 0 DrawObj 
2025-06-09 03:10:44:203 (Playback) Gui.ControlCenter.OnConnect
2025-06-09 03:10:47:655 (Playback) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-06-09 runAsProcess=False
2025-06-09 03:10:47:703 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-09 03:10:47:726 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-09 03:10:47:765 (Playback) Cbi.Connection.Connect1
2025-06-09 03:10:47:786 (Playback) Cbi.Connection.Connect2
2025-06-09 03:10:47:789 (Playback) Cbi.Connection.Connect3
2025-06-09 03:10:47:800 (Playback) Cbi.Connection.CreateAccount: account='Playback101' displayName='Playback101' fcm='' denomination=UsDollar forexLotSize=10000
2025-06-09 03:10:47:809 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-06-09 03:10:47:821 (Playback) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Playback101'
2025-06-09 03:10:47:832 (Playback) Cbi.Connection.Connect4
2025-06-09 03:10:47:835 (Playback) Cbi.Connection.Connect5
2025-06-09 03:10:47:838 (Playback) Adapter.PlaybackAdapter.Connect
2025-06-09 03:10:47:866 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-06-09 03:10:47:868 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connecting priceStatus=Connecting
2025-06-09 03:10:48:337 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-06-09 03:10:48:339 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-06-09 03:10:49:095 (Playback) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-09 03:10:49:103 (Playback) Cbi.Account.OnConnectionStatus: account='Playback101' fcm='' status=Connected previousStatus=Connecting message=''
2025-06-09 03:10:49:110 (Playback) Core.Connection.Statistics: connectAttempts=1/1274.3ms
2025-06-09 03:10:49:110 (Playback) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-06-09 03:10:49:113 Server.HdsClient.Connect: type=HDS server='hds-us-nt-007.ninjatrader.com' port=31655 system='' useSsl=True
2025-06-09 03:10:49:358 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-09 03:10:49:358 (Playback) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Playback status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-06-09 03:10:49:358 (Playback) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Playback status=Connected priceStatus=Connected
2025-06-09 03:10:49:360 (Playback) Cbi.Connection.Connect9 ok
2025-06-09 03:10:49:457 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-06-09 03:10:49:458 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-06-09 03:10:49:890 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:10:49:970 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:10:49:975 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:10:49:976 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:10:49:977 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:10:49:978 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:12:36:375 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 03:12:36:536 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-06-09 03:13:48:192 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:48:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:48:203 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:13:56:375 (Playback) Cbi.Account.CreateOrder: orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45573 time='2025-03-17 00:38:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:13:56:448 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45573 time='2025-03-17 00:38:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:56:450 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45573 time='2025-03-17 00:38:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:56:455 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45573 time='2025-03-17 00:38:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:13:56:486 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:38:01' statementDate='2025-03-17' error=NoError comment='' nr=1
2025-06-09 03:13:56:541 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:38:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:56:542 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:38:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:56:562 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='48fed20597f844b08e4221e780619ab7' maxFillQuantity=1 price=19814.5 thread=43
2025-06-09 03:13:56:562 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='48fed20597f844b08e4221e780619ab7' fillQuantity=1 price=19814.5
2025-06-09 03:13:56:563 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19814.5 time='2025-03-17 00:38:01' statementDate='2025-03-17' error=NoError comment='' nr=4
2025-06-09 03:13:56:579 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='96007fc6b0e24d39a9f04c2194d59cf0' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19814.5 quantity=1 marketPosition=Long operation=Add orderID='48fed20597f844b08e4221e780619ab7' isSod=False time='2025-03-17 00:38:01' statementDate='2025-03-17'
2025-06-09 03:13:56:619 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19814.5 quantity=1 marketPosition=Long operation=Add
2025-06-09 03:13:56:669 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='48fed20597f844b08e4221e780619ab7' maxFillQuantity=7 price=19814.75 thread=43
2025-06-09 03:13:56:669 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='48fed20597f844b08e4221e780619ab7' fillQuantity=7 price=19814.75
2025-06-09 03:13:56:669 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19814.71875 time='2025-03-17 00:38:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:56:699 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='4506f1bcd7374a37b0c9f49e318b2870' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19814.75 quantity=7 marketPosition=Long operation=Add orderID='48fed20597f844b08e4221e780619ab7' isSod=False time='2025-03-17 00:38:01' statementDate='2025-03-17'
2025-06-09 03:13:56:700 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19814.71875 quantity=8 marketPosition=Long operation=Update
2025-06-09 03:13:57:831 (Playback) Cbi.Account.CreateOrder: orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:13:57:832 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:57:832 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:57:833 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:13:57:833 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:862 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:863 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:863 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='32562d0819fe49f0875e4bf6ac5cfa27' maxFillQuantity=1 price=19802.25 thread=43
2025-06-09 03:13:57:863 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='32562d0819fe49f0875e4bf6ac5cfa27' fillQuantity=1 price=19802.25
2025-06-09 03:13:57:863 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19802.25 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:863 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='e01b42c3317b479db648d5e422419ae6' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19802.25 quantity=1 marketPosition=Short operation=Add orderID='32562d0819fe49f0875e4bf6ac5cfa27' isSod=False time='2025-03-17 00:57:03' statementDate='2025-03-17'
2025-06-09 03:13:57:864 Cbi.Position.RemoveFirstEntry: maintain exit order: orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=1 averageFillPrice=19802.25 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:57:869 (Playback) Cbi.Account.OnAddTrade: entryId='96007fc6b0e24d39a9f04c2194d59cf0' exitId='e01b42c3317b479db648d5e422419ae6' profitCurrencyBeforeCommissionAndFees=-245
2025-06-09 03:13:57:869 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19814.75 quantity=7 marketPosition=Long operation=Update
2025-06-09 03:13:57:869 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='32562d0819fe49f0875e4bf6ac5cfa27' maxFillQuantity=7 price=19802 thread=43
2025-06-09 03:13:57:869 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='32562d0819fe49f0875e4bf6ac5cfa27' fillQuantity=7 price=19802
2025-06-09 03:13:57:869 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19802.03125 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:920 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='69c71782eaee45f1aa6c367207cdb4d0' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19802 quantity=7 marketPosition=Short operation=Add orderID='32562d0819fe49f0875e4bf6ac5cfa27' isSod=False time='2025-03-17 00:57:03' statementDate='2025-03-17'
2025-06-09 03:13:57:921 (Playback) Cbi.Account.OnAddTrade: entryId='4506f1bcd7374a37b0c9f49e318b2870' exitId='69c71782eaee45f1aa6c367207cdb4d0' profitCurrencyBeforeCommissionAndFees=-1785
2025-06-09 03:13:57:921 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:13:57:921 (Playback) Cbi.Account.CreateOrder: orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Initialized instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45575 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:13:57:922 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45575 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:57:922 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45575 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:13:57:922 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45575 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:13:57:922 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:979 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Accepted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:979 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Working instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:979 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='05bae75056aa49f9bc8f12aef614621d' maxFillQuantity=1 price=19802.25 thread=43
2025-06-09 03:13:57:979 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='05bae75056aa49f9bc8f12aef614621d' fillQuantity=1 price=19802.25
2025-06-09 03:13:57:979 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19802.25 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:13:57:979 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='c3fd69e6a9c94975b438a1250c47d209' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19802.25 quantity=1 marketPosition=Short operation=Add orderID='05bae75056aa49f9bc8f12aef614621d' isSod=False time='2025-03-17 00:57:03' statementDate='2025-03-17'
2025-06-09 03:13:57:980 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19802.25 quantity=1 marketPosition=Short operation=Add
2025-06-09 03:13:57:980 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='05bae75056aa49f9bc8f12aef614621d' maxFillQuantity=7 price=19802 thread=43
2025-06-09 03:13:57:980 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='05bae75056aa49f9bc8f12aef614621d' fillQuantity=7 price=19802
2025-06-09 03:13:57:980 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19802.03125 time='2025-03-17 00:57:03' statementDate='2025-03-17' error=NoError comment='' nr=5
2025-06-09 03:13:58:031 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='b63108b2ab8c45839762e9d7daeabe0d' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19802 quantity=7 marketPosition=Short operation=Add orderID='05bae75056aa49f9bc8f12aef614621d' isSod=False time='2025-03-17 00:57:03' statementDate='2025-03-17'
2025-06-09 03:13:58:032 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19802.03125 quantity=8 marketPosition=Short operation=Update
2025-06-09 03:13:58:035 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:13:58:035 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:58:035 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:13:58:035 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:00:236 (Playback) Cbi.Account.CreateOrder: orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:00:236 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:00:236 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:00:236 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:00:236 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:280 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:280 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:280 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='d98ab5d29d104c40b656fae40c4bd3ff' maxFillQuantity=4 price=19809 thread=43
2025-06-09 03:14:00:280 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='d98ab5d29d104c40b656fae40c4bd3ff' fillQuantity=4 price=19809
2025-06-09 03:14:00:281 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=4 averageFillPrice=19809 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:281 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='8192f1ea551449489d97fece0b1695f8' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19809 quantity=4 marketPosition=Long operation=Add orderID='d98ab5d29d104c40b656fae40c4bd3ff' isSod=False time='2025-03-17 01:18:25' statementDate='2025-03-17'
2025-06-09 03:14:00:283 Cbi.Position.RemoveFirstEntry: maintain exit order: orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=4 averageFillPrice=19809 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:00:289 (Playback) Cbi.Account.OnAddTrade: entryId='c3fd69e6a9c94975b438a1250c47d209' exitId='8192f1ea551449489d97fece0b1695f8' profitCurrencyBeforeCommissionAndFees=-135
2025-06-09 03:14:00:289 (Playback) Cbi.Account.OnAddTrade: entryId='b63108b2ab8c45839762e9d7daeabe0d' exitId='8192f1ea551449489d97fece0b1695f8' profitCurrencyBeforeCommissionAndFees=-420
2025-06-09 03:14:00:289 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19802 quantity=4 marketPosition=Short operation=Update
2025-06-09 03:14:00:289 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='d98ab5d29d104c40b656fae40c4bd3ff' maxFillQuantity=4 price=19809.25 thread=43
2025-06-09 03:14:00:290 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='d98ab5d29d104c40b656fae40c4bd3ff' fillQuantity=4 price=19809.25
2025-06-09 03:14:00:290 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19809.125 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:324 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='a4827b588c5246ac897f5f13ed0861d1' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19809.25 quantity=4 marketPosition=Long operation=Add orderID='d98ab5d29d104c40b656fae40c4bd3ff' isSod=False time='2025-03-17 01:18:25' statementDate='2025-03-17'
2025-06-09 03:14:00:338 (Playback) Cbi.Account.OnAddTrade: entryId='b63108b2ab8c45839762e9d7daeabe0d' exitId='a4827b588c5246ac897f5f13ed0861d1' profitCurrencyBeforeCommissionAndFees=-580
2025-06-09 03:14:00:338 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:14:00:338 (Playback) Cbi.Account.CreateOrder: orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45577 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:00:341 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45577 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:00:341 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45577 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:00:341 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45577 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:00:342 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:381 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:382 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:382 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' maxFillQuantity=4 price=19809 thread=43
2025-06-09 03:14:00:382 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' fillQuantity=4 price=19809
2025-06-09 03:14:00:382 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=4 averageFillPrice=19809 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:382 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='66acdd5d92c04a7daa37b594f53f2c51' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19809 quantity=4 marketPosition=Long operation=Add orderID='e0ef6c84b80e4c338c0fefebe6fc36ca' isSod=False time='2025-03-17 01:18:25' statementDate='2025-03-17'
2025-06-09 03:14:00:382 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19809 quantity=4 marketPosition=Long operation=Add
2025-06-09 03:14:00:391 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' maxFillQuantity=4 price=19809.25 thread=43
2025-06-09 03:14:00:391 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' fillQuantity=4 price=19809.25
2025-06-09 03:14:00:391 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19809.125 time='2025-03-17 01:18:25' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:00:437 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='6054cd3fc0e9419d9e21cbdd9eab9d0a' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19809.25 quantity=4 marketPosition=Long operation=Add orderID='e0ef6c84b80e4c338c0fefebe6fc36ca' isSod=False time='2025-03-17 01:18:25' statementDate='2025-03-17'
2025-06-09 03:14:00:437 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19809.125 quantity=8 marketPosition=Long operation=Update
2025-06-09 03:14:00:455 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:00:455 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:00:455 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:00:455 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:04:695 (Playback) Cbi.Account.CreateOrder: orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45578 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:04:696 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45578 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:04:696 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45578 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:04:696 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45578 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:04:696 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:733 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:733 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:733 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='d123163dda2a41179d8c6293a8f8ecb4' maxFillQuantity=2 price=19798 thread=43
2025-06-09 03:14:04:733 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='d123163dda2a41179d8c6293a8f8ecb4' fillQuantity=2 price=19798
2025-06-09 03:14:04:733 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=2 averageFillPrice=19798 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:734 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='51ec660f298b4bf49d90cd332ff20cc1' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19798 quantity=2 marketPosition=Short operation=Add orderID='d123163dda2a41179d8c6293a8f8ecb4' isSod=False time='2025-03-17 02:10:02' statementDate='2025-03-17'
2025-06-09 03:14:04:734 (Playback) Cbi.Account.OnAddTrade: entryId='66acdd5d92c04a7daa37b594f53f2c51' exitId='51ec660f298b4bf49d90cd332ff20cc1' profitCurrencyBeforeCommissionAndFees=-440
2025-06-09 03:14:04:734 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19809.********** quantity=6 marketPosition=Long operation=Update
2025-06-09 03:14:04:734 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='d123163dda2a41179d8c6293a8f8ecb4' maxFillQuantity=6 price=19797.75 thread=43
2025-06-09 03:14:04:734 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='d123163dda2a41179d8c6293a8f8ecb4' fillQuantity=6 price=19797.75
2025-06-09 03:14:04:734 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19797.8125 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:769 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='d09bbdc2ad254631a41efd54fbc1d419' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19797.75 quantity=6 marketPosition=Short operation=Add orderID='d123163dda2a41179d8c6293a8f8ecb4' isSod=False time='2025-03-17 02:10:02' statementDate='2025-03-17'
2025-06-09 03:14:04:769 (Playback) Cbi.Account.OnAddTrade: entryId='66acdd5d92c04a7daa37b594f53f2c51' exitId='d09bbdc2ad254631a41efd54fbc1d419' profitCurrencyBeforeCommissionAndFees=-450
2025-06-09 03:14:04:770 (Playback) Cbi.Account.OnAddTrade: entryId='6054cd3fc0e9419d9e21cbdd9eab9d0a' exitId='d09bbdc2ad254631a41efd54fbc1d419' profitCurrencyBeforeCommissionAndFees=-920
2025-06-09 03:14:04:770 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:14:04:770 (Playback) Cbi.Account.CreateOrder: orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Initialized instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45579 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:04:770 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45579 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:04:770 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45579 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:04:770 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45579 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:04:770 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:810 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Accepted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:811 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Working instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:811 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='9ef23ffcb7f540c185087787b411427b' maxFillQuantity=2 price=19798 thread=43
2025-06-09 03:14:04:811 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='9ef23ffcb7f540c185087787b411427b' fillQuantity=2 price=19798
2025-06-09 03:14:04:811 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=2 averageFillPrice=19798 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:811 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='991da4055710436ab0b76127fd54619c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19798 quantity=2 marketPosition=Short operation=Add orderID='9ef23ffcb7f540c185087787b411427b' isSod=False time='2025-03-17 02:10:02' statementDate='2025-03-17'
2025-06-09 03:14:04:812 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19798 quantity=2 marketPosition=Short operation=Add
2025-06-09 03:14:04:812 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='9ef23ffcb7f540c185087787b411427b' maxFillQuantity=6 price=19797.75 thread=43
2025-06-09 03:14:04:812 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='9ef23ffcb7f540c185087787b411427b' fillQuantity=6 price=19797.75
2025-06-09 03:14:04:812 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19797.8125 time='2025-03-17 02:10:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:04:868 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='9efa198110154d42ae80ea5c5b65c66d' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19797.75 quantity=6 marketPosition=Short operation=Add orderID='9ef23ffcb7f540c185087787b411427b' isSod=False time='2025-03-17 02:10:02' statementDate='2025-03-17'
2025-06-09 03:14:04:869 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.8125 quantity=8 marketPosition=Short operation=Update
2025-06-09 03:14:04:875 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:04:875 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:04:875 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:04:875 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:05:978 (Playback) Cbi.Account.CreateOrder: orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45580 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:05:978 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45580 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:05:979 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45580 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:05:979 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45580 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:05:979 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:013 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:013 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:013 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='68e1e9e152244b60b83a2c7a27d94fd6' maxFillQuantity=1 price=19784.5 thread=43
2025-06-09 03:14:06:013 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='68e1e9e152244b60b83a2c7a27d94fd6' fillQuantity=1 price=19784.5
2025-06-09 03:14:06:013 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=1 averageFillPrice=19784.5 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:013 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='d31faa008b5d4b928262b96ad04a0ea5' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19784.5 quantity=1 marketPosition=Long operation=Add orderID='68e1e9e152244b60b83a2c7a27d94fd6' isSod=False time='2025-03-17 02:19:01' statementDate='2025-03-17'
2025-06-09 03:14:06:014 (Playback) Cbi.Account.OnAddTrade: entryId='991da4055710436ab0b76127fd54619c' exitId='d31faa008b5d4b928262b96ad04a0ea5' profitCurrencyBeforeCommissionAndFees=270
2025-06-09 03:14:06:014 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.********** quantity=7 marketPosition=Short operation=Update
2025-06-09 03:14:06:015 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='68e1e9e152244b60b83a2c7a27d94fd6' maxFillQuantity=1 price=19784.75 thread=43
2025-06-09 03:14:06:015 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='68e1e9e152244b60b83a2c7a27d94fd6' fillQuantity=1 price=19784.75
2025-06-09 03:14:06:015 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=2 averageFillPrice=19784.625 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:062 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='3acd24c5e31e40ca86bffe8e7b9f3ad0' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19784.75 quantity=1 marketPosition=Long operation=Add orderID='68e1e9e152244b60b83a2c7a27d94fd6' isSod=False time='2025-03-17 02:19:01' statementDate='2025-03-17'
2025-06-09 03:14:06:062 (Playback) Cbi.Account.OnAddTrade: entryId='991da4055710436ab0b76127fd54619c' exitId='3acd24c5e31e40ca86bffe8e7b9f3ad0' profitCurrencyBeforeCommissionAndFees=265
2025-06-09 03:14:06:062 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.75 quantity=6 marketPosition=Short operation=Update
2025-06-09 03:14:06:063 (Playback) Cbi.Account.CreateOrder: orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45581 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:06:063 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45581 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:06:063 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45581 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:06:063 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45581 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:06:063 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:117 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:118 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:118 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='cc9aae1ebc5242118fa29514be3c48d9' maxFillQuantity=1 price=19784.5 thread=43
2025-06-09 03:14:06:118 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='cc9aae1ebc5242118fa29514be3c48d9' fillQuantity=1 price=19784.5
2025-06-09 03:14:06:118 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=1 averageFillPrice=19784.5 time='2025-03-17 02:19:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:175 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='c82cfc7c396443cca163f0d0b8ef8bad' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19784.5 quantity=1 marketPosition=Long operation=Add orderID='cc9aae1ebc5242118fa29514be3c48d9' isSod=False time='2025-03-17 02:19:01' statementDate='2025-03-17'
2025-06-09 03:14:06:175 (Playback) Cbi.Account.OnAddTrade: entryId='9efa198110154d42ae80ea5c5b65c66d' exitId='c82cfc7c396443cca163f0d0b8ef8bad' profitCurrencyBeforeCommissionAndFees=265
2025-06-09 03:14:06:175 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.75 quantity=5 marketPosition=Short operation=Update
2025-06-09 03:14:06:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:06:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:06:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:06:193 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:06:846 (Playback) Cbi.Account.CreateOrder: orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45582 time='2025-03-17 02:21:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:06:846 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45582 time='2025-03-17 02:21:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:06:846 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45582 time='2025-03-17 02:21:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:06:846 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45582 time='2025-03-17 02:21:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:06:846 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:21:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:886 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:21:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:888 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:21:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:888 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='30878004ba0b4235b1277c8fddeb0271' maxFillQuantity=1 price=19781.5 thread=43
2025-06-09 03:14:06:888 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='30878004ba0b4235b1277c8fddeb0271' fillQuantity=1 price=19781.5
2025-06-09 03:14:06:888 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=1 averageFillPrice=19781.5 time='2025-03-17 02:21:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:888 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='292ad01789cc452280bfab76c587494c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19781.5 quantity=1 marketPosition=Long operation=Add orderID='30878004ba0b4235b1277c8fddeb0271' isSod=False time='2025-03-17 02:21:01' statementDate='2025-03-17'
2025-06-09 03:14:06:888 (Playback) Cbi.Account.OnAddTrade: entryId='9efa198110154d42ae80ea5c5b65c66d' exitId='292ad01789cc452280bfab76c587494c' profitCurrencyBeforeCommissionAndFees=325
2025-06-09 03:14:06:888 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.75 quantity=4 marketPosition=Short operation=Update
2025-06-09 03:14:06:889 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='30878004ba0b4235b1277c8fddeb0271' maxFillQuantity=2 price=19781.75 thread=43
2025-06-09 03:14:06:889 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='30878004ba0b4235b1277c8fddeb0271' fillQuantity=2 price=19781.75
2025-06-09 03:14:06:889 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=3 orderType='Market' filled=3 averageFillPrice=19781.********** time='2025-03-17 02:21:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:06:944 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='7e167835e41f4b118358d98f480540fe' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19781.75 quantity=2 marketPosition=Long operation=Add orderID='30878004ba0b4235b1277c8fddeb0271' isSod=False time='2025-03-17 02:21:01' statementDate='2025-03-17'
2025-06-09 03:14:06:945 (Playback) Cbi.Account.OnAddTrade: entryId='9efa198110154d42ae80ea5c5b65c66d' exitId='7e167835e41f4b118358d98f480540fe' profitCurrencyBeforeCommissionAndFees=640
2025-06-09 03:14:06:945 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19797.75 quantity=2 marketPosition=Short operation=Update
2025-06-09 03:14:06:990 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:06:990 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:06:990 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:06:990 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:09:559 (Playback) Cbi.Account.CreateOrder: orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45583 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:09:560 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45583 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:09:560 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45583 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:09:560 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45583 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:09:560 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:09:588 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:09:588 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:09:588 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='442b95825e3242a2952cdd64b8205ad4' maxFillQuantity=2 price=19787.5 thread=43
2025-06-09 03:14:09:588 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='442b95825e3242a2952cdd64b8205ad4' fillQuantity=2 price=19787.5
2025-06-09 03:14:09:589 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=2 averageFillPrice=19787.5 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:09:633 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='a15efd964a1f4d4884d86cfaa2c751d2' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19787.5 quantity=2 marketPosition=Long operation=Add orderID='442b95825e3242a2952cdd64b8205ad4' isSod=False time='2025-03-17 02:41:04' statementDate='2025-03-17'
2025-06-09 03:14:09:633 (Playback) Cbi.Account.OnAddTrade: entryId='9efa198110154d42ae80ea5c5b65c66d' exitId='a15efd964a1f4d4884d86cfaa2c751d2' profitCurrencyBeforeCommissionAndFees=410
2025-06-09 03:14:09:633 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:14:09:633 (Playback) Cbi.Account.CreateOrder: orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45584 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:09:634 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45584 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:09:634 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45584 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:09:634 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45584 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:09:634 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:09:680 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=2
2025-06-09 03:14:09:680 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=3
2025-06-09 03:14:09:680 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='b71531865fdd46878c74ebfc951e7073' maxFillQuantity=2 price=19787.5 thread=43
2025-06-09 03:14:09:680 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='b71531865fdd46878c74ebfc951e7073' fillQuantity=2 price=19787.5
2025-06-09 03:14:09:680 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=2 averageFillPrice=19787.5 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=4
2025-06-09 03:14:09:680 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='0345b7c8963f48b8b42c25e32cbed7f6' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19787.5 quantity=2 marketPosition=Long operation=Add orderID='b71531865fdd46878c74ebfc951e7073' isSod=False time='2025-03-17 02:41:04' statementDate='2025-03-17'
2025-06-09 03:14:09:681 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19787.5 quantity=2 marketPosition=Long operation=Add
2025-06-09 03:14:09:681 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='b71531865fdd46878c74ebfc951e7073' maxFillQuantity=6 price=19787.75 thread=43
2025-06-09 03:14:09:681 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='b71531865fdd46878c74ebfc951e7073' fillQuantity=6 price=19787.75
2025-06-09 03:14:09:681 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19787.6875 time='2025-03-17 02:41:04' statementDate='2025-03-17' error=NoError comment='' nr=5
2025-06-09 03:14:09:732 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='c92a1a21990240da9166848564ba325c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19787.75 quantity=6 marketPosition=Long operation=Add orderID='b71531865fdd46878c74ebfc951e7073' isSod=False time='2025-03-17 02:41:04' statementDate='2025-03-17'
2025-06-09 03:14:09:732 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19787.6875 quantity=8 marketPosition=Long operation=Update
2025-06-09 03:14:09:749 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:09:749 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:09:749 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:09:749 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:13:060 (Playback) Cbi.Account.CreateOrder: orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45585 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:13:060 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45585 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:13:060 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45585 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:13:060 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45585 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:13:060 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:085 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:085 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:085 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='084ffd9a02ae45e994d5f29429d2a22e' maxFillQuantity=1 price=19766 thread=43
2025-06-09 03:14:13:085 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='084ffd9a02ae45e994d5f29429d2a22e' fillQuantity=1 price=19766
2025-06-09 03:14:13:085 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19766 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:086 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='2602722fe88647ea999f2d864335590b' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19766 quantity=1 marketPosition=Short operation=Add orderID='084ffd9a02ae45e994d5f29429d2a22e' isSod=False time='2025-03-17 03:19:02' statementDate='2025-03-17'
2025-06-09 03:14:13:086 (Playback) Cbi.Account.OnAddTrade: entryId='0345b7c8963f48b8b42c25e32cbed7f6' exitId='2602722fe88647ea999f2d864335590b' profitCurrencyBeforeCommissionAndFees=-430
2025-06-09 03:14:13:086 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19787.********** quantity=7 marketPosition=Long operation=Update
2025-06-09 03:14:13:086 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='084ffd9a02ae45e994d5f29429d2a22e' maxFillQuantity=7 price=19765.75 thread=43
2025-06-09 03:14:13:086 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='084ffd9a02ae45e994d5f29429d2a22e' fillQuantity=7 price=19765.75
2025-06-09 03:14:13:086 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19765.78125 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=5
2025-06-09 03:14:13:104 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='85ac54ec6e9941d497d1b9287a701812' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19765.75 quantity=7 marketPosition=Short operation=Add orderID='084ffd9a02ae45e994d5f29429d2a22e' isSod=False time='2025-03-17 03:19:02' statementDate='2025-03-17'
2025-06-09 03:14:13:105 (Playback) Cbi.Account.OnAddTrade: entryId='0345b7c8963f48b8b42c25e32cbed7f6' exitId='85ac54ec6e9941d497d1b9287a701812' profitCurrencyBeforeCommissionAndFees=-435
2025-06-09 03:14:13:105 (Playback) Cbi.Account.OnAddTrade: entryId='c92a1a21990240da9166848564ba325c' exitId='85ac54ec6e9941d497d1b9287a701812' profitCurrencyBeforeCommissionAndFees=-2640
2025-06-09 03:14:13:105 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:14:13:110 (Playback) Cbi.Account.CreateOrder: orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45586 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:13:110 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45586 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:13:110 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45586 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:13:110 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45586 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:13:110 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Submitted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:131 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Accepted instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:131 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Working instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:131 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='e975e16de8e34cbf9bef4c29478d637c' maxFillQuantity=1 price=19766 thread=43
2025-06-09 03:14:13:131 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='e975e16de8e34cbf9bef4c29478d637c' fillQuantity=1 price=19766
2025-06-09 03:14:13:131 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19766 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:13:132 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='6a7ddb16f57444fd9028c96144557a51' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19766 quantity=1 marketPosition=Short operation=Add orderID='e975e16de8e34cbf9bef4c29478d637c' isSod=False time='2025-03-17 03:19:02' statementDate='2025-03-17'
2025-06-09 03:14:13:132 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19766 quantity=1 marketPosition=Short operation=Add
2025-06-09 03:14:13:132 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='e975e16de8e34cbf9bef4c29478d637c' maxFillQuantity=7 price=19765.75 thread=43
2025-06-09 03:14:13:132 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='e975e16de8e34cbf9bef4c29478d637c' fillQuantity=7 price=19765.75
2025-06-09 03:14:13:137 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19765.78125 time='2025-03-17 03:19:02' statementDate='2025-03-17' error=NoError comment='' nr=5
2025-06-09 03:14:13:166 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='6421907c898f4baf8764844d45f56d51' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19765.75 quantity=7 marketPosition=Short operation=Add orderID='e975e16de8e34cbf9bef4c29478d637c' isSod=False time='2025-03-17 03:19:02' statementDate='2025-03-17'
2025-06-09 03:14:13:166 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19765.78125 quantity=8 marketPosition=Short operation=Update
2025-06-09 03:14:13:176 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:13:176 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:13:176 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:13:176 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:14:120 (Playback) Cbi.Account.CreateOrder: orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:14:121 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:14:121 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:14:121 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:14:121 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=1
2025-06-09 03:14:14:154 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:155 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:155 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='e453e4a9c53c41438625e68b832e0e55' maxFillQuantity=1 price=19788.75 thread=43
2025-06-09 03:14:14:155 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='e453e4a9c53c41438625e68b832e0e55' fillQuantity=1 price=19788.75
2025-06-09 03:14:14:155 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19788.75 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:156 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='eb92eca7df7f4770bba8bde2bdb2878b' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19788.75 quantity=1 marketPosition=Long operation=Add orderID='e453e4a9c53c41438625e68b832e0e55' isSod=False time='2025-03-17 03:27:01' statementDate='2025-03-17'
2025-06-09 03:14:14:156 Cbi.Position.RemoveFirstEntry: maintain exit order: orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=PartFilled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=1 averageFillPrice=19788.75 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:14:156 (Playback) Cbi.Account.OnAddTrade: entryId='6a7ddb16f57444fd9028c96144557a51' exitId='eb92eca7df7f4770bba8bde2bdb2878b' profitCurrencyBeforeCommissionAndFees=-455
2025-06-09 03:14:14:156 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19765.75 quantity=7 marketPosition=Short operation=Update
2025-06-09 03:14:14:158 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='e453e4a9c53c41438625e68b832e0e55' maxFillQuantity=7 price=19789 thread=43
2025-06-09 03:14:14:158 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='e453e4a9c53c41438625e68b832e0e55' fillQuantity=7 price=19789
2025-06-09 03:14:14:158 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19788.96875 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:200 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='26edeb42f7db40c69d047444a824f6de' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19789 quantity=7 marketPosition=Long operation=Add orderID='e453e4a9c53c41438625e68b832e0e55' isSod=False time='2025-03-17 03:27:01' statementDate='2025-03-17'
2025-06-09 03:14:14:200 (Playback) Cbi.Account.OnAddTrade: entryId='6421907c898f4baf8764844d45f56d51' exitId='26edeb42f7db40c69d047444a824f6de' profitCurrencyBeforeCommissionAndFees=-3255
2025-06-09 03:14:14:200 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-06-09 03:14:14:202 (Playback) Cbi.Account.CreateOrder: orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45588 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:14:202 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45588 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:14:202 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45588 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:14:202 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45588 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:14:202 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:235 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:236 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:236 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='93f683d759f24c9d898df8839a899ee9' maxFillQuantity=1 price=19788.75 thread=43
2025-06-09 03:14:14:236 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='93f683d759f24c9d898df8839a899ee9' fillQuantity=1 price=19788.75
2025-06-09 03:14:14:236 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=PartFilled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=1 averageFillPrice=19788.75 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:237 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='01105b2b65914232afe1e466c97d4f83' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19788.75 quantity=1 marketPosition=Long operation=Add orderID='93f683d759f24c9d898df8839a899ee9' isSod=False time='2025-03-17 03:27:01' statementDate='2025-03-17'
2025-06-09 03:14:14:237 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19788.75 quantity=1 marketPosition=Long operation=Add
2025-06-09 03:14:14:237 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='93f683d759f24c9d898df8839a899ee9' maxFillQuantity=7 price=19789 thread=43
2025-06-09 03:14:14:237 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='93f683d759f24c9d898df8839a899ee9' fillQuantity=7 price=19789
2025-06-09 03:14:14:237 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=0 quantity=8 orderType='Market' filled=8 averageFillPrice=19788.96875 time='2025-03-17 03:27:01' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:14:280 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='19af561807764ef7868fa024ee77049c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19789 quantity=7 marketPosition=Long operation=Add orderID='93f683d759f24c9d898df8839a899ee9' isSod=False time='2025-03-17 03:27:01' statementDate='2025-03-17'
2025-06-09 03:14:14:280 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19788.96875 quantity=8 marketPosition=Long operation=Update
2025-06-09 03:14:14:285 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:14:286 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:14:286 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:14:286 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:18:389 (Playback) Cbi.Account.CreateOrder: orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:18:389 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:18:389 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:18:389 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:18:390 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:419 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:419 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:419 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='3059f4ab9a3a4dbcb282597f46e86e9a' maxFillQuantity=1 price=19806.75 thread=43
2025-06-09 03:14:18:419 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='3059f4ab9a3a4dbcb282597f46e86e9a' fillQuantity=1 price=19806.75
2025-06-09 03:14:18:419 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=PartFilled orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=1 averageFillPrice=19806.75 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:421 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='b13d524c7f1e47bcab313ad95a6f304e' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19806.75 quantity=1 marketPosition=Short operation=Add orderID='3059f4ab9a3a4dbcb282597f46e86e9a' isSod=False time='2025-03-17 04:12:05' statementDate='2025-03-17'
2025-06-09 03:14:18:421 Cbi.Position.RemoveFirstEntry: maintain exit order: orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=PartFilled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=1 averageFillPrice=19806.75 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:18:421 (Playback) Cbi.Account.OnAddTrade: entryId='01105b2b65914232afe1e466c97d4f83' exitId='b13d524c7f1e47bcab313ad95a6f304e' profitCurrencyBeforeCommissionAndFees=360
2025-06-09 03:14:18:422 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19789 quantity=7 marketPosition=Long operation=Update
2025-06-09 03:14:18:422 (Playback) Cbi.Simulator.Fill1: realOrderState=PartFilled orderId='3059f4ab9a3a4dbcb282597f46e86e9a' maxFillQuantity=1 price=19806.5 thread=43
2025-06-09 03:14:18:422 (Playback) Cbi.Simulator.Fill2: realOrderState=PartFilled orderId='3059f4ab9a3a4dbcb282597f46e86e9a' fillQuantity=1 price=19806.5
2025-06-09 03:14:18:422 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=2 orderType='Market' filled=2 averageFillPrice=19806.625 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:467 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='17875b28f1fb47e798dd69b2de91700c' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19806.5 quantity=1 marketPosition=Short operation=Add orderID='3059f4ab9a3a4dbcb282597f46e86e9a' isSod=False time='2025-03-17 04:12:05' statementDate='2025-03-17'
2025-06-09 03:14:18:468 (Playback) Cbi.Account.OnAddTrade: entryId='19af561807764ef7868fa024ee77049c' exitId='17875b28f1fb47e798dd69b2de91700c' profitCurrencyBeforeCommissionAndFees=350
2025-06-09 03:14:18:468 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19789 quantity=6 marketPosition=Long operation=Update
2025-06-09 03:14:18:469 (Playback) Cbi.Account.CreateOrder: orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45590 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17' id=-1 comment=''
2025-06-09 03:14:18:469 (Playback) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45590 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:18:469 (Playback) Cbi.Account.Submit1: realOrderState=Initialized orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45590 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:18:469 (Playback) Cbi.Simulator.Submit: realOrderState=Initialized orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Submitted instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=45590 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17' delay=0
2025-06-09 03:14:18:469 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:521 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:521 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=0 averageFillPrice=0 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:522 (Playback) Cbi.Simulator.Fill1: realOrderState=Working orderId='b2d04b00fa324d9e88005aea83742476' maxFillQuantity=1 price=19806.75 thread=43
2025-06-09 03:14:18:522 (Playback) Cbi.Simulator.Fill2: realOrderState=Working orderId='b2d04b00fa324d9e88005aea83742476' fillQuantity=1 price=19806.75
2025-06-09 03:14:18:522 (Playback) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=1 orderType='Market' filled=1 averageFillPrice=19806.75 time='2025-03-17 04:12:05' statementDate='2025-03-17' error=NoError comment='' nr=-1
2025-06-09 03:14:18:573 (Playback) Cbi.Account.ExecutionUpdateCallback: executionId='def58726da004626a7d3d283a73692f8' account='Playback101' instrument='NQ JUN25' exchange=Globex price=19806.75 quantity=1 marketPosition=Short operation=Add orderID='b2d04b00fa324d9e88005aea83742476' isSod=False time='2025-03-17 04:12:05' statementDate='2025-03-17'
2025-06-09 03:14:18:573 (Playback) Cbi.Account.OnAddTrade: entryId='19af561807764ef7868fa024ee77049c' exitId='def58726da004626a7d3d283a73692f8' profitCurrencyBeforeCommissionAndFees=355
2025-06-09 03:14:18:573 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=19789 quantity=5 marketPosition=Long operation=Update
2025-06-09 03:14:18:578 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:18:578 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:18:578 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:18:578 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:23:028 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='48fed20597f844b08e4221e780619ab7' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19814.71875 onBehalfOf='' id=45573 time='2025-03-17 00:38:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='32562d0819fe49f0875e4bf6ac5cfa27' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19802.03125 onBehalfOf='' id=45574 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='05bae75056aa49f9bc8f12aef614621d' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19802.03125 onBehalfOf='' id=45575 time='2025-03-17 00:57:03' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='d98ab5d29d104c40b656fae40c4bd3ff' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19809.125 onBehalfOf='' id=45576 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='e0ef6c84b80e4c338c0fefebe6fc36ca' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19809.125 onBehalfOf='' id=45577 time='2025-03-17 01:18:25' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='d123163dda2a41179d8c6293a8f8ecb4' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19797.8125 onBehalfOf='' id=45578 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='9ef23ffcb7f540c185087787b411427b' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19797.8125 onBehalfOf='' id=45579 time='2025-03-17 02:10:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='68e1e9e152244b60b83a2c7a27d94fd6' account='Playback101' name='TP1 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=2 averageFillPrice=19784.625 onBehalfOf='' id=45580 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='cc9aae1ebc5242118fa29514be3c48d9' account='Playback101' name='Partial Profit Short' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=19784.5 onBehalfOf='' id=45581 time='2025-03-17 02:19:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='30878004ba0b4235b1277c8fddeb0271' account='Playback101' name='TP2 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=3 tif=Gtc oco='' filled=3 averageFillPrice=19781.********** onBehalfOf='' id=45582 time='2025-03-17 02:21:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='442b95825e3242a2952cdd64b8205ad4' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=2 averageFillPrice=19787.5 onBehalfOf='' id=45583 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='b71531865fdd46878c74ebfc951e7073' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19787.6875 onBehalfOf='' id=45584 time='2025-03-17 02:41:04' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='084ffd9a02ae45e994d5f29429d2a22e' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19765.78125 onBehalfOf='' id=45585 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='e975e16de8e34cbf9bef4c29478d637c' account='Playback101' name='Initial Short Entry' orderState=Filled instrument='NQ JUN25' orderAction=SellShort orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19765.78125 onBehalfOf='' id=45586 time='2025-03-17 03:19:02' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='e453e4a9c53c41438625e68b832e0e55' account='Playback101' name='Close position' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19788.96875 onBehalfOf='' id=45587 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='93f683d759f24c9d898df8839a899ee9' account='Playback101' name='Initial Long Entry' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=8 tif=Gtc oco='' filled=8 averageFillPrice=19788.96875 onBehalfOf='' id=45588 time='2025-03-17 03:27:01' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='3059f4ab9a3a4dbcb282597f46e86e9a' account='Playback101' name='TP1 Auto Exit' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=2 tif=Gtc oco='' filled=2 averageFillPrice=19806.625 onBehalfOf='' id=45589 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.ResetSimulationAccount.Cancel.Leave1: realOrderState=Filled orderId='b2d04b00fa324d9e88005aea83742476' account='Playback101' name='Partial Profit Long' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=19806.75 onBehalfOf='' id=45590 time='2025-03-17 04:12:05' gtd='2099-12-01' statementDate='2025-03-17'
2025-06-09 03:14:23:033 (Playback) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='Playback101' avgPrice=0 quantity=0 marketPosition=Long operation=Remove
2025-06-09 03:14:23:049 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:14:23:049 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:14:23:049 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:14:23:050 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:14:23:051 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:25:58:529 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-06-09 03:25:58:588 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-06-09 03:25:58:995 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-06-09 03:25:58:995 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8553242' renewSecs='2399.9276621'
2025-06-09 03:35:55:657 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:35:55:737 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:35:55:738 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:35:55:738 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:35:55:738 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:35:55:738 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:35:55:738 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:35:55:739 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:35:55:740 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:35:55:741 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:38:35:396 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:38:35:526 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:38:35:527 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:38:35:527 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:38:35:527 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:38:35:527 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:38:35:528 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:38:35:529 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:41:32:216 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:41:32:311 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:41:32:314 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:41:32:314 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:41:32:315 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:41:32:316 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:41:32:317 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:41:32:317 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:41:32:317 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:46:14:982 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-06-09 03:46:16:548 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-06-09 03:46:16:559 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.01ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=3.35ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.01ms
2025-06-09 03:53:37:275 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:53:37:290 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:53:37:291 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:54:13:258 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 03:54:13:268 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 03:54:13:269 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 03:56:37:384 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/18/2025 16:59:30'
2025-06-09 03:59:13:380 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/19/2025 16:59:30'
2025-06-09 04:01:15:725 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/20/2025 16:59:30'
2025-06-09 04:05:58:940 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-06-09 04:05:58:941 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-06-09 04:05:59:310 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-06-09 04:05:59:310 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.896319' renewSecs='2399.9481595'
2025-06-09 04:10:57:191 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 04:10:57:202 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 04:10:57:203 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
2025-06-09 04:12:49:245 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/18/2025 16:59:30'
2025-06-09 04:15:37:215 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/19/2025 16:59:30'
2025-06-09 04:18:07:843 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/20/2025 16:59:30'
2025-06-09 04:20:45:533 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/21/2025 16:59:30'
2025-06-09 04:23:25:721 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/24/2025 16:59:30'
2025-06-09 04:27:39:928 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/25/2025 16:59:30'
2025-06-09 04:30:20:775 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/26/2025 16:59:30'
2025-06-09 04:33:21:717 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/27/2025 16:59:30'
2025-06-09 04:36:30:940 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/28/2025 16:59:30'
2025-06-09 04:39:48:401 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='03/31/2025 16:59:30'
2025-06-09 04:41:22:662 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/01/2025 16:59:30'
2025-06-09 04:41:45:564 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/02/2025 16:59:30'
2025-06-09 04:42:12:935 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/03/2025 16:59:30'
2025-06-09 04:42:44:710 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/04/2025 16:59:30'
2025-06-09 04:43:19:382 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/07/2025 16:59:30'
2025-06-09 04:44:34:993 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/08/2025 16:59:30'
2025-06-09 04:45:04:615 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/09/2025 16:59:30'
2025-06-09 04:45:36:214 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/10/2025 16:59:30'
2025-06-09 04:45:59:266 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-06-09 04:45:59:267 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-06-09 04:45:59:612 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-06-09 04:45:59:613 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8971339' renewSecs='2399.94856695'
2025-06-09 04:46:04:525 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/11/2025 16:59:30'
2025-06-09 04:46:14:969 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-06-09 04:46:16:118 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-06-09 04:46:16:123 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.01ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=1.05ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.35ms
2025-06-09 04:46:26:737 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/14/2025 16:59:30'
2025-06-09 04:48:48:489 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/15/2025 16:59:30'
2025-06-09 04:49:23:584 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/16/2025 16:59:30'
2025-06-09 04:50:35:438 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/17/2025 16:59:30'
2025-06-09 04:51:27:161 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/21/2025 16:59:30'
2025-06-09 04:54:27:450 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/22/2025 16:59:30'
2025-06-09 04:55:11:844 NinjaScript.StrategyBase.ExitOnSessionCloseHandling: nextExitOnSessionClose='04/23/2025 16:59:30'
2025-06-09 04:55:41:196 (Playback) Cbi.Account.ResetSimulationAccount.Start: account='Playback101'
2025-06-09 04:55:41:209 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=BuyingPower currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=CashValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Commission currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=Fee currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=InitialMargin currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=IntradayMargin currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongOptionValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LookAheadMaintenanceMargin currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=LongStockValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=MaintenanceMargin currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=NetLiquidationByCurrency currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=PositionMargin currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortOptionValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=ShortStockValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodCashValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=SodLiquidatingValue currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=TotalCashBalance currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.AccountItemUpdateCallback: account='Playback101' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-06-09 04:55:41:210 (Playback) Cbi.Account.ResetSimulationAccount.End: account='Playback101'
