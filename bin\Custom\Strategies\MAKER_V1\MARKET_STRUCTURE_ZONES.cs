using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.Gui.Chart;

namespace NinjaTrader.NinjaScript.Strategies
{
    public partial class MAKER_V1 : Strategy
    {
        #region Market Structure Zone Parameters
        [NinjaScriptProperty]
        [Display(Name = "Enable Market Structure Zones", Order = 1, GroupName = "Market Structure Zones")]
        public bool EnableMarketStructureZones { get; set; }

        [NinjaScriptProperty]
        [Range(5, 50)]
        [Display(Name = "Swing Point Lookback", Order = 2, GroupName = "Market Structure Zones")]
        public int SwingPointLookback { get; set; }

        [NinjaScriptProperty]
        [Range(1, 20)]
        [Display(Name = "Zone Width (Ticks)", Order = 3, GroupName = "Market Structure Zones")]
        public int ZoneWidthTicks { get; set; }

        [NinjaScriptProperty]
        [Range(1, 10)]
        [Display(Name = "Minimum Zone Strength", Order = 4, GroupName = "Market Structure Zones")]
        public int MinimumZoneStrength { get; set; }

        [NinjaScriptProperty]
        [Range(1, 100)]
        [Display(Name = "Zone Proximity Threshold (Ticks)", Order = 5, GroupName = "Market Structure Zones")]
        public int ZoneProximityThresholdTicks { get; set; }
        #endregion

        #region Market Structure Zone Variables
        private Series<double> swingHigh;
        private Series<double> swingLow;
        private List<MarketStructureZone> supportZones;
        private List<MarketStructureZone> resistanceZones;
        private bool isNearSupportZone;
        private bool isNearResistanceZone;
        private MarketStructureZone nearestSupportZone;
        private MarketStructureZone nearestResistanceZone;
        #endregion

        #region Market Structure Zone Classes
        // Class to represent a market structure zone (support or resistance)
        private class MarketStructureZone
        {
            public double Price { get; set; }        // Center price of the zone
            public double UpperBound { get; set; }   // Upper boundary of the zone
            public double LowerBound { get; set; }   // Lower boundary of the zone
            public int Strength { get; set; }        // Number of times price has respected this zone
            public DateTime CreationTime { get; set; } // When the zone was first identified
            public List<DateTime> TouchTimes { get; set; } // Times when price touched this zone
            public bool IsActive { get; set; }       // Whether the zone is still considered valid

            public MarketStructureZone(double price, double zoneWidth, DateTime time)
            {
                Price = price;
                UpperBound = price + (zoneWidth / 2);
                LowerBound = price - (zoneWidth / 2);
                Strength = 1;
                CreationTime = time;
                TouchTimes = new List<DateTime> { time };
                IsActive = true;
            }

            // Check if a price is within this zone
            public bool ContainsPrice(double price)
            {
                return price >= LowerBound && price <= UpperBound;
            }

            // Update zone strength when price respects it
            public void AddTouch(DateTime time)
            {
                Strength++;
                TouchTimes.Add(time);
            }

            // Calculate distance from a price to this zone (negative if price is below zone)
            public double DistanceFromPrice(double price)
            {
                if (price > UpperBound) return price - UpperBound;
                if (price < LowerBound) return price - LowerBound;
                return 0; // Price is within zone
            }
        }
        #endregion

        #region Market Structure Zone Methods
        // Initialize Market Structure Zone components
        private void InitializeMarketStructureZones()
        {
            // Set default values if not specified
            if (SwingPointLookback == 0) SwingPointLookback = 10;
            if (ZoneWidthTicks == 0) ZoneWidthTicks = 4;
            if (MinimumZoneStrength == 0) MinimumZoneStrength = 2;
            if (ZoneProximityThresholdTicks == 0) ZoneProximityThresholdTicks = 10;

            // Initialize data structures
            swingHigh = new Series<double>(this);
            swingLow = new Series<double>(this);
            supportZones = new List<MarketStructureZone>();
            resistanceZones = new List<MarketStructureZone>();
            isNearSupportZone = false;
            isNearResistanceZone = false;

            LogContext(LogLevel.Info, LogCategory.SYSTEM,
                $"Market Structure Zones initialized with: Lookback={SwingPointLookback}, " +
                $"ZoneWidth={ZoneWidthTicks} ticks, MinStrength={MinimumZoneStrength}, " +
                $"ProximityThreshold={ZoneProximityThresholdTicks} ticks");
        }

        // Update swing points and market structure zones on each bar
        private void UpdateMarketStructureZones()
        {
            if (CurrentBar < SwingPointLookback) return;

            // Identify swing points
            IdentifySwingPoints();

            // Update support and resistance zones
            UpdateZones();

            // Check if price is near any zones
            CheckZoneProximity();

            // Log zone information periodically
            if (CurrentBar % 20 == 0 || CurrentLogLevel == LogLevel.Verbose)
            {
                LogZoneInformation();
            }
        }

        // Identify swing high and swing low points
        private void IdentifySwingPoints()
        {
            // Default values
            swingHigh[0] = 0;
            swingLow[0] = 0;

            // Check for swing high
            bool isSwingHigh = true;
            for (int i = 1; i <= SwingPointLookback; i++)
            {
                if (High[0] <= High[i])
                {
                    isSwingHigh = false;
                    break;
                }
            }

            // Check for swing low
            bool isSwingLow = true;
            for (int i = 1; i <= SwingPointLookback; i++)
            {
                if (Low[0] >= Low[i])
                {
                    isSwingLow = false;
                    break;
                }
            }

            // Mark swing points
            if (isSwingHigh)
            {
                swingHigh[0] = High[0];
                LogContext(LogLevel.Debug, LogCategory.SIGNAL, $"Swing High identified at {High[0]}");

                // Add new resistance zone or update existing one
                AddOrUpdateZone(High[0], false);
            }

            if (isSwingLow)
            {
                swingLow[0] = Low[0];
                LogContext(LogLevel.Debug, LogCategory.SIGNAL, $"Swing Low identified at {Low[0]}");

                // Add new support zone or update existing one
                AddOrUpdateZone(Low[0], true);
            }
        }

        // Add a new zone or update an existing one
        private void AddOrUpdateZone(double price, bool isSupport)
        {
            // Convert zone width from ticks to price
            double zoneWidth = ZoneWidthTicks * TickSize;

            // Reference the appropriate zone list
            List<MarketStructureZone> zones = isSupport ? supportZones : resistanceZones;

            // Check if price is near an existing zone
            bool zoneUpdated = false;
            foreach (var zone in zones)
            {
                if (Math.Abs(zone.Price - price) <= zoneWidth)
                {
                    // Update existing zone
                    zone.AddTouch(Time[0]);
                    zoneUpdated = true;
                    LogContext(LogLevel.Debug, LogCategory.SIGNAL,
                        $"Updated {(isSupport ? "support" : "resistance")} zone at {zone.Price}. New strength: {zone.Strength}");
                    break;
                }
            }

            // Create new zone if no existing zone was updated
            if (!zoneUpdated)
            {
                MarketStructureZone newZone = new MarketStructureZone(price, zoneWidth, Time[0]);
                zones.Add(newZone);
                LogContext(LogLevel.Debug, LogCategory.SIGNAL,
                    $"Created new {(isSupport ? "support" : "resistance")} zone at {price}");
            }
        }

        // Update all zones (remove old/weak zones, sort by strength)
        private void UpdateZones()
        {
            // Remove zones that are too old or weak
            DateTime cutoffTime = Time[0].AddDays(-1); // Consider zones from the last day only

            // Update support zones
            supportZones.RemoveAll(z => z.CreationTime < cutoffTime || z.Strength < MinimumZoneStrength);
            supportZones = supportZones.OrderByDescending(z => z.Strength).ToList();

            // Update resistance zones
            resistanceZones.RemoveAll(z => z.CreationTime < cutoffTime || z.Strength < MinimumZoneStrength);
            resistanceZones = resistanceZones.OrderByDescending(z => z.Strength).ToList();
        }

        // Check if current price is near any support or resistance zone
        private void CheckZoneProximity()
        {
            double proximityThreshold = ZoneProximityThresholdTicks * TickSize;
            double currentPrice = Close[0];

            // Reset proximity flags
            isNearSupportZone = false;
            isNearResistanceZone = false;
            nearestSupportZone = null;
            nearestResistanceZone = null;

            // Check support zones
            double minSupportDistance = double.MaxValue;
            foreach (var zone in supportZones)
            {
                double distance = Math.Abs(zone.Price - currentPrice);
                if (distance < minSupportDistance)
                {
                    minSupportDistance = distance;
                    nearestSupportZone = zone;
                }
            }

            // Check resistance zones
            double minResistanceDistance = double.MaxValue;
            foreach (var zone in resistanceZones)
            {
                double distance = Math.Abs(zone.Price - currentPrice);
                if (distance < minResistanceDistance)
                {
                    minResistanceDistance = distance;
                    nearestResistanceZone = zone;
                }
            }

            // Set proximity flags based on threshold
            if (nearestSupportZone != null && minSupportDistance <= proximityThreshold)
            {
                isNearSupportZone = true;
                LogContext(LogLevel.Debug, LogCategory.SIGNAL,
                    $"Price {currentPrice} is near support zone at {nearestSupportZone.Price} (strength: {nearestSupportZone.Strength})");
            }

            if (nearestResistanceZone != null && minResistanceDistance <= proximityThreshold)
            {
                isNearResistanceZone = true;
                LogContext(LogLevel.Debug, LogCategory.SIGNAL,
                    $"Price {currentPrice} is near resistance zone at {nearestResistanceZone.Price} (strength: {nearestResistanceZone.Strength})");
            }
        }

        // Calculate entry signal quality based on market structure zones - Simplified as requested
        private double CalculateMarketStructureSignalQuality()
        {
            // Signal quality calculation removed as requested
            LogContext(LogLevel.Debug, LogCategory.SIGNAL, "Market Structure signal quality calculation removed as requested");
            return 100; // Always return maximum quality since filtering is disabled
        }

        // Log information about current market structure zones
        private void LogZoneInformation()
        {
            StringBuilder sb = new StringBuilder();

            // Log support zones
            sb.AppendLine($"Support Zones ({supportZones.Count}):");
            foreach (var zone in supportZones.Take(5)) // Log top 5 strongest zones
            {
                sb.AppendLine($"  Price: {zone.Price:F2}, Strength: {zone.Strength}, " +
                               $"Range: {zone.LowerBound:F2}-{zone.UpperBound:F2}");
            }

            // Log resistance zones
            sb.AppendLine($"Resistance Zones ({resistanceZones.Count}):");
            foreach (var zone in resistanceZones.Take(5)) // Log top 5 strongest zones
            {
                sb.AppendLine($"  Price: {zone.Price:F2}, Strength: {zone.Strength}, " +
                               $"Range: {zone.LowerBound:F2}-{zone.UpperBound:F2}");
            }

            // Log current price proximity
            sb.AppendLine($"Current Price: {Close[0]:F2}");
            if (isNearSupportZone)
            {
                sb.AppendLine($"Near Support Zone: {nearestSupportZone.Price:F2} (Strength: {nearestSupportZone.Strength})");
            }
            if (isNearResistanceZone)
            {
                sb.AppendLine($"Near Resistance Zone: {nearestResistanceZone.Price:F2} (Strength: {nearestResistanceZone.Strength})");
            }

            LogContext(LogLevel.Info, LogCategory.SIGNAL, sb.ToString());
        }

        // Draw support and resistance zones on the chart
        private void RenderMarketStructureZones(ChartControl chartControl, ChartScale chartScale)
        {
            // Only draw zones if Market Structure Zones are enabled
            if (!EnableMarketStructureZones || !IsVisible) return;

            // Ensure we have a valid RenderTarget
            if (RenderTarget == null) return;

            // Create brushes for drawing zones if they don't exist
            SharpDX.Direct2D1.SolidColorBrush supportZoneBrush = null;
            SharpDX.Direct2D1.SolidColorBrush resistanceZoneBrush = null;
            SharpDX.Direct2D1.SolidColorBrush activeZoneBrush = null;

            try
            {
                // Create brushes with transparency
                supportZoneBrush = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget,
                    new SharpDX.Color(0, 200, 0, 100)); // Semi-transparent green
                resistanceZoneBrush = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget,
                    new SharpDX.Color(200, 0, 0, 100)); // Semi-transparent red
                activeZoneBrush = new SharpDX.Direct2D1.SolidColorBrush(RenderTarget,
                    new SharpDX.Color(255, 255, 0, 150)); // Semi-transparent yellow for active zones

                // Draw support zones
                foreach (var zone in supportZones)
                {
                    // Convert zone prices to Y coordinates
                    float upperY = chartScale.GetYByValue(zone.UpperBound);
                    float lowerY = chartScale.GetYByValue(zone.LowerBound);
                    float zoneHeight = Math.Abs(lowerY - upperY);

                    // Get X coordinates for the visible range
                    float startX = chartControl.CanvasLeft;
                    float endX = chartControl.CanvasRight;

                    // Create rectangle for the zone
                    SharpDX.RectangleF rect = new SharpDX.RectangleF(startX, upperY, endX - startX, zoneHeight);

                    // Draw the zone with appropriate brush based on proximity
                    if (isNearSupportZone && nearestSupportZone == zone)
                    {
                        RenderTarget.FillRectangle(rect, activeZoneBrush);
                    }
                    else
                    {
                        RenderTarget.FillRectangle(rect, supportZoneBrush);
                    }

                    // Draw zone strength indicator
                    string strengthText = $"S{zone.Strength}";
                    SharpDX.DirectWrite.TextFormat textFormat = new SharpDX.DirectWrite.TextFormat(Core.Globals.DirectWriteFactory,
                        "Arial", SharpDX.DirectWrite.FontWeight.Bold, SharpDX.DirectWrite.FontStyle.Normal, 10);

                    // Calculate text position
                    float textX = endX - 40; // Position near right edge
                    float textY = (upperY + lowerY) / 2 - 7; // Vertically centered in zone

                    // Draw text
                    RenderTarget.DrawText(strengthText, textFormat,
                        new SharpDX.RectangleF(textX, textY, 30, 15),
                        isNearSupportZone && nearestSupportZone == zone ? activeZoneBrush : supportZoneBrush);

                    // Dispose text format
                    textFormat.Dispose();
                }

                // Draw resistance zones
                foreach (var zone in resistanceZones)
                {
                    // Convert zone prices to Y coordinates
                    float upperY = chartScale.GetYByValue(zone.UpperBound);
                    float lowerY = chartScale.GetYByValue(zone.LowerBound);
                    float zoneHeight = Math.Abs(lowerY - upperY);

                    // Get X coordinates for the visible range
                    float startX = chartControl.CanvasLeft;
                    float endX = chartControl.CanvasRight;

                    // Create rectangle for the zone
                    SharpDX.RectangleF rect = new SharpDX.RectangleF(startX, upperY, endX - startX, zoneHeight);

                    // Draw the zone with appropriate brush based on proximity
                    if (isNearResistanceZone && nearestResistanceZone == zone)
                    {
                        RenderTarget.FillRectangle(rect, activeZoneBrush);
                    }
                    else
                    {
                        RenderTarget.FillRectangle(rect, resistanceZoneBrush);
                    }

                    // Draw zone strength indicator
                    string strengthText = $"R{zone.Strength}";
                    SharpDX.DirectWrite.TextFormat textFormat = new SharpDX.DirectWrite.TextFormat(Core.Globals.DirectWriteFactory,
                        "Arial", SharpDX.DirectWrite.FontWeight.Bold, SharpDX.DirectWrite.FontStyle.Normal, 10);

                    // Calculate text position
                    float textX = endX - 40; // Position near right edge
                    float textY = (upperY + lowerY) / 2 - 7; // Vertically centered in zone

                    // Draw text
                    RenderTarget.DrawText(strengthText, textFormat,
                        new SharpDX.RectangleF(textX, textY, 30, 15),
                        isNearResistanceZone && nearestResistanceZone == zone ? activeZoneBrush : resistanceZoneBrush);

                    // Dispose text format
                    textFormat.Dispose();
                }
            }
            finally
            {
                // Dispose brushes
                if (supportZoneBrush != null) supportZoneBrush.Dispose();
                if (resistanceZoneBrush != null) resistanceZoneBrush.Dispose();
                if (activeZoneBrush != null) activeZoneBrush.Dispose();
            }
        }
        #endregion
    }
}