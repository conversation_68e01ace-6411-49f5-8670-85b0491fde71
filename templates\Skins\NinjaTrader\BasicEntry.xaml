﻿<ResourceDictionary xmlns		="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x		="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:po	="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options">

	<SolidColorBrush x:Key="BasicEntry.UptickBackground"			Color="Honeydew"		po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.UptickForeground"			Color="Black"			po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.DowntickBackground"			Color="LavenderBlush"	po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.DowntickForeground"			Color="Black"			po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.TextBoxBackground"			Color="White"			po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.PositionQtyLongBackground"	Color="LimeGreen"		po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.PositionQtyShortBackground"	Color="Red"				po:Freeze="true" />
	<SolidColorBrush x:Key="BasicEntry.PnLBackground"				Color="Black"			po:Freeze="true" />
	<LinearGradientBrush	po:Freeze="true"	x:Key="BasicEntry.ButtonBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>		
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="BasicEntry.ActionButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="BasicEntry.BuyButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="BasicEntry.SellButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
</ResourceDictionary>