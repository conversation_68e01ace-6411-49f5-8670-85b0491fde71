#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.NinjaScript;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Strategies;
#endregion

namespace NinjaTrader.NinjaScript.Strategies.KCStrategies
{      
	public enum SignalCombinationMode
    {
        TEFASignalsProOnly,
        JBSignalOnly,
        BothConfirm,
        TEFA_FilteredBy_JB,
        JB_FilteredBy_TEFA
    }
		
    public class TEFABot : KCAlgoBase
    {  
        private TEFASignalsPro tefaSignals;
        private JBSignal jbSignal;

        private SignalCombinationMode signalMode = SignalCombinationMode.TEFASignalsProOnly;

		public override string DisplayName { get { return Name; } }
		
        protected override void OnStateChange()
        {
            base.OnStateChange();

            if (State == State.SetDefaults)
            {
                Description = "Strategy based on the TEFA Signals Pro indicator.";
                Name = "TEFABot v1.0.0";
                StrategyName = "TEFABot";
                Version = "1.0.0 June 2025";
                Credits = "Strategy by Khanh Nguyen";
                ChartType =  "1 Minute Chart";

				// --- Initialize ALL TEFASignalsPro Parameters with their defaults ---
                TEFA_Fast = 5; TEFA_Slow = 8; TEFA_Smooth = 5;
                TEFA_WilliamsRPeriod = 21; TEFA_WilliamsREMAPeriod = 13;
                TEFA_CCIPeriod = 21; TEFA_CCIEMAPeriod = 13;
                TEFA_MomentumPeriod = 21; TEFA_MomentumEMAPeriod = 13;
                TEFA_StochasticKPeriod = 21; TEFA_StochasticDPeriod = 13; TEFA_StochasticSmooth = 3;
                TEFA_SamplingPeriod = 10; TEFA_RangeMultiplier = 1.0;
                TEFA_SelectedMAType = UMAType.SMA; TEFA_PeriodMA = 5; TEFA_TCount = 3; TEFA_VFactor = 0.7; TEFA_SmoothLength = 2;
                TEFA_SelectedTimeFrameMode = TimeFrameModeTEFASignalsPro.ChartTimeFrame;
                TEFA_SecondaryPeriodType = BarsPeriodType.Minute; TEFA_SecondaryPeriodValue = 1;
                TEFA_LookBackBars = 2;
                TEFA_UseMACD = true; TEFA_UseWilliamsR = true; TEFA_UseCCI = true; TEFA_UseMomentum = true; TEFA_UseStochastics = true;
                TEFA_UseRangeFilter = true; TEFA_UseUltimateMA = true; TEFA_UseOpenCloseCondition = true; TEFA_UseHighLowCondition = false;
                TEFA_BuySignalColor = Brushes.Cyan; TEFA_SellSignalColor = Brushes.Magenta; TEFA_ColorBackground = true;
                TEFA_BuyBackgroundOpacity = 0.25; TEFA_SellBackgroundOpacity = 0.25;
                TEFA_SignalFontSize = 12; TEFA_TextOffsetMultiplier = 5;

                // JBSignal Default Parameters
                JB_Fast = 12; JB_Slow = 26; JB_Smooth = 9;
                JB_WilliamsRPeriod = 21; JB_WilliamsREMAPeriod = 13;
                JB_FastAlmaLength = 19; JB_SlowAlmaLength = 20;

                // UseEMATrendFilter = true; // REMOVED
                SignalMode = SignalCombinationMode.TEFASignalsProOnly;
				
				VmaPeriod			= 5;
				VmaVolatilityPeriod	= 8;
				enableVMA			= true;
				showVMA				= true;
				
                InitialStop		= 140;
				ProfitTarget	= 110;
            }
            else if (State == State.DataLoaded)
            {
                InitializeIndicators();
            }
        }

        #region Indicators
        protected override void InitializeIndicators()
        {
            if (SignalMode == SignalCombinationMode.TEFASignalsProOnly ||
                SignalMode == SignalCombinationMode.BothConfirm ||
                SignalMode == SignalCombinationMode.TEFA_FilteredBy_JB ||
                SignalMode == SignalCombinationMode.JB_FilteredBy_TEFA)
            {
                tefaSignals = TEFASignalsPro(
                    TEFA_Fast, TEFA_Slow, TEFA_Smooth,
                    TEFA_WilliamsRPeriod, TEFA_WilliamsREMAPeriod,
                    TEFA_CCIPeriod, TEFA_CCIEMAPeriod,
                    TEFA_MomentumPeriod, TEFA_MomentumEMAPeriod,
                    TEFA_StochasticKPeriod, TEFA_StochasticDPeriod, TEFA_StochasticSmooth,
                    TEFA_SamplingPeriod, TEFA_RangeMultiplier,
                    TEFA_SelectedMAType, TEFA_PeriodMA, TEFA_TCount, TEFA_VFactor, TEFA_SmoothLength,
                    TEFA_SelectedTimeFrameMode, TEFA_SecondaryPeriodType, TEFA_SecondaryPeriodValue,
                    TEFA_LookBackBars,
                    TEFA_UseMACD, TEFA_UseWilliamsR, TEFA_UseCCI, TEFA_UseMomentum, TEFA_UseStochastics,
                    TEFA_UseRangeFilter, TEFA_UseUltimateMA, TEFA_UseOpenCloseCondition, TEFA_UseHighLowCondition,
                    TEFA_BuySignalColor, TEFA_SellSignalColor, TEFA_ColorBackground,
                    TEFA_BuyBackgroundOpacity, TEFA_SellBackgroundOpacity,
                    TEFA_SignalFontSize, TEFA_TextOffsetMultiplier
                );
                AddChartIndicator(tefaSignals);
            }

            if (SignalMode == SignalCombinationMode.JBSignalOnly ||
                SignalMode == SignalCombinationMode.BothConfirm ||
                SignalMode == SignalCombinationMode.TEFA_FilteredBy_JB ||
                SignalMode == SignalCombinationMode.JB_FilteredBy_TEFA)
            {
                jbSignal = JBSignal(JB_Fast, JB_Slow, JB_Smooth, JB_WilliamsRPeriod, JB_WilliamsREMAPeriod, JB_FastAlmaLength, JB_SlowAlmaLength);
                AddChartIndicator(jbSignal);
            }
        }
        #endregion

        protected override void OnBarUpdate()
        {
            if (CurrentBars[0] < BarsRequiredToTrade)
                return;

            base.OnBarUpdate();
        }

        protected override bool ValidateEntryLong()
        {
            bool tefaLongSignal = false;
            if (tefaSignals != null && tefaSignals.Values[1].IsValidDataPoint(0))
            {
                tefaLongSignal = tefaSignals.Values[1][0].ApproxCompare(1) == 0;
            }

            bool jbLongSignal = false;
            if (jbSignal != null)
            {
                jbLongSignal = jbSignal.StrategySignal == 1;
            }

            bool finalLongSignal = false;
            switch (SignalMode)
            {
                case SignalCombinationMode.TEFASignalsProOnly:
                    finalLongSignal = tefaLongSignal;
                    break;
                case SignalCombinationMode.JBSignalOnly:
                    finalLongSignal = jbLongSignal;
                    break;
                case SignalCombinationMode.BothConfirm:
                    finalLongSignal = tefaLongSignal && jbLongSignal;
                    break;
                case SignalCombinationMode.TEFA_FilteredBy_JB:
                    finalLongSignal = tefaLongSignal && jbLongSignal;
                    break;
                case SignalCombinationMode.JB_FilteredBy_TEFA:
                    finalLongSignal = jbLongSignal && tefaLongSignal;
                    break;
            }

            if (!finalLongSignal) return false;

            PrintOnce($"Bot_Long_Valid_{CurrentBar}", $"{Time[0]}: {Name} Valid Long Signal. Mode: {SignalMode}, TEFA: {tefaLongSignal}, JB: {jbLongSignal}");
            return true;
		}

        protected override bool ValidateEntryShort()
        {
            bool tefaShortSignal = false;
            if (tefaSignals != null && tefaSignals.Values[1].IsValidDataPoint(0))
            {
                tefaShortSignal = tefaSignals.Values[1][0].ApproxCompare(-1) == 0;
            }

            bool jbShortSignal = false;
            if (jbSignal != null)
            {
                jbShortSignal = jbSignal.StrategySignal == -1;
            }

            bool finalShortSignal = false;
            switch (SignalMode)
            {
                case SignalCombinationMode.TEFASignalsProOnly:
                    finalShortSignal = tefaShortSignal;
                    break;
                case SignalCombinationMode.JBSignalOnly:
                    finalShortSignal = jbShortSignal;
                    break;
                case SignalCombinationMode.BothConfirm:
                    finalShortSignal = tefaShortSignal && jbShortSignal;
                    break;
                case SignalCombinationMode.TEFA_FilteredBy_JB:
                    finalShortSignal = tefaShortSignal && jbShortSignal;
                    break;
                case SignalCombinationMode.JB_FilteredBy_TEFA:
                    finalShortSignal = jbShortSignal && tefaShortSignal;
                    break;
            }

            if (!finalShortSignal) return false;

            PrintOnce($"Bot_Short_Valid_{CurrentBar}", $"{Time[0]}: {Name} Valid Short Signal. Mode: {SignalMode}, TEFA: {tefaShortSignal}, JB: {jbShortSignal}");
            return true;
        }

//       	protected override bool ValidateExitLong()
//        {
//            // Logic for validating long exits
//            return enableExit? true: false;
//        }

//        protected override bool ValidateExitShort()
//        {
//			// Logic for validating short exits
//			return enableExit? true: false;
//        }
		
		#region Parameters		
        // --- Strategy Signal Combination Logic ---
        [NinjaScriptProperty] [Display(Name = "Signal Combination Mode", Order = 400, GroupName = "07a. Strategy Logic - Signal Source")] public SignalCombinationMode SignalMode { get { return signalMode; } set { signalMode = value; } }
        
        // --- TEFASignalsPro Parameters --- (kept from previous version)
        private const string GRP_TEFA = "07b. Indicator Settings - TEFASignalsPro";
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA MACD Fast", GroupName = GRP_TEFA, Order = 100)] public int TEFA_Fast { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA MACD Slow", GroupName = GRP_TEFA, Order = 101)] public int TEFA_Slow { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA MACD Smooth", GroupName = GRP_TEFA, Order = 102)] public int TEFA_Smooth { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA WilliamsR Period", GroupName = GRP_TEFA, Order = 110)] public int TEFA_WilliamsRPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA WilliamsR EMA Period", GroupName = GRP_TEFA, Order = 111)] public int TEFA_WilliamsREMAPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA CCI Period", GroupName = GRP_TEFA, Order = 120)] public int TEFA_CCIPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA CCI EMA Period", GroupName = GRP_TEFA, Order = 121)] public int TEFA_CCIEMAPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Momentum Period", GroupName = GRP_TEFA, Order = 130)] public int TEFA_MomentumPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Momentum EMA Period", GroupName = GRP_TEFA, Order = 131)] public int TEFA_MomentumEMAPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Stoch K Period", GroupName = GRP_TEFA, Order = 140)] public int TEFA_StochasticKPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Stoch D Period", GroupName = GRP_TEFA, Order = 141)] public int TEFA_StochasticDPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Stoch Smooth", GroupName = GRP_TEFA, Order = 142)] public int TEFA_StochasticSmooth { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Sampling Period", GroupName = GRP_TEFA, Order = 150)] public int TEFA_SamplingPeriod { get; set; }
        [NinjaScriptProperty] [Range(0.1, double.MaxValue)] [Display(Name = "TEFA Range Multiplier", GroupName = GRP_TEFA, Order = 151)] public double TEFA_RangeMultiplier { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA MA Type", GroupName = GRP_TEFA, Order = 160)] public UMAType TEFA_SelectedMAType { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA MA Period", GroupName = GRP_TEFA, Order = 161)] public int TEFA_PeriodMA { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA TCount", GroupName = GRP_TEFA, Order = 162)] public int TEFA_TCount { get; set; }
        [NinjaScriptProperty] [Range(0, double.MaxValue)] [Display(Name = "TEFA VFactor", GroupName = GRP_TEFA, Order = 163)] public double TEFA_VFactor { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Smooth Length", GroupName = GRP_TEFA, Order = 164)] public int TEFA_SmoothLength { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Time Frame Mode", GroupName = GRP_TEFA, Order = 165)] public TimeFrameModeTEFASignalsPro TEFA_SelectedTimeFrameMode { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Secondary Period Type", GroupName = GRP_TEFA, Order = 166)] public BarsPeriodType TEFA_SecondaryPeriodType { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA Secondary Period Value", GroupName = GRP_TEFA, Order = 167)] public int TEFA_SecondaryPeriodValue { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "TEFA LookBack Bars H/L", GroupName = GRP_TEFA, Order = 170)] public int TEFA_LookBackBars { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use MACD", GroupName = GRP_TEFA, Order = 180)] public bool TEFA_UseMACD { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Williams %R", GroupName = GRP_TEFA, Order = 181)] public bool TEFA_UseWilliamsR { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use CCI", GroupName = GRP_TEFA, Order = 182)] public bool TEFA_UseCCI { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Momentum", GroupName = GRP_TEFA, Order = 183)] public bool TEFA_UseMomentum { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Stochastics", GroupName = GRP_TEFA, Order = 184)] public bool TEFA_UseStochastics { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Range Filter", GroupName = GRP_TEFA, Order = 185)] public bool TEFA_UseRangeFilter { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Ultimate MA", GroupName = GRP_TEFA, Order = 186)] public bool TEFA_UseUltimateMA { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use Open/Close Cond", GroupName = GRP_TEFA, Order = 187)] public bool TEFA_UseOpenCloseCondition { get; set; }
        [NinjaScriptProperty] [Display(Name = "TEFA Use High/Low Cond", GroupName = GRP_TEFA, Order = 188)] public bool TEFA_UseHighLowCondition { get; set; }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name = "TEFA Buy Signal Color", GroupName = GRP_TEFA, Order = 190)] public Brush TEFA_BuySignalColor { get; set; }
        [Browsable(false)] public string TEFA_BuySignalColorSerializable { get { return Serialize.BrushToString(TEFA_BuySignalColor); } set { TEFA_BuySignalColor = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name = "TEFA Sell Signal Color", GroupName = GRP_TEFA, Order = 191)] public Brush TEFA_SellSignalColor { get; set; }
        [Browsable(false)] public string TEFA_SellSignalColorSerializable { get { return Serialize.BrushToString(TEFA_SellSignalColor); } set { TEFA_SellSignalColor = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [Display(Name = "TEFA Color Background", GroupName = GRP_TEFA, Order = 192)] public bool TEFA_ColorBackground { get; set; }
        [NinjaScriptProperty] [Range(0.0, 1.0)] [Display(Name = "TEFA Buy BG Opacity", GroupName = GRP_TEFA, Order = 193)] public double TEFA_BuyBackgroundOpacity { get; set; }
        [NinjaScriptProperty] [Range(0.0, 1.0)] [Display(Name = "TEFA Sell BG Opacity", GroupName = GRP_TEFA, Order = 194)] public double TEFA_SellBackgroundOpacity { get; set; }
        [NinjaScriptProperty] [Range(1, 100)] [Display(Name = "TEFA Signal Font Size", GroupName = GRP_TEFA, Order = 195)] public int TEFA_SignalFontSize { get; set; }
        [NinjaScriptProperty] [Range(1, 50)] [Display(Name = "TEFA Text Offset Multi", GroupName = GRP_TEFA, Order = 196)] public int TEFA_TextOffsetMultiplier { get; set; }

        // --- JBSignal Parameters --- (kept from previous version)
        private const string GRP_JB = "07c. Indicator Settings - JBSignal";
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "JB MACD Fast", GroupName = GRP_JB, Order = 200)] public int JB_Fast { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "JB MACD Slow", GroupName = GRP_JB, Order = 201)] public int JB_Slow { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "JB MACD Smooth", GroupName = GRP_JB, Order = 202)] public int JB_Smooth { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "JB WilliamsR Period", GroupName = GRP_JB, Order = 203)] public int JB_WilliamsRPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "JB WilliamsR EMA Period", GroupName = GRP_JB, Order = 204)] public int JB_WilliamsREMAPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, double.MaxValue)] [Display(Name = "JB Fast ALMA Length", GroupName = GRP_JB, Order = 205)] public double JB_FastAlmaLength { get; set; }
        [NinjaScriptProperty] [Range(1, double.MaxValue)] [Display(Name = "JB Slow ALMA Length", GroupName = GRP_JB, Order = 206)] public double JB_SlowAlmaLength { get; set; }

        // --- VMA Parameters ---
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "VMA Period", GroupName = "07d. VMA Settings", Order = 300)] public int VmaPeriod { get; set; }
        [NinjaScriptProperty] [Range(1, int.MaxValue)] [Display(Name = "VMA Volatility Period", GroupName = "07d. VMA Settings", Order = 301)] public int VmaVolatilityPeriod { get; set; }
        [NinjaScriptProperty] [Display(Name = "Enable VMA", GroupName = "07d. VMA Settings", Order = 302)] public bool enableVMA { get; set; }
        [NinjaScriptProperty] [Display(Name = "Show VMA", GroupName = "07d. VMA Settings", Order = 303)] public bool showVMA { get; set; }
		#endregion
    }
}
