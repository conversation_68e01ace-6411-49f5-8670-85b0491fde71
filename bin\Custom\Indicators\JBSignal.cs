#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class JBSignal : Indicator
    {
        // Private INDI
        private MACD macd;
        private WilliamsREMA williamsREMA;
        private ALMA alma;
		private ALMA alma2;
        
        // Bool Signals
        private bool LastbuySignal = false;
        private bool LastsellSignal = false;

        // Strategy Signal property for external access
        public int StrategySignal { get; private set; } = 0; // 0 = no signal, 1 = buy, -1 = sell
        
        #region 1. MACD
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Fast", GroupName = "1. MACD", Order = 0)]
        public int Fast { get; set; } = 12;
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Slow", GroupName = "1. MACD", Order = 1)]
        public int Slow { get; set; } = 26;
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(ResourceType = typeof(Custom.Resource), Name = "Smooth", GroupName = "1. MACD", Order = 2)]
        public int Smooth { get; set; } = 9;
        #endregion
        
        #region 2. WR EMA
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Williams %R Period", GroupName = "2. WR EMA", Order = 0)]
        public int WilliamsRPeriod { get; set; } = 21;
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Williams %R Fast EMA Period", GroupName = "2. WR EMA", Order = 1)]
        public int WilliamsREMAPeriod { get; set; } = 13;
        #endregion
        
        #region 3. ALMA
        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Fast ALMA Length", GroupName = "3. ALMA", Order = 0)]
        public double fastAlmaLength { get; set; } = 19;
		[Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Slow ALMA Length", GroupName = "3. ALMA", Order = 0)]
        public double slowAlmaLength { get; set; } = 20;
        #endregion
        
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Enter the description for your new custom Indicator here.";
                Name = "JBSignal";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true; 
				
				AddPlot(Brushes.Lime, "Fast ALMA");
				AddPlot(Brushes.Orange, "Slow ALMA");
            }
            else if (State == State.Configure)
            {
            }
            else if (State == State.DataLoaded)
            {
                macd = MACD(Fast, Slow, Smooth);
                williamsREMA = WilliamsREMA(WilliamsRPeriod, WilliamsREMAPeriod);
                alma = ALMA(fastAlmaLength, 0.85, 6.0);
				alma2 = ALMA(slowAlmaLength, 0.85, 6.0);
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < Math.Max(Fast, Math.Max(Slow, Smooth)))
                return;
            
            if (CurrentBar < Math.Max(WilliamsRPeriod, WilliamsREMAPeriod) || CurrentBar < Math.Max(fastAlmaLength, slowAlmaLength))
                return;
			
            double macdDiff = macd.Diff[0];
			double macdDiffprev = macd.Diff[1];
            double williamsValue = williamsREMA[0];
            double emaOfWilliamsR = williamsREMA.Values[1][0];
            double almaValue = alma[0];
			double almaValue2 = alma2[0];
            double close = Close[0];
            double open = Open[0];
			
			Values[0][0] = almaValue;
			Values[1][0] = almaValue2;
			
            if (williamsValue > emaOfWilliamsR && macdDiff > 0 && macdDiff > macdDiffprev && close > almaValue && almaValue > almaValue2 && close > open && !LastbuySignal)
            {
                Draw.Text(this, "BUY" + CurrentBar, "▲", 0, Low[0] - 3 * TickSize, Brushes.Lime);

                LastbuySignal = true;
                LastsellSignal = false;
                StrategySignal = 1; // Buy signal
            }
            else if (williamsValue < emaOfWilliamsR && macdDiff < 0 && macdDiff < macdDiffprev && close < almaValue && almaValue < almaValue2 && close < open && !LastsellSignal)
            {
                Draw.Text(this, "SELL" + CurrentBar, "▼", 0, High[0] + 5 * TickSize, Brushes.Orange);

                LastbuySignal = false;
                LastsellSignal = true;
                StrategySignal = -1; // Sell signal
            }
            else
            {
                StrategySignal = 0; // No signal
            }
        }
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private JBSignal[] cacheJBSignal;
		public JBSignal JBSignal(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			return JBSignal(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, fastAlmaLength, slowAlmaLength);
		}

		public JBSignal JBSignal(ISeries<double> input, int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			if (cacheJBSignal != null)
				for (int idx = 0; idx < cacheJBSignal.Length; idx++)
					if (cacheJBSignal[idx] != null && cacheJBSignal[idx].Fast == fast && cacheJBSignal[idx].Slow == slow && cacheJBSignal[idx].Smooth == smooth && cacheJBSignal[idx].WilliamsRPeriod == williamsRPeriod && cacheJBSignal[idx].WilliamsREMAPeriod == williamsREMAPeriod && cacheJBSignal[idx].fastAlmaLength == fastAlmaLength && cacheJBSignal[idx].slowAlmaLength == slowAlmaLength && cacheJBSignal[idx].EqualsInput(input))
						return cacheJBSignal[idx];
			return CacheIndicator<JBSignal>(new JBSignal(){ Fast = fast, Slow = slow, Smooth = smooth, WilliamsRPeriod = williamsRPeriod, WilliamsREMAPeriod = williamsREMAPeriod, fastAlmaLength = fastAlmaLength, slowAlmaLength = slowAlmaLength }, input, ref cacheJBSignal);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.JBSignal JBSignal(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			return indicator.JBSignal(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, fastAlmaLength, slowAlmaLength);
		}

		public Indicators.JBSignal JBSignal(ISeries<double> input , int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			return indicator.JBSignal(input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, fastAlmaLength, slowAlmaLength);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.JBSignal JBSignal(int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			return indicator.JBSignal(Input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, fastAlmaLength, slowAlmaLength);
		}

		public Indicators.JBSignal JBSignal(ISeries<double> input , int fast, int slow, int smooth, int williamsRPeriod, int williamsREMAPeriod, double fastAlmaLength, double slowAlmaLength)
		{
			return indicator.JBSignal(input, fast, slow, smooth, williamsRPeriod, williamsREMAPeriod, fastAlmaLength, slowAlmaLength);
		}
	}
}

#endregion
