#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.NinjaScript;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Strategies;
#endregion

namespace NinjaTrader.NinjaScript.Strategies.KCStrategies
{
    public class Andean : KCAlgoBase
    {
        // Parameters
		private AndeanOscillator AndeanOscillator1; 
        private Series<double> bull;
        private Series<double> bear;
		private Series<double> signal;
		
		public override string DisplayName { get { return Name; } }
		
        protected override void OnStateChange()
        {
            base.OnStateChange();

            if (State == State.SetDefaults)
            {
                Description = "Strategy based on the Andean Oscillator.";
                Name = "Andean v1.0";
                StrategyName = "Andean";
                Version = "1.0 May 2025";
                Credits = "Strategy by Khanh Nguyen";
                ChartType =  "Orenko 34-40-40, Tbars 50";	
				
				Length = 20;
				SignalLineLength = 9;
				
//                InitialStop		= 140;
//				ProfitTarget	= 32;
            }
            else if (State == State.DataLoaded)
            {
                InitializeIndicators();
            }
        }
		
        protected override void OnBarUpdate()
        {
            if (CurrentBars[0] < BarsRequiredToTrade)
                return;			
			
            // Check if AndeanOscillator1 is initialized and ready (optional, but good practice)
            if (AndeanOscillator1 == null) // Or a more robust check if AndeanOscillator1 has an IsReady property
            {
                // Optionally print a message or log if the indicator isn't ready
                return;
            }

		    // Assign current values from the indicator to your series.
            // This is correct if you want to store them, but for CrossAbove,
            // you can often use the indicator plots directly if they are public.
            // However, since AndeanOscillator plots might not be directly ISeries<double>
            // accessible in the same way as `Close`, copying to your own Series<double> is fine.
		    bull[0] = AndeanOscillator1.Bull[0];   // Assuming AndeanOscillator has public Bull, Bear, Signal plots/series
		    bear[0] = AndeanOscillator1.Bear[0];   
		    signal[0] = AndeanOscillator1.SignalLine[0]; // Or whatever the signal plot is named in AndeanOscillator
		
            // Reset signals at the start of each bar update
            longSignal = false;
            shortSignal = false;

            // Corrected CrossAbove calls: Pass the Series<double> objects themselves
			if (CrossAbove(AndeanOscillator1.Bull, AndeanOscillator1.Bear, 1)
			    && (AndeanOscillator1.Bull[0] > AndeanOscillator1.SignalLine[0])
			    && (AndeanOscillator1.SignalLine[0] > AndeanOscillator1.Bear[0]))
			{
				longSignal = true;
			}
			
            // Assuming you meant CrossAbove(bear, bull, 1) for a short signal based on bear crossing above bull
            // OR CrossBelow(bull, bear, 1) if you meant bull crossing below bear.
            // I'll assume bear crossing above bull for this example. Adjust if needed.
			if (CrossAbove(AndeanOscillator1.Bear, AndeanOscillator1.Bull, 1)
			    && (AndeanOscillator1.Bear[0] > AndeanOscillator1.SignalLine[0])
			    && (AndeanOscillator1.SignalLine[0] > AndeanOscillator1.Bull[0]))
			{
				shortSignal = true;
			}
            
            // Example of a more typical short signal structure with CrossAbove(bear, bull)
            // if (CrossAbove(bear, bull, 1) // Bearish momentum crossing above bullish
            //     && (bear[0] > signal[0])   // Bearish momentum is above signal line
            //     && (bull[0] < signal[0]))  // Bullish momentum is below signal line (or bull[0] < bear[0])
            // {
            //     shortSignal = true;
            // }
			
			base.OnBarUpdate();
        }
		
        protected override bool ValidateEntryLong()
        {
            // Logic for validating long entries
			if (longSignal) return true;
			else return false;
        }

        protected override bool ValidateEntryShort()
        {
            // Logic for validating short entries
			if (shortSignal) return true;
            else return false;
        }

        #region Indicators
        protected override void InitializeIndicators()
        {		
			bull = new Series<double> (this);
			bear = new Series<double> (this);
			signal = new Series<double> (this);		
			
			AndeanOscillator1				= AndeanOscillator(Close, Length, SignalLineLength);
			AndeanOscillator1.Plots[0].Brush = Brushes.LimeGreen;
			AndeanOscillator1.Plots[1].Brush = Brushes.Red;
			AndeanOscillator1.Plots[2].Brush = Brushes.Orange;	
			AndeanOscillator1.Plots[0].Width = 2;
			AndeanOscillator1.Plots[1].Width = 2;
			AndeanOscillator1.Plots[2].Width = 2;
			AddChartIndicator(AndeanOscillator1);
        }
        #endregion

        #region Properties

		[NinjaScriptProperty]
        [Display(Name = "Length", Order = 1, GroupName="08a. Strategy Settings")]
        public int Length { get; set; }		
		
		[NinjaScriptProperty]
		[Display(Name="SignalLine Length", Order = 2, GroupName="08a. Strategy Settings")]
		public int SignalLineLength
		{ get; set; }

        #endregion
    }
}
