﻿<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <RecentInstruments>
    <RecentInstrument>YM JUN25</RecentInstrument>
    <RecentInstrument>NQ MAR25</RecentInstrument>
    <RecentInstrument>MES JUN25</RecentInstrument>
    <RecentInstrument>NQDEC24</RecentInstrument>
    <RecentInstrument>NQ DEC24</RecentInstrument>
    <RecentInstrument>ES JUN25</RecentInstrument>
    <RecentInstrument>MNQ JUN25</RecentInstrument>
    <RecentInstrument>NQ JUN25</RecentInstrument>
  </RecentInstruments>
  <PinnedInstruments />
  <LastUsedInstrument>
    <DynamicSuperDom>ES 06-25</DynamicSuperDom>
    <Strategy>NQ 06-25</Strategy>
  </LastUsedInstrument>
  <RecentBrushes>
    <RB>GhostWhite</RB>
    <RB>DodgerBlue</RB>
    <RB>Black</RB>
    <RB>Lime</RB>
    <RB>Crimson</RB>
    <RB>LimeGreen</RB>
  </RecentBrushes>
  <WindowSizes>
    <NinjaTrader.Gui.Chart.BarsDialog>
      <Height>707</Height>
      <Width>630</Width>
    </NinjaTrader.Gui.Chart.BarsDialog>
    <NinjaTrader.Gui.ControlCenter>
      <Height>209</Height>
      <Width>957</Width>
    </NinjaTrader.Gui.ControlCenter>
    <NinjaTrader.Gui.Chart.Chart>
      <Height>705</Height>
      <Width>1102</Width>
    </NinjaTrader.Gui.Chart.Chart>
    <NinjaTrader.Gui.NinjaScript.AtmStrategy.AtmStrategyPropertiesView>
      <Height>220</Height>
      <Width>550</Width>
    </NinjaTrader.Gui.NinjaScript.AtmStrategy.AtmStrategyPropertiesView>
    <NinjaTrader.Gui.Data.PlaybackControlCenter>
      <Height>130</Height>
      <Width>404</Width>
    </NinjaTrader.Gui.Data.PlaybackControlCenter>
    <NinjaTrader.Gui.NinjaScript.Editor.EditorView>
      <Height>837</Height>
      <Width>892</Width>
    </NinjaTrader.Gui.NinjaScript.Editor.EditorView>
    <NinjaTrader.Gui.TradePerformance.TradePerformance>
      <Height>569</Height>
      <Width>700</Width>
    </NinjaTrader.Gui.TradePerformance.TradePerformance>
    <NinjaTrader.Gui.Tools.Options>
      <Height>500</Height>
      <Width>700</Width>
    </NinjaTrader.Gui.Tools.Options>
    <NinjaTrader.Gui.Tools.ObjectDialog>
      <ChartStrategyAdd>
        <Height>711</Height>
        <Width>786</Width>
      </ChartStrategyAdd>
    </NinjaTrader.Gui.Tools.ObjectDialog>
    <NinjaTrader.Gui.Data.HistoricalData>
      <Height>650</Height>
      <Width>750</Width>
    </NinjaTrader.Gui.Data.HistoricalData>
    <NinjaTrader.Gui.NinjaScript.ExportNinjaScript>
      <Height>400</Height>
      <Width>450</Width>
    </NinjaTrader.Gui.NinjaScript.ExportNinjaScript>
    <NinjaTrader.Gui.Tools.IndicatorSelector>
      <NinjaScriptConfigWindow>
        <Height>908</Height>
        <Width>660</Width>
      </NinjaScriptConfigWindow>
    </NinjaTrader.Gui.Tools.IndicatorSelector>
    <NinjaTrader.Gui.Chart.DataBox>
      <Height>300</Height>
      <Width>300</Width>
    </NinjaTrader.Gui.Chart.DataBox>
  </WindowSizes>
  <PropertyGridCategoryStates>
    <NinjaTrader.Cbi.BarchartOptions>
      <Credentials_x0020_-_x0020_Barchart.com>true</Credentials_x0020_-_x0020_Barchart.com>
    </NinjaTrader.Cbi.BarchartOptions>
    <NinjaTrader.Cbi.TradovateOptions>
      <Credentials_x0020_-_x0020_NinjaTrader>true</Credentials_x0020_-_x0020_NinjaTrader>
      <Settings>true</Settings>
    </NinjaTrader.Cbi.TradovateOptions>
    <NinjaTrader.Cbi.SimulatorOptions>
      <Credentials_x0020_-_x0020_Simulator>true</Credentials_x0020_-_x0020_Simulator>
    </NinjaTrader.Cbi.SimulatorOptions>
    <NinjaTrader.NinjaScript.Indicators.EMA>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.EMA>
    <NinjaTrader.Core.TradingOptions>
      <General>true</General>
      <Calculations>true</Calculations>
      <Simulator>true</Simulator>
      <Auto_x0020_close_x0020_position>true</Auto_x0020_close_x0020_position>
    </NinjaTrader.Core.TradingOptions>
    <NinjaTrader.NinjaScript.DrawingTools.PriceLevel>
      <General>true</General>
    </NinjaTrader.NinjaScript.DrawingTools.PriceLevel>
    <NinjaTrader.NinjaScript.DrawingTools.FibonacciRetracements>
      <General>true</General>
      <Data>false</Data>
      <Lines>true</Lines>
    </NinjaTrader.NinjaScript.DrawingTools.FibonacciRetracements>
    <NinjaTrader.NinjaScript.Indicators.RSI>
      <Lines>true</Lines>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.RSI>
    <NinjaTrader.NinjaScript.Indicators.FVGICT>
      <FVG_x0020_Data_x0020_Series>true</FVG_x0020_Data_x0020_Series>
      <Parameters>true</Parameters>
      <Time_x0020_Ranges>true</Time_x0020_Ranges>
      <FVG_x0020_Colors>true</FVG_x0020_Colors>
      <FVG_x0020_Data_x0020_Series_x0020_Label>true</FVG_x0020_Data_x0020_Series_x0020_Label>
      <Trading_x0020_Signals>true</Trading_x0020_Signals>
      <ATM_x0020_Strategy>true</ATM_x0020_Strategy>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.FVGICT>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.KhanhZillaATM>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_3._x0020_Multi_x0020_Time_x0020_Series_x0020_Options>true</_x0031_3._x0020_Multi_x0020_Time_x0020_Series_x0020_Options>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Delta_x0020_Bars>true</_x0031_3._x0020_Timeframes_x003A__x0020_Delta_x0020_Bars>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Heiken_x0020_Ashi_x0020_-_x0020_Minute_x0020_Input>true</_x0031_3._x0020_Timeframes_x003A__x0020_Heiken_x0020_Ashi_x0020_-_x0020_Minute_x0020_Input>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Heiken_x0020_Ashi_x0020_-_x0020_TICK_x0020_Input>true</_x0031_3._x0020_Timeframes_x003A__x0020_Heiken_x0020_Ashi_x0020_-_x0020_TICK_x0020_Input>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Line_x0020_Break_x0020_-_x0020_Minute_x0020_Based>true</_x0031_3._x0020_Timeframes_x003A__x0020_Line_x0020_Break_x0020_-_x0020_Minute_x0020_Based>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Line_x0020_Break_x0020_-_x0020_TICK_x0020_Based>true</_x0031_3._x0020_Timeframes_x003A__x0020_Line_x0020_Break_x0020_-_x0020_TICK_x0020_Based>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Range>true</_x0031_3._x0020_Timeframes_x003A__x0020_Range>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Range_x0020_-_x0020_MEAN>true</_x0031_3._x0020_Timeframes_x003A__x0020_Range_x0020_-_x0020_MEAN>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_NinzaRenko>true</_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_NinzaRenko>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_Orenko>true</_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_Orenko>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_Unirenko>true</_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_Unirenko>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_UnirenkoHA>true</_x0031_3._x0020_Timeframes_x003A__x0020_Renko_x0020_-_x0020_UnirenkoHA>
      <_x0031_3._x0020_Timeframes_x003A__x0020_RV_x0020_Bars>true</_x0031_3._x0020_Timeframes_x003A__x0020_RV_x0020_Bars>
      <_x0031_3._x0020_Timeframes_x003A__x0020_TBars>true</_x0031_3._x0020_Timeframes_x003A__x0020_TBars>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Tick>true</_x0031_3._x0020_Timeframes_x003A__x0020_Tick>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Timeframes_x0020_Minute>true</_x0031_3._x0020_Timeframes_x003A__x0020_Timeframes_x0020_Minute>
      <_x0031_3._x0020_Timeframes_x003A__x0020_Volume>true</_x0031_3._x0020_Timeframes_x003A__x0020_Volume>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.KhanhZillaATM>
    <NinjaTrader.Core.MarketDataOptions>
      <General>true</General>
      <Historical>true</Historical>
      <Real-time>true</Real-time>
    </NinjaTrader.Core.MarketDataOptions>
    <NinjaTrader.Core.AtiOptions>
      <General>true</General>
      <TradeStation_x0020_email_x0020_interface>true</TradeStation_x0020_email_x0020_interface>
    </NinjaTrader.Core.AtiOptions>
    <NinjaTrader.Core.StrategiesOptions>
      <ATM_x0020_Strategy>true</ATM_x0020_Strategy>
      <NinjaScript>true</NinjaScript>
    </NinjaTrader.Core.StrategiesOptions>
    <NinjaTrader.Core.GeneralOptions>
      <Preferences>true</Preferences>
      <Sounds>true</Sounds>
    </NinjaTrader.Core.GeneralOptions>
    <NinjaTrader.Gui.Chart.ChartTraderProperties>
      <General>true</General>
      <Colors>false</Colors>
      <Lines>true</Lines>
    </NinjaTrader.Gui.Chart.ChartTraderProperties>
    <NinjaTrader.NinjaScript.DrawingTools.HorizontalLine>
      <General>true</General>
      <Data>false</Data>
    </NinjaTrader.NinjaScript.DrawingTools.HorizontalLine>
    <NinjaTrader.NinjaScript.Indicators.PriorDayOHLC>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.PriorDayOHLC>
    <NinjaTrader.NinjaScript.Indicators.CurrentDayOHL>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.CurrentDayOHL>
    <NinjaTrader.NinjaScript.Indicators.Range>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.Range>
    <NinjaTrader.NinjaScript.Indicators.JonnySignals>
      <Misc>true</Misc>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.JonnySignals>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.Andean>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.Andean>
    <NinjaTrader.NinjaScript.Indicators.CandlestickPattern>
      <General>true</General>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.CandlestickPattern>
    <NinjaTrader.NinjaScript.Indicators.BarTimer>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.BarTimer>
    <NinjaTrader.NinjaScript.Indicators.HigherTimeframeCandles>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.HigherTimeframeCandles>
    <NinjaTrader.NinjaScript.Indicators.HFT_WARHAMMER_MOD>
      <Acc_x0020_Tracking>true</Acc_x0020_Tracking>
      <Martingale>true</Martingale>
      <Order_x0020_Type>true</Order_x0020_Type>
      <Skip_x0020_Economic_x0020_Data_x0020_HHMMSS_x0020_Format>true</Skip_x0020_Economic_x0020_Data_x0020_HHMMSS_x0020_Format>
      <Sound>true</Sound>
      <Time_x0020_Settings_x0020_HHMMSS_x0020_Format>true</Time_x0020_Settings_x0020_HHMMSS_x0020_Format>
      <Total_x0020_Strategy_x0020_Profit_x0020_Target_x0020__x002F__x0020_StopLoss>true</Total_x0020_Strategy_x0020_Profit_x0020_Target_x0020__x002F__x0020_StopLoss>
      <Visuals_x0020_For_x0020_Target_x002F_Stop>true</Visuals_x0020_For_x0020_Target_x002F_Stop>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.HFT_WARHAMMER_MOD>
    <NinjaTrader.NinjaScript.Alert>
      <General>true</General>
      <Conditions>true</Conditions>
      <Message>true</Message>
      <Actions>true</Actions>
    </NinjaTrader.NinjaScript.Alert>
    <NinjaTrader.NinjaScript.Strategies.SampleMultiInstrument>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.SampleMultiInstrument>
    <NinjaTrader.NinjaScript.Strategies.TrailStopMACrossOver>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.TrailStopMACrossOver>
    <NinjaTrader.Gui.Chart.ChartControlProperties>
      <General>true</General>
      <Colors>true</Colors>
      <Lines>false</Lines>
      <Window>true</Window>
    </NinjaTrader.Gui.Chart.ChartControlProperties>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.Casher>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_4._x0020_Three-step_x0020_Trailing_x0020_Stop>true</_x0030_4._x0020_Three-step_x0020_Trailing_x0020_Stop>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.Casher>
    <NinjaTrader.NinjaScript.Strategies.MAKER_V1>
      <ATM_x0020_Strategy>true</ATM_x0020_Strategy>
      <Logging>true</Logging>
      <Market_x0020_Structure_x0020_Zones>true</Market_x0020_Structure_x0020_Zones>
      <Metrics_x0020_Panel>true</Metrics_x0020_Panel>
      <Presets>true</Presets>
      <RSI_x0020_Parameters>true</RSI_x0020_Parameters>
      <Trend_x0020_Filter>true</Trend_x0020_Filter>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.MAKER_V1>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.Momo>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.Momo>
    <NinjaTrader.Gui.Tools.Account.AccountWrapper>
      <General>true</General>
    </NinjaTrader.Gui.Tools.Account.AccountWrapper>
    <NinjaTrader.NinjaScript.Indicators.RegressionChannelHighLow>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.RegressionChannelHighLow>
    <NinjaTrader.NinjaScript.Indicators.RegressionChannel>
      <General>true</General>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.RegressionChannel>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.ORBot2>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_3._x0020_Strategy_x0020_Settings>true</_x0030_3._x0020_Strategy_x0020_Settings>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.ORBot2>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.ORBot>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_3._x0020_Strategy_x0020_Settings>true</_x0030_3._x0020_Strategy_x0020_Settings>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.ORBot>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperRex>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperRex>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperBot>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperBot>
    <NinjaTrader.NinjaScript.Strategies.CandleStopTrailExample>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.CandleStopTrailExample>
    <NinjaTrader.NinjaScript.Strategies.ORB_Bot>
      <Setup>true</Setup>
      <Global_x0020_Settings>true</Global_x0020_Settings>
      <Time_x0020_Settings>true</Time_x0020_Settings>
      <Position_x0020_Settings>true</Position_x0020_Settings>
      <Entry_x0020_Settings>true</Entry_x0020_Settings>
      <Exit_x0020_Settings>true</Exit_x0020_Settings>
      <Martingale_x0020_Settings>true</Martingale_x0020_Settings>
      <Plots>true</Plots>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.ORB_Bot>
    <NinjaTrader.NinjaScript.Strategies.RangeRSIStrategy_v4>
      <ATM_x0020_Strategy>true</ATM_x0020_Strategy>
      <RSI_x0020_Parameters>true</RSI_x0020_Parameters>
      <Trend_x0020_Filter>true</Trend_x0020_Filter>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.RangeRSIStrategy_v4>
    <NinjaTrader.NinjaScript.Strategies.SamuraiJackDTFX.DTFXAlgoZonesStrategySJ>
      <_x0031_._x0020_Structure>true</_x0031_._x0020_Structure>
      <_x0032_._x0020_Zones>true</_x0032_._x0020_Zones>
      <_x0033_._x0020_Invalidation>true</_x0033_._x0020_Invalidation>
      <_x0034_._x0020_Fib_x0020_Levels>true</_x0034_._x0020_Fib_x0020_Levels>
      <_x0035_._x0020_Style>true</_x0035_._x0020_Style>
      <_x0036_._x0020_Strategy_x0020_Settings>true</_x0036_._x0020_Strategy_x0020_Settings>
      <_x0037_._x0020_Multi-Timeframe>true</_x0037_._x0020_Multi-Timeframe>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.SamuraiJackDTFX.DTFXAlgoZonesStrategySJ>
    <NinjaTrader.NinjaScript.Indicators.SMA>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.SMA>
    <NinjaTrader.NinjaScript.Strategies.MARKET_MAKER.MAKER>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.MARKET_MAKER.MAKER>
    <NinjaTrader.NinjaScript.Indicators.TradeSaber.InsideBarTS>
      <_x0030_1._x0020_Inside_x0020_Bar_x0020_Offset>true</_x0030_1._x0020_Inside_x0020_Bar_x0020_Offset>
      <_x0030_2._x0020_Inside_x0020_Bar_x0020_Custom_x0020_Color>true</_x0030_2._x0020_Inside_x0020_Bar_x0020_Custom_x0020_Color>
      <_x0030_3._x0020_Flash_x0020_Alert>true</_x0030_3._x0020_Flash_x0020_Alert>
      <_x0030_4._x0020_Audio_x0020_Alert>true</_x0030_4._x0020_Audio_x0020_Alert>
      <_x0032_9._x0020_TradeSaber_x0020_Socials>true</_x0032_9._x0020_TradeSaber_x0020_Socials>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.TradeSaber.InsideBarTS>
    <NinjaTrader.NinjaScript.Indicators.iGRID_PACK_2.DualBands>
      <Main_x0020_Bands>true</Main_x0020_Bands>
      <Fast_x0020_Bands>true</Fast_x0020_Bands>
      <Signals>true</Signals>
      <Advanced>true</Advanced>
      <Alerts>true</Alerts>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.iGRID_PACK_2.DualBands>
    <NinjaTrader.NinjaScript.Indicators.TradeSaber_SignalMod.TOWilliamsTraderOracleSignalMOD>
      <Predator_x0020_Signals>true</Predator_x0020_Signals>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Lines>true</Lines>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.TradeSaber_SignalMod.TOWilliamsTraderOracleSignalMOD>
    <NinjaTrader.NinjaScript.Indicators.TradeSaber.ORB_TradeSaber>
      <Display>true</Display>
      <Opening_x0020_Range>true</Opening_x0020_Range>
      <Predator_x0020_X_x0020_Signals>true</Predator_x0020_X_x0020_Signals>
      <TradeSaber_x0020_Socials>true</TradeSaber_x0020_Socials>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.TradeSaber.ORB_TradeSaber>
    <NinjaTrader.NinjaScript.Indicators.TradeSaber.MultiSeriesHL>
      <_x0030_0._x0020_Display_x0020_Levels>true</_x0030_0._x0020_Display_x0020_Levels>
      <_x0030_1._x0020_Customize_x0020_1_x0020_Min_x0020_Lines>true</_x0030_1._x0020_Customize_x0020_1_x0020_Min_x0020_Lines>
      <_x0030_2._x0020_Customize_x0020_5_x0020_Min_x0020_Lines>true</_x0030_2._x0020_Customize_x0020_5_x0020_Min_x0020_Lines>
      <_x0030_3._x0020_Customize_x0020_15_x0020_Min_x0020_Lines>true</_x0030_3._x0020_Customize_x0020_15_x0020_Min_x0020_Lines>
      <_x0030_4._x0020_Customize_x0020_30_x0020_Min_x0020_Lines>true</_x0030_4._x0020_Customize_x0020_30_x0020_Min_x0020_Lines>
      <_x0030_5._x0020_Customize_x0020_60_x0020_Min_x0020_Lines>true</_x0030_5._x0020_Customize_x0020_60_x0020_Min_x0020_Lines>
      <_x0030_6._x0020_Customize_x0020_240_x0020_Min_x0020_Lines>true</_x0030_6._x0020_Customize_x0020_240_x0020_Min_x0020_Lines>
      <_x0030_7._x0020_Customize_x0020_Previous_x0020_Daily_x0020_Lines>true</_x0030_7._x0020_Customize_x0020_Previous_x0020_Daily_x0020_Lines>
      <_x0030_8._x0020_Customize_x0020_Current_x0020_Daily_x0020_Lines>true</_x0030_8._x0020_Customize_x0020_Current_x0020_Daily_x0020_Lines>
      <_x0030_9._x0020_Customize_x0020_Previous_x0020_Weekly_x0020_Lines>true</_x0030_9._x0020_Customize_x0020_Previous_x0020_Weekly_x0020_Lines>
      <_x0031_0._x0020_Customize_x0020_Current_x0020_Weekly_x0020_Lines>true</_x0031_0._x0020_Customize_x0020_Current_x0020_Weekly_x0020_Lines>
      <_x0032_9._x0020_TradeSaber_x0020_Socials>true</_x0032_9._x0020_TradeSaber_x0020_Socials>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.TradeSaber.MultiSeriesHL>
    <NinjaTrader.NinjaScript.Indicators.ADX>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Lines>true</Lines>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.ADX>
    <NinjaTrader.NinjaScript.Indicators.aiDuplicateAccountActions>
      <Main_x0020_Features>true</Main_x0020_Features>
      <Account_x0020_Management>true</Account_x0020_Management>
      <Account_x0020_Name_x0020_Adjustments>true</Account_x0020_Name_x0020_Adjustments>
      <Account_x0020_Details_x0020_Privacy>true</Account_x0020_Details_x0020_Privacy>
      <Functionality>true</Functionality>
      <Columns>true</Columns>
      <Duplicate_x0020_Account_x0020_Actions>true</Duplicate_x0020_Account_x0020_Actions>
      <Account_x0020_Risk_x0020_Manager>true</Account_x0020_Risk_x0020_Manager>
      <Exit_x0020_Shield>true</Exit_x0020_Shield>
      <One_x0020_Trade>true</One_x0020_Trade>
      <Rejected_x0020_Order_x0020_Handling>true</Rejected_x0020_Order_x0020_Handling>
      <Total_x0020_Rows>true</Total_x0020_Rows>
      <Window_x0020_Display>true</Window_x0020_Display>
      <Control_x0020_Buttons>true</Control_x0020_Buttons>
      <Flatten_x0020_Everything_x0020_Button>true</Flatten_x0020_Everything_x0020_Button>
      <Refresh_x0020_Positions_x0020_Button>true</Refresh_x0020_Positions_x0020_Button>
      <Status_x0020_Messages>true</Status_x0020_Messages>
      <Safety>true</Safety>
      <TradingView>true</TradingView>
      <Keyboard>true</Keyboard>
      <Advanced>true</Advanced>
      <All_x0020_Prop_x0020_Firms>true</All_x0020_Prop_x0020_Firms>
      <Language>true</Language>
      <License>true</License>
    </NinjaTrader.NinjaScript.Indicators.aiDuplicateAccountActions>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperBot2>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_Settings>true</_x0030_8a._x0020_Strategy_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.SuperBot2>
    <NinjaTrader.NinjaScript.Indicators.BlueZ.BlueZHMAHooks>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.BlueZ.BlueZHMAHooks>
    <NinjaTrader.NinjaScript.Indicators.Momentum>
      <Lines>true</Lines>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.Momentum>
    <NinjaTrader.NinjaScript.Indicators.Prop_Trader_Tools.PropTraderAccountTool>
      <_x0030_._x0020_Support>true</_x0030_._x0020_Support>
      <_x0031_._x0020_Trading_x0020_Accounts_x0020_>true</_x0031_._x0020_Trading_x0020_Accounts_x0020_>
      <_x0032_._x0020_Function_x0020_settings>true</_x0032_._x0020_Function_x0020_settings>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.Prop_Trader_Tools.PropTraderAccountTool>
    <NinjaTrader.NinjaScript.Indicators.BuySellPressure>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Lines>true</Lines>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.BuySellPressure>
    <NinjaTrader.NinjaScript.Strategies.SampleAtmCandleStrategy>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.SampleAtmCandleStrategy>
    <NinjaTrader.NinjaScript.Indicators.PriceLine>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
    </NinjaTrader.NinjaScript.Indicators.PriceLine>
    <NinjaTrader.NinjaScript.Strategies.SampleMultiTimeFrame>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.SampleMultiTimeFrame>
    <NinjaTrader.NinjaScript.Strategies.SampleMACrossOver>
      <Strategy_x0020_parameters>true</Strategy_x0020_parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.SampleMACrossOver>
    <NinjaTrader.NinjaScript.Strategies.IFVG>
      <Setup>true</Setup>
      <Global_x0020_Settings>true</Global_x0020_Settings>
      <Position_x0020_Settings>true</Position_x0020_Settings>
      <Time_x0020_Settings>true</Time_x0020_Settings>
      <Entry_x0020_Settings>true</Entry_x0020_Settings>
      <Day_x0020_Filter_x0020_Settings>true</Day_x0020_Filter_x0020_Settings>
      <Exit_x0020_Settings>true</Exit_x0020_Settings>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.IFVG>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.Swinger>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Strategy_x0020_-_x0020_DHL_x0020_Params>true</_x0030_8a._x0020_Strategy_x0020_-_x0020_DHL_x0020_Params>
      <_x0030_8a._x0020_Strategy_x0020_-_x0020_Entry_x0020_Types>true</_x0030_8a._x0020_Strategy_x0020_-_x0020_Entry_x0020_Types>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.Swinger>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.TEFABot>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_7a._x0020_Strategy_x0020_Logic_x0020_-_x0020_Signal_x0020_Source>true</_x0030_7a._x0020_Strategy_x0020_Logic_x0020_-_x0020_Signal_x0020_Source>
      <_x0030_7b._x0020_Indicator_x0020_Settings_x0020_-_x0020_TEFASignalsPro>true</_x0030_7b._x0020_Indicator_x0020_Settings_x0020_-_x0020_TEFASignalsPro>
      <_x0030_7c._x0020_Indicator_x0020_Settings_x0020_-_x0020_JBSignal>true</_x0030_7c._x0020_Indicator_x0020_Settings_x0020_-_x0020_JBSignal>
      <_x0030_7d._x0020_VMA_x0020_Settings>true</_x0030_7d._x0020_VMA_x0020_Settings>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.TEFABot>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.BalaBot>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8a._x0020_Indicator_x0020_Settings_x0020_-_x0020_Bala2Channels>true</_x0030_8a._x0020_Indicator_x0020_Settings_x0020_-_x0020_Bala2Channels>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Strategy_x0020_Logic>true</Strategy_x0020_Logic>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.BalaBot>
    <NinjaTrader.NinjaScript.Indicators.ADL>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.ADL>
    <NinjaTrader.NinjaScript.Indicators.HeikenAshi8>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Visual>true</Visual>
      <Plots>true</Plots>
    </NinjaTrader.NinjaScript.Indicators.HeikenAshi8>
    <NinjaTrader.Gui.Chart.BarsProperties>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Time_x0020_frame>true</Time_x0020_frame>
      <Chart_x0020_style>true</Chart_x0020_style>
      <Visual>true</Visual>
      <Trades>true</Trades>
    </NinjaTrader.Gui.Chart.BarsProperties>
    <NinjaTrader.NinjaScript.Strategies.KCStrategies.Johny5>
      <_x0030_1a._x0020_Release_x0020_Notes>true</_x0030_1a._x0020_Release_x0020_Notes>
      <_x0030_1b._x0020_Support_x0020_Developer>true</_x0030_1b._x0020_Support_x0020_Developer>
      <_x0030_2._x0020_Order_x0020_Settings>true</_x0030_2._x0020_Order_x0020_Settings>
      <_x0030_3._x0020_Order_x0020_Management>true</_x0030_3._x0020_Order_x0020_Management>
      <_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>true</_x0030_5._x0020_Profit_x002F_Loss_x0020_Limit_x0009_>
      <_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>true</_x0030_6._x0020_Trades_x0020_Per_x0020_Direction>
      <_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>true</_x0030_7._x0020_Other_x0020_Trade_x0020_Controls>
      <_x0030_8b._x0020_Default_x0020_Settings>true</_x0030_8b._x0020_Default_x0020_Settings>
      <_x0030_9._x0020_Market_x0020_Condition>true</_x0030_9._x0020_Market_x0020_Condition>
      <_x0031_0._x0020_Timeframes>true</_x0031_0._x0020_Timeframes>
      <_x0031_1._x0020_Status_x0020_Panel>true</_x0031_1._x0020_Status_x0020_Panel>
      <_x0031_1._x0020_Webhook>true</_x0031_1._x0020_Webhook>
      <Strategy_x0020_Parameters_x0020_-_x0020_JBSignal1>true</Strategy_x0020_Parameters_x0020_-_x0020_JBSignal1>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.KCStrategies.Johny5>
    <NinjaTrader.NinjaScript.Strategies.neVsSignals_Strategy_v1>
      <ALMA>true</ALMA>
      <Legacy>true</Legacy>
      <MACD>true</MACD>
      <Position_x0020_Management>true</Position_x0020_Management>
      <Profit_x0020_Management>true</Profit_x0020_Management>
      <Range_x0020_Filter>true</Range_x0020_Filter>
      <Risk_x0020_Management>true</Risk_x0020_Management>
      <TEMA>true</TEMA>
      <Williams_x0020__x0025_R>true</Williams_x0020__x0025_R>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.neVsSignals_Strategy_v1>
    <NinjaTrader.NinjaScript.Strategies.BreakevenMultipleTarget>
      <Triple_x0020_SMA_x0020_Parameters>true</Triple_x0020_SMA_x0020_Parameters>
      <Parameters>true</Parameters>
      <Data_x0020_Series>true</Data_x0020_Series>
      <Setup>true</Setup>
      <Historical_x0020_fill_x0020_processing>true</Historical_x0020_fill_x0020_processing>
      <Order_x0020_handling>true</Order_x0020_handling>
      <Order_x0020_properties>true</Order_x0020_properties>
    </NinjaTrader.NinjaScript.Strategies.BreakevenMultipleTarget>
  </PropertyGridCategoryStates>
  <RecentFolders>
    <GridExportSave>C:\Users\<USER>\Desktop</GridExportSave>
    <NinjaScriptZipOpen>C:\Users\<USER>\Downloads</NinjaScriptZipOpen>
  </RecentFolders>
  <PlaybackControl>
    <Location>1435;390</Location>
    <Size>404;130</Size>
    <IsDataSourceHistorical>False</IsDataSourceHistorical>
  </PlaybackControl>
  <DoNotShowMessages>
    <a3a1b764c1b84a40bebb4f7df38edb90 />
  </DoNotShowMessages>
  <IntervalSelector>
    <IntervalCollection Type="4">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>2</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>3</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>4</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>10</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>15</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>30</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>60</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>240</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>4</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>7</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="3">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>2</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>3</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>4</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>10</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>15</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>30</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>3</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>60</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="2">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>2</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>3</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>4</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>10</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>15</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>30</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>60</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>8</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>2</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>12</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="0">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>100</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>200</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>300</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>400</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>500</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>0</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="1">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>5000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>10000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>25000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>50000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>1</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>100000</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="5">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>5</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="6">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>6</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="7">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>7</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
    <IntervalCollection Type="8">
      <Interval Label="@VALUE">
        <Period>
          <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <BarsPeriodTypeSerialize>8</BarsPeriodTypeSerialize>
            <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
            <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
            <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
            <MarketDataType>Last</MarketDataType>
            <PointAndFigurePriceType>Close</PointAndFigurePriceType>
            <ReversalType>Tick</ReversalType>
            <Value>1</Value>
            <Value2>1</Value2>
          </BarsPeriod>
        </Period>
      </Interval>
    </IntervalCollection>
  </IntervalSelector>
  <Recorder>
    <Instruments />
    <IsRunning>False</IsRunning>
  </Recorder>
  <BPControlCenter>
    <IsRunning>False</IsRunning>
  </BPControlCenter>
  <DSControlCenter>
    <IsRunning>False</IsRunning>
  </DSControlCenter>
  <GWControlCenter>
    <IsRunning>False</IsRunning>
  </GWControlCenter>
  <HDSControlCenter>
    <IsRunning>False</IsRunning>
  </HDSControlCenter>
  <ISControlCenter>
    <IsRunning>False</IsRunning>
  </ISControlCenter>
  <LastUsedCalculationMode>Currency</LastUsedCalculationMode>
  <ColumnWidths>
    <ChartStrategyAdd>
      <Column0Width>249</Column0Width>
      <Column2Width>527</Column2Width>
      <FirstRowHeight>337</FirstRowHeight>
      <SecondRowHeight>280</SecondRowHeight>
    </ChartStrategyAdd>
  </ColumnWidths>
  <LastBarsPeriod>
    <BarsPeriod xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <BarsPeriodTypeSerialize>9</BarsPeriodTypeSerialize>
      <BaseBarsPeriodType>Minute</BaseBarsPeriodType>
      <BaseBarsPeriodValue>1</BaseBarsPeriodValue>
      <VolumetricDeltaType>BidAsk</VolumetricDeltaType>
      <MarketDataType>Last</MarketDataType>
      <PointAndFigurePriceType>Close</PointAndFigurePriceType>
      <ReversalType>Tick</ReversalType>
      <Value>1</Value>
      <Value2>1</Value2>
    </BarsPeriod>
  </LastBarsPeriod>
</NinjaTrader>