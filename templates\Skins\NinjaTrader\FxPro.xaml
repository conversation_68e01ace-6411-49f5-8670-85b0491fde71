﻿<ResourceDictionary xmlns		="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x		="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:po	="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options">

	<SolidColorBrush x:Key="FxPro.TextBoxBackground"			Color="White"			po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.PositionQtyLongBackground"	Color="LimeGreen"		po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.PositionQtyShortBackground"	Color="Red"				po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.PnLBackground"				Color="Black"			po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.UptickBackground"				Color="Blue"			po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.UptickForeground"				Color="Black"			po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.DowntickBackground"			Color="Red"				po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.DowntickForeground"			Color="Black"			po:Freeze="true" />
	<SolidColorBrush x:Key="FxPro.ButtonForeground"				Color="White"			po:Freeze="true" />
	<LinearGradientBrush	po:Freeze="true"	x:Key="FxPro.ButtonBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="FxPro.ActionButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="FxPro.BuyButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
	<LinearGradientBrush	po:Freeze="true"	x:Key="FxPro.SellButtonsBackground" StartPoint="0.5,-0.05"	EndPoint="0.5,0.66">
		<LinearGradientBrush.GradientStops>
			<GradientStop Color="#FF58595B" Offset="0"/>
			<GradientStop Color="#FF58595B" Offset="1"/>
		</LinearGradientBrush.GradientStops>
	</LinearGradientBrush>
</ResourceDictionary>