//
// Copyright (C) 2015, NinjaTrader LLC <www.ninjatrader.com>
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component
// Coded by NinjaTrader_PaulH, NinjaTrader_ChelseaB
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using SharpDX;
#endregion

//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators
{
	// 04-27-2018 Changed line 220 from: chartControl.GetXByBarIndex(chartControl.BarsArray[0], idx); to: chartControl.GetXByBarIndex(ChartBars, idx); to
	// ensure compatibility when used in any panel on the chart.
	// Changed default shadow from black to dimgray.
	public class HeikenAshi8 : Indicator
	{
		private Dictionary<string, DXMediaMap>	dxmBrushes;
		private int								barX, barLeftX, yClose, yHigh, yLow, yOpen;
		private SharpDX.RectangleF				reuseRectangle;
		private SharpDX.Vector2					reuseVector1, reuseVector2;
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"HeikenAshi technique discussed in the article 'Using Heiken-Ashi Technique' in February 2004 issue of TASC magazine.";
				Name						= "HeikenAshi8";
				Calculate					= Calculate.OnEachTick;
				IsOverlay					= true;
				DisplayInDataBox			= true;
				PaintPriceMarkers			= false;
				IsSuspendedWhileInactive	= false;
				BarsRequiredToPlot			= 1;

				ShadowWidth					= 1;

				if (dxmBrushes == null)
				{
					dxmBrushes = new Dictionary<string, DXMediaMap>()
					{
						{ "barColorDown",	new DXMediaMap() { MediaBrush = Brushes.Red } },
						{ "barColorUp",		new DXMediaMap() { MediaBrush = Brushes.Lime } },
						{ "shadowColor",	new DXMediaMap() { MediaBrush = Brushes.DimGray } },
					};
				}

				AddPlot(Brushes.Gray, "HAOpen");
				AddPlot(Brushes.Gray, "HAHigh");
				AddPlot(Brushes.Gray, "HALow");
				AddPlot(Brushes.Gray, "HAClose");
			}
		}

		protected override void OnBarUpdate()
		{
			//Clear out regular candles
			BarBrushes[0]				= Brushes.Transparent;
			CandleOutlineBrushes[0]		= Brushes.Transparent;

			if (CurrentBar == 0)
            {
                HAOpen[0]	= Open[0];
                HAHigh[0]	= High[0];
                HALow[0]	= Low[0];
                HAClose[0]	= Close[0];
                return;
            }

            HAClose[0]	= (Open[0] + High[0] + Low[0] + Close[0]) * 0.25; // Calculate the close
            HAOpen[0]	= (HAOpen[1] + HAClose[1]) * 0.5; // Calculate the open
            HAHigh[0]	= Math.Max(High[0], HAOpen[0]); // Calculate the high
            HALow[0]	= Math.Min(Low[0], HAOpen[0]); // Calculate the low
		}

		#region Properties
		[XmlIgnore]
		[NinjaScriptProperty]
		[Display(Name = "BarColorDown", Description = "Color of Down bars", Order = 2, GroupName = "Parameters")]
		public Brush BarColorDown
		{
			get { return dxmBrushes["barColorDown"].MediaBrush; }
			set { dxmBrushes["barColorDown"].MediaBrush = value; }
		}

		[Browsable(false)]
		public string BarColorDownSerializable
		{
			get { return Serialize.BrushToString(BarColorDown); }
			set { BarColorDown = Serialize.StringToBrush(value); }
		}

		[XmlIgnore]
		[NinjaScriptProperty]
		[Display(Name = "BarColorUp", Description = "Color of Up bars", Order = 1, GroupName = "Parameters")]
		public Brush BarColorUp
		{
			get { return dxmBrushes["barColorUp"].MediaBrush; }
			set { dxmBrushes["barColorUp"].MediaBrush = value; }
		}

		[Browsable(false)]
		public string BarColorUpSerializable
		{
			get { return Serialize.BrushToString(BarColorUp); }
			set { BarColorUp = Serialize.StringToBrush(value); }
		}

		[XmlIgnore]
		[NinjaScriptProperty]
		[Display(Name = "ShadowColor", Description = "Wick/tail color", Order = 3, GroupName = "Parameters")]
		public Brush ShadowColor
		{
			get { return dxmBrushes["shadowColor"].MediaBrush; }
			set { dxmBrushes["shadowColor"].MediaBrush = value; }
		}

		[Browsable(false)]
		public string ShadowColorSerializable
		{
			get { return Serialize.BrushToString(ShadowColor); }
			set { ShadowColor = Serialize.StringToBrush(value); }
		}	

		[Range(1, int.MaxValue)]
		[Display(Name = "ShadowWidth", Description = "Shadow (tail/wick) width", Order = 4, GroupName = "Parameters")]
		public int ShadowWidth
		{ get; set; }

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> HAOpen
		{
			get { return Values[0]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> HAHigh
		{
			get { return Values[1]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> HALow
		{
			get { return Values[2]; }
		}

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> HAClose
		{
			get { return Values[3]; }
		}

		#endregion

		#region Miscellaneous		

		[Browsable(false)]
		public class DXMediaMap
		{
			public SharpDX.Direct2D1.Brush		DxBrush;
			public System.Windows.Media.Brush	MediaBrush;
		}

		private void InitDxBrushes()
		{
			foreach (KeyValuePair<string, DXMediaMap> item in dxmBrushes)
				if (item.Value.DxBrush != null)
					item.Value.DxBrush.Dispose();
			
			if (RenderTarget == null || RenderTarget.IsDisposed)
				return;
			
			try
			{
				foreach (KeyValuePair<string, DXMediaMap> item in dxmBrushes)
						item.Value.DxBrush = item.Value.MediaBrush.ToDxBrush(RenderTarget);
			}
			catch (Exception exception)
			{
				Log(exception.ToString(), LogLevel.Error);
			}	
		}

       	public override void OnCalculateMinMax()
        {
            base.OnCalculateMinMax();
			
            if (Bars == null || ChartControl == null)
                return;

            for (int idx = ChartBars.FromIndex; idx <= ChartBars.ToIndex; idx++)
            {
                double tmpHigh 	= 	HAHigh.GetValueAt(idx);
                double tmpLow 	= 	HALow.GetValueAt(idx);
				
                if (tmpHigh != 0 && tmpHigh > MaxValue)
                    MaxValue = tmpHigh;
                if (tmpLow != 0 && tmpLow < MinValue)
                    MinValue = tmpLow;						
            }
        }
		
		protected override void OnRender(ChartControl chartControl, ChartScale chartScale)
        {			
            if (Bars == null || ChartControl == null || !IsVisible)
                return;

			int barPaintWidth = chartControl.GetBarPaintWidth(chartControl.BarsArray[0]) - 1;

			for (int idx = ChartBars.FromIndex; idx <= ChartBars.ToIndex; idx++)
            {
                if (idx - Displacement < 0 || idx - Displacement >= BarsArray[0].Count || ( idx - Displacement < BarsRequiredToPlot)) 
                    continue;

				barX		= chartControl.GetXByBarIndex(chartControl.BarsArray[0], idx);				
				barLeftX	= (barX - barPaintWidth / 2);
				yOpen		= chartScale.GetYByValue(HAOpen.GetValueAt(idx));
				yHigh		= chartScale.GetYByValue(HAHigh.GetValueAt(idx));
				yLow		= chartScale.GetYByValue(HALow.GetValueAt(idx));
				yClose		= chartScale.GetYByValue(HAClose.GetValueAt(idx));

				UpdateVectors(ref reuseVector1, ref reuseVector2, barX, yHigh, barX, yLow);

				RenderTarget.DrawLine(reuseVector1, reuseVector2, dxmBrushes["shadowColor"].DxBrush, ShadowWidth);

                if (yClose == yOpen)
				{
					UpdateVectors(ref reuseVector1, ref reuseVector2, barLeftX - 1, yOpen, (barX + barPaintWidth / 2) - 1, yOpen);
					RenderTarget.DrawLine(reuseVector1, reuseVector2, dxmBrushes["shadowColor"].DxBrush, ShadowWidth);
				}
                else
                {
                    if (yClose > yOpen)
					{
						UpdateRect(ref reuseRectangle, barLeftX, yOpen, barPaintWidth - 1, Math.Abs(yClose - yOpen));
						RenderTarget.FillRectangle(reuseRectangle, dxmBrushes["barColorDown"].DxBrush);
					}
                    else
					{
						UpdateRect(ref reuseRectangle, barLeftX, yClose, barPaintWidth - 1, Math.Abs(yClose - yOpen));
						RenderTarget.FillRectangle(reuseRectangle, dxmBrushes["barColorUp"].DxBrush);
					}

					UpdateRect(ref reuseRectangle, barLeftX - (ShadowWidth / 2), Math.Min(yOpen, yClose), barPaintWidth - (ShadowWidth / 2), Math.Abs(yClose - yOpen));
					RenderTarget.DrawRectangle( reuseRectangle, dxmBrushes["shadowColor"].DxBrush, ShadowWidth);
				}
            }
		}

		public override void OnRenderTargetChanged()
		{
			InitDxBrushes();
		}

		private void UpdateRect(ref SharpDX.RectangleF updateRectangle, float x, float y, float width, float height)
		{
			updateRectangle.X		= x;
			updateRectangle.Y		= y;
			updateRectangle.Width	= width;
			updateRectangle.Height	= height;
		}

		private void UpdateVectors(ref SharpDX.Vector2 updateVector1, ref SharpDX.Vector2 updateVector2, int x1, int y1, int x2, int yHigh)
		{
			updateVector1.X	= (float)x1;
			updateVector1.Y = (float)y1;
			updateVector2.X = (float)x2;
			updateVector2.Y = (float)yHigh;
		}

		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private HeikenAshi8[] cacheHeikenAshi8;
		public HeikenAshi8 HeikenAshi8(Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			return HeikenAshi8(Input, barColorDown, barColorUp, shadowColor);
		}

		public HeikenAshi8 HeikenAshi8(ISeries<double> input, Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			if (cacheHeikenAshi8 != null)
				for (int idx = 0; idx < cacheHeikenAshi8.Length; idx++)
					if (cacheHeikenAshi8[idx] != null && cacheHeikenAshi8[idx].BarColorDown == barColorDown && cacheHeikenAshi8[idx].BarColorUp == barColorUp && cacheHeikenAshi8[idx].ShadowColor == shadowColor && cacheHeikenAshi8[idx].EqualsInput(input))
						return cacheHeikenAshi8[idx];
			return CacheIndicator<HeikenAshi8>(new HeikenAshi8(){ BarColorDown = barColorDown, BarColorUp = barColorUp, ShadowColor = shadowColor }, input, ref cacheHeikenAshi8);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.HeikenAshi8 HeikenAshi8(Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			return indicator.HeikenAshi8(Input, barColorDown, barColorUp, shadowColor);
		}

		public Indicators.HeikenAshi8 HeikenAshi8(ISeries<double> input , Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			return indicator.HeikenAshi8(input, barColorDown, barColorUp, shadowColor);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.HeikenAshi8 HeikenAshi8(Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			return indicator.HeikenAshi8(Input, barColorDown, barColorUp, shadowColor);
		}

		public Indicators.HeikenAshi8 HeikenAshi8(ISeries<double> input , Brush barColorDown, Brush barColorUp, Brush shadowColor)
		{
			return indicator.HeikenAshi8(input, barColorDown, barColorUp, shadowColor);
		}
	}
}

#endregion
