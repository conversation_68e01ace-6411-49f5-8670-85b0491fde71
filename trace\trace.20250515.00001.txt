******************* Session Start (Version *******) *******************
2025-05-15 20:48:30:249 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-05-15 20:48:30:254 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-05-15 20:48:30:620 Core.Instrumentation.ActivitySource: enabled=True randomPercent=32.15505 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-05-15 20:48:30:656 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-05-15 20:48:32:027 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-15 20:48:32:044 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.6644081' renewSecs='2399.83220405'
2025-05-15 20:48:32:892 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-05-15 20:48:33:760 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-05-15 20:48:34:974 PrimaryMonitorWPFDPIScale=1.00
2025-05-15 20:48:38:848 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab (Beta) Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-05-15 20:48:39:030 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-05-15 20:48:39:030 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-05-15 20:48:39:030 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-05-15 20:48:39:031 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-05-15 20:48:39:031 OSLanguage='en-US'
2025-05-15 20:48:39:031 OSEnvironment='64bit'
2025-05-15 20:48:39:031 Processors=8
2025-05-15 20:48:39:031 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-05-15 20:48:40:085 ProcessorSpeed=2.4 GHz
2025-05-15 20:48:40:085 PhysicalMemory=8192 MB
2025-05-15 20:48:40:252 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-05-15 20:48:40:252 Monitors=2/1280x720|1920x1080
2025-05-15 20:48:40:252 .NET/CLR Version='4.8'/64bit
2025-05-15 20:48:40:253 SQLiteVersion='1.0.116.0'
2025-05-15 20:48:40:254 ApplicationTimezone=EST +0 hour(s)
2025-05-15 20:48:40:254 ApplicationTimezone=UTC -4 hour(s)
2025-05-15 20:48:40:254 LocalTimezone=EST +0 hour(s)
2025-05-15 20:48:40:254 LocalTimezone=UTC -4 hour(s)
2025-05-15 20:48:40:315 DirectXRenderingHW
2025-05-15 20:48:40:315 Copying custom assemblies...
2025-05-15 20:48:40:348 Loading custom assemblies...
2025-05-15 20:48:40:348 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-05-15 20:48:40:535 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-05-15 20:48:40:549 Deleting temporary files...
2025-05-15 20:48:40:672 Copying db and restoring templates...
2025-05-15 20:48:40:710 Loading third party assemblies...
2025-05-15 20:48:40:769 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-05-15 20:48:40:770 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-05-15 20:48:40:770 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-05-15 20:48:40:770 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-05-15 20:48:40:770 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-05-15 20:48:40:770 Initializing database...
2025-05-15 20:48:40:770 Loading master instruments...
2025-05-15 20:48:40:989 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-05-15 20:48:40:992 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-05-15 20:48:41:536 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-05-15 20:48:41:678 Loading instruments...
2025-05-15 20:48:42:104 Loading accounts...
2025-05-15 20:48:42:245 Loading users...
2025-05-15 20:48:42:304 Downloading server info...
2025-05-15 20:48:42:304 Starting instrument management...
2025-05-15 20:48:42:317 Starting timer...
2025-05-15 20:48:42:317 Creating file type watcher...
2025-05-15 20:48:42:318 Setting ATI...
2025-05-15 20:48:42:357 Connecting ATI server...
2025-05-15 20:48:42:358 Server.AtiServer.Connect0
2025-05-15 20:48:42:358 Starting adapter server...
2025-05-15 20:48:42:364 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-15 20:48:42:365 Server.AtiServer.Connect1: Port='36973'
2025-05-15 20:48:42:368 Server.AtiServer.Connect2
2025-05-15 20:48:42:380 Starting bars dictionary...
2025-05-15 20:48:42:381 Starting recorder...
2025-05-15 20:48:42:385 Starting server(s)...
2025-05-15 20:48:42:442 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3126
2025-05-15 20:48:42:443 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-05-15 20:48:42:443 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9777
2025-05-15 20:48:42:443 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=11981
2025-05-15 20:48:42:443 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5059
2025-05-15 20:48:42:552 Required resource key 'brushOrderWorking' is missing.
2025-05-15 20:48:42:552 Required resource key 'brushOrderAccepted' is missing.
2025-05-15 20:48:42:552 Required resource key 'brushOrderPartFilled' is missing.
2025-05-15 20:48:42:552 Required resource key 'brushOrderInitialized' is missing.
2025-05-15 20:48:42:628 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarPrice='' SnapModeDisabled='' SnapModePrice='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-05-15 20:48:42:630 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-05-15 20:48:42:630 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-15 20:48:42:631 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-05-15 20:48:42:633 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='Ctrl+Z' SimulatedOrder=''
2025-05-15 20:48:42:644 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-15 20:48:42:644 OrderEntryHotKeys=disabled
2025-05-15 20:48:42:646 AutoClose=disabled
2025-05-15 20:48:43:594 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-05-15 20:48:46:711 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=2 Messages=3 Risks=1 RolloverCollection=259 TradingHours=0
2025-05-15 20:48:47:079 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 20:48:47:709 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 20:48:47:918 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 20:48:51:488 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 20:48:51:877 Core.Instrumentation.LogActivity: activityType=NinjaScript errorCode=NoError errorMessage=''
2025-05-15 20:48:53:090 SQLite error (17): statement aborts at 44: [UPDATE MasterInstruments SET AutoLiquidation=?,Currency=?,Description=?,InstrumentType=?,IsServerSupported=?,MergePolicy=?,Name=?,PointValue=?,PriceLevel=?,TradingHours=?,TickSize=?,Ur
2025-05-15 20:48:53:146 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.23ms InstrumentLists=1.00ms MasterInstruments=61.44ms Messages=0.73ms Risks=19.20ms RolloverCollection=6350.37ms TradingHours=0.18ms
2025-05-15 20:48:53:148 Starting server message polling timer with interval 3600 seconds...
2025-05-15 20:48:54:053 2 Chart Chart1Tab1 1 Ser 3 Ind 0 DrawObj Chart2Tab1 1 Ser 0 Ind 0 DrawObj 
2025-05-15 20:48:58:370 SQLite error (17): statement aborts at 50: [SELECT Id,Account,BarIndex,Commission,Exchange,ExecutionId,Fee,Instrument,IsEntry,IsEntryStrategy,IsExit,IsExitStrategy,LotSize,MarketPosition,MaxPrice,MinPrice,Name,OrderId,Position,P
2025-05-15 20:49:18:932 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-15 20:49:19:034 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-15 runAsProcess=False
2025-05-15 20:49:19:061 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 20:49:19:067 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 20:49:19:094 (TRADIFY) Cbi.Connection.Connect1
2025-05-15 20:49:19:112 (TRADIFY) Cbi.Connection.Connect2
2025-05-15 20:49:19:112 (TRADIFY) Cbi.Connection.Connect3
2025-05-15 20:49:19:147 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-15 20:49:19:167 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 20:49:19:173 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-15 20:49:19:176 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 20:49:19:179 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-15 20:49:19:189 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-15 20:49:19:189 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 20:49:19:189 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-15 20:49:19:190 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-15 20:49:19:190 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 20:49:19:190 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-15 20:49:19:190 (TRADIFY) Cbi.Connection.Connect4
2025-05-15 20:49:19:259 (TRADIFY) Cbi.Connection.Connect5
2025-05-15 20:49:19:272 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-15 20:49:19:310 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-15 20:49:19:573 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-15 20:49:19:574 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-15 20:49:19:574 Core.Instrumentation.LogActivity: activityType=Adapter errorCode=NoError errorMessage=''
2025-05-15 20:49:19:881 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-15 20:49:19:882 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8693129' renewSecs='2399.********'
2025-05-15 20:49:19:937 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-15 20:49:20:302 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-15 20:49:20:303 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-15 20:49:20:305 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-15 20:49:20:317 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-15 20:49:21:381 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 20:49:21:806 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 20:49:21:822 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 20:49:21:838 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 20:49:21:895 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 20:49:24:015 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000017' displayName='TDYA150355451300000017' fcm='' denomination=UsDollar forexLotSize=1
2025-05-15 20:49:24:015 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 20:49:24:016 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000017'
2025-05-15 20:49:24:020 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-15 20:49:24:020 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 20:49:24:020 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-15 20:49:24:038 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 20:49:24:040 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 20:49:24:040 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 20:49:24:040 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 20:49:24:040 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 20:49:24:113 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21405.75 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-15 20:30:16' statementDate='1800-01-01'
2025-05-15 20:49:24:158 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21404.75 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-15 20:31:18' statementDate='1800-01-01'
2025-05-15 20:49:24:159 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21383.5 quantity=1 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-15 20:46:26' statementDate='1800-01-01'
2025-05-15 20:49:24:167 (TRADIFY) Cbi.Account.OnAddTrade: entryId='************_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=445
2025-05-15 20:49:24:188 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYG150355451300000018' avgPrice=21404.75 quantity=1 marketPosition=Short operation=Add
2025-05-15 20:49:24:191 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-15 20:49:24:204 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-15 20:49:24:277 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21405.75 onBehalfOf='' id=16423 time='2025-05-15 20:30:16' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 20:49:24:585 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21404.75 onBehalfOf='' id=16424 time='2025-05-15 20:31:18' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 20:49:24:615 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21383.5 onBehalfOf='' id=16425 time='2025-05-15 20:46:26' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 20:49:24:648 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 20:49:24:653 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 20:49:24:655 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 20:49:24:655 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 20:49:24:655 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 20:49:24:655 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 20:49:24:716 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-15 20:49:24:718 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000017' fcm=''
2025-05-15 20:49:24:720 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-15 20:49:24:721 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-15 20:49:24:721 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-15 20:49:25:938 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-15 20:49:26:438 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000017' fcm=''
2025-05-15 20:49:27:070 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-15 20:49:27:123 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-15 20:49:27:365 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-15 20:49:27:366 (TRADIFY) Core.Connection.Statistics: connectAttempts=1/8106.2ms
2025-05-15 20:49:27:366 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-15 20:49:27:372 Server.HdsClient.Connect: type=HDS server='hds-us-nt-015.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-15 20:49:27:447 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 20:49:27:447 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 20:49:27:447 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-15 20:49:27:553 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 20:49:27:553 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-15 20:49:27:579 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-15 20:49:27:585 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='14/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 20:49:27:586 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 20:49:28:400 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-15 20:49:28:401 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='Sim101' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 20:49:28:401 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount2' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 20:49:28:401 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='Sim101' fcm='' fcmDate='2025-05-15'
2025-05-15 20:49:28:402 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount2' fcm='' fcmDate='2025-05-15'
2025-05-15 20:49:28:402 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='SimAccount1' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 20:49:28:402 (TRADIFY) Cbi.Account.OnTimerTick.Simulator.Roll: account='SimAccount1' fcm='' fcmDate='2025-05-15'
2025-05-15 20:49:29:058 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 20:49:29:058 Cbi.Instrument.RequestBars (to Provider): instrument='MNQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 20:49:40:127 (TRADIFY) Cbi.Account.CreateOrder: orderId='adf2732da36544edb7514f58c7d5a115' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:40' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 20:49:40:156 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='adf2732da36544edb7514f58c7d5a115' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:40' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:49:40:157 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='adf2732da36544edb7514f58c7d5a115' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:40' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:49:40:176 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 20:49:40:176 (TRADIFY) Tradovate.Adapter.Submit1: orderId='adf2732da36544edb7514f58c7d5a115' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:40' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:49:40:683 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 20:49:40:765 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:49:40:774 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "clOrdId": "adf2732da36544edb7514f58c7d5a115",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:40:871 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='adf2732da36544edb7514f58c7d5a115' orderId='************' account='TDYG150355451300000018' name='' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=21307.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:40' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:40:955 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21307.75,
  "timeInForce": "Day"
}'
2025-05-15 20:49:41:047 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:49:40.542Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 20:49:41:089 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "clOrdId": "adf2732da36544edb7514f58c7d5a115",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:41:089 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:49:41:089 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:49:40.542Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 20:49:41:090 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21307.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:40' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:41:090 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=21307.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:40' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:41:090 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "clOrdId": "adf2732da36544edb7514f58c7d5a115",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:41:090 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "action": "Buy",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:49:41:109 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.***************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:40.542Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "1.****************"
}'
2025-05-15 20:49:43:159 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:43' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21307.25 stopPriceChanged=0 quantityChanged=1
2025-05-15 20:49:43:159 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=21307.75 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:43' statementDate='2025-05-15' error=NoError comment='' nr=4
2025-05-15 20:49:43:162 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:43' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21307.25 quantityChanged=1 stopPriceChanged=0
2025-05-15 20:49:43:167 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 20:49:43:167 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.75 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:49:43' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:49:43:541 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 20:49:43:541 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:43.409Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:43:541 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21307.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:43' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:43:541 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21307.25,
  "timeInForce": "Day"
}'
2025-05-15 20:49:43:541 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:49:43.411Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 20:49:43:541 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:43.409Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:43:542 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:49:43.412Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 20:49:43:542 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21307.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:43' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:43:542 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=21307.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:49:43' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:49:43:542 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:43.409Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:43:542 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:49:43.412Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 20:49:43:542 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "clOrdId": "adf2732da36544edb7514f58c7d5a115",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:49:43:542 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:43.411Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "1.****************"
}'
2025-05-15 20:50:02:183 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.25 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:50:02' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21303.5 stopPriceChanged=0 quantityChanged=1
2025-05-15 20:50:02:184 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=21307.25 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:50:02' statementDate='2025-05-15' error=NoError comment='' nr=8
2025-05-15 20:50:02:189 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.25 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:50:02' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21303.5 quantityChanged=1 stopPriceChanged=0
2025-05-15 20:50:02:189 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 20:50:02:189 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21307.25 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 20:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:50:02:420 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 20:50:02:420 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:02.289Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:02:420 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21303.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:02:420 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21303.5,
  "timeInForce": "Day"
}'
2025-05-15 20:50:02:421 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:02.291Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 20:50:02:421 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:02.289Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:02:421 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:02.292Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 20:50:02:421 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21303.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:02:422 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=21303.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 20:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:02:422 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:02.289Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:02:422 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:02.292Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 20:50:02:422 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:49:43.409Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:02:423 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:02.291Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.*****************"
}'
2025-05-15 20:50:27:121 (TRADIFY) Cbi.Account.CreateOrder: orderId='7aaea1161c854df5aedbf0a4893cf43f' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:27' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 20:50:27:127 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='7aaea1161c854df5aedbf0a4893cf43f' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:27' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:50:27:127 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='7aaea1161c854df5aedbf0a4893cf43f' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:27' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:50:27:127 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 20:50:27:127 (TRADIFY) Tradovate.Adapter.Submit1: orderId='7aaea1161c854df5aedbf0a4893cf43f' account='TDYG150355451300000018' name='' orderState=Initialized instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:27' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:50:27:371 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 20:50:27:371 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:50:27:371 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "clOrdId": "7aaea1161c854df5aedbf0a4893cf43f",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:27:371 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='7aaea1161c854df5aedbf0a4893cf43f' orderId='************' account='TDYG150355451300000018' name='' orderState=Submitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21404 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:27' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:27:395 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21404.0,
  "timeInForce": "Day"
}'
2025-05-15 20:50:27:408 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:27.243Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 20:50:27:408 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "clOrdId": "7aaea1161c854df5aedbf0a4893cf43f",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:27:408 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:50:27:408 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:27.245Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 20:50:27:408 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21404 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:27' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:27:409 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21404 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:27' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:27:409 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "clOrdId": "7aaea1161c854df5aedbf0a4893cf43f",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:27:409 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "action": "Buy",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 20:50:27:409 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:27.243Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 20:50:46:792 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:46' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 stopPriceChanged=21437.75 quantityChanged=1
2025-05-15 20:50:46:792 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21404 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:46' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 20:50:46:796 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:46' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21437.75
2025-05-15 20:50:46:796 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 20:50:46:797 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21404 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 20:50:46' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 20:50:47:028 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 20:50:47:028 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:46.897Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:47:028 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21437.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:46' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:47:028 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21437.75,
  "timeInForce": "Day"
}'
2025-05-15 20:50:47:029 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:46.899Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 20:50:47:029 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:46.897Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:47:029 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:46.899Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 20:50:47:029 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21437.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:46' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:47:029 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21437.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 20:50:46' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 20:50:47:029 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:46.897Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:47:029 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T00:50:46.899Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 20:50:47:030 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "clOrdId": "7aaea1161c854df5aedbf0a4893cf43f",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 20:50:47:030 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:46.899Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 20:51:20:840 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=Panic errorMessage='Chart Render Time: 571.75 ms'
2025-05-15 20:59:45:914 (TRADIFY) Tradovate.Adapter.MarketDataReceiveB {"e":"md","d":{"ticks":[{"id":3703587,"tks":[{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":3,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473630},{"t":*************,"p":21359.5,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473631},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473632},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473633},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473634},{"t":*************,"p":21359.0,"s":2,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473635},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473636},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473637},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473638},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473639},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473640},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473641},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473642},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473643},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473644},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473645},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473646},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473647},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473648},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473649},{"t":1747357185720,"p":21359.25,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473650},{"t":1747357185722,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":45,"as":1,"id":25473651},{"t":1747357185722,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473652},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":42,"as":1,"id":25473653},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":41,"as":1,"id":25473654},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":40,"as":1,"id":25473655},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":38,"as":1,"id":25473656},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":37,"as":1,"id":25473657},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":36,"as":1,"id":25473658},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":34,"as":1,"id":25473659},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473660},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473661},{"t":1747357185724,"p":21359.0,"s":3,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473662},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":30,"as":2,"id":25473663},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":28,"as":2,"id":25473664},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473665},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473666},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":23,"as":2,"id":25473667},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":22,"as":2,"id":25473668},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":21,"as":3,"id":25473669},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":20,"as":3,"id":25473670},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":4,"id":25473671},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":5,"id":25473672},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":18,"as":5,"id":25473673},{"t":1747357185725,"p":21359.0,"s":4,"b":21359.0,"a":21359.25,"bs":17,"as":5,"id":25473674},{"t":1747357185725,"p":21359.0,"s":6,"b":21359.0,"a":21359.25,"bs":12,"as":5,"id":25473675},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":6,"as":5,"id":25473676},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473677},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473678},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473679},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473680},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473681},{"t":1747357185727,"p":21358.75,"s":1,"b":"NaN","a":21358.75,"bs":0,"as":1,"id":25473682},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473683},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473684},{"t":1747357185729,"p":21359.0,"s":1,"b":21358.25,"a":21359.0,"bs":1,"as":1,"id":25473685},{"t":1747357185729,"p":21359.25,"s":1,"b":21358.25,"a":21359.25,"bs":1,"as":5,"id":25473686},{"t":1747357185731,"p":21358.75,"s":1,"b":21358.75,"a":21359.0,"bs":1,"as":2,"id":25473687},{"t":1747357185732,"p":21358.75,"s":1,"b":21358.25,"a":21358.75,"bs":2,"as":1,"id":25473688}]}]}}
2025-05-15 20:59:45:914 (TRADIFY) Tradovate.Adapter.MarketDataReceiveB {"e":"md","d":{"ticks":[{"id":3703587,"tks":[{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":3,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473630},{"t":*************,"p":21359.5,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473631},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473632},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473633},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473634},{"t":*************,"p":21359.0,"s":2,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473635},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473636},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473637},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473638},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473639},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473640},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473641},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473642},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473643},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473644},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473645},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473646},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473647},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473648},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473649},{"t":1747357185720,"p":21359.25,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473650},{"t":1747357185722,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":45,"as":1,"id":25473651},{"t":1747357185722,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473652},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":42,"as":1,"id":25473653},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":41,"as":1,"id":25473654},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":40,"as":1,"id":25473655},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":38,"as":1,"id":25473656},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":37,"as":1,"id":25473657},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":36,"as":1,"id":25473658},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":34,"as":1,"id":25473659},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473660},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473661},{"t":1747357185724,"p":21359.0,"s":3,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473662},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":30,"as":2,"id":25473663},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":28,"as":2,"id":25473664},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473665},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473666},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":23,"as":2,"id":25473667},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":22,"as":2,"id":25473668},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":21,"as":3,"id":25473669},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":20,"as":3,"id":25473670},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":4,"id":25473671},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":5,"id":25473672},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":18,"as":5,"id":25473673},{"t":1747357185725,"p":21359.0,"s":4,"b":21359.0,"a":21359.25,"bs":17,"as":5,"id":25473674},{"t":1747357185725,"p":21359.0,"s":6,"b":21359.0,"a":21359.25,"bs":12,"as":5,"id":25473675},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":6,"as":5,"id":25473676},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473677},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473678},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473679},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473680},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473681},{"t":1747357185727,"p":21358.75,"s":1,"b":"NaN","a":21358.75,"bs":0,"as":1,"id":25473682},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473683},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473684},{"t":1747357185729,"p":21359.0,"s":1,"b":21358.25,"a":21359.0,"bs":1,"as":1,"id":25473685},{"t":1747357185729,"p":21359.25,"s":1,"b":21358.25,"a":21359.25,"bs":1,"as":5,"id":25473686},{"t":1747357185731,"p":21358.75,"s":1,"b":21358.75,"a":21359.0,"bs":1,"as":2,"id":25473687},{"t":1747357185732,"p":21358.75,"s":1,"b":21358.25,"a":21358.75,"bs":2,"as":1,"id":25473688}]}]}}
2025-05-15 20:59:45:914 (TRADIFY) Tradovate.Adapter.MarketDataReceiveB {"e":"md","d":{"ticks":[{"id":3703587,"tks":[{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21360.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":3,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":********},{"t":*************,"p":21359.75,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473630},{"t":*************,"p":21359.5,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473631},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473632},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473633},{"t":*************,"p":21359.25,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473634},{"t":*************,"p":21359.0,"s":2,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473635},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473636},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473637},{"t":*************,"p":21359.0,"s":1,"b":21360.0,"a":21361.0,"bs":2,"as":2,"id":25473638},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473639},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473640},{"t":1747357185719,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473641},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473642},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473643},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473644},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473645},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473646},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473647},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473648},{"t":1747357185720,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473649},{"t":1747357185720,"p":21359.25,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473650},{"t":1747357185722,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":45,"as":1,"id":25473651},{"t":1747357185722,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":44,"as":1,"id":25473652},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":42,"as":1,"id":25473653},{"t":1747357185723,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":41,"as":1,"id":25473654},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":40,"as":1,"id":25473655},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":38,"as":1,"id":25473656},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":37,"as":1,"id":25473657},{"t":1747357185724,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":36,"as":1,"id":25473658},{"t":1747357185724,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":34,"as":1,"id":25473659},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473660},{"t":1747357185724,"p":21359.25,"s":1,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473661},{"t":1747357185724,"p":21359.0,"s":3,"b":21359.0,"a":21359.25,"bs":33,"as":1,"id":25473662},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":30,"as":2,"id":25473663},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":28,"as":2,"id":25473664},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473665},{"t":1747357185725,"p":21359.0,"s":2,"b":21359.0,"a":21359.25,"bs":26,"as":2,"id":25473666},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":23,"as":2,"id":25473667},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":22,"as":2,"id":25473668},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":21,"as":3,"id":25473669},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":20,"as":3,"id":25473670},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":4,"id":25473671},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":19,"as":5,"id":25473672},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":18,"as":5,"id":25473673},{"t":1747357185725,"p":21359.0,"s":4,"b":21359.0,"a":21359.25,"bs":17,"as":5,"id":25473674},{"t":1747357185725,"p":21359.0,"s":6,"b":21359.0,"a":21359.25,"bs":12,"as":5,"id":25473675},{"t":1747357185725,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":6,"as":5,"id":25473676},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473677},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":5,"as":5,"id":25473678},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473679},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473680},{"t":1747357185726,"p":21359.0,"s":1,"b":21359.0,"a":21359.25,"bs":3,"as":5,"id":25473681},{"t":1747357185727,"p":21358.75,"s":1,"b":"NaN","a":21358.75,"bs":0,"as":1,"id":25473682},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473683},{"t":1747357185728,"p":21358.5,"s":1,"b":"NaN","a":21359.25,"bs":0,"as":5,"id":25473684},{"t":1747357185729,"p":21359.0,"s":1,"b":21358.25,"a":21359.0,"bs":1,"as":1,"id":25473685},{"t":1747357185729,"p":21359.25,"s":1,"b":21358.25,"a":21359.25,"bs":1,"as":5,"id":25473686},{"t":1747357185731,"p":21358.75,"s":1,"b":21358.75,"a":21359.0,"bs":1,"as":2,"id":25473687},{"t":1747357185732,"p":21358.75,"s":1,"b":21358.25,"a":21358.75,"bs":2,"as":1,"id":25473688}]}]}}
2025-05-15 21:27:51:645 -- SetDefaults --
2025-05-15 21:28:17:900 -- SetDefaults --
2025-05-15 21:28:31:897 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-15 21:28:31:915 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-15 21:28:32:375 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-15 21:28:32:375 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8919454' renewSecs='2399.9459727'
2025-05-15 21:29:19:833 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-15 21:29:19:833 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-15 21:29:20:237 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8676208' renewSecs='2399.9338104'
2025-05-15 21:29:39:656 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21437.75 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 21:29:39' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 stopPriceChanged=21402.75 quantityChanged=1
2025-05-15 21:29:39:657 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21437.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 21:29:39' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 21:29:39:662 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21437.75 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 21:29:39' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 quantityChanged=1 stopPriceChanged=21402.75
2025-05-15 21:29:39:664 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 21:29:39:664 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21437.75 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16427 time='2025-05-15 21:29:39' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 21:29:39:868 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 21:29:39:868 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:29:39.763Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:29:39:871 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21402.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 21:29:39' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:29:39:871 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Stop",
  "stopPrice": 21402.75,
  "timeInForce": "Day"
}'
2025-05-15 21:29:39:871 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:29:39.765Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 21:29:39:871 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:29:39.763Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:29:39:872 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:29:39.766Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 21:29:39:872 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21402.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 21:29:39' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:29:39:872 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21402.75 quantity=1 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 21:29:39' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:29:39:872 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:29:39.763Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:29:39:872 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:29:39.766Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 21:29:39:872 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:46.897Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:29:39:873 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T01:29:39.765Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 21:30:49:450 -- SetDefaults --
2025-05-15 21:34:57:818 -- SetDefaults --
2025-05-15 21:35:31:807 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21303.5 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 21:35:31' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21320 stopPriceChanged=0 quantityChanged=1
2025-05-15 21:35:31:807 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy limitPrice=21303.5 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 21:35:31' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 21:35:31:812 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21303.5 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 21:35:31' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=21320 quantityChanged=1 stopPriceChanged=0
2025-05-15 21:35:31:812 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 21:35:31:812 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='' orderState=ChangePending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21303.5 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 21:35:31' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 21:35:32:011 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 21:35:32:011 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:35:31.909Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:35:32:011 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 21:35:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:35:32:016 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 1,
  "orderType": "Limit",
  "price": 21320.0,
  "timeInForce": "Day"
}'
2025-05-15 21:35:32:016 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:35:31.911Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 21:35:32:016 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:35:31.909Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:35:32:016 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:35:31.912Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 21:35:32:016 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='' orderState=Accepted instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 21:35:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:35:32:017 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 21:35:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 21:35:32:017 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:35:31.909Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:35:32:017 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T01:35:31.912Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 21:35:32:017 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T00:50:02.289Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 21:35:32:017 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T01:35:31.911Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.*****************"
}'
2025-05-15 21:48:53:176 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-15 21:48:54:192 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-15 21:48:54:200 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=1.22ms Risks=0.00ms RolloverCollection=0.00ms TradingHours=0.00ms
2025-05-15 22:08:32:332 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-15 22:08:32:335 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-15 22:08:32:700 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-15 22:08:32:700 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8677765' renewSecs='2399.********'
2025-05-15 22:09:20:176 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-15 22:09:20:176 (TRADIFY) NinjaTrader.Core.Authentication.RenewToken
2025-05-15 22:09:20:480 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.9014053' renewSecs='2399.********'
2025-05-15 22:30:54:089 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:30:53.882Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Buy",
  "cumQty": 1,
  "avgPx": 21403.0,
  "lastQty": 1,
  "lastPx": 21403.0,
  "externalClOrdId": "1.****************"
}'
2025-05-15 22:30:54:170 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ************,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:30:53.882Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "action": "Buy",
  "qty": 1,
  "price": 21403.0,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-15 22:30:54:268 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Buy limitPrice=0 stopPrice=21402.75 quantity=1 orderType='Stop Market' filled=1 averageFillPrice=21403 time='2025-05-15 22:30:53' statementDate='1800-01-01' error=NoError comment='' nr=12
2025-05-15 22:30:54:304 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21403 quantity=1 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-15 22:30:53' statementDate='1800-01-01'
2025-05-15 22:30:54:312 (TRADIFY) Cbi.Account.OnAddTrade: entryId='' exitId='************_1' profitCurrencyBeforeCommissionAndFees=35
2025-05-15 22:30:54:437 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:30:54:437 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:438 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:445 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:30:53.882Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "netPos": 0,
  "bought": 2,
  "boughtValue": 42786.5,
  "sold": 2,
  "soldValue": 42810.5,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ************
}'
2025-05-15 22:30:54:563 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYG150355451300000018' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-15 22:30:54:599 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:30:54:599 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:599 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:30:54:599 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:50:27.240Z",
  "action": "Buy",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:30:55:071 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:40:59:279 (TRADIFY) Gui.Chart.OrderStack.Cancel.Cancel1: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=Working instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21320 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 21:35:31' gtd='2099-12-01' statementDate='1800-01-01'
2025-05-15 22:40:59:289 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21320 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 22:40:59' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:40:59:290 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:40:59' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 22:40:59:297 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21320 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 22:40:59' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:40:59:304 (TRADIFY) Tradovate.Adapter.Cancel0: count=1
2025-05-15 22:40:59:305 (TRADIFY) Tradovate.Adapter.Cancel1: orderId='************' account='TDYG150355451300000018' name='' orderState=CancelPending instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21320 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16426 time='2025-05-15 22:40:59' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:40:59:608 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 22:40:59:608 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:40:59.459Z",
  "commandType": "Cancel",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:40:59:608 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:40:59.460Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:40:59:611 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:40:59.459Z",
  "commandType": "Cancel",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:40:59:611 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='************' account='TDYG150355451300000018' name='' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:40:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:40:59:611 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='************' account='TDYG150355451300000018' name='' orderState=Cancelled instrument='NQ JUN25' orderAction=Buy limitPrice=21320 stopPrice=0 quantity=1 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:40:59' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:40:59:627 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:40:59.460Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 22:40:59:627 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T01:35:31.909Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:40:59:627 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:40:59.459Z",
  "orderId": ************,
  "execType": "Canceled",
  "ordStatus": "Canceled",
  "action": "Buy",
  "externalClOrdId": "1.****************"
}'
2025-05-15 22:40:59:627 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T00:49:40.539Z",
  "action": "Buy",
  "ordStatus": "Canceled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:42:16:195 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 22:42:16:332 Core.Instrumentation.LogActivity: activityType=ChartPerformance errorCode=NoError errorMessage=''
2025-05-15 22:46:25:014 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage2 Empty 
2025-05-15 22:46:25:029 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-15 22:46:25:029 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 2,002
2025-05-15 22:46:25:029 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:46:25:031 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:46:25:032 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:46:25:032 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:25:032 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:25:032 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:25:546 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 22:46:25:948 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 22:46:25:948 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 22:46:25:949 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 22:46:25:950 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 22:46:26:171 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:26:171 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:26:171 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:26:171 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-15 22:46:26:171 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:26:171 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-15 22:46:26:174 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage exception='The remote party closed the WebSocket connection without completing the close handshake. ' webSocketErrorCode='ConnectionClosedPrematurely'
2025-05-15 22:46:26:174 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='The remote party closed the WebSocket connection without completing the close handshake.' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-15 22:46:26:174 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 224
2025-05-15 22:46:26:174 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:26:174 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:26:174 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:26:175 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:26:175 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:26:175 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:26:679 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 22:46:27:114 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 22:46:27:115 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 22:46:27:115 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 22:46:27:116 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 22:46:27:344 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:27:344 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:27:345 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage exception='The remote party closed the WebSocket connection without completing the close handshake. ' webSocketErrorCode='ConnectionClosedPrematurely'
2025-05-15 22:46:27:345 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='The remote party closed the WebSocket connection without completing the close handshake.' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-15 22:46:27:345 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 1,395
2025-05-15 22:46:27:345 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:27:345 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:27:346 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:27:346 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:27:346 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:27:346 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:27:347 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:27:347 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:27:347 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:27:347 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:27:857 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 22:46:28:209 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 22:46:28:209 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 22:46:28:209 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 22:46:28:212 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 22:46:28:445 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:28:446 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:28:446 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage exception='The remote party closed the WebSocket connection without completing the close handshake. ' webSocketErrorCode='ConnectionClosedPrematurely'
2025-05-15 22:46:28:446 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='The remote party closed the WebSocket connection without completing the close handshake.' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-15 22:46:28:446 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 2,497
2025-05-15 22:46:28:446 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:28:446 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:28:446 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:28:446 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:28:446 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:28:446 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:28:448 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=ConnectionLost previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError='The remote party closed the WebSocket connection without completing the close handshake.'
2025-05-15 22:46:28:448 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=ConnectionLost
2025-05-15 22:46:28:448 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=True foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:28:448 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=ConnectionLost
2025-05-15 22:46:28:948 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 22:46:29:288 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 22:46:29:288 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 22:46:29:289 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 22:46:29:290 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 22:46:29:528 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage2 NormalClosure Bye
2025-05-15 22:46:29:528 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData errorMsg='Bye' tradeStatus='Connected' mdStatus='ConnectionLost'
2025-05-15 22:46:29:528 (TRADIFY) Tradovate.Adapter.OnDisconnectMarketData: Milliseconds from last marketDataHeartbeatTime 238
2025-05-15 22:46:29:528 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:29:528 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:29:529 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connected previousPriceStatus=ConnectionLost errorCode=NoError nativeError=''
2025-05-15 22:46:29:529 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-15 22:46:29:529 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:46:29:530 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-15 22:48:09:088 (TRADIFY) Cbi.Account.CreateOrder: orderId='af481b9f8514470fb99c64d2169c7e23' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 22:48:09:112 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='af481b9f8514470fb99c64d2169c7e23' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:09:112 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='af481b9f8514470fb99c64d2169c7e23' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:09:112 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 22:48:09:112 (TRADIFY) Tradovate.Adapter.Submit1: orderId='af481b9f8514470fb99c64d2169c7e23' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:09:113 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='af481b9f8514470fb99c64d2169c7e23' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:09:344 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 22:48:09:344 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:48:09:344 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "clOrdId": "af481b9f8514470fb99c64d2169c7e23",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:09:344 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='af481b9f8514470fb99c64d2169c7e23' orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:09' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:48:09:357 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 5,
  "orderType": "StopLimit",
  "price": 21397.75,
  "stopPrice": 21398.0,
  "timeInForce": "GTC"
}'
2025-05-15 22:48:09:357 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:48:09.213Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:48:09:357 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "clOrdId": "af481b9f8514470fb99c64d2169c7e23",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:09:358 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:48:09:358 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:48:09.215Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:48:09:358 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:09' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:48:09:358 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:09' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:48:09:358 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "clOrdId": "af481b9f8514470fb99c64d2169c7e23",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:09:358 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "action": "Sell",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:48:09:358 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:09.213Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.*****************"
}'
2025-05-15 22:48:31:693 (TRADIFY) Gui.Chart.OrderStack.Cancel.Cancel1: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:31' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:31:693 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:31' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:31:693 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:31' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 22:48:31:696 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:31' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:31:697 (TRADIFY) Tradovate.Adapter.Cancel0: count=1
2025-05-15 22:48:31:697 (TRADIFY) Tradovate.Adapter.Cancel1: orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelPending instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16428 time='2025-05-15 22:48:31' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:48:31:924 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 22:48:31:924 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:31.793Z",
  "commandType": "Cancel",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:31:925 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:48:31.794Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:48:31:925 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:31.793Z",
  "commandType": "Cancel",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:31:925 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='************' account='TDYG150355451300000018' name='Entry' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:48:31:926 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell limitPrice=21397.75 stopPrice=21398 quantity=5 orderType='Stop Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:48:31' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:48:31:942 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:48:31.794Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 22:48:31:942 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "clOrdId": "af481b9f8514470fb99c64d2169c7e23",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:48:31:942 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.***************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:31.793Z",
  "orderId": ************,
  "execType": "Canceled",
  "ordStatus": "Canceled",
  "action": "Sell",
  "externalClOrdId": "0.*****************"
}'
2025-05-15 22:48:31:942 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:48:09.210Z",
  "action": "Sell",
  "ordStatus": "Canceled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:48:31:948 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********'
2025-05-15 22:48:32:647 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal1
2025-05-15 22:48:32:647 (Simulation) NinjaTrader.Core.Authentication.RenewToken
2025-05-15 22:48:33:201 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal3
2025-05-15 22:48:33:201 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8643208' renewSecs='2399.9321604'
2025-05-15 22:48:43:581 (TRADIFY) Gui.ControlCenter.OnDisconnect
2025-05-15 22:48:43:593 (TRADIFY) Cbi.Connection.Disconnect
2025-05-15 22:48:43:593 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:48:43:594 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-15 22:48:43:594 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-15 22:48:43:594 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-15 22:48:43:594 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-15 22:48:43:594 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnecting previousStatus=Connected message=''
2025-05-15 22:48:43:597 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:48:43:597 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close1
2025-05-15 22:48:43:602 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnecting priceStatus=Disconnecting previousStatus=Connected previousPriceStatus=Connected errorCode=NoError nativeError=''
2025-05-15 22:48:43:602 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Disconnecting priceStatus=Disconnecting
2025-05-15 22:48:43:602 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=False
2025-05-15 22:48:43:602 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Disconnected
2025-05-15 22:48:43:605 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close3
2025-05-15 22:48:43:611 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close4
2025-05-15 22:48:43:611 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close5
2025-05-15 22:48:43:611 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close6a
2025-05-15 22:48:43:612 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close7
2025-05-15 22:48:43:612 (TRADIFY) Cbi.Connection.ConnectionStatusCallback.Close8
2025-05-15 22:48:43:659 (TRADIFY) Tradovate.Adapter.DisposeMarketData0
2025-05-15 22:48:44:180 (TRADIFY) Tradovate.Adapter.DisposeTrade0
2025-05-15 22:48:45:165 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-15 22:48:45:200 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Disconnected previousStatus=Disconnecting message=''
2025-05-15 22:48:45:211 Flushing DB thread
2025-05-15 22:48:45:214 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-15 22:48:45:228 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Disconnected priceStatus=Disconnected previousStatus=Disconnecting previousPriceStatus=Disconnecting errorCode=NoError nativeError=''
2025-05-15 22:48:45:228 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Using args status=Disconnected
2025-05-15 22:48:53:177 Server.HdsClient.Connect: type=IS server='is-us-nt-006.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-15 22:48:54:252 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=0 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=0 TradingHours=0
2025-05-15 22:48:54:252 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.00ms InstrumentLists=0.00ms MasterInstruments=0.00ms Messages=0.23ms Risks=0.00ms RolloverCollection=0.02ms TradingHours=0.00ms
2025-05-15 22:48:54:762 (TRADIFY) Gui.ControlCenter.OnConnect
2025-05-15 22:48:54:774 (TRADIFY) Cbi.Connection.Connect0: status=Disconnected assembly=******* date=2025-05-15 runAsProcess=False
2025-05-15 22:48:54:774 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 22:48:54:774 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 22:48:54:775 (TRADIFY) Cbi.Connection.Connect1
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Connection.Connect2
2025-05-15 22:48:54:776 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connecting priceStatus=Connecting previousStatus=Disconnected previousPriceStatus=Disconnected errorCode=NoError nativeError=''
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Connection.Connect3
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Connection.CreateAccount: account='Sim101' displayName='Sim101' fcm='' denomination=UsDollar forexLotSize=10000
2025-05-15 22:48:54:776 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connecting priceStatus=Connecting
2025-05-15 22:48:54:776 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=True foundConnected=False
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='Sim101'
2025-05-15 22:48:54:776 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connecting
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount1' displayName='SimAccount1' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount1'
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Connection.CreateAccount: account='SimAccount2' displayName='SimAccount2' fcm='' denomination=UsDollar forexLotSize=1000
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 22:48:54:776 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='SimAccount2'
2025-05-15 22:48:54:778 (TRADIFY) Cbi.Connection.Connect4
2025-05-15 22:48:54:778 (TRADIFY) Cbi.Connection.Connect5
2025-05-15 22:48:54:778 (TRADIFY) Tradovate.Adapter.Connect: user='TDY001310' accountType='Simulation' useLocalOcoSimulation=True
2025-05-15 22:48:54:782 (TRADIFY) Cbi.Connection.Connect9 ok
2025-05-15 22:48:55:414 (TRADIFY) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-15 22:48:55:414 (TRADIFY) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.8652103' renewSecs='2399.********'
2025-05-15 22:48:55:418 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket
2025-05-15 22:48:55:796 (TRADIFY) Tradovate.Adapter.StartTradeWebSocket1
2025-05-15 22:48:55:796 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeSendWorker
2025-05-15 22:48:55:796 (TRADIFY) Tradovate.Adapter.StartWebSocketTradeReceiveWorker
2025-05-15 22:48:55:796 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketTradeMessage
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYA150355451300000017' displayName='TDYA150355451300000017' fcm='' denomination=UsDollar forexLotSize=1
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYA150355451300000017'
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Connection.CreateAccount: account='TDYG150355451300000018' displayName='TDYG150355451300000018' fcm='' denomination=UsDollar forexLotSize=1
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connecting previousStatus=Disconnected message=''
2025-05-15 22:48:56:481 (TRADIFY) Cbi.Account.OnConnectionStatus.PositionExecutions: account='TDYG150355451300000018'
2025-05-15 22:48:56:482 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:48:56:482 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYA150355451300000017' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:48:56:482 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:48:56:482 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:48:56:482 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:48:56:483 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21403 quantity=1 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-15 22:30:53' statementDate='1800-01-01'
2025-05-15 22:48:56:483 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21404.75 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-15 20:31:18' statementDate='1800-01-01'
2025-05-15 22:48:56:483 (TRADIFY) Cbi.Account.OnAddTrade: entryId='************_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=35
2025-05-15 22:48:56:483 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21383.5 quantity=1 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-15 20:46:26' statementDate='1800-01-01'
2025-05-15 22:48:56:484 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21405.75 quantity=1 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-15 20:30:16' statementDate='1800-01-01'
2025-05-15 22:48:56:484 (TRADIFY) Cbi.Account.OnAddTrade: entryId='************_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=445
2025-05-15 22:48:56:484 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc0
2025-05-15 22:48:56:485 (TRADIFY) Tradovate.Adapter.SetupUnrealizedPnlCalc1
2025-05-15 22:48:56:487 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Stop Market' limitPrice=0 stopPrice=21402.75 quantity=1 tif=Day oco='' filled=1 averageFillPrice=21403 onBehalfOf='' id=16429 time='2025-05-15 20:50:27' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:499 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21404.75 onBehalfOf='' id=16430 time='2025-05-15 20:31:18' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:508 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket
2025-05-15 22:48:56:510 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Buy orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21383.5 onBehalfOf='' id=16431 time='2025-05-15 20:46:26' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:523 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Cancelled instrument='NQ JUN25' orderAction=Sell orderType='Stop Limit' limitPrice=21397.75 stopPrice=21398 quantity=5 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16432 time='2025-05-15 22:48:09' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:523 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Cancelled instrument='NQ JUN25' orderAction=Buy orderType='Limit' limitPrice=21320 stopPrice=0 quantity=1 tif=Day oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16433 time='2025-05-15 20:49:40' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:523 (TRADIFY) Cbi.Account.CreateOrder: orderId='************' account='TDYG150355451300000018' name='' orderState=Filled instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=1 tif=Gtc oco='' filled=1 averageFillPrice=21405.75 onBehalfOf='' id=16434 time='2025-05-15 20:30:16' gtd='2099-12-01' statementDate='2099-12-01' id=-1 comment=''
2025-05-15 22:48:56:843 (TRADIFY) Tradovate.Adapter.StartMarketDataWebSocket1
2025-05-15 22:48:56:843 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataSendWorker
2025-05-15 22:48:56:843 (TRADIFY) Tradovate.Adapter.StartWebSocketMarketDataReceiveWorker
2025-05-15 22:48:56:844 (TRADIFY) Tradovate.Adapter.ReceiveWebSocketMarketDataMessage
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.OnConnectionStatus: account='Sim101' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.Restore.Start: account='Sim101' fcm=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount1' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.OnConnectionStatus: account='SimAccount2' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYA150355451300000017' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount1' fcm=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.Restore.Start: account='SimAccount2' fcm=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.Restore.Start: account='TDYA150355451300000017' fcm=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.OnConnectionStatus: account='TDYG150355451300000018' fcm='' status=Connected previousStatus=Connecting message=''
2025-05-15 22:48:57:169 (TRADIFY) Cbi.Account.Restore.Start: account='TDYG150355451300000018' fcm=''
2025-05-15 22:48:57:532 (TRADIFY) Cbi.Account.Restore.End: account='TDYG150355451300000018' fcm=''
2025-05-15 22:48:57:671 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount2' fcm=''
2025-05-15 22:48:57:676 (TRADIFY) Cbi.Account.Restore.End: account='TDYA150355451300000017' fcm=''
2025-05-15 22:48:57:868 (TRADIFY) Cbi.Account.Restore.End: account='SimAccount1' fcm=''
2025-05-15 22:48:58:230 (TRADIFY) Cbi.Account.Restore.End: account='Sim101' fcm=''
2025-05-15 22:48:58:230 (TRADIFY) Core.Connection.Statistics: connectAttempts=2/5779.0ms
2025-05-15 22:48:58:230 (TRADIFY) Cbi.Connection.ConnectionStatusCallback: autoClosePositionTime='04/29/2025 00:00:00' autoClosePosition=False
2025-05-15 22:48:58:230 Server.HdsClient.Connect: type=HDS server='hds-us-nt-013.ninjatrader.com' port=31655 system='' useSsl=True
2025-05-15 22:48:58:242 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 22:48:58:242 (TRADIFY) NinjaTrader.Gui.ControlCenter.OnConnectionStatus.DispatcherInvoke: Provider31 status=Connected priceStatus=Connected previousStatus=Connecting previousPriceStatus=Connecting errorCode=NoError nativeError=''
2025-05-15 22:48:58:242 (TRADIFY) Gui.ControlCenter.OnConnectionStatus.Adding: provider=Provider31 status=Connected priceStatus=Connected
2025-05-15 22:48:58:242 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: foundPriceLoss=False foundTradeLoss=False foundConnectingStatus=False foundConnected=True
2025-05-15 22:48:58:242 NinjaTrader.Gui.ControlCenter.OnConnectionStatus: Status=Connected
2025-05-15 22:48:58:243 (TRADIFY) Tradovate.Adapter.QueryNotificationsAsync0
2025-05-15 22:48:58:247 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 22:48:59:234 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:48:59:679 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 22:48:59:679 Cbi.Instrument.RequestBars (to Provider): instrument='NQ JUN25' from='15/05/2025 12:00:00 AM' to='15/05/2025 12:00:00 AM' period='1 Minute'
2025-05-15 22:50:02:251 (TRADIFY) Cbi.Account.CreateOrder: orderId='c3e01dad66784ce1851e52cda8ab6d25' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=10 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16435 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 22:50:02:256 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='c3e01dad66784ce1851e52cda8ab6d25' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=10 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16435 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:256 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='c3e01dad66784ce1851e52cda8ab6d25' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=10 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16435 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:256 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='c3e01dad66784ce1851e52cda8ab6d25' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=10 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16435 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:256 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 22:50:02:256 (TRADIFY) Tradovate.Adapter.Submit1: orderId='c3e01dad66784ce1851e52cda8ab6d25' account='TDYG150355451300000018' name='Entry' orderState=Initialized instrument='NQ JUN25' orderAction=Sell orderType='Market' limitPrice=0 stopPrice=0 quantity=10 tif=Gtc oco='' filled=0 averageFillPrice=0 onBehalfOf='' id=16435 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:493 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 22:50:02:493 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:493 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "clOrdId": "c3e01dad66784ce1851e52cda8ab6d25",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:02:494 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='c3e01dad66784ce1851e52cda8ab6d25' orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Submitted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=10 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:510 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 10,
  "orderType": "Market",
  "timeInForce": "GTC"
}'
2025-05-15 22:50:02:510 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.360Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:50:02:510 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "clOrdId": "c3e01dad66784ce1851e52cda8ab6d25",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:02:510 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "action": "Sell",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:510 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.361Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:50:02:511 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Accepted instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=10 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:511 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Working instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=10 orderType='Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:511 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "clOrdId": "c3e01dad66784ce1851e52cda8ab6d25",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:02:511 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "action": "Sell",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:511 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.360Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Sell",
  "externalClOrdId": "0.*****************"
}'
2025-05-15 22:50:02:511 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.360Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Sell",
  "cumQty": 10,
  "avgPx": 21396.0,
  "lastQty": 10,
  "lastPx": 21396.0,
  "externalClOrdId": "0.*****************"
}'
2025-05-15 22:50:02:512 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ************,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.360Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "action": "Sell",
  "qty": 10,
  "price": 21396.0,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-15 22:50:02:512 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYG150355451300000018' name='Entry' orderState=Filled instrument='NQ JUN25' orderAction=Sell limitPrice=0 stopPrice=0 quantity=10 orderType='Market' filled=10 averageFillPrice=21396 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:535 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21396 quantity=10 marketPosition=Short operation=Add orderID='************' isSod=False time='2025-05-15 22:50:02' statementDate='1800-01-01'
2025-05-15 22:50:02:548 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders0: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********' filled=10 outstanding=0 stopTargetHandling=PerEntryExecution filledOrders='************+=10 ' outstandingOrders='' thread=14
2025-05-15 22:50:02:548 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:50:02:548 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:548 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:50:02:549 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.360Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "netPos": -10,
  "netPrice": 21396.0,
  "bought": 2,
  "boughtValue": 42786.5,
  "sold": 12,
  "soldValue": 256770.5,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ************
}'
2025-05-15 22:50:02:549 (TRADIFY) NinjaScript.AtmStrategy.ManageBracketOrders1: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********' initialEntryOrderId='************' bracket=0 qty=10 stopOrdersOutstandingQuantity=0 quantity2Add=10 exitOrders=''
2025-05-15 22:50:02:560 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYG150355451300000018' avgPrice=21396 quantity=10 marketPosition=Short operation=Add
2025-05-15 22:50:02:566 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.357Z",
  "action": "Sell",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:571 (TRADIFY) NinjaScript.AtmStrategy.ManageStopOrder: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********' idx=0 orderType=StopMarket quantity=10 limitPrice=0 stopPrice=21476 oco='9260eca55c1547dcbda80ac327c2b1bb'
2025-05-15 22:50:02:571 (TRADIFY) Cbi.Account.CreateOrder: orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 22:50:02:575 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:575 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:576 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:576 (TRADIFY) Cbi.Account.CreateOrder: orderId='cef9f6a564f4436ca4c3f8a50d791c5f' account='TDYG150355451300000018' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15' id=-1 comment=''
2025-05-15 22:50:02:576 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 22:50:02:576 (TRADIFY) Tradovate.Adapter.Submit1: orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:579 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=False orderId='cef9f6a564f4436ca4c3f8a50d791c5f' account='TDYG150355451300000018' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:579 (TRADIFY) Cbi.Account.Submit1: realOrderState=Initialized orderId='cef9f6a564f4436ca4c3f8a50d791c5f' account='TDYG150355451300000018' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:580 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='ff330636683149bda22a9de52bbc22e4' account='TDYG150355451300000018' name='Stop1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:580 (TRADIFY) Cbi.Account.Submit0: realOrderState=Initialized isPendingSubmit=True orderId='cef9f6a564f4436ca4c3f8a50d791c5f' account='TDYG150355451300000018' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:595 (TRADIFY) Tradovate.Adapter.Submit0: count=1
2025-05-15 22:50:02:595 (TRADIFY) Tradovate.Adapter.Submit1: orderId='cef9f6a564f4436ca4c3f8a50d791c5f' account='TDYG150355451300000018' name='Target1' orderState=Initialized instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:50:02' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:02:851 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 22:50:02:851 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:851 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "clOrdId": "ff330636683149bda22a9de52bbc22e4",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:862 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='ff330636683149bda22a9de52bbc22e4' orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21476 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:862 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 10,
  "orderType": "Stop",
  "stopPrice": 21476.0,
  "timeInForce": "GTC"
}'
2025-05-15 22:50:02:862 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.720Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:50:02:864 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "clOrdId": "ff330636683149bda22a9de52bbc22e4",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:864 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:864 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.720Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:50:02:864 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21476 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:864 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21476 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:864 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "clOrdId": "ff330636683149bda22a9de52bbc22e4",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:864 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "action": "Buy",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:865 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.*****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.720Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:50:02:865 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 status='200' data='{
  "orderId": ************
}'
2025-05-15 22:50:02:865 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:865 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "clOrdId": "cef9f6a564f4436ca4c3f8a50d791c5f",
  "commandType": "New",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:865 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Submitted oldOrderId='cef9f6a564f4436ca4c3f8a50d791c5f' orderId='************' account='TDYG150355451300000018' name='Target1' orderState=Submitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:865 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 10,
  "orderType": "Limit",
  "price": 21391.0,
  "timeInForce": "GTC"
}'
2025-05-15 22:50:02:866 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.730Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:50:02:866 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "clOrdId": "cef9f6a564f4436ca4c3f8a50d791c5f",
  "commandType": "New",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:866 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "action": "Buy",
  "ordStatus": "PendingNew",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:866 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:02.730Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:50:02:866 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Target1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:866 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Target1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:50:02' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:02:867 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "clOrdId": "cef9f6a564f4436ca4c3f8a50d791c5f",
  "commandType": "New",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:02:867 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "action": "Buy",
  "ordStatus": "Working",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:50:02:867 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.730Z",
  "orderId": ************,
  "execType": "New",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:50:07:836 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:07' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 stopPriceChanged=21409.5 quantityChanged=10
2025-05-15 22:50:07:837 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21476 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:07' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 22:50:07:840 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:07' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 quantityChanged=10 stopPriceChanged=21409.5
2025-05-15 22:50:07:843 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 22:50:07:843 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21476 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:07' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:08:075 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 22:50:08:075 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:07.940Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:08:075 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21409.5 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:07' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:08:075 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 10,
  "orderType": "Stop",
  "stopPrice": 21409.5,
  "timeInForce": "GTC"
}'
2025-05-15 22:50:08:076 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:07.943Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:50:08:076 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:07.940Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:08:076 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:07.943Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:50:08:076 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21409.5 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:07' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:08:077 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21409.5 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:07' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:08:077 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:07.940Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:08:077 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:07.943Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 22:50:08:077 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "clOrdId": "ff330636683149bda22a9de52bbc22e4",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:50:08:078 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "1.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:07.943Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:50:19:747 (TRADIFY) Cbi.Account.Change0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21409.5 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:19' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 stopPriceChanged=21404.25 quantityChanged=10
2025-05-15 22:50:19:747 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21409.5 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:19' statementDate='2025-05-15' error=NoError comment='' nr=-1
2025-05-15 22:50:19:752 (TRADIFY) Cbi.Account.Change1: realOrderState=ChangePending orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21409.5 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:19' gtd='2099-12-01' statementDate='2025-05-15' limitPriceChanged=0 quantityChanged=10 stopPriceChanged=21404.25
2025-05-15 22:50:19:752 (TRADIFY) Tradovate.Adapter.Change0: count=1
2025-05-15 22:50:19:752 (TRADIFY) Tradovate.Adapter.Change1: orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangePending instrument='NQ JUN25' orderAction=BuyToCover orderType='Stop Market' limitPrice=0 stopPrice=21409.5 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16436 time='2025-05-15 22:50:19' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:50:20:060 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 22:50:20:060 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:19.926Z",
  "commandType": "Modify",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:20:060 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=ChangeSubmitted orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=ChangeSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21404.25 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:19' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:20:061 (TRADIFY) Tradovate.Adapter.ProcessOrderVersionEntity data='{
  "id": ************,
  "orderId": ************,
  "orderQty": 10,
  "orderType": "Stop",
  "stopPrice": 21404.25,
  "timeInForce": "GTC"
}'
2025-05-15 22:50:20:061 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:19.928Z",
  "commandStatus": "RiskPassed"
}'
2025-05-15 22:50:20:061 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:19.926Z",
  "commandType": "Modify",
  "commandStatus": "RiskPassed",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:20:062 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:19.928Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:50:20:062 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Accepted orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Accepted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21404.25 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:19' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:20:062 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Working instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21404.25 quantity=10 orderType='Stop Market' filled=0 averageFillPrice=0 time='2025-05-15 22:50:19' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:50:20:062 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:19.926Z",
  "commandType": "Modify",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:20:062 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:50:19.928Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 22:50:20:062 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:07.940Z",
  "commandType": "Modify",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:50:20:062 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:19.928Z",
  "orderId": ************,
  "execType": "Replaced",
  "ordStatus": "Working",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:57:16:602 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "************_1",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:57:16.460Z",
  "orderId": ************,
  "execType": "Trade",
  "ordStatus": "Filled",
  "action": "Buy",
  "cumQty": 10,
  "avgPx": 21404.5,
  "lastQty": 10,
  "lastPx": 21404.5,
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:57:16:603 (TRADIFY) Tradovate.Adapter.ProcessFillEntity data='{
  "id": ************,
  "orderId": ************,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:57:16.460Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "action": "Buy",
  "qty": 10,
  "price": 21404.5,
  "active": true,
  "finallyPaired": 0,
  "external": false
}'
2025-05-15 22:57:16:603 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Filled orderId='************' account='TDYG150355451300000018' name='Stop1' orderState=Filled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=0 stopPrice=21404.25 quantity=10 orderType='Stop Market' filled=10 averageFillPrice=21404.5 time='2025-05-15 22:57:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:57:16:619 (TRADIFY) Cbi.Order.OrderUpdateCallback.HandleOco.TriggerCancel: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:57:16' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:57:16:620 (TRADIFY) Cbi.Account.Cancel0: realOrderState=Working orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:57:16' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:57:16:620 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:57:16' statementDate='2025-05-15' error=NoError comment='' nr=4
2025-05-15 22:57:16:621 (TRADIFY) Cbi.Account.Cancel1: realOrderState=CancelPending orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:57:16' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:57:16:622 (TRADIFY) Cbi.Account.ExecutionUpdateCallback: executionId='************_1' account='TDYG150355451300000018' instrument='NQ JUN25' exchange=Globex price=21404.5 quantity=10 marketPosition=Long operation=Add orderID='************' isSod=False time='2025-05-15 22:57:16' statementDate='1800-01-01'
2025-05-15 22:57:16:622 (TRADIFY) Tradovate.Adapter.Cancel0: count=1
2025-05-15 22:57:16:622 (TRADIFY) Tradovate.Adapter.Cancel1: orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelPending instrument='NQ JUN25' orderAction=BuyToCover orderType='Limit' limitPrice=21391 stopPrice=0 quantity=10 tif=Gtc oco='9260eca55c1547dcbda80ac327c2b1bb' filled=0 averageFillPrice=0 onBehalfOf='' id=16437 time='2025-05-15 22:57:16' gtd='2099-12-01' statementDate='2025-05-15'
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.OnAddTrade: entryId='************_1' exitId='************_1' profitCurrencyBeforeCommissionAndFees=-1700
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:628 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:629 (TRADIFY) Tradovate.Adapter.ProcessPositionLogEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:57:16.460Z",
  "tradeDate": {
    "year": 2025,
    "month": 5,
    "day": 16
  },
  "netPos": 0,
  "bought": 12,
  "boughtValue": 256831.5,
  "sold": 12,
  "soldValue": 256770.5,
  "archived": false,
  "positionChangeType": "Trade",
  "fillId": ************
}'
2025-05-15 22:57:16:629 (TRADIFY) Cbi.Account.PositionUpdateCallback: instrument='NQ JUN25' account='TDYG150355451300000018' avgPrice=0 quantity=0 marketPosition=Flat operation=Remove
2025-05-15 22:57:16:629 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=CashValue currency=UsDollar value=*****
2025-05-15 22:57:16:629 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=RealizedProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:629 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=WeeklyProfitLoss currency=UsDollar value=*****
2025-05-15 22:57:16:630 (TRADIFY) NinjaScript.AtmStrategy.OnExecutionUpdate.Terminate2: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********'
2025-05-15 22:57:16:630 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.717Z",
  "action": "Buy",
  "ordStatus": "Filled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:57:16:633 (TRADIFY) NinjaScript.AtmStrategy.CloseStrategy: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********'
2025-05-15 22:57:16:646 (TRADIFY) Cbi.Position.Close0: instrument='NQ JUN25' account='TDYG150355451300000018' currentQuantity=0 signalName='Close'
2025-05-15 22:57:16:647 (TRADIFY) Cbi.Position.Close1: instrument='NQ JUN25' account='TDYG150355451300000018'
2025-05-15 22:57:16:852 (TRADIFY) Tradovate.Adapter.TradeMessageReceive3 UNEXPECTED status='200' data='{
  "commandId": ************
}'
2025-05-15 22:57:16:852 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:57:16.720Z",
  "commandType": "Cancel",
  "commandStatus": "Pending",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:57:16:853 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:57:16.720Z",
  "commandStatus": "AtExecution"
}'
2025-05-15 22:57:16:854 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:57:16.720Z",
  "commandType": "Cancel",
  "commandStatus": "AtExecution",
  "senderId": 3554513,
  "userSessionId": **********
}'
2025-05-15 22:57:16:854 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=CancelSubmitted orderId='************' account='TDYG150355451300000018' name='Target1' orderState=CancelSubmitted instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:57:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:57:16:854 (TRADIFY) Cbi.Account.OrderUpdateCallback: realOrderState=Cancelled orderId='************' account='TDYG150355451300000018' name='Target1' orderState=Cancelled instrument='NQ JUN25' orderAction=BuyToCover limitPrice=21391 stopPrice=0 quantity=10 orderType='Limit' filled=0 averageFillPrice=0 time='2025-05-15 22:57:16' statementDate='1800-01-01' error=NoError comment='' nr=-1
2025-05-15 22:57:16:855 (TRADIFY) Tradovate.Adapter.ProcessCommandReportEntity data='{
  "id": ************,
  "commandId": ************,
  "timestamp": "2025-05-16T02:57:16.721Z",
  "commandStatus": "ExecutionStopped"
}'
2025-05-15 22:57:16:855 (TRADIFY) Tradovate.Adapter.ProcessCommandEntity data='{
  "id": ************,
  "orderId": ************,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "clOrdId": "cef9f6a564f4436ca4c3f8a50d791c5f",
  "commandType": "New",
  "commandStatus": "ExecutionStopped",
  "senderId": 3554513,
  "userSessionId": **********,
  "isAutomated": true
}'
2025-05-15 22:57:16:856 (TRADIFY) Tradovate.Adapter.ProcessExecutionReportEntity data='{
  "id": ************,
  "commandId": ************,
  "name": "0.****************",
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:57:16.720Z",
  "orderId": ************,
  "execType": "Canceled",
  "ordStatus": "Canceled",
  "action": "Buy",
  "externalClOrdId": "0.****************"
}'
2025-05-15 22:57:16:856 (TRADIFY) Tradovate.Adapter.ProcessOrderEntity data='{
  "id": ************,
  "accountId": ********,
  "contractId": 3703587,
  "timestamp": "2025-05-16T02:50:02.727Z",
  "action": "Buy",
  "ordStatus": "Canceled",
  "executionProviderId": 9,
  "archived": false,
  "external": false,
  "admin": false
}'
2025-05-15 22:57:16:866 (TRADIFY) NinjaScript.AtmStrategy.OnOrderUpdate.Terminate: account='TDYG150355451300000018' instrument='NQ JUN25' id='*********'
2025-05-15 22:57:17:260 (TRADIFY) Cbi.Account.AccountItemUpdateCallback: account='TDYG150355451300000018' accountItem=GrossRealizedProfitLoss currency=UsDollar value=*****
