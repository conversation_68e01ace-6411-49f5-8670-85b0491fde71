#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Windows.Media; // For Brush
using System.Xml.Serialization; // For [XmlIgnore], [Browsable(false)]

using NinjaTrader.Data;
using NinjaTrader.Gui; // For Serialize
using NinjaTrader.Gui.Chart; // For ScaleJustification
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.DrawingTools; // For DashStyleHelper, Draw.*
// using NinjaTrader.NinjaScript.Indicators; // NOT strictly needed if only using MAX/MIN and not other indicator *classes*
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class SwingHiLow : Indicator // Or DualLookbackHighLowLevels (using MAX/MIN)
    {
        // No private Highest/Lowest indicator instances are needed when using MAX()/MIN() methods

        // Tags for our drawn lines
        private string tagP1High;
        private string tagP1Low;
        private string tagP2High;
        private string tagP2Low;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Draws horizontal lines for HH & LL of two periods using MAX/MIN.";
                Name = "SwingHiLow_MAXMIN"; // Name to reflect method
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                Period1 = 30;
                Period2 = 60;
                LineWidth = 2;

                ColorPeriod1High = Brushes.Cyan;
                ColorPeriod1Low = Brushes.Magenta;
                ColorPeriod2High = Brushes.LimeGreen;
                ColorPeriod2Low = Brushes.OrangeRed;

                StylePeriod1 = DashStyleHelper.Solid;
                StylePeriod2 = DashStyleHelper.Dash;

                AddPlot(Brushes.Transparent, "Period1HighValue");
                AddPlot(Brushes.Transparent, "Period1LowValue");
                AddPlot(Brushes.Transparent, "Period2HighValue");
                AddPlot(Brushes.Transparent, "Period2LowValue");
            }
            else if (State == State.Configure)
            {
                // Create unique tags for drawing objects ONCE.
                string uniqueBase = Name + "_" + 
                                    Instrument.FullName.Replace(" ", "").Replace("/", "") + "_" + // Sanitize instrument name
                                    BarsPeriod.BarsPeriodType.ToString() + // Get the enum name (e.g., "Minute", "Day")
                                    BarsPeriod.Value.ToString();          // Get the period value (e.g., "5", "1")

                tagP1High = uniqueBase + "_P1H";
                tagP1Low  = uniqueBase + "_P1L";
                tagP2High = uniqueBase + "_P2H";
                tagP2Low  = uniqueBase + "_P2L";

                Print($"Generated tags: {tagP1High}, {tagP1Low}, {tagP2High}, {tagP2Low}"); // For debugging;
            }
            else if (State == State.DataLoaded)
            {
                // No separate indicator instances to initialize for MAX/MIN
            }
            else if (State == State.Terminated)
            {
                if (tagP1High != null) RemoveDrawObject(tagP1High); // Add null checks for safety
                if (tagP1Low != null) RemoveDrawObject(tagP1Low);
                if (tagP2High != null) RemoveDrawObject(tagP2High);
                if (tagP2Low != null) RemoveDrawObject(tagP2Low);
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar == 0)
            {
                Period1HighValue[0] = Input[0]; Period1LowValue[0] = Input[0];
                Period2HighValue[0] = Input[0]; Period2LowValue[0] = Input[0];
                return;
            }

            // --- Period 1 Calculation & Drawing ---
            if (Period1 > 0 && CurrentBar >= Period1 -1) // MAX/MIN need Period-1 bars of history
            {
                double p1High = MAX(High, Period1)[0]; // Using NinjaScript's MAX() method
                double p1Low  = MIN(Low, Period1)[0];  // Using NinjaScript's MIN() method

                Period1HighValue[0] = p1High;
                Period1LowValue[0]  = p1Low;

                Draw.Line(this, tagP1High, false, Period1 - 1, p1High, 0, p1High, ColorPeriod1High, StylePeriod1, LineWidth);
                Draw.Line(this, tagP1Low,  false, Period1 - 1, p1Low,  0, p1Low,  ColorPeriod1Low,  StylePeriod1, LineWidth);
            }
            else 
            {
                RemoveDrawObject(tagP1High);
                RemoveDrawObject(tagP1Low);
                Period1HighValue[0] = CurrentBar > 0 ? Period1HighValue[1] : Input[0];
                Period1LowValue[0]  = CurrentBar > 0 ? Period1LowValue[1]  : Input[0];
            }

            // --- Period 2 Calculation & Drawing ---
            if (Period2 > 0 && CurrentBar >= Period2 -1)
            {
                double p2High = MAX(High, Period2)[0];
                double p2Low  = MIN(Low, Period2)[0];

                Period2HighValue[0] = p2High;
                Period2LowValue[0]  = p2Low;

                Draw.Line(this, tagP2High, false, Period2 - 1, p2High, 0, p2High, ColorPeriod2High, StylePeriod2, LineWidth);
                Draw.Line(this, tagP2Low,  false, Period2 - 1, p2Low,  0, p2Low,  ColorPeriod2Low,  StylePeriod2, LineWidth);
            }
            else 
            {
                RemoveDrawObject(tagP2High);
                RemoveDrawObject(tagP2Low);
                Period2HighValue[0] = CurrentBar > 0 ? Period2HighValue[1] : Input[0];
                Period2LowValue[0]  = CurrentBar > 0 ? Period2LowValue[1]  : Input[0];
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Period 1 (e.g., 20)", Order=1, GroupName="Parameters")]
        public int Period1 { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Period 2 (e.g., 40)", Order=2, GroupName="Parameters")]
        public int Period2 { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Line Width", Order=3, GroupName="Visuals")]
        public int LineWidth { get; set; }

        [XmlIgnore]
        [Display(Name="Period 1 High Color", Order=4, GroupName="Visuals")]
        public Brush ColorPeriod1High { get; set; }

        [Browsable(false)]
        public string ColorPeriod1HighSerializable
        {
            get { return NinjaTrader.Gui.Serialize.BrushToString(ColorPeriod1High); }
            set { ColorPeriod1High = NinjaTrader.Gui.Serialize.StringToBrush(value); }
        }

        [XmlIgnore]
        [Display(Name="Period 1 Low Color", Order=5, GroupName="Visuals")]
        public Brush ColorPeriod1Low { get; set; }

        [Browsable(false)]
        public string ColorPeriod1LowSerializable
        {
            get { return NinjaTrader.Gui.Serialize.BrushToString(ColorPeriod1Low); }
            set { ColorPeriod1Low = NinjaTrader.Gui.Serialize.StringToBrush(value); }
        }

        [XmlIgnore]
        [Display(Name="Period 2 High Color", Order=6, GroupName="Visuals")]
        public Brush ColorPeriod2High { get; set; }

        [Browsable(false)]
        public string ColorPeriod2HighSerializable
        {
            get { return NinjaTrader.Gui.Serialize.BrushToString(ColorPeriod2High); }
            set { ColorPeriod2High = NinjaTrader.Gui.Serialize.StringToBrush(value); }
        }

        [XmlIgnore]
        [Display(Name="Period 2 Low Color", Order=7, GroupName="Visuals")]
        public Brush ColorPeriod2Low { get; set; }

        [Browsable(false)]
        public string ColorPeriod2LowSerializable
        {
            get { return NinjaTrader.Gui.Serialize.BrushToString(ColorPeriod2Low); }
            set { ColorPeriod2Low = NinjaTrader.Gui.Serialize.StringToBrush(value); }
        }

        [NinjaScriptProperty]
        [Display(Name="Period 1 Line Style", Order=8, GroupName="Visuals")]
        public DashStyleHelper StylePeriod1 { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Period 2 Line Style", Order=9, GroupName="Visuals")]
        public DashStyleHelper StylePeriod2 { get; set; }
        
        // Public accessors for the plot values
        [Browsable(false)][XmlIgnore] public Series<double> Period1HighValue { get { return Values[0]; } }
        [Browsable(false)][XmlIgnore] public Series<double> Period1LowValue  { get { return Values[1]; } }
        [Browsable(false)][XmlIgnore] public Series<double> Period2HighValue { get { return Values[2]; } }
        [Browsable(false)][XmlIgnore] public Series<double> Period2LowValue  { get { return Values[3]; } }
        #endregion
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private SwingHiLow[] cacheSwingHiLow;
		public SwingHiLow SwingHiLow(int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			return SwingHiLow(Input, period1, period2, lineWidth, stylePeriod1, stylePeriod2);
		}

		public SwingHiLow SwingHiLow(ISeries<double> input, int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			if (cacheSwingHiLow != null)
				for (int idx = 0; idx < cacheSwingHiLow.Length; idx++)
					if (cacheSwingHiLow[idx] != null && cacheSwingHiLow[idx].Period1 == period1 && cacheSwingHiLow[idx].Period2 == period2 && cacheSwingHiLow[idx].LineWidth == lineWidth && cacheSwingHiLow[idx].StylePeriod1 == stylePeriod1 && cacheSwingHiLow[idx].StylePeriod2 == stylePeriod2 && cacheSwingHiLow[idx].EqualsInput(input))
						return cacheSwingHiLow[idx];
			return CacheIndicator<SwingHiLow>(new SwingHiLow(){ Period1 = period1, Period2 = period2, LineWidth = lineWidth, StylePeriod1 = stylePeriod1, StylePeriod2 = stylePeriod2 }, input, ref cacheSwingHiLow);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.SwingHiLow SwingHiLow(int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			return indicator.SwingHiLow(Input, period1, period2, lineWidth, stylePeriod1, stylePeriod2);
		}

		public Indicators.SwingHiLow SwingHiLow(ISeries<double> input , int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			return indicator.SwingHiLow(input, period1, period2, lineWidth, stylePeriod1, stylePeriod2);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.SwingHiLow SwingHiLow(int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			return indicator.SwingHiLow(Input, period1, period2, lineWidth, stylePeriod1, stylePeriod2);
		}

		public Indicators.SwingHiLow SwingHiLow(ISeries<double> input , int period1, int period2, int lineWidth, DashStyleHelper stylePeriod1, DashStyleHelper stylePeriod2)
		{
			return indicator.SwingHiLow(input, period1, period2, lineWidth, stylePeriod1, stylePeriod2);
		}
	}
}

#endregion
