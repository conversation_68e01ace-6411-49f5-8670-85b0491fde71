******************* Session Start (Version *******) *******************
2025-05-11 22:04:49:644 Cbi.Globals.MachineId: True/True A704A6037C73334DE437BD564DB72FD7
2025-05-11 22:04:49:649 NinjaTrader.Gui.LoginInternal user='mike3066' mode='Simulation'
2025-05-11 22:04:49:920 Core.Instrumentation.ActivitySource: enabled=True randomPercent=71.41518 enabledSessionsPercent=100 chartMs=250 enabledTypes='ChartPerformance Application NinjaScript Adapter Login '
2025-05-11 22:04:49:931 Core.Instrumentation.LogActivity: activityType=Login errorCode=NoError errorMessage=''
2025-05-11 22:04:50:694 (Simulation) NinjaTrader.Core.Authentication.GetAccessToken6
2025-05-11 22:04:50:705 (Simulation) NinjaTrader.Core.Authentication.QueueAccessTokenRenewal0 expSpanSeconds='4799.7851667' renewSecs='2399.89258335'
2025-05-11 22:04:51:501 Cbi.UserEntitlement.VerifyAsync.0 apiUrl='https://live.tradovateapi.com'
2025-05-11 22:04:52:166 Cbi.UserEntitlement.VerifyAsync.98: Evaluation expiration=2095-12-01, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31, Evaluation expiration=2095-12-31
2025-05-11 22:04:52:883 PrimaryMonitorWPFDPIScale=1.00
2025-05-11 22:04:53:214 Cbi.UserEntitlement.VerifyAsync.99: subscription=Free licensedProviders='Playback Simulator NinjaTrader NinjaTrader Continuum NinjaTrader FXCM IQFeed E-Signal External FOREX.com Interactive Brokers Barchart.com Kinetick FXCM Coinbase cTrader Schwab (Beta) Rithmic for NinjaTrader Brokerage Custom0 Custom2 Custom3 Custom4 Custom6 Custom7 Custom8 Custom9 Custom10 Provider13 Custom14 Provider26 Custom15 Custom16 Custom17 Custom18 Custom19 Custom20 Custom21 Custom22 Custom23 Custom24 Custom25 Custom26 Custom27 Custom28 Custom29 Custom30 Custom31 Custom32 Custom33 Custom34 Custom35 Custom36 Custom37 Custom38 Custom39 Custom40'
2025-05-11 22:04:53:350 InstallDir='C:\Program Files\NinjaTrader 8\'
2025-05-11 22:04:53:350 UserDataDir='C:\Users\<USER>\Documents\NinjaTrader 8\'
2025-05-11 22:04:53:350 MachineID='A704A6037C73334DE437BD564DB72FD7'
2025-05-11 22:04:53:351 OS='Microsoft Windows NT 10.0.26100.0'/'Win32NT'
2025-05-11 22:04:53:351 OSLanguage='en-US'
2025-05-11 22:04:53:351 OSEnvironment='64bit'
2025-05-11 22:04:53:351 Processors=8
2025-05-11 22:04:53:351 ThreadPool: minWorkerThreads=8 maxWorkerThreads=32767 minCompletionPortThreads=8 maxCompletionPortThreads=1000
2025-05-11 22:04:54:436 ProcessorSpeed=2.4 GHz
2025-05-11 22:04:54:436 PhysicalMemory=8192 MB
2025-05-11 22:04:54:752 DisplayAdapters=1/Intel(R) Iris(R) Xe Graphics
2025-05-11 22:04:54:752 Monitors=2/1280x720|1920x1080
2025-05-11 22:04:54:752 .NET/CLR Version='4.8'/64bit
2025-05-11 22:04:54:755 SQLiteVersion='1.0.116.0'
2025-05-11 22:04:54:755 ApplicationTimezone=EST +0 hour(s)
2025-05-11 22:04:54:755 ApplicationTimezone=UTC -4 hour(s)
2025-05-11 22:04:54:755 LocalTimezone=EST +0 hour(s)
2025-05-11 22:04:54:755 LocalTimezone=UTC -4 hour(s)
2025-05-11 22:04:54:826 DirectXRenderingHW
2025-05-11 22:04:54:826 Copying custom assemblies...
2025-05-11 22:04:54:855 Loading custom assemblies...
2025-05-11 22:04:54:855 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Custom.dll...
2025-05-11 22:04:54:980 Loading C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll...
2025-05-11 22:04:54:991 Deleting temporary files...
2025-05-11 22:04:55:349 Copying db and restoring templates...
2025-05-11 22:04:55:396 Loading third party assemblies...
2025-05-11 22:04:55:435 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\AIDuplicateAccountActions.dll...
2025-05-11 22:04:55:435 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IFVGBOT.dll...
2025-05-11 22:04:55:436 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\IGRIDPACK2CLIENT.dll...
2025-05-11 22:04:55:437 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\<EMAIL>...
2025-05-11 22:04:55:437 Loading 3rd party C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\ORBBOT.dll...
2025-05-11 22:04:55:437 Initializing database...
2025-05-11 22:04:55:437 Loading master instruments...
2025-05-11 22:04:55:656 (Db) Cbi.DB.get_Current.CreateConnection.Start
2025-05-11 22:04:55:657 (Db) Cbi.DB: connectionString='Data Source="C:\Users\<USER>\Documents\NinjaTrader 8\db\NinjaTrader.sqlite";Version=3;Synchronous=Full;Journal Mode=Off;Pooling=True;Max Pool Size=100;foreign keys=true;'
2025-05-11 22:04:55:967 (Db) Cbi.DB.get_Current.CreateConnection.End
2025-05-11 22:04:56:062 Loading instruments...
2025-05-11 22:04:56:326 Loading accounts...
2025-05-11 22:04:56:389 Loading users...
2025-05-11 22:04:56:409 Downloading server info...
2025-05-11 22:04:56:409 Starting instrument management...
2025-05-11 22:04:56:421 Starting timer...
2025-05-11 22:04:56:421 Creating file type watcher...
2025-05-11 22:04:56:422 Setting ATI...
2025-05-11 22:04:56:437 Server.HdsClient.Connect: type=IS server='is-us-nt-005.ninjatrader.com' port=31658 system='NT' useSsl=True
2025-05-11 22:04:56:442 Connecting ATI server...
2025-05-11 22:04:56:442 Server.AtiServer.Connect0
2025-05-11 22:04:56:443 Starting adapter server...
2025-05-11 22:04:56:446 Server.AtiServer.Connect1: Port='36973'
2025-05-11 22:04:56:448 Server.AtiServer.Connect2
2025-05-11 22:04:56:452 Starting bars dictionary...
2025-05-11 22:04:56:453 Starting recorder...
2025-05-11 22:04:56:454 Starting server(s)...
2025-05-11 22:04:56:479 Core.Globals.ApplicationStart.Commission: minVersion=1 maxVersion=3117
2025-05-11 22:04:56:479 Core.Globals.ApplicationStart.InstrumentList: minVersion=-456 maxVersion=456
2025-05-11 22:04:56:479 Core.Globals.ApplicationStart.MasterInstrument: minVersion=1 maxVersion=9777
2025-05-11 22:04:56:479 Core.Globals.ApplicationStart.Risk: minVersion=1 maxVersion=11957
2025-05-11 22:04:56:479 Core.Globals.ApplicationStart.TradingHours: minVersion=1 maxVersion=5059
2025-05-11 22:04:56:520 Required resource key 'brushOrderWorking' is missing.
2025-05-11 22:04:56:520 Required resource key 'brushOrderAccepted' is missing.
2025-05-11 22:04:56:520 Required resource key 'brushOrderPartFilled' is missing.
2025-05-11 22:04:56:520 Required resource key 'brushOrderInitialized' is missing.
2025-05-11 22:04:56:548 ChartHotKeys: Alerts='' DisableAllAlerts='' EnableAllAlerts='' DataSeries='Ctrl+F' Indicators='Ctrl+I' Strategies='Ctrl+S' Properties='Ctrl+T' CrosshairPointer='Ctrl+R' CrosshairLocal='Ctrl+Q' CrosshairGlobal='Ctrl+G' CrosshairGlobal2='Ctrl+N' CrosshairLock='Ctrl+L' AutoScaleAndReturn='' CyclePlotExecutions='Ctrl+E' ReloadHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5' SaveChartImage='Alt+S' ShowDataBox='Ctrl+D' ShowChartTrader='' ShowChartTraderHidden='' ShowScrollBar='' SnapModeBar='' SnapModeBarPrice='' SnapModeDisabled='' SnapModePrice='' StayInDrawMode='' ZoomIn='Ctrl+Alt+Z' ZoomOut='Ctrl+Alt+O' BarSpacingPlus='Ctrl+Down' BarSpacingMinus='Ctrl+Up' BarWidthPlus='Alt+Down' BarWidthMinus='Alt+Up' Arc='Ctrl+F7' AndrewsPitchfork='Ctrl+F8' ArrowDown='Alt+F3' ArrowLine='Ctrl+F2' ArrowUp='Alt+F2' Diamond='Alt+F5' Dot='Alt+F6' Ellipse='Ctrl+F11' ExtendedLine='F4' FibonacciCircle='F11' FibonacciExtension='F9' FibonacciRetracement='F8' FibonacciTimeExtension='F10' GannFan='Ctrl+F9' HorizontalLine='F6' Line='F2' Path='Ctrl+4' Polygon='Alt+F10' VolumeProfile='Ctrl+3' VWAP='Ctrl+5' Ray='F3' Rectangle='Ctrl+F12' RegressionChannel='Ctrl+F10' HideDrawingObjects='' RemoveDrawingObjects='' RegionHighlightX='Shift+F1' RegionHighlightY='Shift+F2' RiskReward='Ctrl+F4' Ruler='Ctrl+F3' Square='Alt+F7' Text='F12' TimeCycles='Alt+F11' TrendChannel='Ctrl+2' Triangle='Ctrl+F6' TriangleDown='Alt+F9' TriangleUp='Alt+F8' VerticalLine='F7'
2025-05-11 22:04:56:549 GlobalHotKeys: CloseWorkspace='' NextWorkspace='Shift+F3' PreviousWorkspace='' SaveWorkspace='' SaveAllWorkspaces='' NewAccountData='' NewTradePerformance='' NewAlertsLog='' NewBasicEntry='' NewChart='' NewFxBoard='' NewFxPro='' NewHotListAnalyzer='' NewLevel2='' NewMarketAnalyzer='' NewMarketWatch='' NewNews='' NewNinjaScriptEditor='' NewNinjaScriptOutput='' NewOrderTicket='' NewStrategyAnalyzer='' NewDynamicDom='' NewStaticDom='' NewTimeAndSales='' CancelAllOrders='' FlattenEverything='' OpenInstrumentManager='' OpenInstrumentList='' OpenDatabase='' OpenHotKeyManager='' OpenHistoricalData='' OpenCommission='' OpenRisk='' OpenTradingHoursManager='' OpenRemoveNinjaScript='' CloseOtherTabs='' CloseTab='' DuplicateInNewTab='' DuplicateInNewWindow='' Export='' Find='Ctrl+F' MoveToNewWindow='' Print='Ctrl+P' Share='Ctrl+Shift+S' LoadTemplate='' SaveTemplate='' SaveTemplateAsDefault=''
2025-05-11 22:04:56:549 MarketAnalyzerHotKeys: AddBlankRow='' AddLabelRow='' Columns='' Alerts='' EnableAllAlerts='' DisableAllAlerts='' AutoSort='' RowFilter='' ReloadHistorical='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-11 22:04:56:549 NinjaScriptEditorHotKeys: InsertCodeSnippet='F2' FindNext='F3' Compile='F5' GoToLine='Ctrl+G' CommentSelection='Ctrl+K' InlineSyntaxChecking='Ctrl+Alt+S' CollapseToDefinitions='' ExpandAllOutlining='' StartOutlining='' StopOutlining='' HideSelection='' StopHidingSelection='' ToggleAllOutlining='' ToggleOutlining='' Save='Ctrl+S' Undo='Ctrl+Z' Redo='Ctrl+Y'
2025-05-11 22:04:56:550 OrderEntryHotKeys: BuyAsk='' BuyBid='' BuyMarket='' SellAsk='' SellBid='' SellMarket='' BreakevenAtmStrategy='' BreakevenPosition='' CancelLastOrder='' CloseAtmStrategy='' ClosePosition='' DecreaseLastOrderPrice='' IncreaseLastOrderPrice='' ModifyLastOrder2Fill='' Reverse='' OcoOrder='Ctrl+Z' SimulatedOrder=''
2025-05-11 22:04:56:550 SuperDomHotKeys: Center='' Columns='' Indicators='' ReloadAllHistoricalData='Ctrl+Shift+R' ReloadNinjaScript='F5'
2025-05-11 22:04:56:550 OrderEntryHotKeys=disabled
2025-05-11 22:04:56:559 AutoClose=disabled
2025-05-11 22:04:56:959 Core.Instrumentation.LogActivity: activityType=Application errorCode=NoError errorMessage=''
2025-05-11 22:04:59:975 Server.HdsClient.ProcessStartupInfo.Info0: Commissions=0 InstrumentLists=9 MasterInstruments=0 Messages=3 Risks=0 RolloverCollection=259 TradingHours=0
2025-05-11 22:05:02:519 Server.HdsClient.ProcessStartupInfo.Info1: Commissions=0.10ms InstrumentLists=0.41ms MasterInstruments=0.07ms Messages=0.61ms Risks=0.08ms RolloverCollection=2541.81ms TradingHours=0.08ms
2025-05-11 22:05:02:520 Starting server message polling timer with interval 3600 seconds...
2025-05-11 22:05:51:290 Core.Instrumentation.LogActivity: activityType=Application errorCode=Panic errorMessage=''
2025-05-11 22:05:51:303 *************** unhandled exception trapped ***************
2025-05-11 22:05:51:303 Could not find a part of the path 'C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\MarketAnalyzerColumns'.
2025-05-11 22:05:51:303 System.IO.DirectoryNotFoundException: Could not find a part of the path 'C:\Users\<USER>\Documents\NinjaTrader 8\bin\Custom\MarketAnalyzerColumns'.
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileSystemEnumerableIterator`1.CommonInit()
   at System.IO.FileSystemEnumerableIterator`1..ctor(String path, String originalUserPath, String searchPattern, SearchOption searchOption, SearchResultHandler`1 resultHandler, Boolean checkHost)
   at System.IO.Directory.GetFiles(String path, String searchPattern, SearchOption searchOption)
   at NinjaTrader.Gui.NinjaScript.ExportSelectFileView..ctor(ObservableCollection`1 exportItems, Dispatcher dispatcher)
   at NinjaTrader.Gui.NinjaScript.ExportNinjaScriptViewModel.OnExportItemAdd(Object sender, RoutedEventArgs e)
   at System.Windows.Input.CommandBinding.OnExecuted(Object sender, ExecutedRoutedEventArgs e)
   at System.Windows.Input.CommandManager.ExecuteCommandBinding(Object sender, ExecutedRoutedEventArgs e, CommandBinding commandBinding)
   at System.Windows.Input.CommandManager.FindCommandBinding(CommandBindingCollection commandBindings, Object sender, RoutedEventArgs e, ICommand command, Boolean execute)
   at System.Windows.Input.CommandManager.FindCommandBinding(Object sender, RoutedEventArgs e, ICommand command, Boolean execute)
   at System.Windows.Input.CommandManager.OnExecuted(Object sender, ExecutedRoutedEventArgs e)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.RoutedEventHandlerInfo.InvokeHandler(Object target, RoutedEventArgs routedEventArgs)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.Input.RoutedCommand.ExecuteImpl(Object parameter, IInputElement target, Boolean userInitiated)
   at MS.Internal.Commands.CommandHelpers.CriticalExecuteCommandSource(ICommandSource commandSource, Boolean userInitiated)
   at System.Windows.Controls.Primitives.ButtonBase.OnClick()
   at System.Windows.Controls.Button.OnClick()
   at System.Windows.Controls.Primitives.ButtonBase.OnMouseLeftButtonUp(MouseButtonEventArgs e)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.RoutedEventHandlerInfo.InvokeHandler(Object target, RoutedEventArgs routedEventArgs)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.ReRaiseEventAs(DependencyObject sender, RoutedEventArgs args, RoutedEvent newEvent)
   at System.Windows.UIElement.OnMouseUpThunk(Object sender, MouseButtonEventArgs e)
   at System.Windows.RoutedEventArgs.InvokeHandler(Delegate handler, Object target)
   at System.Windows.RoutedEventHandlerInfo.InvokeHandler(Object target, RoutedEventArgs routedEventArgs)
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.UIElement.RaiseTrustedEvent(RoutedEventArgs args)
   at System.Windows.Input.InputManager.ProcessStagingArea()
   at System.Windows.Input.InputManager.ProcessInput(InputEventArgs input)
   at System.Windows.Input.InputProviderSite.ReportInput(InputReport inputReport)
   at System.Windows.Interop.HwndMouseInputProvider.ReportInput(IntPtr hwnd, InputMode mode, Int32 timestamp, RawMouseActions actions, Int32 x, Int32 y, Int32 wheel)
   at System.Windows.Interop.HwndMouseInputProvider.FilterMessage(IntPtr hwnd, WindowMessage msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at System.Windows.Interop.HwndSource.InputFilterMessage(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndWrapper.WndProc(IntPtr hwnd, Int32 msg, IntPtr wParam, IntPtr lParam, Boolean& handled)
   at MS.Win32.HwndSubclass.DispatcherCallbackOperation(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-05-11 22:15:40:805 Shutting down NinjaTrader
2025-05-11 22:15:40:806 Shutting down instrument management
2025-05-11 22:15:40:810 Shutting down instrument threads
2025-05-11 22:15:40:810 Shutting down BP thread
2025-05-11 22:15:40:810 Shutting down recorder
2025-05-11 22:15:40:833 Shutting down file type watcher
2025-05-11 22:15:40:833 Shutting down ATI server
2025-05-11 22:15:40:841 Shutting down auto trade component
2025-05-11 22:15:40:844 Shutting down SMTP server
2025-05-11 22:15:40:851 Shutting down adapter server
2025-05-11 22:15:40:875 Shutting down server(s)
2025-05-11 22:15:40:880 Shutting down mail thread
2025-05-11 22:15:40:880 Shutting down sound thread
2025-05-11 22:15:40:880 Shutting down timer
2025-05-11 22:15:40:880 Shutting down alerts timer
2025-05-11 22:15:40:881 Shutting down message timer
2025-05-11 22:15:40:881 Shutting down db
2025-05-11 22:15:40:884 Flushing DB thread
2025-05-11 22:15:41:014 Shutting down bars dictionary
2025-05-11 22:15:41:029 Shutting down user entitlement threads
2025-05-11 22:15:41:041 Shutting down logs
2025-05-11 22:15:41:061 Shutting down bars types
2025-05-11 22:15:41:070 Shutting down UI threads
2025-05-11 22:15:41:078 ************************ Session End ************************
